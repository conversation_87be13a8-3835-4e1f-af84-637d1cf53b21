<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					   <text >负责人离职客户</text>
					</view>
					<view slot="right" >
						<view class="trwhtip" @click="gotodyx">
							<text class="iconfont icon-tishi2" ></text>
						</view>
					</view>
		</tn-nav-bar>
		
	<view class="tabs-fixed-blank"></view>
	<!-- 顶部自定义导航 -->

		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 75vw;overflow: hidden;padding-left:10px;">

					<view class="tbdhdown">
						<view class="xite" >
							 <text class="iconfont icon-kehuC" @click="seljsdx" style="margin-right:10upx;font-size:32upx"></text>
							 <text @click="seljsdx"  >{{ygname ? ygname : '离职员工' }}</text>
							 <block v-if="ygid > 0">
							 	<text class="iconfont icon-cuohao02" style="margin-left:10upx;font-size:24upx" @click="clsygid"></text> 
							 </block>
						</view>
						
					</view>
				</view>
				<view class="justify-content-item gnssrr" style="width: 25vw;overflow: hidden;">
					<!-- <view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view> -->
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
				</view>
			</view>

		</view>

		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>
		
		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>
		
		
			<block v-if="listdata.length>0">
			
			     
						   <view class="ygc">
						   	<block v-for="(item,index) in listdata"	:key="index">
						   		<view class="item"  >
						   			
						   			<view class="inf1">
						   				  <view class="djcon">{{item.dengjitxt}}</view>
						   				  <view class="tx"> 
						   					  <view class="avatar"><image :src="staticsfile + item.avatar"></image></view>
						   					  <view class="sexicon" v-if="item.sex > 0"><image :src="'/static/images/sexicon'+item.sex+'.png'"></image></view>
						   				  </view>
						   				  <view class="xx">
						   					  <view class="tit">
						   						  <text class="name">{{item.realname}}</text>
						   						  <text class="uid" >（编号:{{item.id}})</text>
						   					  </view>
						   					  <view class="ssyg">所属门店：{{item.mdname}}</view>
											  <view class="ssyg" >所属员工：{{item.ygname}}</view>
						   				  </view>
										  
						   				  <view class="clear"></view>
						   			</view>
									
									<view class="inf3">
										<view class="ite" @click="gotojytj" :data-khid="item.id" :data-khname="item.realname">
											<view class="ic"><image src="/static/images/khglicon1.png"></image></view><view class="wb">交易统计</view><view class="clear"></view>
										</view>
										<view class="ite" @click="gotokhxq" :data-khid="item.id">
											<view class="ic" ><image src="/static/images/khglicon2.png"></image></view><view class="wb">查看详情</view><view class="clear"></view>
										</view>
										<view class="ite" @click="gotosetfzr" :data-khid="item.id">
											<view class="ic" ><image src="/static/images/khglicon4.png"></image></view><view class="wb">变更负责人</view><view class="clear"></view>
										</view>
										<view class="clear"></view>
									</view>
									
						   		</view>  
						   	</block>
						   </view>  
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关数据</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">客户名称</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入客户名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
			
		<tn-popup v-model="jsdxselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">请选择离职员工</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="selqyyglist">
					<radio-group @change="jsrysetChange">
						<block v-for="(item, index) in yglisdata" :key="index">
							<view class="item">
								<label>
									<view class="icon">
										<radio :value="item.id" :data-name="item.realanme" color="#FFD427" />
									</view>
									<view class="vstxt">{{item.realname}}</view>
									<view class="mdtt">({{item.mdname}})</view>
									<view class="clear"></view>
								</label>
							</view>
						</block>
					</radio-group>
					</block>
		
					<view style="height:160upx"></view>
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<view class="bomsfleft">
								<button class="bomsubbtn" @click="closejsdx" style="background:#ddd;">
									<text>关闭</text>
								</button>
							</view>
							<view class="bomsfright">
								<button class="bomsubbtn" @click="qrzdysxm">
									<text>确认选择</text>
								</button>
							</view>
							<view class="clear"></view>
						</view>
					</view>
		
				</view>
			</scroll-view>
		</tn-popup>
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[],
				show3:false,
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				tjdata:[],
				
				dwtype:0,
				ygid:0,
				ygname:'',
				ygdata:[],
				
				ygkw: '',
				yglisdata: [],
				jsdxselshow: false,
				dpselshow: false,
				dpdata: '',
				thmdid: 0,
				thmdname: '',
			}
		},
		onLoad(options) {
			let that=this;
			setTimeout(function() {
				  that.page = 0;
				  that.listdata = [];
				  that.isloading = false;
				  that.loadingType = -1;
				  that.loaditems(); 
			}, 100);
			
		},
		onShow() {
			let that=this;
	
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			tbdhdownclk(e) {
				let that = this;
				let dwtype = e.currentTarget.dataset.dwtype;
			
				that.dwtype = dwtype;
				if (dwtype == 0) {
					that.tbdhdownshow = false;
					this.thkw = '';
					this.thkwtt = '';
					that.rqkstime = '';
					that.rqjstime = '';
					that.thkw = '';
					that.ygid = 0;
					that.ygname = '';
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				} else {
					that.tbdhdownshow = true;
				}
			
			},
			gotosetfzr(e){
				let khid = e.currentTarget.dataset.khid;
				uni.navigateTo({
					url:'/pages/work/qykhgl/setfzr?khid='+khid
				})
			},
			gotojytj(e){
				let khid=e.currentTarget.dataset.khid;
				let khname=e.currentTarget.dataset.khname;
				uni.navigateTo({
					url:'/pages/work/khgl/jytj?khid='+khid+'&khname='+khname
				})
			},
			gotokhxq(e){
				  let that=this;
				  let khid=e.currentTarget.dataset.khid;
				  console.log(khid);
				  uni.navigateTo({
					url:'./khset?khid='+khid
				  })
			},
			gotodyx() {
					uni.navigateTo({
						url: '/pages/danye/index?id=12'
					})
			},
		
			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					// this.thkw='';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			
			
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;
			
				// this.rqkstime='';
				// this.rqjstime='';
				// that.ddtype = 0;
				// that.sta = 0;
			
				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							ygid:that.ygid ? that.ygid : 0 ,
							dpid:that.thmdid ? that.thmdid : 0 ,
							kw:that.thkw ? that.thkw : '' ,
						}
					if(page!=1){
						that.isloading=true;
					} 
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/qykhgl/lzygkh', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
				 
				 seljsdx() {
				 
				 	let that = this;
				 	let da = {
				 		ygkw: that.ygkw,
						khid: 0,
						mdid: that.thmdid ? that.thmdid : 0,
				 		gtype: 2,
						bgfzr: 0,
				 	}
				 	uni.showLoading({
				 		title: ''
				 	})
					
				 	this.$req.post('/v1/qykhgl/getqyygdata', da)
				 		.then(res => {
				 			uni.hideLoading();
							console.log(res);
				 			if (res.errcode == 0) {
				 				that.yglisdata = res.data.yglisdata;
								this.jsdxselshow = true;
				 			} else {
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
				 					success() {
				 						
				 					}
				 				})
				 			}
				 		})
				 },
				clsygid(){
					let that=this;
					this.ygid=0;
					this.ygname='';
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				},
				 closejsdx() {
				 	this.jsdxselshow = false;
				 },
				 jsrysetChange(e) {
				 	console.log(e);
				 	let value = e.detail.value;
				 	this.thsjsdxtemp = value;
				 },
				 
				 qrzdysxm() {
				 	let that = this;
				 	let jsvalue = this.thsjsdxtemp;
					console.log(jsvalue);
				 	if (!jsvalue ) {
				 		uni.showToast({
				 			icon: 'none',
				 			title: '请选择离职人员',
				 		});
						return false;
				 	}
				 	let da = {
				 		ygid: jsvalue,
				 	
				 	}
				 	uni.showLoading({
				 		title: ''
				 	})
				 	this.$req.post('/v1/qykhgl/getjsygdata', da)
				 		.then(res => {
				 			uni.hideLoading();
				 			console.log(res);
				 			if (res.errcode == 0) {
				 				that.jsdxselshow = false;
				 				if(res.data.thygdata){
				 					that.ygid = res.data.thygdata.id;
				 					that.ygname = res.data.thygdata.realname;
				 				}
				 			} else {
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
				 					success() {
				 
				 					}
				 				})
				 			}
				 		})
				 
				 },
		
		   
		}
	}
</script>

<style lang='scss'>
.ygc{margin:0 32upx}
	.ygc .item{background: #FFFFFF;border-radius: 20rpx;margin-bottom:20upx;padding:28upx 28upx;position: relative;}
	.ygc .item .wgluid{position: absolute;right:30upx;top:80upx;font-size:20upx;background:#FFD427;border-radius: 100upx;padding:5upx 15upx;}
	.ygc .item .tx{float:left;position: relative;}
	.ygc .item .tx .avatar image{width: 100upx;height:100upx;border-radius:100upx;display: block;}
	.ygc .item .tx .sexicon{position: absolute;bottom:-20upx;left:35upx}
	.ygc .item .tx .sexicon image{width: 32upx;height:32upx;}
	.ygc .item .xx{float:left;margin-left:30upx}
	.ygc .item .xx .tit{height:40upx;line-height:40upx;font-size:28upx;padding-top:10upx;margin-bottom:20upx}
	.ygc .item .xx .tit .name{font-size:28upx;font-weight:600}
	.ygc .item .xx .tit .uid{font-size: 24rpx;margin-left:10upx;color:#888}
	.ygc .item .xx .ssyg{height:30upx;line-height:30upx;font-size:20upx;margin-top:0upx;color: #7F7F7F;overflow: hidden;}
	.ygc .djcon{position: absolute;right:0;top:0;font-size:24upx;background:#E7FFE2;border-radius: 0rpx 20rpx 0rpx 20rpx;height:42upx;line-height:42upx;padding:0 15upx;color:#0EC45C}
	.ygc .inf3{text-align: center;margin-top:30upx}
	.ygc .inf3 .ite{float:left;width:33%;}
	.ygc .inf3 .ite .ic{display: inline-block;}
	.ygc .inf3 .ite image{width:36upx;height:36upx;vertical-align: middle;}
	.ygc .inf3 .ite .wb{display: inline-block;font-size: 24rpx;margin-left:10upx}
	

</style>
