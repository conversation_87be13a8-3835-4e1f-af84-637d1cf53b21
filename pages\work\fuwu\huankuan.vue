<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">


			<form @submit="formSubmit">
				<view class="sqbdcon">

					<block v-if="isshcz!=1">
						<view class="maxcon">
							<view class="shztip">
								<view class="icon"><text class="iconfont icon-jigou_wushuju"></text></view>
								<view class="tit">{{isshcztip}}</view>
							</view>
						</view>
					</block>

					<view class="maxcon" style="padding-bottom:10upx">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ddtypetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>单据编号</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ordernum}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户姓名</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khname}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>预付金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">￥{{djdata.yfjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总利息</view>
							<view class="inpcon">
								<view class="inp ">￥{{djdata.lxzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总还款金额</view>
							<view class="inpcon">
								<view class="inp ">￥{{djdata.hkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>


					</view>



					<block v-if="type==1">
						<view class="maxcon" style="padding-bottom:10upx">

							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>本期还款时间</view>
								<view class="inpcon">
									<view class="inp">{{hkdata.jhhktime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>本期应还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">￥{{hkdata.yhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>本期已还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">￥{{hkdata.bqyhkjine}}</view>
								</view>
								<view class="clear"></view>
							</view>

						</view>
						<view class="maxcon">
							<view class="vstit">本期还款金额</view>
							<view class="jinecon">
								<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
								<input type="digit" name='hkjine' v-model="hkjine" placeholder='在此输入还款金额' maxlength='60'
									class="inputss" :disabled="isshcz!=1" />
							</view>
						</view>
					</block>

					<block v-if="type==2 ">
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtimetxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">￥{{djdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp ">￥{{djdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>

						<view class="maxcon">
							<view class="vstit">还款金额</view>
							<view class="jinecon">
								<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
								<input type="digit" name='hkjine' v-model="hkjine" placeholder='在此输入还款金额' maxlength='60'
									class="inputss" :disabled="isshcz!=1" />
							</view>
						</view>

					</block>




					<block v-if="type==3">
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtimetxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">￥{{djdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp ">￥{{djdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>

						<view class="maxcon">
							<view class="vstit">提前结清金额</view>
							<view class="jinecon">
								<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
								<input type="digit" name='hkjine' v-model="hkjine" placeholder='在此输入金额' maxlength='60'
									class="inputss" :disabled="djdata.hkfs==2 || isshcz!=1" @blur="jslrks" />
							</view>
						</view>



						<view class="maxcon" v-if="djdata.hkfs==1">
							<block v-if="orshfeiyong >= 0">
								<view>
									利润：<text style="color:#ff0000;font-size:32upx">{{shfeiyong}}</text>
								</view>
							</block>
							<block v-else>
								<view>
									亏损：<text style="color:#4aac09;font-size:32upx">{{shfeiyong}}</text>
								</view>
							</block>
						</view>

					</block>



					<block v-if="type==4">
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtimetxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">￥{{djdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp ">￥{{djdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>亏损金额</view>
								<view class="inpcon">
									<view class="inp " style="color:#4aac09">￥{{ksjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>

						<!-- 	<view class="maxcon">
												<view class="vstit">亏损金额</view>
												<view class="jinecon">
													<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
													<input  type="digit"  name='hkjine' v-model="hkjine"  placeholder='在此输入亏损金额' maxlength='60' class="inputss" />
												</view>
										</view> -->
					</block>
					
					
					
					
					<block v-if="type==5">
						<view class="maxcon" style="padding-bottom:10upx">
					
							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">￥{{djdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp ">￥{{djdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
					
						</view>
						
						<view class="maxcon">
							<view class="vstit">本次还款明细</view>
							
							<view class="khjhcon" style="padding-left:70upx">
								<view class="hkjh-timeline">
									<view class="hkjh-timeline-content">
								
											<block v-for="(item,index) in dbhkdata" :key="index">
												<view class="item hkjh-timeline-item" >
													<em class="yd-timeline-icon"></em>
													<view class="hkjine">
														<view >¥{{item.yhkzjine}}</view>
														<block v-if="item.bqsyhkjine > 0">
															<view class="yhdbc">
																<text class="yhtipm">已还 {{item.bqyhkjine}}</text> 
																<text class="yhtipm" >本次还款 <text class="bc">{{item.bqsyhkjine}}</text></text>
															</view>
														</block>
													</view>
													<view class="qbcon">
														<view class="t">{{item.xhtit}}</view>
														<view class="r">{{item.rq}}</view>
													</view>
												</view>
											</block>
							
									</view>
								</view>
							</view>
							
						</view>
						
						<view class="maxcon">
							<view class="rowx">
									<view class="inp ">本次还款：<text style="color:#ff0000">{{dbhkqs}}</text> 笔，合计：<text style="color:#ff0000">￥{{hkjine}}</text></view>
							</view>
						</view>
						
						
						
					</block>
					
					
					
					

					<block v-if="isshcz==1">
						<view class="maxcon">
							<view class="vstit">备注说明</view>
							<view class="bzcon">
								<textarea name="remark" class="beizhu" v-model="remark"></textarea>
							</view>

							<view class="rowxmttp">
								<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
									<block v-for="(item,index) in img_url_ok" :key="index">
										<view class='item'>
											<view class="del" @tap="deleteImg" :data-index="index"><i
													class="iconfont icon-shanchu2"></i></view>
											<image :src="staticsfile+item" :data-src='staticsfile + item '
												@tap='previewImage'></image>
										</view>
									</block>
									<view class='item' v-if="img_url_ok.length <= 8">
										<image @tap="chooseimage" src='/static/images/uppicaddx.png'></image>
									</view>
									<view class="clear"></view>
								</view>
							</view>


						</view>
					</block>


				</view>


				<block v-if="isshcz==1">
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="bomsubbtn" form-type="submit">
								<text>确认提交审核</text>
							</button>
						</view>
					</view>
				</block>
			</form>




		</view>

		<view style="height:120upx"></view>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				status: 0,
				jine: 0,
				img_url: [],
				img_url_ok: [],
				spid: 0,
				djid: 0,
				fyid: 0,
				spname: '',
				spdata: [],
				fydata: [],
				jine: '',
				remark: '',
				type: 1,
				xtitle: '',
				djdata: '',
				shfeiyong: 0,
				orshfeiyong: 0,
				hkid: 0,
				hkdata: '',
				hkjine: 0,
				ksjine: 0,
				xtitle: '',

				sqdata: '',
				isshcz: 1,
				isshcztip: '',
				hkidstr:'',
				dbhkdata:'',
				dbhkqs:0,

			}
		},

		onLoad(options) {
			let that = this;
			let type = options.type ? options.type : 0;
			let djid = options.djid ? options.djid : 0;
			let hkid = options.hkid ? options.hkid : 0;
			let hkidstr = options.hkidstr ? options.hkidstr : '';
			

			let xtitle = '还款';
			if (type == 3) {
				xtitle = '提前结清';
			}
			if (type == 4) {
				xtitle = '亏损';
			}
			this.xtitle = xtitle;
			this.type = type;
			this.hkid = hkid;
			this.djid = djid;
			this.hkidstr = hkidstr;
			this.loadData();
		},
		onShow() {

		},

		methods: {
			toback: function() {
				uni.navigateBack();
			},
			jslrks() {
				let that=this;
				if(that.hkjine < 0){
					uni.showToast({
						icon: 'none',
						title: '请输入结清金额！'
					});
					return false;
				}
				let da = {
					djid: that.djid,
					hkjine: that.hkjine ? that.hkjine : 0  ,
				}
				this.$req.post('/v1/fuwu/tchqjslrks', da)
					.then(res => {
						console.log(res);
						if (res.errcode == 0) {
							that.shfeiyong = res.data.shfeiyong;
							that.orshfeiyong = res.data.orshfeiyong;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},

			loadData() {
				let that = this;
				let da = {
					hkid: that.hkid,
					djid: that.djid,
					type: that.type,
					hkidstr: that.hkidstr ?  that.hkidstr : '',
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/huankuan', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.hkdata = res.data.hkdata;
							that.sqdata = res.data.sqdata;
							that.ksjine = res.data.ksjine;
							that.shfeiyong = res.data.shfeiyong;
							that.orshfeiyong = res.data.orshfeiyong;
							if (res.data.sqdata) {
								that.type = res.data.sqdata.vtype;
							}

							that.hkjine = res.data.hkjine;
							that.isshcz = res.data.isshcz;
							that.isshcztip = res.data.isshcztip;
							
							that.dbhkdata = res.data.dbhkdata;
							that.dbhkqs = res.data.dbhkqs;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},



			formSubmit: function(e) {
				let that = this;
				var formdata = e.detail.value

				let pvalue = e.detail.value;

				if (that.type == 1 || that.type == 2) {
					if (!that.hkjine || that.hkjine < 0) {
						uni.showToast({
							icon: 'none',
							title: '请输入还款金额',
						});
						return false;
					}
				}
				// if (!that.remark) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请输入备注说明',
				// 	});
				// 	return false;
				// }

				uni.showModal({
					title: '',
					content: '以上信息已确认无误并提交吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							uni.showLoading({
								title: '处理中...'
							})
							let da = {
								'type': that.type,
								'djid': that.djid,
								'hkid': that.hkid,
								'hkjine': that.hkjine,
								'remark': that.remark ? that.remark : '',
								'pictures': that.img_url_ok ? that.img_url_ok : '',
								'hkidstr': that.hkidstr ?  that.hkidstr : '',
							}
							that.$req.post('/v1/fuwu/huankuansave', da)
								.then(res => {
									uni.hideLoading();
									console.log(res);
									if (res.errcode == 0) {
										uni.setStorageSync('thupsplist', 1)
										uni.setStorageSync('thuprkdshlist', 1);
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000)
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})


						}
					}
				})







			},



		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

	.matxcont {
		margin: 32upx;
		padding: 32upx;
		border-radius: 20upx;
		background: #fff;
		min-height: 500upx;
	}

	.notixiancon {
		text-align: center;
	}

	.tipcon {
		padding: 50px 0
	}

	.tipcon text {
		font-size: 55px;
		color: #FFD427
	}

	.smlink {
		margin-top: 40px
	}

	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
		padding-bottom: 32upx;
		margin-bottom: 32upx
	}

	.ttinfo .inf1 .tx {}

	.ttinfo .inf1 .xx {
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
		margin: 0 auto;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}

	.ttinfo .inf2 {
		text-align: center;
		padding-bottom: 30upx
	}

	.ttinfo .inf2 .kyjine {
		font-size: 48rpx;
		font-weight: 600;
	}

	.ttinfo .inf2 .tmsm {
		color: #7F7F7F;
		margin-top: 20upx;
		font-size: 24rpx;
	}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.jinecon {
		position: relative;
		margin: 10upx 0
	}

	.jinecon .icon {
		position: absolute;
		left: 0;
		top: 10upx;
	}

	.jinecon .icon .iconfont {
		font-size: 40upx
	}

	.jinecon .inputss {
		font-size: 40upx;
		color: #000;
		padding-left: 40upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 110px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
</style>