<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>
					选择物品
				</text>
			</view>
			<view slot="right">
			</view>

		</tn-nav-bar>
		<view class="tabs-fixed-blank"></view>


		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 100;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 75vw;overflow: hidden;padding-left:10px;">

					<view class="tbdhdown">
						<view class="xite" :class="ddtype==0 &&  sortid==0  && ckid==0    ? 'hoverss' : ''"
							@click="tbdhdownclk" :data-dwtype="0">
							<view class="con">全部</view>
						</view>
						<view class="xite" :class="dwtype==1 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="1">
							<view class="con">{{ddtype!=0 ? ddtypetxt : '类型'}} <text
									class="iconfont icon-jiantou-copy-copy"></text></view>
						</view>
						<view class="xite" :class="dwtype==9 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="9">
							<view class="con">{{pxfs!=0 ? pxfstxt : '排序'}} <text
									class="iconfont icon-jiantou-copy-copy"></text></view>
						</view>
					</view>
				</view>
				<view class="justify-content-item gnssrr" style="width: 25vw;overflow: hidden;">
					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>



		</view>


		<view class="tabs-fixed " style="z-index: 99;" :style="{top: vuex_custom_bar_height + 38 + 'px'}">
			<view class="flckselcon">
				<view class="sf1">
					<view class="wpflsel">
						<view class="da0"><text class="iconfont icon-fenlei"></text></view>
						<view class="da1">

							<view @click="selwpfl">{{sortid ? sortnamep : '按物品分类筛选'}}</view>

						</view>
						<view class="da2">
							<block v-if="sortid > 0">
								<text class="iconfont icon-cuohao02" style="margin-left:10upx"
									@click="clssortid"></text>
							</block>
							<block v-else>
								<text class="iconfont icon-jiantou2" style="margin-left:10upx"></text>
							</block>
						</view>
						<view class="clear"></view>
					</view>
				</view>
				<view class="sf2">
					<view class="wpcksel">
						<text class="iconfont icon-cangkupeizhi" style="margin-right:10upx"></text>

						<text @click="selcangku">{{ckname ? ckname : '按仓库筛选' }}</text>
						<block v-if="ckid > 0">
							<text class="iconfont icon-cuohao02" style="margin-left:10upx" @click="clsckid"></text>
						</block>
						<block v-else>
							<text class="iconfont icon-jiantou2" style="margin-left:10upx"></text>
						</block>

					</view>
				</view>
				<view class="clear"></view>
			</view>
		</view>
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 40 + 'px'}"></view>

		<view class="tjtiaox">
			<block v-if="thkw || rqkstime">
				<block v-if="thkw">
					<view class="ssgjc">
						<text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text>
						</view>
					</view>
				</block>
				<block v-if="rqkstime">
					<view class="ssgjc">
						<text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text>
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</block>
		</view>



		<block v-if="listdata.length>0">



			<view class="splist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="item ">


						<view class="inf1">
							<view class="spbh">编号：{{item.id}}</view>
							<view class="otbstxt" v-if="item.otbstxt">{{item.otbstxt}}</view>
							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + item.smallpic" @tap='previewImagev'
										:data-picarr="item.picturesarr"></image>
								</view>
								<view class="xx">
									<view class="n0"><text class="ddtype"
											:class="'ys'+item.ddtype">{{item.ddtypename}}</text> {{item.sortname}}
									</view>
									<view class="n1">{{item.title}}</view>
									<view class="n2">库存：{{item.kucun}}</view>
									<view class="n2">售价：<text class="zj">￥{{item.sjjiage}}</text></view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
						<view class="inf2">
							<view class="f1">入库时间：{{item.rktime}}</view>
							<view class="f1">物品所在：{{item.mdname}} <text style="margin:0 15upx">-</text> {{item.ckname}}
							</view>
							<block v-if="item.isxz==1">
								<view class="setbtn" @click="addckd" :data-spid='item.id' :data-spname='item.title'
									:data-sjjiage='item.sjjiage' :data-kucun='item.kucun'>加入订单</view>
							</block>
							<block v-else>
								<view class="setbtn btn22" @click="bkxzcz">加入订单</view>
							</block>
						</view>




					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关物品</view>
			</view>
		</block>





		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">物品搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入物品名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>

		<tn-popup v-model="tbdhdownshow" mode="top" :marginTop="vuex_custom_bar_height + 38" :zIndex=100
			:borderRadius="20">
			<block v-if="dwtype==1">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in ddtypedata" :key="index">
						<view class="vitem1" :class="item.value==ddtype ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="1" :data-value="item.value" :data-name="item.name">
							{{item.name}}
							<text class="iconfont icon-duigou1 "></text>
						</view>
					</block>
				</view>
			</block>

			<block v-if="dwtype==9">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in pxdata" :key="index">
						<view class="vitem1" :class="item.value==pxfs ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="9" :data-value="item.value" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==pxfs"></text>
						</view>
					</block>
				</view>
			</block>

		</tn-popup>



		<tn-popup v-model="ckselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择仓库</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="xselcklist">
					<block v-for="(item,index) in ckdata" :key="index">

						<view class="item" @click="selthck" :data-ckid="item.id" :data-ckname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>

					</block>
				</view>
			</scroll-view>
		</tn-popup>

		<tn-popup v-model="spsxshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">

				<view class="rowx">
					<view class="tit">出库价格</view>
					<view class="inpcon">
						<view class="inp"><input class="input" type="number" name="ckjiage" v-model="thckjiage"
								placeholder="请输入出库价格" placeholder-class="placeholder" /></view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="rowx">
					<view class="tit">出库数量</view>
					<view class="inpcon">
						<view class="inp">
						<!-- 	<tn-number-box v-model="thcknum" :min="1" :max="thkucun" :positiveInteger="false"
								:step="1"></tn-number-box> -->
						   <view class="inp"><input class="input" type="number" name="thcknum" v-model="thcknum"  :placeholder="'请输入出库数量'" placeholder-class="placeholder"   /></view>
						</view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="qrsel">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>

		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择入库日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>


		<block v-if="stype==2">
			<view class="tn-flex tn-footerfixed">
				<view class="diczcon">
					<view class="sf1">
						<text class="iconfont icon-gouwucheman"></text>
						已选 <text class="tnum">{{gwctj.gwcspnum}}</text> 件,
						<text class="tnum">￥{{gwctj.gwcspzj}}</text>
					</view>
					<view class="sf2">
						<view class="qrbtn" @click="gotogwc">查看详情</view>
					</view>
					<view class="clear"></view>
				</view>
			</view>
		</block>


	<tn-wpfl v-model="wpflshow" @qrxz="wpflqrxz" @close="wpflclose" ></tn-wpfl>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				statxt: '状态',
				ddtype: 0,
				ddtypetxt: '类型',
				index: 1,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						value: 0,
						name: '全部'
					},
					{
						value: 1,
						name: '可售'
					},
				],
				ddtypedata: [],
				pxdata: [{
						value: 0,
						name: '默认排序',
						vname: '排序',
					},
					{
						value: 1,
						name: '入库时间升序',
						vname: '升序'
					},
					{
						value: 2,
						name: '入库时间降序',
						vname: '降序'
					},
				],
				pxfs: 0,
				pxfstxt: '排序',

				show3: false,
				checked: true,
				tbdhdownshow: false,
				dwtype: 0,

				tjdata: [],
				spsortdata: [],

				rqshow: false,
				rqkstime: 0,
				rqjstime: 0,

				wpflshow: false,
				sortid: 0,
				sortnamep: '',

				ckselshow: false,
				thckdata: '',
				ckdata: '',
				ckid: 0,
				ckname: '',
				djid: 0,
				thspid: 0,
				thspname: '',
				thsjjiage: 0,
				thkucun: 0,
				thcknum: 1,
				thckjiage: 0,
				spsxshow: false,

				stype: 1,
				bkxztip: '',

				gwctj: [],

			}
		},
		onLoad(options) {
			let that = this;
			let stype = options.stype ? options.stype : 1;
			let ckid = options.ckid ? options.ckid : 0;
			let djid = options.djid ? options.djid : 0;
			this.stype = stype;
			this.djid = djid;
			this.ckid = ckid;
			this.loadData();
			that.page = 0;
			that.listdata = [];
			that.loaditems();
		},
		onShow() {
			let that = this;
			if (uni.getStorageSync('thupcksplist') == 1) {
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thupcksplist', 0)
			}
		},
		methods: {
			previewImagev: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;

				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			gotogwc() {
				uni.navigateBack();
			},
			bkxzcz() {
				let bkxztip = this.bkxztip;
				uni.showModal({
					content: bkxztip,
					showCancel: false
				})
			},
			addckd(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				let thsjjiage = e.currentTarget.dataset.sjjiage;
				let thkucun = e.currentTarget.dataset.kucun;
				this.thspid = spid;
				this.thspname = spname;
				this.thsjjiage = thsjjiage;
				this.thckjiage = thsjjiage;
				this.thkucun = thkucun;
				this.thcknum = 1;
				this.spsxshow = true;
				console.log(spid);
			},
			qrsel() {
				let that = this;
				let stype = this.stype;
				let da = {
					stype: stype,
					djid: this.djid,
					spid: this.thspid,
					cknum: this.thcknum,
					ckjiage: this.thckjiage,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/selwp/qrsel', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							if (stype == 1) {
								uni.setStorageSync('thupsplist', 1);
								uni.navigateBack();
							}
							if (stype == 2) {
								uni.showToast({
									icon: 'none',
									title: res.msg,
									success() {
										setTimeout(function() {
											that.spsxshow = false;
											that.gwctj = res.data.gwctj;
										}, 1000);
									}
								});
							}
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {

								}
							})
						}
					})

			},


			selcangku() {
				this.ckselshow = true;
			},
			selthck(e) {
				let that = this;
				let ckid = e.currentTarget.dataset.ckid;
				let ckname = e.currentTarget.dataset.ckname;
				this.ckid = ckid;
				this.ckname = ckname;
				this.ckselshow = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssortid() {
				let that = this;
				this.sortid = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clsckid() {
				let that = this;
				this.ckid = 0;
				this.ckname = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			loadData() {
				let that = this;
				let da = {

					tjtype: 2,
					ckid: that.ckid ? that.ckid : 0
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/selwp/getcktj', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							that.spsortdata = res.data.spsortdata;
							that.ddtypedata = res.data.ddtypedata;
							that.ckdata = res.data.ckdata;
							that.bkxztip = res.data.bkxztip;
							that.gwctj = res.data.gwctj;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					this.thkw = '';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},



			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					stype: this.stype,
					ckid: that.ckid ? that.ckid : 0,
					sortid: that.sortid ? that.sortid : 0,
					pxfs: that.pxfs ? that.pxfs : 0,
					ddtype: that.ddtype ? that.ddtype : 0,
					kw: that.thkw ? that.thkw : '',
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/selwp/shoplist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},


			tbdhdownclk(e) {
				let that = this;
				let dwtype = e.currentTarget.dataset.dwtype;

				that.dwtype = dwtype;
				if (dwtype == 0) {
					that.tbdhdownshow = false;
					this.thkw = '';
					this.thkwtt = '';
					that.rqkstime = '';
					that.rqjstime = '';

					that.ddtype = 0;
					that.sortid = 0;

					if (that.djid == 0) {
						that.ckid = 0;
						that.ckname = '';
					}

					that.thkw = '';
					that.sta = 0;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				} else {
					that.tbdhdownshow = true;
				}

			},
			tbdhdownitemsel(e) {
				let that = this;
				let dwtype = e.currentTarget.dataset.dwtype;
				let value = e.currentTarget.dataset.value;
				let name = e.currentTarget.dataset.name;
				let vname = e.currentTarget.dataset.vname;

				if (dwtype == 1) {
					that.ddtype = value;
					that.ddtypetxt = name;
				}

				if (dwtype == 2) {
					that.sta = value;
					that.statxt = name;
				}
				if (dwtype == 9) {
					that.pxfs = value;
					that.pxfstxt = vname;
				}
				that.dwtype = dwtype;
				that.tbdhdownshow = false;

				// that.thkw = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();

			},




			onchange(e) {},
			onnodeclick(node) {
				console.log(node);
				let sortid = node.value;
				let that = this;
				that.sortid = sortid;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			selwpfl() {
				this.wpflshow = true;
			}, 
			wpflqrxz(node) {
				let sortid = node.sortid;
				let that = this;
				that.sortid = sortid;
				that.sortnamep = node.sortnamep;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.wpflshow = false;
			},
			wpflclose() {
				this.wpflshow = false;
			}




		}
	}
</script>





<style lang='scss'>
	.splist {
		padding: 0 32upx 32upx 32upx
	}

	.splist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx;
		position: relative;
	}

	.splist .item .spbh {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 20upx;
		color: #1677FF
	}

	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .otbstxt {
		position: absolute;
		right: 20upx;
		bottom: 45upx;
		font-size: 24upx;
		color: #1677FF
	}

	.splist .item .inf1 .status {
		position: absolute;
		right: 28upx;
		bottom: 80upx;
		border-radius: 30upx;
		padding: 5upx 15upx;
		color: #fff;
		background: #ccc;
		font-size: 24upx
	}

	.splist .item .inf1 .sjstatus {
		position: absolute;
		right: 50upx;
		bottom: 30upx;
		font-size: 24upx;
	}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 200upx;
		height: 200upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: right;
		width: calc(100% - 226upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 45upx;
		line-height: 45upx;
		overflow: hidden;
		margin-bottom: 15upx;
		font-size: 28upx
	}

	.splist .item .inf1 .xx .n2 {
		margin-top: 10upx;
	}

	.splist .item .inf1 .xx .n2 text {
		color: #ff0000
	}

	.splist .item .inf1 .xx .n2 .zj {
		font-weight: 600
	}

	.splist .item .inf2 {
		background: #F0F0F0;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 15upx 28upx;
		font-size: 20rpx;
		color: #333;
		position: relative;
		line-height: 40upx;
	}

	.splist .item .inf2 .rktime {}

	.splist .item .inf2 .f1 {}

	.splist .item .inf2 .setbtn {
		position: absolute;
		right: 25upx;
		top: 29upx;
		width: 120rpx;
		height: 55upx;
		line-height: 55upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .inf2 .setbtn.btn22 {
		background: #ddd;
		color: #888;
	}

	.splist .item .inf2 .setbtn2 {
		position: absolute;
		right: 145upx;
		top: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fff;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}

	.splist .item .status.sta1 {
		background: #54D216;
	}

	.splist .item .status.sta2 {
		background: #FF6600;
		color: #fff
	}

	.splist .item .status.sta3 {
		background: #FF2828;
		color: #fff
	}

	.splist .item .status.sta8 {
		background: #888;
	}

	.splist .item .status.sta9 {
		background: #c76096;
		color: #fff
	}

	.splist .item .sjstatus.sta1 {
		color: #43b058;
	}

	.splist .item .sjstatus.sta2 {
		color: #888;
	}



	.cznrcon {
		padding: 40upx;
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inpcon .colorhs {
		color: #ff0000
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
</style>