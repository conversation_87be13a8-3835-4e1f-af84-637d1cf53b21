<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>企业员工</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">

				<view class="maxcon">
					<view class="ttinfo">


						<view class="status" @click="setsta" :data-ygid='ygdata.id'>
							<view class="aa">{{ygdata.statusname}}</view>
							<view class="aa"><tn-switch v-model="ygdata.status==1 ? true : false " activeColor="#54D216"
									inactiveColor="#7F7F7F" leftIcon="success" right-icon="close"
									change="ss"></tn-switch></view>
						</view>


						<view class="inf1">

							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + ygdata.avatar"></image>
								</view>
								<view class="xx">
									<view class="name">{{ygdata.realname}}</view>
									<view class="tel"><text>员工号:{{ygdata.zzhuid}}</text></view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
					</view>
				</view>


				<view class="maxcon">
					<view class="vstit">个人资金（合计）</view>
					<view class="inf2">
						<view class="vtm" @click="gotoygglurl('/pages/work/ygzh/ygrcd/cwrcd')">
							<view class="tt">余额</view>
							<view class="vv">{{ygdata.yue}}</view>
						</view>
						<view class="vtm" @click="gotoygglurl('/pages/work/ygzh/ygrcd/zjrcd',3)">
							<view class="tt">当前已出资</view>
							<view class="vv">{{ygdata.czzjine}}</view>
						</view>
						<view class="vtm" @click="gotoygglurl('/pages/work/ygzh/ygrcd/zjrcd')">
							<view class="tt">可用出资</view>
							<view class="vv">{{ygdata.czkyjine}}</view>
						</view>
						<view class="clear"></view>
					</view>

				</view>



				<view class="maxcon" style="padding:28upx 0 0 0">
					<view class="vstit" style="padding-left:28upx;margin-bottom:28upx">记录查询</view>
					<view class="ifwdh">
						<view class='tjmul smx'>
							<view class='mli' @click="gotoygglurl('/pages/work/ygzh/ygrcd/jyrcd')">

								<view class='icon'>
									<image src="/static/images/ygglicon21.png"></image>
								</view>
								<view class='tit'>订单</view>

							</view>
							<view class='mli' @click="gotoygglurl('/pages/work/ygzh/ygrcd/jfrcd')">

								<view class='icon'>
									<image src="/static/images/ygglicon22.png"></image>
								</view>
								<view class='tit'>奖罚</view>

							</view>
							<view class='mli ' @click="gotoygglurl('/pages/work/ygzh/ygrcd/cwrcd',6)">

								<view class='icon'>
									<image src="/static/images/ygglicon23.png"></image>
								</view>
								<view class='tit'>分润</view>

							</view>
							<view class='mli ' @click="gotoygglurl('/pages/work/ygzh/ygrcd/txrcd')">

								<view class='icon'>
									<image src="/static/images/ygglicon24.png"></image>
								</view>
								<view class='tit'>提现</view>

							</view>
							<view class='mli' @click="gotoygglurl('/pages/work/ygzh/ygrcd/bxrcd')">

								<view class='icon'>
									<image src="/static/images/ygglicon25.png"></image>
								</view>
								<view class='tit'>报销</view>

							</view>
							<view class='mli' @click="gotoygglurl('/pages/work/ygzh/ygrcd/fyrcd')">

								<view class='icon'>
									<image src="/static/images/ygglicon25.png"></image>
								</view>
								<view class='tit'>费用申请</view>

							</view>

							<view class='mli' @click="gotoygglurl('/pages/work/ygzh/ygrcd/gzyzrcd')">

								<view class='icon'>
									<image src="/static/images/ygglicon26.png"></image>
								</view>
								<view class='tit'>工资预支</view>

							</view>

							<view class='mli' @click="gotoygglurl('/pages/work/ygzh/ygrcd/zjrcd',3)">

								<view class='icon'>
									<image src="/static/images/ygglicon26.png"></image>
								</view>
								<view class='tit'>出资</view>

							</view>



							<view class="clear"></view>
						</view>

					</view>

				</view>




				<view class="maxcon">

					<view class="vstit">基本信息</view>

					<view class="rowx" @click="showModal3" data-zdtype="2">
						<view class="icon"><text class="iconfont  icon-bianji"></text></view>
						<view class="tit">姓名</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.realname}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx" @click="showModal3" data-zdtype="3">
						<view class="icon"><text class="iconfont  icon-bianji"></text></view>
						<view class="tit">电话号码</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.lxtel}}</view>
						</view>
						<view class="clear"></view>
					</view>


					<view class="rowx" @click="showModal3" data-zdtype="4">
						<view class="icon"><text class="iconfont  icon-bianji"></text></view>
						<view class="tit">身份证</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.sfzhao ? ygdata.sfzhao : '设置身份证号'}}</view>
						</view>
						<view class="clear"></view>
					</view>


					<view class="rowx" @click="seldianpu">
						<view class="icon"></view>
						<view class="tit">所属店铺</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.ormdname}}</view>
						</view>
						<view class="clear"></view>
					</view>


					<view class="rowx" v-if="ygdata.dpjstxt">
						<view class="icon"></view>
						<view class="tit">店铺角色</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.dpjstxt}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx" @click="seljiaose" v-if="ygdata.isadmin==2">
						<view class="icon"><text class="iconfont  icon-bianji"></text></view>
						<view class="tit">员工角色</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.jiaosetxt ? ygdata.jiaosetxt : '请选择角色'}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" @click="seljiaose2">
						<view class="icon"><text class="iconfont  icon-bianji"></text></view>
						<view class="tit">超级角色</view> 
						<view class="inpcon">
							<view class="inp" :class="ygdata.isadmin==1 ? 'colorhs' : '' ">{{ygdata.jiaosetxt2 ? ygdata.jiaosetxt2 : '点击设置'}}</view>
						</view>
						<view class="clear"></view>
					</view>


					<view class="rowx">
						<view class="icon"></view>
						<view class="tit">入职时间</view>
						<view class="inpcon">
							<view class="inp">{{ygdata.rztimetxt}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx" v-if="ygdata.lztimetxt">
						<view class="icon"></view>
						<view class="tit">离职时间</view>
						<view class="inpcon"><view class="inp">{{ygdata.lztimetxt}}</view></view>
						<view class="clear"></view>
					</view>
				</view>

				<view class="maxcon">
					<view class="rowxmttp">
						<view class="tit" style="font-weight:600">
							<text class="redst"></text>身份信息
							<text class="iconfont  icon-bianji" style="margin-left:10upx;font-weight:400"></text>
						</view>


						<view class="uprzimgcon">
							<view class="zzx sf1">
								<view class="con">
									<view class="vt1" @click="uprzimg1">
										<block v-if="rzimg1">
											<image :src='staticsfile + rzimg1'></image>
										</block>
										<block v-else>
											<image src="/static/images/rzupimg1.png"></image>
										</block>
									</view>
									<view class="vt2">身份证头像面</view>
									<view class="clear"></view>
								</view>
							</view>
							<view class="zzx sf2">
								<view class="con">
									<view class="sf2" @click="uprzimg2">
										<block v-if="rzimg2">
											<image :src='staticsfile + rzimg2'></image>
										</block>
										<block v-else>
											<image src="/static/images/rzupimg2.png"></image>
										</block>
									</view>
									<view class="vt2">身份证国徽面</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>


					</view>
				</view>




			</view>











		</view>

		<view style="height:20upx"></view>


		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">请输入{{zdtxt}}</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;text-align: center;">
						<input :placeholder="'请输入'+zdtxt" v-model="zdvalue" name="input"
							placeholder-style="color:#AAAAAA" maxlength="50"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="upzdvalue">
						<text style="color:#333">保 存</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>


		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">

						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>

					</block>
				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="jsselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">请选择员工角色</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">

				<view class="jslist">

					<checkbox-group @change="jssetChange">
						<block v-for="(item, index) in jiaosedata" :key="index">
							<view class="item">
								<label>
									<view class="icon">
										<checkbox :value="item.id" color="#FFD427" />
									</view>
									<view class="vstxt">{{item.title}}</view>
									<view class="clear"></view>
								</label>
							</view>
						</block>
					</checkbox-group>
					</block>



					<view style="height:160upx"></view>
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<view class="bomsfleft">
								<button class="bomsubbtn" @click="closejiaose" style="background:#ddd;">
									<text>关闭</text>
								</button>
							</view>
							<view class="bomsfright">
								<button class="bomsubbtn" @click="qrzdysxm">
									<text>确认选择</text>
								</button>
							</view>
							<view class="clear"></view>
						</view>
					</view>

				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="jsselshow2" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">请选择超级角色</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="jslxcon">
					<radio-group @change="radio_ygjstype">
						<label>
							<radio :value="1" :checked="ygjstype == 1" color="#FFD427" /> 超级角色
						</label>
						<label style="margin-left:30px">
							<radio :value="2" :checked="ygjstype == 2" color="#FFD427" /> 超级BOOS
						</label>
					</radio-group>
				</view>



				<view class="jslist">
					
					        <block v-if="ygjstype==1">
								<checkbox-group @change="jssetChange2">
									<block v-for="(item, index) in jiaosedata2" :key="index">
										<view class="item">
											<label>
												<view class="icon">
													<checkbox :value="item.id" color="#FFD427" />
												</view>
												<view class="vstxt">{{item.title}}</view>
												<view class="clear"></view>
											</label>
										</view>
									</block>
								</checkbox-group>
				            </block>
							<block v-else>
								<view class="jsvvtip">
									<view class="icon"><text class="iconfont icon-renyuan"></text></view>
									<view class="tit">{{cjglysm}}</view>
								</view>
							</block>


					<view style="height:160upx"></view>
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<view class="bomsfleft">
								<button class="bomsubbtn" @click="closejiaose2" style="background:#ddd;">
									<text>关闭</text>
								</button>
							</view>
							<view class="bomsfright">
								<button class="bomsubbtn" @click="qrzdysxm2">
									<text>确认选择</text>
								</button>
							</view>
							<view class="clear"></view>
						</view>
					</view>

				</view>



			</scroll-view>
		</tn-popup>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				ygdata: '',
				status: 0,
				multiArray: [],
				objectDiqudata: [],
				multiIndex: [0, 0, 0],
				img_url: [],
				img_url_ok: [],
				show3: false,
				zdvalue: '',
				zdtxt: '',
				zdtype: '',
				rzimg1: '',
				rzimg2: '',
				jsselshow: false,
				jsselshow2: false,
				dpselshow: false,
				dpdata: '',
				thsjstemp: '',
				thsjstemp2: '',
				jiaosedata: [],
				jiaosedata2: [],
				ygjstype: 1,
				cjglysm: '',
				cjglysmx: '',
				isedit: 0,
			}
		},

		onLoad(options) {
			let that = this;
			let ygid = options.ygid ? options.ygid : 0;
			this.ygid = ygid;
		},
		onShow() {
			this.loadData();
		},

		methods: {

			radio_ygjstype(e) {
				console.log(e.detail.value);
				this.ygjstype = e.detail.value;
			},

			seljiaose() {
				this.jsselshow = true;
			},

			seljiaose2() {
				this.jsselshow2 = true;
			},

			closejiaose() {
				this.jsselshow = false;
			},
			closejiaose2() {
				this.jsselshow2 = false;
			},
			jssetChange(e) {
				let value = e.detail.value;
				this.thsjstemp = value;
			},
			jssetChange2(e) {
				let value = e.detail.value;
				this.thsjstemp2 = value;
			},
			qrzdysxm() {
				let that = this;
				let jsvalue = this.thsjstemp;
				let ygjstype = this.ygjstype;


				if (!jsvalue || jsvalue.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择角色',
					});
				}
				jsvalue = jsvalue.join(',');


				let ygid = that.ygid;
				let da = {
					ygid: ygid,
					ygjstype: ygjstype,
					jsvalue: jsvalue,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ygzh/upygjiaose', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							this.jsselshow = false;
							uni.setStorageSync('thupyglist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {

								}
							})
						}
					})



			},
			
			qrzdysxm2() {
				let that = this;
				let jsvalue = this.thsjstemp2;
				let ygjstype = this.ygjstype;
			
			    if(ygjstype==1){
					if (!jsvalue || jsvalue.length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请选择角色',
						});
					}else{ 
						jsvalue = jsvalue.join(',');
					}
				}else{
					jsvalue='';
				} 
			
				
				let ygid = that.ygid;
				let da = {
					ygid: ygid,
					ygjstype: ygjstype,
					jsvalue: jsvalue,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ygzh/upygjiaose2', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							this.jsselshow2 = false;
							uni.setStorageSync('thupyglist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
			
								}
							})
						}
					})
			
			
			
			},
			
			seldianpu() {
				return false;
				this.dpselshow = true;
			},
			selthdp(e) {
				let dpid = e.currentTarget.dataset.dpid;
				this.zdtype = 11;
				this.zdvalue = dpid;
				this.upzdvalue();
				this.dpselshow = false;
			},



			loadData() {
				let that = this;
				let ygid = that.ygid;
				let da = {
					ygid: ygid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ygzh/ygset', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.isedit = res.data.isedit;
							that.cjglysm = res.data.cjglysm;
							that.cjglysmx = res.data.cjglysmx;
							that.ygdata = res.data.ygdata;
							that.dpdata = res.data.dpdata;
							that.jiaosedata = res.data.jiaosedata;
							that.jiaosedata2 = res.data.jiaosedata2;
							that.rzimg1 = res.data.ygdata.sfzimg1 ? res.data.ygdata.sfzimg1 : '';
							that.rzimg2 = res.data.ygdata.sfzimg2 ? res.data.ygdata.sfzimg2 : '';
							that.ygjstype = res.data.ygdata.isadmin == 1 ? 2 : 1;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			gotoygglurl(url, type = 0) {
				let ygname = this.ygdata.realname;
				uni.navigateTo({
					url: url + '?ygid=' + this.ygid + '&ygname=' + ygname + '&type=' + type
				})
			},



			setsta(e) {
				let that = this;
				let da = {
					'ygid': that.ygid
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/ygzh/setsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thupyglist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})


			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				console.log(src);
				let imgarr = [src];
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},

			showModal3(e) {
				console.log(e);
				let that = this;
				if (that.isedit == 0) {
					if (that.cjglysmx) {
						uni.showToast({
							icon: 'none',
							title: that.cjglysmx,
						});
					}
					return false;
				}


				let dataset = e.currentTarget.dataset;
				let zdtype = dataset.zdtype;
				if (zdtype == 2) {
					that.zdtype = 2;
					that.zdtxt = '姓名';
					that.zdvalue = that.ygdata.realname;
				}
				if (zdtype == 3) {
					that.zdtype = 3;
					that.zdtxt = '电话号码';
					that.zdvalue = that.ygdata.lxtel;
				}
				if (zdtype == 4) {
					that.zdtype = 4;
					that.zdtxt = '身份证号';
					that.zdvalue = that.ygdata.sfzhao;
				}
				console.log(zdtype, that.zdvalue);
				this.openModal3()
			},
			// 打开模态框
			openModal3() {
				this.show3 = true
			},


			upzdvalue() {
				let that = this;
				let zdtype = that.zdtype;
				let zdvalue = that.zdvalue;
				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'zdtype': zdtype,
					'zdvalue': zdvalue,
					'uygid': that.ygid,
				}
				this.$req.post('/v1/ygzh/upgrinfo', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thupyglist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
										that.show3 = false;
									}, 1000)
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},

			uprzimg1() {
				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg1 = res.data.fname;

									that.zdtype = 8;
									that.zdvalue = res.data.fname;
									that.upzdvalue();

									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},

			uprzimg2() {
				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg2 = res.data.fname;

									that.zdtype = 9;
									that.zdvalue = res.data.fname;
									that.upzdvalue();

									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},


		}
	}
</script>

<style lang="scss">
	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .dptit {
		text-align: center;
	}



	.ttinfo {
		position: relative;
	}

	.ttinfo .status {
		position: absolute;
		right: 0;
		top: 0
	}

	.ttinfo .status .aa {
		float: left;
		margin-left: 10upx;
		line-height: 50upx;
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}


	.inf2 {
		padding-top: 20upx;
	}

	.inf2 .vtm {
		float: left;
		width: 33.33%;
		font-size: 28upx
	}

	.inf2 .vtm .tt {
		color: #7F7F7F;
		font-size: 24upx
	}

	.inf2 .vtm .vv {
		color: #333333;
		margin-top: 15upx
	}


	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #333
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}
	.rowx .inp.colorhs{color:#ff0000}

	.rowx:last-child {
		border-bottom: 0;
	}

	.jslxcon {
		padding: 0 30upx;
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px solid #F0F0F0;
	}


	.rowxmttp {
		margn-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx
	}

	.imgconmt image {
		width: 240upx;
		height: 160upx;
		border-radius: 10upx;
	}

	.ifwdh {}

	.ifwdh .tjmul {}

	.ifwdh .tjmul .mli {
		float: left;
		width: 25%;
		text-align: center;
	}

	.ifwdh .tjmul .mli .icon {
		position: relative;
		width: 60upx;
		height: 60upx;
		margin: 0 auto;
	}

	.ifwdh .tjmul .mli .icon image {
		width: 60upx;
		height: 60upx;
		display: block;
		margin: 0 auto;
	}

	.ifwdh .tjmul .tit {
		margin-top: 20upx;
		font-size: 24upx;
		color: #333
	}

	.ifwdh .tjmul.smx .mli {
		margin-bottom: 28upx;
	}

	.cjgly .inp {
		color: #ff6600
	}

	.cjglysm {
		padding: 30upx;
		color: #ff6600;
	}

	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}


	.jslist {}

	.jslist .item {
		padding: 0 30upx;
		border-bottom: 1px solid #efefef;
		height: 100upx;
		line-height: 100upx
	}

	.jslist .item .icon {
		float: left
	}

	.jslist .item .vstxt {
		float: left;
		mragin-left: 20upx
	}
	
	.jsvvtip{text-align: center;padding:50px 0}
	.jsvvtip .icon{margin-bottom:20px} 
	.jsvvtip .icon .iconfont{font-size:45px;color:#333;} 
	.jsvvtip .tit{height:45px;line-height:45px;color:#ff0000;}
	
</style>