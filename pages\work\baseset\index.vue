<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					<view slot="back" class='tn-custom-nav-bar__back'  >
						<text class='icon tn-icon-left'></text>
					</view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >系统设置</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>
	<view class="tabs-fixed-blank" ></view>
	<!-- 顶部自定义导航 -->
	<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
	  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
		<view class="justify-content-item" style="width: 100vw;overflow: hidden;">
		  <tn-tabs :list="vtypedata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :gutter="20" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
		</view>
	  </view>  
	</view>
	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 3 + 'px'}"></view>

		

			<view class="" >
					  <form @submit="formSubmit" >
						  
						  
						  
								<view class="sqbdcon" v-show="vtype==1">
									 
									<view class="maxcon">
													<view class="vstit">企业信息</view>
													<view class="more"></view>
													
													<block v-if="sjdata.rzsta!=1" >
														<view class="rowx">
															<view class="tit"><text class="redst"></text>企业名称</view>
															<view class="inpcon">
															   <view class="inp"><input class="input" type="text" name="title"  v-model="qytitle" placeholder="请输入企业信息" placeholder-class="placeholder" /></view>
															</view>
															<view class="clear"></view>
														</view>
													</block>
													<block v-else>
														<view class="rowx">
															<view class="tit"><text class="redst"></text>企业名称</view>
															<view class="inpcon">
															   <view class="inp">{{qytitle}}</view>
															</view>
															<view class="clear"></view>
														</view>
													</block>
													
													<view class="rowx">
														<view class="tit"><text class="redst"></text>联系人</view>
														<view class="inpcon">
														   <view class="inp"><input class="input" type="text" name="lxren" v-model="sjdata.lxren"  placeholder="请输入联系人" placeholder-class="placeholder" /></view>
														</view>
														<view class="clear"></view>
													</view>
													
													<view class="rowx">
														<view class="tit"><text class="redst"></text>联系电话</view>
														<view class="inpcon">
														   <view class="inp"><input class="input" type="text" name="lxtel" v-model="sjdata.lxtel"  placeholder="请输入联系电话" placeholder-class="placeholder" /></view>
														</view>
														<view class="clear"></view>
													</view>
													<view class="rowx">
														<view class="tit"><text class="redst"></text>联系地址</view>
														<view class="inpcon">
														   <view class="inp"><input class="input" type="text" name="dizhi" v-model="sjdata.dizhi"  placeholder="请输入联系地址" placeholder-class="placeholder" /></view>
														</view>
														<view class="clear"></view>
													</view>
													
													<view class="rowx">
														<view class="tit"><text class="redst"></text>认证状态</view>
														<view class="inpcon">
														   <view class="inp">
															   <block v-if="sjdata.rzsta==1">
																	<text style="color:#43b058">已认证</text> <text style="margin-left:20px;color:#888;font-size:24upx" @click="gotorz">认证详情 > </text>
															   </block>
															   <block v-else>
																	<text style="color:#ff6600">待认证</text> <text style="margin-left:20px;color:#888;font-size:24upx" @click="gotorz">去认证 > </text>
															   </block>
														   </view>
														</view>
														<view class="clear"></view>
													</view>
													
													<view class="rowx" style="padding:10upx 0">
														<view class="tit"><text class="redst"></text>企业LOGO</view>
														<view class="inpcon">
														   <view class="inp" @click="uplogo">
																	<image :src='staticsfile + dwlogo' style="width:100upx;height:100upx;border-radius:100px"></image>
														   </view>
														</view>
														<view class="clear"></view>
													</view>
													
													
											</view>	
									<view class="maxcon" v-if="istcyinfo==1">
											<view class="vstit">系统套餐</view>
											<view class="more"></view>
											<view class="rowx">
												<view class="tit"><text class="redst"></text>套餐版本</view>
												<view class="inpcon">
												   <view class="inp">
												       {{sjdata.tctxt}}
												       <view class="sjbtn"  @click="gototcbbv"> {{sjdata.tcsjtxt}} </view>
												   </view>
												</view>
												<view class="clear"></view>
											</view>
											<view class="rowx">
												<view class="tit"><text class="redst"></text>到期时间</view>
												<view class="inpcon">
												   <view class="inp">{{sjdata.dqdate}}</view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>客户数量</view>
												<view class="inpcon">
												   <view class="inp"><text class="ysynum">{{sjdata.khnumoo}}</text> / <text class="znum">{{sjdata.khnum}}</text></view>
												</view>
												<view class="clear"></view>
											</view>
											<view class="rowx">
												<view class="tit"><text class="redst"></text>员工数量</view>
												<view class="inpcon">
												   <view class="inp"><text class="ysynum">{{sjdata.ygnumoo}}</text> / <text class="znum">{{sjdata.ygnum}}</text></view>
												</view>
												<view class="clear"></view>
											</view>
											<view class="rowx">
												<view class="tit"><text class="redst"></text>店铺数量</view>
												<view class="inpcon">
												   <view class="inp"><text class="ysynum">{{sjdata.dpnumoo}}</text> / <text class="znum">{{sjdata.dpnum}}</text></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>仓库数量</view>
												<view class="inpcon">
												   <view class="inp"><text class="ysynum">{{sjdata.cknumoo}}</text> / <text class="znum">{{sjdata.cknum}}</text></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>订单数量</view>
												<view class="inpcon">
												   <view class="inp"><text class="ysynum">{{sjdata.ddnumoo}}</text> / <text class="znum">{{sjdata.ddnum}}</text></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>人脸识别</view>
												<view class="inpcon">
												   <view class="inp">剩余次数  <text class="ysynum" style="margin-left:10upx"> {{sjdata.rlsbnum}} </text> 次</view>
												</view>
												<view class="clear"></view>
											</view>
											
											
											
									</view>	
								</view>	
								
								<view class="sqbdcon" v-show="vtype==2">
							          
									  
									  <view class="maxcon">
									  		<view class="vstit">报销相关</view>
									  		<view class="rowx">
									  			<view class="tit"><text class="redst"></text>报销功能</view>
									  			<view class="inpcon">
									  			   <view class="inp">
									  				   
									  				   <radio-group  @change="radio_ygbxsta">
																<label  ><radio :value="1" :checked="yg_bx_sta == 1" color="#FFD427" /> 开启</label>
																<label  style="margin-left:10px"><radio :value="2" :checked="yg_bx_sta == 2" color="#FFD427" /> 关闭</label>
									  				   </radio-group>
									  				   
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  </view>
									  <view class="maxcon">
									  		<view class="vstit">入账开单</view>
									  		<view class="rowx">
									  			<view class="tit"><text class="redst"></text>入账开单</view>
									  			<view class="inpcon">
									  			   <view class="inp">
									  				   
									  				   <radio-group  @change="radio_ygrzdpsta">
																<label  ><radio :value="1" :checked="yg_rzdp_sta == 1" color="#FFD427" /> 开启</label>
																<label  style="margin-left:10px"><radio :value="2" :checked="yg_rzdp_sta == 2" color="#FFD427" /> 关闭</label>
									  				   </radio-group>
									  				   
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  </view>
									  <view class="maxcon">
									  		<view class="vstit">费用申请</view>
									  		<view class="rowx">
									  			<view class="tit"><text class="redst"></text>费用申请</view>
									  			<view class="inpcon">
									  			   <view class="inp">
									  				   
									  				   <radio-group  @change="radio_ygfysta">
																<label  ><radio :value="1" :checked="yg_fy_sta == 1" color="#FFD427" /> 开启</label>
																<label  style="margin-left:10px"><radio :value="2" :checked="yg_fy_sta == 2" color="#FFD427" /> 关闭</label>
									  				   </radio-group>
									  				   
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  </view>
									  <view class="maxcon">
									  		<view class="vstit">工资预支申请</view>
									  		<view class="rowx">
									  			<view class="tit"><text class="redst"></text>工资预支</view>
									  			<view class="inpcon">
									  			   <view class="inp">
									  				   
									  				   <radio-group  @change="radio_yggzyzsta">
																<label  ><radio :value="1" :checked="yg_gzyz_sta == 1" color="#FFD427" /> 开启</label>
																<label  style="margin-left:10px"><radio :value="2" :checked="yg_gzyz_sta == 2" color="#FFD427" /> 关闭</label>
									  				   </radio-group>
									  				   
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  </view>
									  
									  <view class="maxcon">
									  		<view class="vstit">提现相关</view>
									  		
									  		<view class="rowx">
									  			<view class="tit"><text class="redst"></text>提现功能</view>
									  			<view class="inpcon">
									  			   <view class="inp">
									  				   
									  				   <radio-group  @change="radio_ygtxsta">
															<label  ><radio :value="1" :checked="yg_tx_sta == 1" color="#FFD427" /> 开启</label>
															<label  style="margin-left:10px"><radio :value="2" :checked="yg_tx_sta == 2" color="#FFD427" /> 关闭</label>
									  				   </radio-group>
									  				   
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
											
									  		
									  		<view class="rowx">
									  			<view class="icon">%</view>
									  			<view class="tit"><text class="redst"></text>提现费率</view>
									  			<view class="inpcon">
									  			   <view class="inp"><input class="input" type="number" name="yg_tx_sxf" v-model="sjdata.yg_tx_sxf"  placeholder="请输入提现费率" placeholder-class="placeholder" /></view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  		<view class="rowx">
									  			<view class="icon">元</view>
									  			<view class="tit"><text class="redst"></text>提现最低金额</view>
									  			<view class="inpcon">
									  			   <view class="inp"><input class="input" type="number" name="yg_tx_min" v-model="sjdata.yg_tx_min"  placeholder="请输入提现最低金额" placeholder-class="placeholder" /></view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  		<view class="rowx">
									  			<view class="icon">元</view>
									  			<view class="tit"><text class="redst"></text>最小手续费</view>
									  			<view class="inpcon">
									  			   <view class="inp"><input class="input" type="number" name="yg_tx_min_sxf" v-model="sjdata.yg_tx_min_sxf"  placeholder="请输入最小手续费" placeholder-class="placeholder" /></view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  		
									  </view>	
									  
									  <view class="maxcon">
									  		<view class="vstit">付款相关</view>
									  		<view class="rowx">
									  			<view class="tit"><text class="redst"></text>员工收款方式</view>
									  			<view class="inpcon" style="height:180upx">
									  			   <view class="inp">
													     <view class="txditem"><checkbox  value="1" color="#FFD427"  checked="true" :disabled="true" >内部结算</checkbox></view>
														   <view class="clear"></view>
														  <checkbox-group  @change="ygtxskfsGroupChange" >
															  <view class="txditem"><checkbox  value="4" color="#FFD427"  :checked="txd4==1 ? true : false"  >银行卡</checkbox></view>
															  <view class="txditem"><checkbox  value="3" color="#FFD427"  :checked="txd3==1 ? true : false"  >支付宝</checkbox></view>
															  <view class="txditem"><checkbox  value="2" color="#FFD427"  :checked="txd2==1 ? true : false"  >微信</checkbox></view>
														  </checkbox-group>
									  				
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  </view>
									  
							    </view>	
							    <view class="sqbdcon" v-show="vtype==3">
								   
								 <view class="maxcon" v-if="thdpdata">
								 		<view class="vstit">首页提示数量校正</view>
										<view class="rowx">
											<view class="tit"><text class="redst"></text>当前店铺</view>
											<view class="inpcon">
											   <view class="inp" style="color:#888">{{thdpdata.title}}</view>
											</view>
											<view class="clear"></view>
										</view>
								 		<view class="rowx">
								 			<view class="tit"><text class="redst"></text>上次校正</view>
								 			<view class="inpcon">
								 			   <view class="inp" style="color:#888">{{thdpdata.updptiplasttime}}</view>
											    <view class="sjbtn" @click="gotojzsytipsj">校正</view>
								 			</view>
								 			<view class="clear"></view>
								 		</view>
										<view class="rowx">
											<view class="inpcon1">
											   <view class="inp" style="color:#888;font-size:24upx;padding:22upx 0 22upx 0"><text class="iconfont icon-tishi6" style="color:#FFD427;margin-right:10upx"></text> {{thdpdata.uptipsjsm}}</view>
											</view>
											<view class="clear"></view>
										</view>
								 </view>
								 
								 
								</view>	
							    <view class="sqbdcon" v-show="vtype==4">
							          
									<!--  <view class="maxcon">
									  		<view class="vstit">信用预付-等额本息</view>
											<view class="rowx">
												<view class="tit">计息还款</view>
												<view class="inpcon" style="height:130upx;line-height:55upx;">
												   <view class="inp">
													   <radio-group  @change="radio_isjxhkfs1">
																<view><label  ><radio :value="1" :checked="isjxhkfs1 == 1" color="#FFD427" /> 预付当日计息还款</label></view>
																<view><label  ><radio :value="2" :checked="isjxhkfs1 == 2" color="#FFD427" /> 预付下个周期还款</label></view>
													   </radio-group>
												   </view>
												</view>
												<view class="clear"></view>
											</view>
									  </view> -->
								<!-- 	  
									  <view class="maxcon">
									  		<view class="vstit">信用预付-先息后本</view>
											<view class="rowx">
												<view class="tit">计息还款</view>
												<view class="inpcon" style="height:130upx;line-height:55upx;">
												   <view class="inp">
													   <radio-group  @change="radio_isjxhkfs2">
																<view><label  ><radio :value="1" :checked="isjxhkfs2 == 1" color="#FFD427" /> 预付当日计息还款</label></view>
																<view><label  ><radio :value="2" :checked="isjxhkfs2 == 2" color="#FFD427" /> 预付下个周期还款</label></view>
													   </radio-group>
												   </view>
												</view>
												<view class="clear"></view>
											</view>
									  		<view class="rowx" style="padding-top:15upx">
									  			<view class="tit"><text class="redst"></text>先息后本</view>
									  			<view class="inpcon" style="height:130upx;line-height:55upx;">
									  			   <view class="inp">
									  				   <radio-group  @change="radio_isxxhbfs">
																<view><label  ><radio :value="1" :checked="isxxhbfs == 1" color="#FFD427" /> 一次性还清利息，最后还本</label></view>
																<view><label  ><radio :value="2" :checked="isxxhbfs == 2" color="#FFD427" /> 按期还息，最后本+息</label></view>
									  				   </radio-group>
									  			   </view>
									  			</view>
									  			<view class="clear"></view>
									  		</view>
									  </view> -->
									  
									  <view class="maxcon">
									  		<view class="vstit">入库审核</view>
									  		<!-- <view class="rowx">
									  			<view class="tit"><text class="redst"></text>合计出资</view>
												
												<view class="inpcon" style="height:130upx;line-height:55upx;">
												   <view class="inp">
													   <radio-group  @change="radio_djsh_ishjczyz">
																<view><label  ><radio :value="1" :checked="djsh_ishjczyz == 1" color="#FFD427" /> 无需要验证出资总额</label></view>
																<view><label  ><radio :value="2" :checked="djsh_ishjczyz == 2" color="#FFD427" /> 全部出资总额不能小于总成本</label></view>
													   </radio-group>
												   </view>
												</view>
												
									  			<view class="clear"></view>
									  		</view> -->
											<view class="rowx" style="padding-top:15upx">
												<view class="tit"><text class="redst"></text>个人出资</view> 
												<view class="inpcon" style="height:130upx;line-height:55upx;">
												   <view class="inp">
													   <radio-group  @change="radio_djsh_isgrczyz">
																<view><label  ><radio :value="1" :checked="djsh_isgrczyz == 1" color="#FFD427" /> 无需要验证个人可出资余额</label></view>
																<view><label  ><radio :value="2" :checked="djsh_isgrczyz == 2" color="#FFD427" /> 需要验证个人帐户可出资余额</label></view>
													   </radio-group>
												   </view>
												</view>
												<view class="clear"></view>
											</view>
									  </view>
									  


																  
							    </view>	
							
								<view class="tn-flex tn-footerfixed" style="z-index:3000;">
								  <view class="tn-flex-1 justify-content-item tn-text-center">
									<button class="bomsubbtn"  form-type="submit">
									  <text>确认提交</text>
									</button>
								  </view>
								</view>
					  </form>
		
		     </view>
		
	 
	
	
	<view style="height:120upx"></view>
    
	
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 sjdata:'',
			 sjtcdata:'',
			 dwlogo:'',
			 qytitle:'',
			 current:0,
			 vtype:1,
			 vtypedata:[
			 	{vtype: 1,name: '基本信息'},
			 	{vtype: 2,name: '功能配置'},
			 	{vtype: 3,name: '其他配置'},
			 	// {vtype: 4,name: '入库单据'},
			 ],
			 sjdata:[],
			 yg_tx_sta:1,
			 yg_bx_sta:1,
			 yg_rzdp_sta:1,
			 yg_fy_sta:1,
			 yg_gzyz_sta:1,
			 ygtxdstr:'',
			 txd2:0,
			 txd3:0,
			 txd4:0,
			 isxxhbfs:1,
			 isjxhkfs1:1,
			 isjxhkfs2:1,
			 djsh_ishjczyz:1,
			 djsh_isgrczyz:1,
			 thdpdata:'',
			 istcyinfo:0,
		  }
	},

	onLoad(options) {
	
	},
    onShow(){
		let that=this;
		this.loadData();
	},
    methods: {
		gotoback(){
			uni.navigateBack();
		},
		gotorz(){
			uni.navigateTo({
				url:'../qyrz/index'
			})
		},
		gototcbbv(){
			uni.navigateTo({
				url:'/pages/taocanbb/index'
			})
		},
		loadData(){
				  let that=this;
				  let da={
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/sjgl/sjinfo', da)
				         .then(res => {
				 		    uni.hideLoading();
							console.log(res);
				 			if(res.errcode==0){
				 		        that.sjdata=res.data.sjdata;
				 		        that.thdpdata=res.data.thdpdata;
				 		        that.sjtcdata=res.data.sjtcdata;
								that.qytitle = res.data.sjdata.title ? res.data.sjdata.title : '' ;
								that.dwlogo = res.data.sjdata.logo ? res.data.sjdata.logo : '' ;
								
								that.yg_tx_sta=res.data.sjdata.yg_tx_sta;
								that.yg_bx_sta=res.data.sjdata.yg_bx_sta;
								that.yg_rzdp_sta=res.data.sjdata.yg_rzdp_sta;
								that.yg_fy_sta=res.data.sjdata.yg_fy_sta;
								that.yg_gzyz_sta=res.data.sjdata.yg_gzyz_sta;
								
								that.ygtxdstr=res.data.sjdata.ygtxdarr;
								
								that.istcyinfo=res.data.istcyinfo;
								that.txd2=res.data.txd2;
								that.txd3=res.data.txd3;
								that.txd4=res.data.txd4;
								
								that.isjxhkfs1=res.data.sjdata.isjxhkfs1;
								that.isjxhkfs2=res.data.sjdata.isjxhkfs2;
								that.isxxhbfs=res.data.sjdata.isxxhbfs;
								that.djsh_ishjczyz=res.data.sjdata.djsh_ishjczyz;
								that.djsh_isgrczyz=res.data.sjdata.djsh_isgrczyz;
							
								
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
		gotosqsm(){
			uni.navigateTo({
				url:'/pages/danye/index?id=5'
			})
		},
		
		tabChange(index) {
			let that=this;
			let vtype=that.vtypedata[index].vtype;
			console.log(vtype);
			that.current = index;
			that.vtype = vtype;
		},
				
		uplogo(){
				   let that = this;
				   uni.chooseImage({
					   count: 1, //默认9
					   sizeType: ['original', 'compressed'],
					   success: (resx) => {
						  const tempFilePaths = resx.tempFilePaths;
						  
										  let da={
											  filePath:tempFilePaths[0],
											  name: 'file'
										  } 
										  uni.showLoading({title: '上传中...'})
										  this.$req.upload('/v1/upload/upfile', da)
												  .then(res => {
													uni.hideLoading();
													// res = JSON.parse(res);
													console.log(res);
													if(res.errcode==0){
														that.dwlogo=res.data.fname;
														uni.showToast({
															icon: 'none',
															title: res.msg
														});
													}else{
														uni.showModal({
															content: res.msg,
															showCancel: false
														})
													}
										  
										  })
								
						   
					   }
				   });
			   
			},
			
		
		
		radio_ygtxsta(e) {
			this.yg_tx_sta=e.detail.value;
		},	
		radio_ygbxsta(e) {
			this.yg_bx_sta=e.detail.value;
		},	
		radio_ygrzdpsta(e) {
			this.yg_rzdp_sta=e.detail.value;
		},
		radio_ygfysta(e){
			this.yg_fy_sta=e.detail.value;
		},
		radio_yggzyzsta(e){
			this.yg_gzyz_sta=e.detail.value;
		},
		radio_isjxhkfs1(e){
			this.isjxhkfs1=e.detail.value;
		},
		radio_isjxhkfs2(e){
			this.isjxhkfs2=e.detail.value;
		},
		radio_isxxhbfs(e){
			this.isxxhbfs=e.detail.value;
		},
		radio_djsh_ishjczyz(e){
			this.djsh_ishjczyz=e.detail.value;
		},
		radio_djsh_isgrczyz(e){
			this.djsh_isgrczyz=e.detail.value;
		},
		
		ygtxskfsGroupChange(e){
			let value=e.detail.value;
			this.ygtxdstr=value;
			console.log(value);
		},
	
		formSubmit: function(e) {
						let that=this;
									 let pvalue = e.detail.value;
									 
									 let ygtxdstr=that.ygtxdstr;
									 
									 if(ygtxdstr.length == 0){
									 	ygtxdstr='';
									 }else{
									 	ygtxdstr=ygtxdstr.join(',');
									 }
									 
									 if(!that.qytitle){
									   	uni.showToast({
									   		icon: 'none',
									   		title: '请输入企业名称',
									   	});
									   	return false;
									 }
									 if(!pvalue.lxren){
									 	uni.showToast({
									 		icon: 'none',
									 		title: '请输入联系人',
									 	});
									 	return false;
									 }
										if(!pvalue.lxtel){
										  uni.showToast({
											icon: 'none',
											title: '请输入联系电话',
										  });
										  return false;
										}
										
										if(!pvalue.dizhi){
											uni.showToast({
												icon: 'none',
												title: '请输入联系地址',
											});
											return false;
										}
									
								
									
										
							  uni.showLoading({title: '处理中...'})
							  let da={
								'title': that.qytitle,
								'lxren': pvalue.lxren ? pvalue.lxren : '',
								'lxtel': pvalue.lxtel ? pvalue.lxtel : '',
								'dizhi': pvalue.dizhi ? pvalue.dizhi : '',
						        'logo': that.dwlogo ? that.dwlogo : '',
								
								'yg_bx_sta': that.yg_bx_sta,
								'yg_rzdp_sta': that.yg_rzdp_sta,
								'yg_fy_sta': that.yg_fy_sta,
								'yg_gzyz_sta': that.yg_gzyz_sta,
								'ygtxdstr':  ygtxdstr,
								
								'yg_tx_sta': that.yg_tx_sta,
								'yg_tx_sxf': pvalue.yg_tx_sxf ? pvalue.yg_tx_sxf : 0,
								'yg_tx_min': pvalue.yg_tx_min ? pvalue.yg_tx_min : 0,
								'yg_tx_min_sxf': pvalue.yg_tx_min_sxf ? pvalue.yg_tx_min_sxf : 0,
								
								'isjxhkfs1': that.isjxhkfs1,
								'isjxhkfs2': that.isjxhkfs2,
								'isxxhbfs': that.isxxhbfs,
								'djsh_ishjczyz': that.djsh_ishjczyz,
								'djsh_isgrczyz': that.djsh_isgrczyz,
								
							  }
							  this.$req.post('/v1/sjgl/sjinfosave', da)
									  .then(res => {
										uni.hideLoading();
										console.log(res);
										if(res.errcode==0){
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													setTimeout(function(){
													   uni.navigateBack()
													},1000)
												}
											});
										}else{
											uni.showModal({
												content: res.msg,
												showCancel: false
											})
										}
										
									  })
									  .catch(err => {
									
									  })
							  
		   },
		
		   gotojzsytipsj(){
				let that=this;
				 let da={
					 dpid:that.thdpdata.id
				 }
				uni.showLoading({title: ''})
				this.$req.post('/v1/sjgl/jzsytipsj', da)
				        .then(res => {
						    uni.hideLoading();
							console.log(res);
							if(res.errcode==0){
						          
								  uni.showToast({
								  	icon: 'none',
								  	title: res.msg,
								  	success() {
								  		setTimeout(function(){
								  		   that.loadData();
								  		},1000)
								  	}
								  });
							}else{
								uni.showModal({
									content: res.msg,
									showCancel: false,
									success() {
										uni.navigateBack();
									}
								})
							}
				        })
							
		   }
		
	
    }
  }
</script>

<style lang="scss" >
	
.sqbdcon{margin:20upx 0 ;}
.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
	border-bottom:1px solid #efefef;
}
.rowx .icon{position: absolute;right:32upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#888;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 110px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{}
.rowx .inp .input{height:90upx;line-height:90upx;}
.rowx:last-child{border-bottom:0;}
.rowx .sjbtn{position: absolute;right:10upx;top:10upx;height:60upx;line-height:60upx;background: #FFD427;padding:0 30upx;border-radius:100px;}

.maxcon{background:#fff;border-radius:20upx;padding:28upx 28upx 10upx 28upx;margin-bottom:20upx;position: relative;}
.maxcon .vstit{font-weight:700;margin-bottom:5upx}
.maxcon .dptit{text-align: center;}
.maxcon .more{position: absolute;right:28upx;top:28upx;color:#888;font-size: 24rpx;}
.maxcon .more .t1{color: #7F7F7F;}
.maxcon .more .t3{color: #1677FF;margin-left:25upx}
.maxcon .rzicon{position: absolute;right:28upx;top:28upx;}
.maxcon .rzicon .iconfont{font-size:120upx;color:#00aa00}
 
.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.txditem{float:left;margin-right:40upx;}
 
 .ysynum{color:#ff6600;margin-right:10upx}
 .znum{margin-left:10upx}

 
</style>

