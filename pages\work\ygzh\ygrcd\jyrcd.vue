<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex='100'>
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text style="font-weight:700">{{ygname}}</text> <text style="margin:0 10upx"  > - 入库单</text>
					</view>
					<view slot="right" >
				        <view style="margin-right:30upx;color:#1677FF" @click="gotoqh"><text class="iconfont icon-tiaobochuku" style="margin-right:10upx"></text> 出库单</view>
					</view>
					
		</tn-nav-bar>
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 75vw;overflow: hidden;padding-left:10px;">
			
                <view class="tbdhdown">
					<view class="xite" :class="ddtype==0 && sta==0  ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="0"><view class="con">全部</view></view>
					<view class="xite" :class="dwtype==1 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="1"><view class="con">{{ddtype!=0 ? ddtypetxt : '类型'}} <text class="iconfont icon-jiantou-copy-copy"></text></view></view>
					<view class="xite" :class="dwtype==2 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="2"><view class="con">{{sta!=0 ? statxt : '状态'}} <text class="iconfont icon-jiantou-copy-copy"></text></view></view>
					<view class="xite" :class="dwtype==9 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="9">
						<view class="con">{{pxfs!=0 ? pxfstxt : '排序'}} <text class="iconfont icon-jiantou-copy-copy"></text></view>
					</view>
				</view>
			</view>
			<view class="justify-content-item gnssrr" style="width: 25vw;overflow: hidden;" >
				<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		  
		</view> 
	
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 7 + 'px'}" ></view>
		
		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>
		
		<view class="tjtiao" >
			
				<view class="fless">
					<view class="ite" @click="tbdhdownitemsel" :data-dwtype="2" :data-value="0" data-name="全部">
						<view class="sz">{{tjdata.tjnum0}}</view>
						<view class="tx">全部</view>
					</view>
					<view class="ite" @click="tbdhdownitemsel" :data-dwtype="2" :data-value="2" data-name="待审核">
						<view class="sz">{{tjdata.tjnum2}}</view>
						<view class="tx">待审核</view>
					</view>
					<view class="ite" @click="tbdhdownitemsel" :data-dwtype="2" :data-value="1" data-name="已审核">
						<view class="sz">{{tjdata.tjnum1}}</view>
						<view class="tx">已审核</view>
					</view>
					<view class="ite" @click="tbdhdownitemsel" :data-dwtype="2" :data-value="3" data-name="未通过">
						<view class="sz">{{tjdata.tjnum3}}</view>
						<view class="tx">未通过</view>
					</view>
				</view>
		
		</view>
		
		
			<block v-if="listdata.length>0">
			
			
			    
						    <view class="djlist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="item "  >
											
													<view class="con"  >
														
														<view class="status" :class="'ys'+item.status">{{item.statusname}}</view>
														<view class="inf0">
															<view class="tf1">
															 <text class="ddtype" :class="'ys'+item.ddtype">{{item.ddtypetxt}}</text> {{item.ordernum}}
															</view>
															<view class="tf2">
																<text class="rq">{{item.addtime}}</text>
															</view>
															<view class="clear"></view>
														</view>
														
														<view class="inf1">
															<view class="xx">
																<view>客户信息：<text class="khname">{{item.khname}}</text><text
																		class="khid">（{{item.khid}})</text></view>
																<block v-if="item.djsptxtstr">
																	<view>物品信息：{{item.djsptxtstr}}</view>
																</block>
														
																<block v-if="item.ddtype == 1">
																	<view>寄卖底价：￥{{item.spzongjia}}</view>
																	<view>寄卖服务费：<text class="yfjine">￥{{item.fwfzjine}}</text></view>
																	<view v-if="item.ywdqtime">到期时间：{{item.ywdqtime}}</view>
																</block>
														
																<block v-if="item.ddtype == 2">
																	<view>总金额：<text class="yfjine">￥{{item.zongjia}}</text></view>
																</block>
														
																<block v-if="item.ddtype == 3">
																	<view>总金额：￥{{item.zongjia}}</view>
																	<view>服务费：<text class="yfjine">￥{{item.fwfzjine}}</text></view>
																	<view v-if="item.ywdqtime">到期时间：{{item.ywdqtime}}</view>
																</block>
														
																<block v-if="item.ddtype == 4">
																	<!-- 先息后本 -->
																	<block v-if="item.hkfs == 2">
																		<view>预付本金：<text class="yfjine">￥{{item.yfjine}}</text></view>
																		<view v-if="item.ywdqtime">到期时间：{{item.ywdqtime}}</view>
																	</block>
																	<block v-else>
																		<view>预付本金：<text class="yfjine">￥{{item.yfjine}}</text></view>
																		<view v-if="item.ywdqtime">到期时间：{{item.ywdqtime}}</view>
																	</block>
																</block>
																<view>业务人员：{{item.ywrytxtstr ? item.ywrytxtstr : '--' }}</view>
															</view> 
															<view class="clear"></view>
														</view>
														
													
															
																<view class="setbtn3" @click="caozuo" :data-djid='item.id'  :data-cztype='1'  >查看</view>
														
														
													</view>
													
											</view>
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无交易记录</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入单号'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	    <tn-popup v-model="tbdhdownshow" mode="top" :marginTop="vuex_custom_bar_height + 38" :zIndex=100  :borderRadius="20" >
			<block v-if="dwtype==1">
	          <view class="tbdhdownxxcon">
				  <block v-for="(item,index) in ddtypedata" :key="index">
					  <view class="vitem1" :class="item.value==ddtype ? 'hov' : '' " @click="tbdhdownitemsel" :data-dwtype="1" :data-value="item.value" :data-name="item.name" >
						  {{item.name}}
						  <text class="iconfont icon-duigou1 " ></text>
					  </view>
				  </block>	  
			  </view>
			</block> 
			<block v-if="dwtype==2">
			  <view class="tbdhdownxxcon">
				  <block v-for="(item,index) in stadata" :key="index">
					  <view class="vitem1"  :class="item.value==sta ? 'hov' : '' " @click="tbdhdownitemsel" :data-dwtype="2" :data-value="item.value" :data-name="item.name" >
						  {{item.name}}
						  <text class="iconfont icon-duigou1 hov"  v-if="item.value==sta"></text>
					  </view>
				  </block>	  
			  </view>
			</block>
			
			<block v-if="dwtype==9">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in pxdata" :key="index">
						<view class="vitem1" :class="item.value==pxfs ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="9" :data-value="item.value" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==pxfs"></text>
						</view>
					</block>
				</view>
			</block>
			
	    </tn-popup>
	

		
		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427" activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20" :closeBtn="true"></tn-calendar>
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				statxt:'状态',
				ddtype:0,
				ddtypetxt:'类型',
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{value: 0,name: '不限状态'},
					{value: 1,name: '已审核'},
					{value: 2,name: '待审核'},
					{value: 3,name: '未通过'},
				],
				ddtypedata:[
					{value: 0,name: '不限类型'},
					{value: 1,name: '寄卖服务'},
					{value: 2,name: '实物回收'},
					{value: 3,name: '质押暂存'},
					{value: 4,name: '信用预付'},
				],
				pxdata: [{
						value: 0,
						name: '默认排序',
						vname: '排序',
					},
					{
						value: 1,
						name: '时间升序',
						vname: '升序'
					},
					{
						value: 2,
						name: '时间降序',
						vname: '降序'
					},
				],
				pxfs: 0,
				pxfstxt: '排序',
				show3:false,
			    checked: true,
				tbdhdownshow:false,
				dwtype:0,
				
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				tjdata:[],
				
				ygid:0,
				ygname:'',
				
			}
		},
		onLoad(options) {
			let that=this;
			let ygid=options.ygid ? options.ygid : 0;
			let ygname=options.ygname ? options.ygname : '';
		    this.ygid=ygid;
		    this.ygname=ygname;
			
			this.loadData();
			
			setTimeout(function() {
				  that.page = 0;
				  that.listdata = [];
				  that.isloading = false;
				  that.loadingType = -1;
				  that.loaditems(); 
			}, 100);
		},
		onShow() {
		
			let that=this;
		
		
		},
		methods: {
			
			gotoqh(){
			   uni.navigateTo({
			   	url:'./jyckrcd?ygid='+this.ygid+'&ygname='+this.ygname
			   })
			},
			
			rqmodal(){
				this.rqshow=true
			},
			rqmodalchange(e){
				let that=this;
				if(e.startDate && e.endDate){
					// this.thkw='';
					this.rqkstime=e.startDate;
					this.rqjstime=e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch(){
				let that=this;
				this.rqkstime='';
				this.rqjstime='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			
			
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				// this.rqkstime='';
				// this.rqjstime='';
				// that.ddtype = 0;
				// that.sta = 0;
				
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

		
			
 			loadData(){
					  let that=this;
					  let da={
						  tjtype:6,
						  ygid:this.ygid
					  }
				
					 this.$req.post('/v1/ygzh/getygtjnum', da)
					         .then(res => {
					 		 
							     console.log(111,res);
					 			if(res.errcode==0){
									that.tjdata= res.data.tjdata;
					 			}else{
					 				uni.showModal({
					 					content: res.msg,
					 					showCancel: false,
										success() {
											uni.navigateBack();
										}
					 				})
					 			}
					         })
				
			},
			
				
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							ygid:that.ygid,
							pxfs: that.pxfs ? that.pxfs : 0,
							sta:that.sta ? that.sta : 0 ,
							ddtype:that.ddtype ? that.ddtype : 0 ,
							kw:that.thkw ? that.thkw : '' ,
							rqkstime:that.rqkstime ? that.rqkstime : 0 ,
							rqjstime:that.rqjstime ? that.rqjstime : 0 ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/ygzh/jyrcd', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
				
						
			    tbdhdownclk(e){
					let that=this;
					let dwtype = e.currentTarget.dataset.dwtype;
				
					that.dwtype=dwtype;
					if(dwtype==0){
						that.tbdhdownshow=false;
						this.thkw='';
						this.thkwtt='';
						that.rqkstime='';
						that.rqjstime='';
						
						that.ddtype = 0;
						
						that.thkw = '';
						that.sta = 0;
						that.page = 0;
						that.listdata = [];
						that.isloading = false;
						that.loadingType = -1;
						that.loaditems();
					}else{
						that.tbdhdownshow=true;
					}
					
				},
				tbdhdownitemsel(e){
					let that=this;
					let dwtype = e.currentTarget.dataset.dwtype;
					let value = e.currentTarget.dataset.value;
					let name = e.currentTarget.dataset.name;
					let vname = e.currentTarget.dataset.vname;
				
				    if(dwtype==1){
				    	that.ddtype=value;
				    	that.ddtypetxt=name;
				    }
				
				    if(dwtype==2){
						that.sta=value;
						that.statxt=name;
					}
					if (dwtype == 9) {
						that.pxfs = value;
						that.pxfstxt = vname;
					}
					that.dwtype=dwtype;
					that.tbdhdownshow=false;
					
					// that.thkw = '';
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
					
				},
				caozuo:function(e){
					  let that=this;
					  let djid = e.currentTarget.dataset.djid;
					  let cztype = e.currentTarget.dataset.cztype;
					  if(cztype==1){
						 uni.navigateTo({
							url:'/pages/work/rukudan/view?djid='+djid
						 })
					  }
				  },	
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}
 

 


</style>
