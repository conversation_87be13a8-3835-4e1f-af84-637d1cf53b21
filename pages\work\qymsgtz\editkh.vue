<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">


			<view class="sqbdcon">


			
				<view class="maxcon" style="padding:10upx 28upx">
					<view class="rowx" @click="seldxtype">
						<view class="icon"><text class="iconfont icon-jiantou"></text></view>
						<view class="tit"><text class="redst"></text>通知对象</view>
						<view class="inpcon">
							<view class="inp">
								{{dxtypetxt ? dxtypetxt : '请选择通知对象'}}
							</view>
						</view>
						<view class="clear"></view>
					</view>
				</view>
				<view class="maxcon" v-if="dxtype==2">
					<view class="vstit" style="margin-bottom:0">接收客户</view>
					<view class="more vls" @click="seljsdx">选择客户 <text class="iconfont icon-tianjia3"></text></view>
					<view class="rowx" style="margin-top:20upx">
						<view class="inp">{{jsdxnamestr}}</view>
					</view>
				</view>
				<view class="maxcon" style="padding:10upx 28upx" v-if="dxtype==3">
					<view class="rowx" @click="seldianpu">
						<view class="icon"><text class="iconfont  icon-jiantou"></text></view>
						<view class="tit">通知门店</view>
						<view class="inpcon">
							<view class="inp">{{mdname ? mdname : '请选择门店'}}</view>
						</view>
						<view class="clear"></view>
					</view>
				</view>

				<view class="maxcon" style="padding:10upx 28upx">
					<view class="rowx">
						<view class="tit"><text class="redst"></text>通知标题</view>
						<view class="inpcon">
							<view class="inp"><input class="input" type="text" name="title" v-model="title"
									placeholder="请输入通知标题" placeholder-class="placeholder" /></view>
						</view>
						<view class="clear"></view>
					</view>
				</view>





				<view class="maxcon">
					<view style="margin-bottom:20upx;"><text class="redst"></text>通知详情</view>
					<view>
						<view class='bjqcon'>
							<view class='toolbar' @tap="format">
								<view :class="formats.bold ? 'ql-active' : ''" class="bianjiqi icon-bold"
									data-name="bold"> </view>
								<view :class="formats.italic ? 'ql-active' : ''" class="bianjiqi icon-italic"
									data-name="italic"> </view>
								<view :class="formats.underline ? 'ql-active' : ''" class="bianjiqi icon-underline"
									data-name="underline"></view>
								<view :class="formats.align === 'left' ? 'ql-active' : ''"
									class="bianjiqi icon-zuoduiqi" data-name="align" data-value="left"></view>
								<view :class="formats.align === 'center' ? 'ql-active' : ''"
									class="bianjiqi icon-center" data-name="align" data-value="center"></view>
								<view :class="formats.align === 'right' ? 'ql-active' : ''"
									class="bianjiqi icon-youduiqi" data-name="align" data-value="right"></view>
								<view :class="formats.fontSize === '24px' ? 'ql-active' : ''"
									class="bianjiqi icon-728bianjiqi_zitidaxiao" data-name="fontSize" data-value="24px">
								</view>
								<view class="bianjiqi icon-image" @tap="insertImage"></view>
							</view>

							<editor id="editor" class="ql-container" placeholder="开始输入..." showImgSize showImgToolbar
								showImgResize @statuschange="onStatusChange" :read-only="false" @ready="onEditorReady"
								@input="getContents">
							</editor>
						</view>


					</view>
				</view>


			</view>



			<view class="tn-flex tn-footerfixed">
				<view class="tn-flex-1 justify-content-item tn-text-center">
					<view class="bomsfleft">
						<view class="bomsubbtn" @click="subtj" :data-vstatus='2' style="background:#ddd;">
							<text class="iconfont icon-read" style="margin-right:10upx;font-size:28upx"></text>
							<text>暂存草稿箱</text>
						</view>
					</view>
					<view class="bomsfright">
						<view class="bomsubbtn" @click="subtj" :data-vstatus='1'>
							<text class="iconfont icon-tiaobodan" style="margin-right:10upx;font-size:28upx"></text>
							<text>立即发送</text>
						</view>
					</view>
					<view class="clear"></view>
				</view>
			</view>










		</view>

		<view style="height:20px"></view>



		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
				<view class="popuptit">选择企业</view>
				<scroll-view scroll-y="true" style="height: 800rpx;">
					  <view class="seldplist">
						  <block v-for="(item,index) in dpdata" :key="index">
							  
							  <view class="item" @click="selthdp" :data-dpid="item.id"  :data-dpname="item.title"  >
									<view class="xzbtn"  >选择</view>
									<view class="tx"><image :src="staticsfile + item.logo"></image></view>
									<view class="xx">
										<view class="name">{{item.title}}</view>
										<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
									</view>
									<view class="clear"></view>
							  </view>
							  
						  </block>		  
					  </view>
					</scroll-view>
		</tn-popup>

		<tn-popup v-model="jsdxselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">请选择接收客户</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<block v-if="yglisdata">
					<view class="jsdxlist">
						<checkbox-group @change="jsrysetChange">
							<block v-for="(item, index) in yglisdata" :key="index">
								<view class="item">
									<label>
										<view class="icon">
											<checkbox :value="item.id" :data-name="item.realanme" color="#FFD427"
												:checked="item.checked" />
										</view>
										<view class="vstxt">{{item.realname}}</view>
										<view class="mdtt">({{item.mdname}})</view>
										<view class="clear"></view>
									</label>
								</view>
							</block>
						</checkbox-group>


						<view style="height:160upx"></view>
						<view class="tn-flex tn-footerfixed">
							<view class="tn-flex-1 justify-content-item tn-text-center">
								<view class="bomsfleft">
									<button class="bomsubbtn" @click="closejsdx" style="background:#ddd;">
										<text>关闭</text>
									</button>
								</view>
								<view class="bomsfright">
									<button class="bomsubbtn" @click="qrzdysxm">
										<text>确认选择</text>
									</button>
								</view>
								<view class="clear"></view>
							</view>
						</view>

					</view>
				</block>
				<block v-else>

					<view class="gwcno">
						<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
						<view class="tit">暂无绑定漏鱼号的客户</view>
					</view>

				</block>


			</scroll-view>
		</tn-popup>




	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				tzdata: '',
				title: '',
				status: 0,
				formats: {},
				timer: null,
				editorDetail: "",
				tzid: 0,
				dxtype: 1,
				dxtypetxt: '全部企业客户',
				vstatus: 2,
				dpdata: '',
				dpselshow: false,
				mdid: '',
				mdname: '',
				thsjsdxtemp: '',
				yglisdata: [],
				jsdxselshow: false,
				jsdxidstr: '',
				jsdxnamestr: '',
			}
		},
		mounted() {
			this.onEditorReady();
			// 将数据渲染到富文本内
			this.timer = setInterval(() => {
				if (this.editorDetail) {
					clearInterval(this.timer)
					this.editorCtx.setContents({
						html: this.editorDetail,
						success(res) {
							// console.log(res);
						}
					})
				}
			}, 500)
		},
		onLoad(options) {
			let that = this;
			let dxtype = options.dxtype ? options.dxtype : 1;
			let tzid = options.tzid ? options.tzid : 0;
			this.dxtype = dxtype;
			this.tzid = tzid;
			if (tzid > 0) {
				this.xtitle = '编辑企业客户通知';
			} else {
				this.xtitle = '新增企业客户通知';
			}
			this.loadData();
		},

		onShow() {

		},

		methods: {

			seljsdx() {
				this.jsdxselshow = true;
			},
			closejsdx() {
				this.jsdxselshow = false;
			},
			jsrysetChange(e) {
				let value = e.detail.value;
				this.thsjsdxtemp = value;
			},


			qrzdysxm() {
				let that = this;
				let jsvalue = this.thsjsdxtemp;
				let ygjstype = this.ygjstype;
				if (!jsvalue || jsvalue.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择接收客户',
					});
				}
				jsvalue = jsvalue.join(',');
				let da = {
					ygidstr: jsvalue,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/qymsgtz/getkhnamestr', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.jsdxselshow = false;
							that.jsdxidstr = res.data.jsdxidstr;
							that.jsdxnamestr = res.data.jsdxnamestr;
							that.yglisdata = res.data.yglisdata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {

								}
							})
						}
					})

			},



			seldianpu() {
				this.dpselshow = true;
			},
			selthdp(e) {
				let dpid = e.currentTarget.dataset.dpid;
				let dpname = e.currentTarget.dataset.dpname;
				this.mdid = dpid;
				this.mdname = dpname;
				this.dpselshow = false;
			},

			radioGroupChange(e) {
				this.vstatus = e.detail.value;
			},
			loadData() {
				let that = this;
				let tzid = that.tzid;
				let dxtype = that.dxtype;
				let da = {
					tzid: tzid,
					dxtype: dxtype,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/qymsgtz/tzedit2', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.tzdata = res.data.tzdata;
							that.dpdata = res.data.dpdata;
							that.mdid = res.data.mdid;
							that.mdname = res.data.mdname;
							that.yglisdata = res.data.yglisdata;
							if (res.data.tzdata) {
								that.title = res.data.tzdata.title;
								that.dxtype = res.data.tzdata.dxtype;
								that.dxtypetxt = res.data.tzdata.dxtypetxt;
								that.editorDetail = res.data.tzdata.content;
								that.vstatus = res.data.tzdata.vstatus;
								that.jsdxidstr = res.data.tzdata.jsdxidstr;
								that.jsdxnamestr = res.data.tzdata.jsdxnamestr;
								that.mdid = res.data.tzdata.mdid;
								that.mdname = res.data.tzdata.mdname;
							}
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			seldxtype: function() {
				let that = this;
				if (that.tzid > 0 && that.vstatus == 1) {
					uni.showToast({
						icon: 'none',
						title: '通知对象不支持修改！',
					});
					return false;
				}
				uni.showActionSheet({
					itemList: ['全部企业客户', '指定企业客户', '指定店铺全部客户'],
					success: function(res) {
						var index = res.tapIndex;
						if (index == 0) {
							that.dxtype = 1;
							that.dxtypetxt = '全部企业客户';
						}
						if (index == 1) {
							that.dxtype = 2;
							that.dxtypetxt = '指定企业客户';
						}
						if (index == 2) {
							that.dxtype = 3;
							that.dxtypetxt = '指定店铺全部客户';
						}
					},
				});
			},



			subtj(e) {
				let that = this;
				let vstatus = e.currentTarget.dataset.vstatus;

			    //如果是指定店铺
				if (that.dxtype == 3) {

					if (!that.mdid) {
						uni.showToast({ 
							icon: 'none',
							title: '请选择通知店铺',
						});
						return false;
					}

				}

				if (!that.title) {
					uni.showToast({
						icon: 'none',
						title: '请输入通知标题',
					});
					return false;
				}

				//如果是指定客户
				if (that.dxtype == 2) {

					if (!that.jsdxidstr) {
						uni.showToast({
							icon: 'none',
							title: '请选择通知客户',
						});
						return false;
					}

				}

				if (!that.editorDetail) {
					uni.showToast({
						icon: 'none',
						title: '请输入通知内容',
					});
					return false;
				}


				let cztip = '暂存不会立即发送消息！确认暂存到草稿箱吗？';
				if (vstatus == 1) {
					cztip = '确认立即发送吗？';
				}
				uni.showModal({
					title: '操作确认',
					content: cztip,
					success: function(e) {
						//点击确定
						if (e.confirm) {
							uni.showLoading({
								title: '处理中...'
							})
							let da = {
								'tzid': that.tzid ? that.tzid : 0,
								'title': that.title,
								'dxtype': that.dxtype,
								'mdid': that.mdid ? that.mdid : 0 ,
								'vstatus': vstatus,
								'content': that.editorDetail ? that.editorDetail : '',
								// 'mdid': that.mdid ? that.mdid  : 0 ,
								// 'mdname': that.mdname ? that.mdname  : '' ,
								'jsdxidstr': that.jsdxidstr ? that.jsdxidstr : '',
								'jsdxnamestr': that.jsdxnamestr ? that.jsdxnamestr : '',
							}
							that.$req.post('/v1/qymsgtz/tzedit2save', da)
								.then(res => {
									uni.hideLoading();
									console.log(res);
									if (res.errcode == 0) {
										uni.setStorageSync('thuplist', 1);
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000)
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
								.catch(err => {

								})
						}
					}
				})
			},







			// 失去焦点时触发，获取富文本的内容
			getContents() {
				let _this = this
				this.editorCtx.getContents({
					success(res) {
						_this.$emit('getContents', res.html);
						_this.editorDetail = res.html;
					}
				})
			},
			//编辑框初始化时触发
			onEditorReady() {
				uni.createSelectorQuery().select('#editor').context((res) => {
					this.editorCtx = res.context;
				}).exec()
			},
			//撤销
			undo() {
				this.editorCtx.undo()
			},
			//恢复
			redo() {
				this.editorCtx.redo()
			},
			format(e) {
				let {
					name,
					value
				} = e.target.dataset
				if (!name) return
				this.editorCtx.format(name, value)

			},
			//通过 Context 方法改变编辑器内样式时触发，返回选区已设置的样式
			onStatusChange(e) {
				this.formats = e.detail
				console.log(e);
			},
			//插入分隔符
			insertDivider() {
				this.editorCtx.insertDivider({
					success: function() {
						console.log('insert divider success')
					}
				})
			},
			//清空全部内容
			clear() {
				this.editorCtx.clear({
					success: function(res) {
						console.log("clear success")
					}
				})
			},
			//清除样式
			removeFormat() {
				this.editorCtx.removeFormat()
			},
			//插入当前日期
			insertDate() {
				const date = new Date()
				const formatDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
				this.editorCtx.insertText({
					text: formatDate
				})
			},
			//插入图片
			insertImage() {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.editorCtx.insertImage({
										src: that.staticsfile + res.data.fname,
										alt: '',
										success: function() {
											console.log('insert image success')
										}
									})
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})


					}
				})
			}




		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.rowxmttp {
		margn-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 112upx;
		height: 112upx;
		display: block;
		border-radius: 3px
	}


	.bjqcon {
		border: 1px solid #eee;
		margin-top: 5px;
	}

	.bjqcon .bianjiqi {
		display: inline-block;
		padding: 10px 15px;
		width: 24px;
		height: 24px;
		cursor: pointer;
		font-size: 20px;
	}


	.bjqcon .toolbar {
		box-sizing: border-box;
		border-bottom: 0;
		font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
		background-color: #ffffff;
		position: -webkit-sticky;
		position: sticky;
		top: var(--window-top);
		z-index: 99;
		border-bottom: 1px solid #eee;
		background: #fafafa;
		height: 43px;
	}

	.bjqcon .ql-container {
		box-sizing: border-box;
		padding: 8px 10px;
		width: 100%;
		min-height: 30vh;
		max-height: 60vh;
		height: auto;
		background: #fff;
		margin-top: 0px;
		font-size: 16px;
		line-height: 1.5;
	}

	.bjqcon .ql-active {
		color: #06c;
	}


	.jsdxlist {}

	.jsdxlist .item {
		padding: 0 30upx;
		border-bottom: 1px solid #efefef;
		height: 100upx;
		line-height: 100upx
	}

	.jsdxlist .item .icon {
		float: left
	}

	.jsdxlist .item .vstxt {
		float: left;
		mragin-left: 20upx
	}
	.jsdxlist .item .mdtt{float:left;margin-left:20upx;font-size:24upx;color:#888}
</style>