<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >员工管理</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 60vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 40vw;overflow: hidden;" >
				<view class="item btnimg" @click="gotoyqewm" >
					<image src="../../../static/images/addbtn.png"></image>
				</view>
				<view class="item btnimg" @click="ygmsg" >
					<view class="msgbtn"><view class="msgnum">8</view><image src="../../../static/images/msgbtn.png"></image></view>
				</view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		</view>
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 7 + 'px'}" ></view>
	

		
			<block v-if="listdata.length>0">
			
			
			    
						    <view class="yglist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="item " :class="item.status!=1 ? 'lzstyle' : '' " >
													<view class="status" @click="setsta" :data-ygid='item.id' >
														<view class="aa">{{item.statusname}}</view> 
														<view class="aa"><tn-switch v-model="item.status==1 ? true : false " activeColor="#54D216"  inactiveColor="#7F7F7F" leftIcon="success" right-icon="close" change="ss"></tn-switch></view>
													</view>
													<view class="inf1"  >
													
														<view class="tf1">
															<view class="tx"><image :src="staticsfile + item.avatar"></image></view>
															<view class="xx">
																<view class="name">{{item.realname}}</view>
																<view class="tel"><text>员工ID:{{item.id}}</text>电话：{{item.lxtel}}</view>
															</view>
															<view class="clear"></view>
														</view>	
													</view>
													
													<view class="inf2">
													   <view class="vtm">
														   <view class="tt">余额</view>
														   <view class="vv">{{item.yue}}</view>
													   </view>
													   <view class="vtm">
														   <view class="tt">出资额</view>
														   <view class="vv">{{item.czzjine}}</view>
													   </view>
													   <view class="vtm">
														   <view class="tt">可用出资额</view>
														   <view class="vv">{{item.czkyjine}}</view>
													   </view>
													   <view class="clear"></view>
													</view>
													
													<view class="inf3">
														<view class="f1">
															<view class="x1" v-if="item.mdname">{{item.mdname}}</view>
															<view class="x2">{{item.jiaosetxt}}</view>
															<view class="clear"></view>
														</view>
														<view class="f2"><view class="setbtn" @click="ygset" :data-ygid='item.id'>员工设置</view></view>
														<view class="clear"></view>
													</view>
												
												
												
													
											</view>
																					
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无员工</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">员工搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入员工名称/员工ID'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 1,name: '在职'},
					{sta: 2,name: '离职'},
				],
				show3:false,
			}
		},
		onLoad(options) {
			let that=this;
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
		},
		onShow() {
		
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			gotoyqewm(){
				uni.navigateTo({
					url:'./yqewm'
				})
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			ygedit(e){
				let that=this;
				let ygid = e.currentTarget.dataset.ygid;
				uni.navigateTo({
					url:'./edit?ygid='+ygid
				})
			},
			ygset(e){
				let that=this;
				let ygid = e.currentTarget.dataset.ygid;
				uni.navigateTo({
					url:'./ygset?ygid='+ygid
				})
			},
			
			// tab选项卡切换
				tabChange(index) {
					let that=this;
					let sta=that.stadata[index].sta;
					console.log(sta);
					that.thkw = '';
					that.current = index;
					that.sta = sta;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				},
				
			
							
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							sta:that.sta,
							kw:that.thkw ? that.thkw : '' ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/yggl/yglist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
		
		
		
		
				odcz(e){
					let that=this;
					let odid = e.currentTarget.dataset.odid;  
					let czsta = e.currentTarget.dataset.czsta;
					let cztiptxt='';
						if(czsta==8){cztiptxt='确认接单吗？';}
						if(czsta==7){
							cztiptxt='服务确认已完成了吗？';
						}	
							uni.showModal({
							title: '',
							content: cztiptxt,
							success: function(e) {
							
								if (e.confirm) {
									let da = {
										'odid': odid,
										'czsta': czsta
									}
									uni.showLoading({title: ''})
									that.$req.post('/v1/muser/odcz', da)
										.then(res => {
											uni.hideLoading();
											if (res.errcode == 0) {
												uni.showToast({
													icon: 'none',
													title: res.msg,
													success() {
														setTimeout(function() {
																that.page = 0;
																that.listdata = [];
																that.isloading = false;
																that.loadingType = -1;
																that.loaditems();
														}, 1000);
													}
												});
											} else {
												uni.showModal({
													content: res.msg,
													showCancel: false,
												})
											}
										})
								}
							}
						})
					
				},
				
						
				setsta(e){
					let that=this; 
					let ygid = e.currentTarget.dataset.ygid;
					let da = {
						'ygid': ygid
					}
					uni.showLoading({title: ''})
					that.$req.post('/v1/yggl/setsta', da)
						.then(res => {
							uni.hideLoading();
							if (res.errcode == 0) {
								uni.showToast({
									icon: 'none',
									title: res.msg,
									success() {
										setTimeout(function() {
												that.page = 0;
												that.sta = 0;
												that.listdata = [];
												that.isloading = false;
												that.loadingType = -1;
												that.loaditems();
										}, 1000);
									}
								});
							} else {
								uni.showModal({
									content: res.msg,
									showCancel: false,
								})
							}
					})
					
					
				},
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}



.ssnrtip{height:124upx;line-height:124upx;background:#f6f6f6;position: relative;padding:0 32upx}
.ssnrtip .con{}
.ssnrtip .con text{color:#3366ff}
.ssnrtip .cls{position: absolute;right:32upx;}
.ssnrtip .cls text{font-size:45upx}


.yglist{padding:0 32upx 32upx 32upx}
.yglist .item{margin-bottom:32upx;padding:28upx;border-radius:20upx 20upx 0 0;background:#fff;position: relative;}
.yglist .item .status{position: absolute;right:32upx;top:32upx}
.yglist .item .status .aa{float:left;margin-left:10upx;line-height:50upx;}
.yglist .item .inf1{}
.yglist .item .inf1 .tx{float:left}
.yglist .item .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
.yglist .item .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;}
.yglist .item .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.yglist .item .inf1 .tel{color: #7F7F7F;font-size: 24rpx;margin-top:10upx}
.yglist .item .inf1 .tel text{margin-right:30upx}


.yglist .item .inf2{border-top:1px solid #F0F0F0;border-bottom:1px solid #F0F0F0;margin:20upx 0;padding:20upx 0;text-align: center;}
.yglist .item .inf2 .vtm{float:left;width:33.33%;font-size:28upx}
.yglist .item .inf2 .vtm .tt{color: #7F7F7F;font-size:24upx}
.yglist .item .inf2 .vtm .vv{color: #333333;margin-top:15upx}


.yglist .item .inf3{margin-top:30upx}
.yglist .item .inf3 .f1{float:left;height: 60upx;line-height:60upx;font-weight:600}
.yglist .item .inf3 .f1 .x1{float:left;margin-right:15upx}
.yglist .item .inf3 .f1 .x2{float:left;background: #FFD427;height: 44rpx;line-height:44upx;border-radius: 22rpx;padding:0 15upx;font-size:24upx;margin-top:7upx}
.yglist .item .inf3 .f2{float:right;}
.yglist .item .inf3 .setbtn{width: 140rpx;height: 60upx;line-height:60upx;border-radius: 8rpx;border: 2rpx solid #B2B2B2;font-size: 24rpx;text-align: center;}

.yglist .item.lzstyle{background: #F0F0F0;}
.yglist .item.lzstyle .inf3 .f1 .x1{color: #7F7F7F}
.yglist .item.lzstyle .inf3 .f1 .x2{background:#7F7F7F;color:#fff}
.yglist .item.lzstyle .inf2{border-top:1px solid #fff;}

</style>
