<template>
	<view>
		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>余额提现</text>
			</view>
			<view slot="right">
				<view style="margin-right:30upx">
					<navigator url="./txrcd" hover-class="none">提现记录</navigator>
				</view>
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		<view class="matxcont">
			<block v-if="systixian.istx!=1">

				<!--暂不能提现TIP-->
				<view class="notixiancon">
					<view class='tipcon'>
						<text class="iconfont icon-tishi3"></text>
					</view>
					<view class='tipsmzcon'>
						<view class='tipwz'>
							<view class='t1'>{{tipwztxt}}</view>
						</view>
					</view>
					<view class="smlink">
						<navigator url="./txrcd" hover-class="none">【查看提现记录】</navigator>
					</view>
					<view class='wsarcon' style="padding:50px">
						<view class='wanshanbtn'>
							<button @tap="toback" class="bomsubbtn">
								返回
							</button>
						</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class='czcon'>
					<view class='ttip'>可提现金额：<span>￥{{yue}}</span></view>

					<form @submit="formSubmit">
						<view class='titlecon'>
							<view class="title">
								<input type="number" @input="txjs" name='jine' :value="txjine" placeholder='在此输入提现金额'
									maxlength='60' style="font-size:18px;color:#333;" />
							</view>
							<view class="dao" v-if="txjine">
								手续费：<span>{{sxf}}</span> 元 ,
								到帐金额：<span>{{dzjine}}</span> 元
							</view>

							<view class="rowx">
								<view class="icon"><text class="iconfont icon-jiantou"></text></view>
								<view class="tit"><text class="redst"></text>收款方式</view>
								<view class="inpcon" @click="seltxd">
									<view class="inp">
										{{txdtxt ? txdtxt : '请选择收款方式'}}
									</view>
								</view>
								<view class="clear"></view>
							</view>


							<block v-if="txd==4">
								<block v-if="usbank.length>0">

									<view class="rowx" @tap="selbank">
										<view class="icon"><text class="iconfont icon-jiantou"></text></view>
										<view class="tit"><text class="redst"></text>所属银行</view>
										<view class="inpcon">
											<view class="inp">{{selusbank.b_name}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx" @tap="selbank">
										<view class="icon"><text class="iconfont icon-jiantou"></text></view>
										<view class="tit"><text class="redst"></text>开户银行</view>
										<view class="inpcon">
											<view class="inp">{{selusbank.b_kaihu}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx" @tap="selbank">
										<view class="icon"><text class="iconfont icon-jiantou"></text></view>
										<view class="tit"><text class="redst"></text>开户姓名</view>
										<view class="inpcon">
											<view class="inp">{{selusbank.b_huming}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx" @tap="selbank">

										<view class="tit"><text class="redst"></text>银行账号</view>
										<view class="inpcon">
											<view class="inp">{{selusbank.b_number}}</view>
										</view>
										<view class="clear"></view>
									</view>

								</block>
								<block v-else>
									<view class="tjyhkbtn">
										<navigator url="/pages/user/usbank/edit" hover-class="none">还未设置银行卡,立即添加
										</navigator>
									</view>
								</block>
							</block>

							<block v-if="txd==3">
								<block v-if="skzfbdata">
									<view class="rowx">
										<view class="tit"><text class="redst"></text>支付宝名称</view>
										<view class="inpcon">
											<view class="inp">{{skzfbdata.skzfbname}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx">
										<view class="tit"><text class="redst"></text>支付宝账号</view>
										<view class="inpcon">
											<view class="inp">{{skzfbdata.skzfbhao}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx" v-if="skzfbdata.skzfbewm" style="height:200upx">
										<view class="tit"><text class="redst"></text>支付宝码</view>
										<view class="inpcon" style="padding-top:15upx">
											<view class="inp">
												<image :src="staticsfile + skzfbdata.skzfbewm" @click="previewImagex"
													:data-src="staticsfile + skzfbdata.skzfbewm"
													style="width:180upx;height:180upx;"></image>
											</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								<block v-else>
									<view class="tjyhkbtn">
										<navigator url="/pages/user/skzfbwx" hover-class="none">还未设置收款支付宝</navigator>
									</view>
								</block>
							</block>

							<block v-if="txd==2">
								<block v-if="skwxdata">

									<view class="rowx">
										<view class="tit"><text class="redst"></text>微信名称</view>
										<view class="inpcon">
											<view class="inp">{{skwxdata.skwxname}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx">
										<view class="tit"><text class="redst"></text>微信账号</view>
										<view class="inpcon">
											<view class="inp">{{skwxdata.skwxhao}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx" v-if="skwxdata.skwxewm" style="height:200upx">
										<view class="tit"><text class="redst"></text>微信码</view>
										<view class="inpcon" style="padding-top:15upx">
											<view class="inp">
												<image :src="staticsfile + skwxdata.skwxewm" @click="previewImagex"
													:data-src="staticsfile + skwxdata.skwxewm"
													style="width:180upx;height:180upx;"></image>
											</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								<block v-else>
									<view class="tjyhkbtn">
										<navigator url="/pages/user/skzfbwxm" hover-class="none">还未设置收款微信</navigator>
									</view>
								</block>
							</block>

							<view style="margin-top:30px">
								<button class="bomsubbtn" form-type="submit" style="margin:0">确认提交</button>
							</view>
						</view>
					</form>







				</view>


				<tn-popup v-model="visible" mode="bottom" :borderRadius="20">
					<view class="drawer_boxma">
						<view class='closebtn' @click="closeDrawer"><i class='iconfont icon-guanbi3'></i></view>
						<view class="drtit">选择收款银行卡</view>
						<view class="drawer_c">

							<block v-if="usbank.length>0">
								<scroll-view scroll-y="true" style="height:580upx;">
									<view class="dzcon">
										<block v-for="(item, index) in usbank" :key="index">
											<view class="dzli">
												<view class="nrcon" @tap="selthdz" :data-dzindex="index">
													<view class="czbtn">选择</view>
													<view class="yx">{{item.b_name}}</view>
													<view class="kh"><text
															style="font-weight:700;font-style: italic;color:#3366ff">{{item.b_number}}</text>
														<text style="margin-left:30upx">{{item.b_huming}}</text></view>
												</view>
											</view>
										</block>
									</view>
								</scroll-view>
							</block>

						</view>
					</view>
				</tn-popup>


			</block>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				visible: false,
				txd: 0,
				txdtxt: '',
				yue: 0,
				sxf: 0,
				dzjine: 0,
				jine: 0,
				txjine: '',
				isnotxtip: true,
				systixian: '',
				tipwztxt: '',
				usbank: '',
				selusbank: '',
				skzfbdata: '',
				skwxdata: '',
				txdsz: '',
			}
		},
		onLoad(options) {

		},
		onShow() {
			let that = this;
			that.loadData();
		},


		methods: {
			selbank() {
				this.visible = true;
			},
			closeDrawer() {
				this.visible = false;
			},
			selthdz: function(e) {
				let that = this;
				let dzindex = e.currentTarget.dataset.dzindex;
				let usbank = that.usbank;
				let selusbank = usbank[dzindex];
				that.selusbank = selusbank;
				that.visible = false;
			},

			seltxd: function() {
				let that = this;
				uni.showActionSheet({
					itemList: that.txdsz,
					success: function(res) {

						let index = res.tapIndex;
						let tit = that.txdsz[res.tapIndex];
						if (tit == '内部结算') {
							that.txd = 1;
						}
						if (tit == '银行卡') {
							that.txd = 4;
						}
						if (tit == '支付宝') {
							that.txd = 3;
						}
						if (tit == '微信') {
							that.txd = 2;
						}
						that.txdtxt = tit;
					},
				});
			},


			loadData() {
				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/muser/ygtixian', da)
					.then(res => {

						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							console.log(res);
							let systixian = res.data.systixian;
							//如果暂不允许提现则提示
							if (systixian.istx != 1) {
								that.isnotxtip = false;
								that.tipwztxt = '系统维护中，暂停提现!';
							}
							that.yue = res.data.yue;
							that.systixian = systixian;
							that.usbank = res.data.usbank;
							that.selusbank = res.data.selusbank;
							that.skzfbdata = res.data.skzfbdata;
							that.skwxdata = res.data.skwxdata;
							that.txdsz = res.data.txdsz;

						} else {
							if (res.errcode === 102) {
								that.isnotxtip = false;
								that.tipwztxt = res.msg;
							} else {
								uni.showModal({
									title: '错误提示',
									content: res.msg,
									showCancel: false,
									success(res) {
										uni.navigateBack()
									}
								});
							}
						}
					})
			},
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},
			toback: function() {
				uni.navigateBack();
			},
			validateNumber(val) {
				return val.replace(/\D/g, '')
			},
			txjs: function(e) {

				let that = this;
				let value = this.validateNumber(e.detail.value);
				let txjine = value;
				let yue = that.yue;
				let systixian = that.systixian;
				let yg_tx_min_sxf = systixian.yg_tx_min_sxf;
				let yg_tx_sxf = systixian.yg_tx_sxf;
				if (txjine > yue) {

					txjine = yue;
					uni.showToast({
						title: '最多可提现' + yue + '元',
						icon: 'none'
					});
					this.txjine = yue;
					return false;


				}

				let sxf = txjine * yg_tx_sxf / 100;
				if (sxf < yg_tx_min_sxf) {
					sxf = yg_tx_min_sxf;
				}
				sxf = sxf.toFixed(2);
				let dzjine = txjine - sxf;
				dzjine = dzjine.toFixed(2);

				that.txjine = txjine;
				that.sxf = sxf;
				that.dzjine = dzjine;



			},
			radioChange(e) {
				let that = this;
				let txd = e.detail.value;
				that.txd = txd;
			},
			gotobdxcxdl: function() {
				uni.navigateTo({
					url: '/pages/user/yzmobile?st=3'
				})

			},

			formSubmit(e) {
				let pvalue = e.detail.value;
				let that = this;
				let yue = that.yue;
				let txd = that.txd;
				let jine = pvalue.jine;

				let selusbank = that.selusbank;
				let bid = 0;
				if (selusbank) {
					bid = selusbank.id;
				}

				let yg_tx_min = that.systixian.yg_tx_min;
				if (!pvalue.jine) {
					uni.showModal({
						title: '错误提示',
						content: '请输入提现金额！',
						showCancel: false
					});
					return false;
				}
				if (parseInt(jine) < parseInt(yg_tx_min)) {
					uni.showModal({
						title: '错误提示',
						content: '单笔提现金额不少于 ' + yg_tx_min + ' 元！',
						showCancel: false
					});
					return false;
				}
				if (parseInt(jine) > parseInt(yue)) {
					uni.showModal({
						title: '错误提示',
						content: '可提现金额 ' + yue + ' 元！',
						showCancel: false
					});
					return false;
				}

				if (!txd) {
					uni.showModal({
						title: '错误提示',
						content: '请选择收款方式！',
						showCancel: false
					});
					return false;
				}

				if (txd == 4 && selusbank.length <= 0) {
					uni.showModal({
						title: '错误提示',
						content: '请选择收款银行卡！',
						showCancel: false
					});
					return false;
				}



				uni.showModal({
					title: '信息提示',
					content: '确认提现吗？',
					success: function(res) {
						if (res.confirm) {
							// 发起网络请求
							let da = {
								jine: jine,
								txd: txd ? txd : 0,
								bid: bid ? bid : 0
							};
							uni.showLoading();
							that.$req.post('/v1/muser/ygtixiansave', da)
								.then(res => {
									uni.hideLoading();

									if (res.errcode == 0) {
										let odnum = res.data.odnum;
										uni.showToast({
											title: res.msg,
											icon: 'none',
											duration: 1000,
											success(res) {
												setTimeout(function() {
													uni.redirectTo({
														url: './txrcd'
													})
												}, 1000);
											}
										})
									} else {
										uni.showModal({
											title: '错误提示',
											content: res.msg,
											showCancel: false
										})
									}
								})
						}
					}
				})




			},




		}
	}
</script>

<style lang='scss'>
	.czcon {
		margin-top: 10rpx;
	}

	.czcon .titlecon {
		padding: 40rpx 80rpx 80rpx 80rpx;
	}

	.czcon .title {
		margin-bottom: 20rpx;
	}

	.czcon .title input {
		height: 100rpx;
		line-height: 100rpx;
		border: 1px solid #eee;
		text-align: center;
		background: #f7f7f7;
		border-radius: 10upx;
	}

	.czcon .ttip {
		text-align: center;
		font-size: 18px;
		margin-top: 50rpx;
	}

	.czcon .ttip span {
		color: #d60e19;
		font-size: 18px
	}

	.czcon .smcon span {
		color: #cc0000;
	}

	.czcon .dao {
		text-align: center;
		margin-bottom: 10px;
		font-size: 12px
	}

	.czcon .dao span {
		color: #cc0000
	}

	.czcon .txd1 {
		margin-bottom: 5px
	}

	.czcon .txd2 {}

	.czcon .tjyhkbtn {
		text-align: center;
		color: #ff6600;
		margin-bottom: 25px;
		margin-top: 25px
	}

	.czcon .selthusbank {
		margin-bottom: 15px;
		height: 50px;
		line-height: 20px;
		font-size: 12px;
		position: relative;
	}

	.czcon .selthusbank .qhbtn {
		position: absolute;
		right: 0;
		top: 30upx
	}

	.czcon .selthusbank .qhbtn text {
		font-size: 25px;
	}



	.txrcdlink {
		position: absolute;
		bottom: 60rpx;
		right: 60rpx;
	}


	.dzcon {
		padding: 0;
	}

	.dzli {
		position: relative;
		border-bottom: 1px solid #efefef;
		padding: 30rpx 30rpx;
	}

	.dzli .yx {
		font-size: 14px;
		margin-bottom: 10upx
	}

	.dzli .kh {
		font-size: 14px
	}

	.dzli .czbtn {
		position: absolute;
		right: 20upx;
		top: 45upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		font-size: 24upx;
	}



	.matxcont {
		margin: 32upx;
		padding: 32upx;
		border-radius: 20upx;
		background: #fff;
		min-height: 500upx;
	}

	.notixiancon {
		text-align: center;
	}

	.tipcon {
		padding: 50px 0
	}

	.tipcon text {
		font-size: 55px;
		color: #FFD427
	}

	.smlink {
		margin-top: 40px
	}

	.skyhktip {
		margin-bottom: 10upx;
		margin-top: 20upx;
		text-align: center;
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
</style>