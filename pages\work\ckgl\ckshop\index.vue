<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >
						  <block v-if="isck == 1">
						      {{thckdata.title}}
						  </block>
						  <block v-else>
							  店铺物品
						  </block>
				      </text>
					</view>
					<view slot="right" >
					</view>
					 
		</tn-nav-bar>
		<view class="tabs-fixed-blank" ></view>
	
	
			<!-- 顶部自定义导航 -->
			<view class="tabs-fixed tn-bg-white " style="z-index: 100;" :style="{top: vuex_custom_bar_height + 'px'}">
			  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 75vw;overflow: hidden;padding-left:10px;">
				
					<view class="tbdhdown">
						<block v-if="isck==0">
							<view class="xite" :class="ddtype==0 && sta==0  && sortid==0  && ckid==0    ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="0"><view class="con">全部</view></view>
						</block>
						<block v-else>
							<view class="xite" :class="ddtype==0 && sta==0  && sortid==0  ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="0"><view class="con">全部</view></view>
						</block>
						<view class="xite" :class="dwtype==1 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="1"><view class="con">{{ddtype!=0 ? ddtypetxt : '类型'}} <text class="iconfont icon-jiantou-copy-copy"></text></view></view>
						<view class="xite" :class="dwtype==2 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="2"><view class="con">{{sta!=0 ? statxt : '状态'}} <text class="iconfont icon-jiantou-copy-copy"></text></view></view>
						<view class="xite" :class="dwtype==9 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="9">
							<view class="con">{{pxfs!=0 ? pxfstxt : '排序'}} <text class="iconfont icon-jiantou-copy-copy"></text></view>
						</view>
					</view>
				</view>
				<view class="justify-content-item gnssrr" style="width: 25vw;overflow: hidden;" >
					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
				</view>
			  </view>  
			  
			  
			</view>  
				
			<view class="tabs-fixed " style="z-index: 99;" :style="{top: vuex_custom_bar_height + 38 + 'px'}">
				<view class="flckselcon">
					<view class="sf1">
						<view class="wpflsel">
							<view class="da0"><text class="iconfont icon-fenlei"></text></view>
							<view class="da1">
								<view @click="selwpfl" >{{sortid ? sortnamep : '按物品分类筛选'}}</view>
								
							
									
							</view>
							<view class="da2">
								<block v-if="sortid > 0">
									<text class="iconfont icon-cuohao02" style="margin-left:10upx" @click="clssortid"></text>
								</block>
								<block v-else>
									<text class="iconfont icon-jiantou2" style="margin-left:10upx"></text>
								</block>
							</view>
							<view class="clear"></view>
						</view>
					</view>
					<view class="sf2">
					
					   <block v-if="isck == 1">
							<view class="wpcksel">
								<text @click="qhseldianpu" class="iconfont icon-qiehuandianpu1" style="margin-right:10upx"></text>
								<text @click="qhseldianpu">{{mdname ? mdname : '按店铺筛选' }}</text>
								<block v-if="mdid > 0">
									<text class="iconfont icon-cuohao02" style="margin-left:10upx" @click="clsmdid"></text>
								</block>
								<block v-else>
									<text class="iconfont icon-jiantou2" style="margin-left:10upx" ></text>
								</block>
							</view>
					   </block>
					   <block v-else>
					   	<view class="wpcksel">
					   		<text class="iconfont icon-cangkupeizhi" style="margin-right:10upx"></text>
					   		<text @click="selcangku">{{ckname ? ckname : '按仓库筛选' }}</text>
					   		<block v-if="ckid > 0">
					   			<text class="iconfont icon-cuohao02" style="margin-left:10upx" @click="clsckid"></text>
					   		</block>
					   		<block v-else>
					   			<text class="iconfont icon-jiantou2" style="margin-left:10upx" ></text>
					   		</block>
					   	</view>
					   </block>
					
					</view>
					<view class="clear"></view>
				</view>
			</view>
			
			<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 40 + 'px'}"></view>
	      
			
			<block v-if="thkw || rqkstime">
				<view class="ssvtiao">
					<block v-if="rqkstime">
						<view class="ssgjc">
							 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
							<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
							</view>
						</view>
					</block>
					<block v-if="thkw">
						<view class="ssgjc">
							 <text class="gjc">" {{thkw}} "</text>
							<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
							</view>
						</view>
					</block>
					<view class="clear"></view>
				</view>
			</block>
	
			<view class="tjtiao">
				
					<view class="fless">
						<view class="ite">
							<view class="sz">{{tjdata.zcbjine}}</view>
							<view class="tx">物品总成本</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjdata.zongshu}}</view>
							<view class="tx">物品总数</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjdata.tjnum1}}</view>
							<view class="tx">已上架</view>
						</view> 
						<view class="ite">
							<view class="sz">{{tjdata.tjnum8}}</view>
							<view class="tx">已售</view>
						</view>
					</view>
				
			</view>
	

		
			<block v-if="listdata.length>0">
			
			
			    
						    <view class="splist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="item "  >
													<view class="sjstatus" :class="'sta'+item.sjsta">{{item.sjstatusname}}</view>
													<view class="inf1"  >
														<view class="spbh">编号：{{item.id}}</view>
														<view class="status" :class="'sta'+item.status" v-if="item.statusname">{{item.statusname}}</view>
														<view class="otbstxt" v-if="item.otbstxt">{{item.otbstxt}}</view>
														
														<view class="tf1">
															<view class="tx"><image :src="staticsfile + item.smallpic" @tap='previewImagev' :data-picarr="item.picturesarr" ></image></view>
															<view class="xx"> 
																<view class="n0"><text class="ddtype" :class="'ys'+item.ddtype">{{item.ddtypename}}</text> {{item.sortname}}</view>
																<view class="n1">{{item.title}}</view>
															<!-- 	<view class="n2">成本：{{item.danjia}} x {{item.kucun}} = <text class="zj">￥{{item.zongjia}}</text></view>
																<view class="n2">上架：<text class="zj">￥{{item.sjjiage}}</text></view> -->
																<view class="n2">{{item.dtptxt}}数量：{{item.shuliang}}，库存：{{item.kucun}}</view>
																<view class="n2">{{item.dtptxt}}单价：￥{{item.danjia}}</view>
																<view class="n2">{{item.dtptxt}}总价：<text class="zj">￥{{item.orzongjia}}</text></view>
																<view class="n2">上架单价：<text >￥{{item.sjjiage}}</text></view>
															   
															</view>
															<view class="clear"></view>
														</view>	
													</view>
													<view class="inf2">
													
														
														<block v-if="item.rksta_cf==1">
																<view class="f1" v-if="isck">所属店铺：{{item.mdname_cf}}</view>
																<view class="f1" >所在仓库：{{item.ckname_cf}}</view>
																<view class="f1" v-if="item.rktime_cf">入库时间：{{item.rktime_cf}}</view>
														</block>
														<block  v-else>
															<block v-if="item.rksta==1">
																<view class="f1" v-if="isck">所属店铺：{{item.mdname}}</view>
																<view class="f1" v-if="item.rktime">入库时间：{{item.rktime}}</view>
																<view class="f1" v-if="isck == 0">所属仓库：{{item.ckname}}</view>
															</block>
															<block v-else>
																<view class="f1">入库状态：<text style="color:#ff0000">{{item.rkstatxt}}</text></view>
																<view class="f1" v-if="item.ckname">入库仓库：{{item.ckname}}</view>
															</block>
														</block>
														
														<view class="f1">业务人员：{{item.ywrytxtstr}}</view>
														<view class="f1">在库时间：{{item.zktime}}</view>
														<block v-if="item.ismydpsp==1">
													    	<view class="setbtn" @click="spedit" :data-spid='item.id'>管理</view>
														</block>
														<block  v-else>
															<view class="setbtn" @click="spview" :data-spid='item.id'>查看</view>
														</block>
													</view>
												
												
												
													
											</view>
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关物品</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">物品搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'物品名称、编号、业务员'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
		<tn-popup v-model="tbdhdownshow" mode="top" :marginTop="vuex_custom_bar_height + 38" :zIndex=100  :borderRadius="20" >
			<block v-if="dwtype==1">
		      <view class="tbdhdownxxcon">
				  <block v-for="(item,index) in ddtypedata" :key="index">
					  <view class="vitem1" :class="item.value==ddtype ? 'hov' : '' " @click="tbdhdownitemsel" :data-dwtype="1" :data-value="item.value" :data-name="item.name" >
						  {{item.name}}
						  <text class="iconfont icon-duigou1 " ></text>
					  </view>
				  </block>	  
			  </view>
			</block> 
			<block v-if="dwtype==2">
			  <view class="tbdhdownxxcon">
				  <block v-for="(item,index) in stadata" :key="index">
					  <view class="vitem1"  :class="item.value==sta ? 'hov' : '' " @click="tbdhdownitemsel" :data-dwtype="2" :data-value="item.value" :data-name="item.name" >
						  {{item.name}}
						  <text class="iconfont icon-duigou1 hov"  v-if="item.value==sta"></text>
					  </view>
				  </block>	  
			  </view>
			</block>
			
			<block v-if="dwtype==9">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in pxdata" :key="index">
						<view class="vitem1" :class="item.value==pxfs ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="9" :data-value="item.value" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==pxfs"></text>
						</view>
					</block>
				</view>
			</block>
			
		</tn-popup>
		
		
		
		<tn-popup v-model="ckselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择仓库</view>
		    <scroll-view scroll-y="true" style="height: 800rpx;"> 
		          <view class="xselcklist">
		              <block v-for="(item,index) in ckdata" :key="index">
						  
						  <view class="item" @click="selthck" :data-ckid="item.id"  :data-ckname="item.title"  >
							    <view class="xzbtn"  >选择</view>
						  		<view class="tx"><image :src="staticsfile + item.logo"></image></view>
						  		<view class="xx">
						  			<view class="name">{{item.title}}</view>
						  			<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
						  		</view>
						  		<view class="clear"></view>
						  </view>
						  
				      </block>		  
		          </view>
		        </scroll-view>
		</tn-popup>
		
	    <tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择入库日期范围" btnColor="#FFD427" activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20" :closeBtn="true"></tn-calendar>
		
		<tn-wpfl v-model="wpflshow" @qrxz="wpflqrxz" @close="wpflclose" ></tn-wpfl>
		
		<tn-popup v-model="qhdpselshow" mode="bottom" :borderRadius="20" :closeBtn='true' :zIndex="30000">
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
		
				<view class="qhsflist">
					<block v-for="(itemx,indexx) in mydpdata.glsfdata" :key="indexx">
						<view class="sfitem" style="height: 60px; display: flex; flex-direction: column; justify-content: center;">
						  <view class="tit">{{itemx.mdname}}
							<text class="iconfont icon-mendian cjglicon" v-if="itemx.iszzh==1 "></text>
						  </view> 
						  <view class="zss" v-if="itemx.jiaosetxt">店铺角色：<text class="jstxt">{{itemx.jiaosetxt}}</text></view>
						  <view class="jrbtn" @click="jrdp" :data-mdname="itemx.mdname" :data-mdid="itemx.mdid">选择</view>
						</view>
					</block> 
				</view>
		
		
			</scroll-view>
		</tn-popup>
	</view>
</template>

<script>

	export default {
	
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				statxt:'状态',
				ddtype:0,
				ddtypetxt:'类型',
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[],
				ddtypedata:[],
				pxdata: [{
						value: 0,
						name: '默认排序',
						vname: '排序',
					},
					{
						value: 1,
						name: '入库时间升序',
						vname: '升序'
					},
					{
						value: 2,
						name: '入库时间降序',
						vname: '降序'
					},
					{
						value: 3,
						name: '在库时间升序',
						vname: '升序'
					},
					{
						value: 4,
						name: '在库时间降序',
						vname: '降序'
					},
				],
				pxfs: 0,
				pxfstxt: '排序',
				
				show3:false,
			    checked: true,
				tbdhdownshow:false,
				dwtype:0,
				
				tjdata:[],
				spsortdata: [],
				
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				
				
				
				ckselshow:false,
				thckdata:'',
				ckdata:'',
				ckid:0,
				ckname:'',
				isck:0,
				
				wpflshow:false,
				sortid:0,
				sortnamep:'',
				
				qhdpselshow: false,
				mydpdata: [],
				mdid: 0,
				mdname: '',
			}
		},
		onLoad(options) {
			let that=this;
			let ckid=options.ckid ? options.ckid : 0; 
			let isck=options.isck ? options.isck : 0; 
			let thkw=options.thkw ? options.thkw : ''; 
			this.isck=isck;
			this.ckid=ckid;
			this.loadData();
			
			if(thkw){
				this.thkw=thkw
			}
			
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
		},
		onShow() {
			let that=this;
			if(uni.getStorageSync('thupcksplist')==1){
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thupcksplist',0)
			}
			
		
		},
		methods: {
		
			
			previewImagev: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;
			   
				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			selcangku(){
				this.ckselshow=true;
			},
			selthck(e){
				let that=this;
				let ckid=e.currentTarget.dataset.ckid;
				let ckname=e.currentTarget.dataset.ckname;
				this.ckid=ckid;
				this.ckname=ckname;
				this.ckselshow=false;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssortid(){
				let that=this;
				this.sortid=0;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clsckid(){
				let that=this;
				this.ckid=0;
				this.ckname='';
				that.page = 0;
				this.loadData();
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			loadData() {
				let that = this;
				let da = {
					tjtype:2,
					sortid:that.sortid ? that.sortid : 0 ,
					ckid:that.ckid ? that.ckid : 0 ,
					ddtype:that.ddtype ? that.ddtype : 0 ,
					kw:that.thkw ? that.thkw : '' ,
					rqkstime:that.rqkstime ? that.rqkstime : 0 ,
					rqjstime:that.rqjstime ? that.rqjstime : 0 ,
					cxmdid:that.mdid ? that.mdid : 0
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/getcktj', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							that.tjdata = res.data.tjdata;
							that.spsortdata=res.data.spsortdata;
							that.stadata=res.data.stadata;
							that.ddtypedata=res.data.ddtypedata;
							that.ckdata=res.data.ckdata;
							that.thckdata=res.data.thckdata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			
			},
			rqmodal(){
				this.rqshow=true
			},
			rqmodalchange(e){
				let that=this;
				if(e.startDate && e.endDate){
					// this.thkw='';
					this.rqkstime=e.startDate;
					this.rqjstime=e.endDate;
					this.loadData();
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch(){
				let that=this;
				this.rqkstime='';
				this.rqjstime='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.loadData();
			},
			
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.loadData();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.loadData();
			},
			
			spedit(e){
				let that=this;
				let spid = e.currentTarget.dataset.spid;
				uni.navigateTo({
					url:'./edit?spid='+spid
				})
			},
			spview(e){
				let that=this;
				let spid = e.currentTarget.dataset.spid;
				uni.navigateTo({
					url:'./spview?spid='+spid
				})
			},
		
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							ckid: that.ckid ? that.ckid : 0,
							sortid: that.sortid ? that.sortid : 0,
							pxfs: that.pxfs ? that.pxfs : 0,
							sta:that.sta ? that.sta : 0 ,
							ddtype:that.ddtype ? that.ddtype : 0 ,
							kw:that.thkw ? that.thkw : '' ,
							rqkstime:that.rqkstime ? that.rqkstime : 0 ,
							rqjstime:that.rqjstime ? that.rqjstime : 0,
							cxmdid:that.mdid ? that.mdid : 0
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading(); 
					this.$req.get('/v1/ckgl/ckshoplist', da)
						   .then(res => {
							    console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
				
					tbdhdownclk(e){
						let that=this;
						let dwtype = e.currentTarget.dataset.dwtype;
					
						that.dwtype=dwtype;
						if(dwtype==0){
							that.tbdhdownshow=false;
							this.thkw='';
							this.thkwtt='';
							that.rqkstime='';
							that.rqjstime='';
							
							that.ddtype = 0;
							that.sortid = 0;
							
							if(that.isck==0){
								that.ckid = 0;
								that.ckname = '';
							}
							
							that.thkw = '';
							that.sta = 0;
							this.loadData();
							that.page = 0;
							that.listdata = [];
							that.isloading = false;
							that.loadingType = -1;
							that.loaditems();
						}else{
							that.tbdhdownshow=true;
						}
						
					},
					tbdhdownitemsel(e){
						let that=this;
						let dwtype = e.currentTarget.dataset.dwtype;
						let value = e.currentTarget.dataset.value;
						let name = e.currentTarget.dataset.name;
						let vname = e.currentTarget.dataset.vname;
					
						if(dwtype==1){
							that.ddtype=value;
							that.ddtypetxt=name;
						}
					
						if(dwtype==2){
							that.sta=value;
							that.statxt=name;
						}
						if (dwtype == 9) {
							that.pxfs = value;
							that.pxfstxt = vname;
						}
						that.dwtype=dwtype;
						that.tbdhdownshow=false;
						
						// that.thkw = '';
						this.loadData();
						that.page = 0;
						that.listdata = [];
						that.isloading = false;
						that.loadingType = -1;
						that.loaditems();
						
					},
		
		
		
		
		     	 onchange(e) {
					  console.log('1111',e);
					  
				 },
				 
				 selwpfl(){
				 	this.wpflshow=true;
				 },
				 wpflqrxz(node) {
					 console.log(node);
					 let sortid=node.sortid;
					 let that=this;
					 that.sortid=sortid;
					 that.sortnamep=node.sortnamep;
					 this.loadData();
					 that.page = 0;
					 that.listdata = [];
					 that.isloading = false;
					 that.loadingType = -1;
					 that.loaditems();	
					  this.wpflshow=false;	   
				 },
				 wpflclose(){
					 this.wpflshow=false;
				 },
				 
			qhseldianpu() {
				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				that.$req.post('/v1/selsf/dpnqh', da)
					.then(res => {
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.mydpdata = res.data.mydpdata;
							that.qhdpselshow = true;
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
						console.log(res)
					})


			},
			jrdp(e) {
				let that = this;
				that.mdid = e.currentTarget.dataset.mdid;
				that.mdname = e.currentTarget.dataset.mdname;
				let da = {
					mdid: that.mdid 
				};
				that.qhdpselshow = false;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems(); 
			}, 
			clsmdid(){
				let that=this;
				this.mdid=0;
				this.mdname='';
				that.page = 0;
				this.loadData();
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
		}
	}
</script>




 
<style lang='scss'>

.splist{padding:15upx 32upx 32upx 32upx} 
.splist .item{margin-bottom:32upx;background:#fff;border-radius:20upx;position: relative;}
.splist .item .spbh{position: absolute;right:28upx;top:28upx;font-size:20upx;color:#1677FF}

.splist .item .inf1{padding:28upx;border-radius:20upx 20upx 0 0;background:#fff;position: relative;}
.splist .item .inf1 .status{position: absolute;right:20upx;bottom:100upx;border-radius:30upx;padding:5upx 15upx;color:#fff;background:#ccc;font-size:24upx}
.splist .item .inf1 .otbstxt{position: absolute;right:20upx;bottom:45upx;font-size:24upx;color:#1677FF}
.splist .item .inf1 .tx{float:left}
.splist .item .inf1 .tx image{width:240upx;height:240upx;display: block;border-radius:8upx;}
.splist .item .inf1 .xx{float:left;margin-left:20upx;width: calc(100% - 265upx);font-size:24upx}
.splist .item .inf1 .xx .n0{margin-bottom:15upx;}
.splist .item .inf1 .xx .n1{font-weight:600;height:45upx;line-height:45upx;overflow: hidden;margin-bottom:5upx;font-size:28upx}
.splist .item .inf1 .xx .n2{margin-top:2upx;}
.splist .item .inf1 .xx .n2 text{} 
.splist .item .inf1 .xx .n2 .zj{color:#ff0000}
.splist .item .inf2{background: #F0F0F0;border-radius: 0rpx 0rpx 20rpx 20rpx;padding:15upx 28upx;font-size: 20rpx;color: #333;position: relative;line-height:40upx;}
.splist .item .inf2 .rktime{} 
.splist .item .inf2 .f1{} 
.splist .item .inf2 .setbtn{position: absolute;right:25upx;top:40upx;width: 100rpx;height: 50upx;line-height:50upx;background: #FFD427;border-radius: 8rpx;text-align: center;color:#333}
.splist .item .inf2 .setbtn2{position: absolute;right:145upx;top:40upx;width: 100rpx;height: 50upx;line-height:50upx;background: #fff;border-radius: 8rpx;text-align: center;color:#333}

.splist .item .ddtype{padding:5upx 15upx;background:#efefef;border-radius:100upx;margin-right:10upx;font-size:20upx}
.splist .item .ddtype.ys1{background:#f86c02;color:#fff} 
.splist .item .ddtype.ys2{background:#63b8ff;color:#fff}
.splist .item .ddtype.ys3{background:#c76096;color:#fff} 
.splist .item .ddtype.ys4{background:#43b058;color:#fff}


.splist .item .status.sta1{background:#54D216;}
.splist .item .status.sta12{background:#54D216;}
.splist .item .status.sta2{background:#FF6600;color:#fff}
.splist .item .status.sta3{background:#FF2828;color:#fff}
.splist .item .status.sta8{background:#888;}
.splist .item .status.sta9{background:#c76096;color:#fff}
 
.splist .item .sjstatus{font-size:20upx;border-radius: 0upx 20upx 20upx 0upx;height:40upx;line-height:40upx;padding:0 25upx ;color:#0EC45C;margin-right:10upx;position: absolute;left:18upx;top:40upx;z-index:1;
border-left:5px solid #FFD427;}
.splist .item .sjstatus.sta1{background:#43b058;color:#fff}
.splist .item .sjstatus.sta2{background:#888;color:#fff} 




</style>
