<template>
  <view class="tn-safe-area-inset-bottom">

	<tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
				 <view slot="back" class='tn-custom-nav-bar__back'  >
					  <text class='icon tn-icon-left'></text>
				 </view>
		  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
		  		  <text  >设置手机号</text>
		  		</view>
		  </tn-nav-bar>
	
	 
	
	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
	  
	  <view >
			
			<view style="margin:30upx;padding:20upx;background:#fff;border-radius:10upx">	
					<view class="login-type-form">
						
							<view class="input-item">
							
								<input
										class="login-type-input"
										type="number"
										name="mobile"
										v-model="mobile"
										placeholder="请输入新手机号码"
										maxlength="11"
								/>
							</view>
							<view class="input-item input-item-sms-code">
							
								<view class="input-wrapper">
									<view class="rf-input-wrapper">
										<input
												type="number"
												class="login-type-input"
												v-model="mscode"
												placeholder="请输入验证码"
												maxlength="6"
										/>
									</view>
									<button
											:disabled='disabled'
											class="sms-code-btn"
											@tap.stop="getVerificationCode()"
									>
										<text>{{codename}}</text>
									</button>
								</view>
							</view>
				
					</view>
				
					
					<view class="tn-flex-1 justify-content-item tn-margin-sm tn-text-center">
							<tn-button backgroundColor="#FFD427" padding="40rpx 0"  width="100%"  fontBold @click="tosubmit">
							  <text class="tn-color-white">确认提交</text>
							</tn-button>			
					</view>
					
					
					
					
			</view>
			
			
		</view>
		
	</view>
</template>

<script>

	export default {

		data() {
			return {
				disabled:false,
				codename:'获取验证码',
				mobile:'',
				mscode:'',
				btnLoading: false,
				reqBody: {},
				codeSeconds: 0, // 验证码发送时间间隔
			}
		},
		onLoad(option){
		
		},
		onShow(){
		
		},
		methods: {
		
			tosubmit() {
			   let that=this;
			   
			
				   if (this.mobile.length <= 0) {
				   	uni.showToast({ 
				   		icon: 'none',
				   		title: '请输入手机号'
				   	});
				   	return;
				   }
					if (this.mscode.length <= 0) {
						uni.showToast({
							icon: 'none',
							title: '请输入验证码'
						});
						return;
					}
			
			
			   uni.showLoading({title: ''})
			   this.$req.post('/v1/muser/setmobilesave', {
			   		mobile: this.mobile,
			   		mscode: this.mscode,
			   	})
			   		.then(res => {
			   			uni.hideLoading();
			   					
			   			if(res.errcode==0){
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function(){
										 uni.navigateBack()
									},1000)
								}
							});
			   			}else{
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
			   			}
			   			
			   		})
			   		
			},
					getCode(){
					    let mobile = this.mobile;
					    let that = this;
					    if (mobile == "") {
					      uni.showToast({
					        title: '手机号不能为空',
					        icon: 'none',
					        duration: 1000
					      });
					      return false;
					    }
					    if (mobile.length!=11) {
					      uni.showToast({
					        title: '请输入正确的手机号',
					        icon: 'none',
					        duration: 1000
					      });
					      return false;
					    }
					
					      // 发起网络请求
					      let da = {
					        'mobile': mobile
					      };
						  uni.showLoading({ 'title': '' });
						  this.$req.post('/v1/muser/setmobilecodesd', da)
						  	.then(res => {
						  
								uni.hideLoading();
						  		if (res.errcode == 0) {
									
						  			 that.disabled= true;
						  			 uni.showToast({
						  			   title: res.msg,
						  			   icon: 'none', 
						  			   duration: 1000,
						  			   success: function () {
						  			     var num = 61;
						  			     var timer = setInterval(function () {
						  			       num--;
						  			       if (num <= 0) {
						  			         clearInterval(timer);
						  			           that.codename='重新发送';
						  			           that.disabled= false;
						  			       } else {
											    that.codename= num + "s"
						  			       }
						  			     }, 1000)
						  			 			
						  			 			
						  			   }
						  			 })
						  				
						  		} else {
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
						  		}
						  
						  	})
						  
						  
					 
					  },
					  //获取验证码
					  getVerificationCode() {
					    this.getCode();
					  },
		}
	}
</script>

<style lang="scss">
	page{background: #fff;}
	
	.smtips{padding:20upx}
	.smtips .tit{font-weight:700;margin-bottom:10px}
	.smtips .smitem{margin-bottom:5px;line-height:23px;}

	.login-type-form {
			width: 90%;
			margin: 50upx auto;
			.input-item {
				position: relative;
				height: 90upx;
				line-height: 90upx;
				margin-bottom: 30upx;
				.iconfont {
					color:#666;
					font-size: 38upx;
					position: absolute;
					left: 0;
				}
				.login-type-input {
					height: 90upx;
					border-bottom: 1upx solid #eee;
				}
				.sms-code-btn, sms-code-resend {
					width: 240upx;
					font-size: 26upx;
				}
			}
		}
		.login-type-tips {
			width: 80%;
			margin:0 auto;
			display: flex;
			justify-content: space-between;
			font-size: 28upx;
			color: #666;
		}
  // 发送验证码样式
    .input-item-sms-code {
      .input-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

    .sms-code-btn {
      width: 170upx;
      background-color: #fff;
      display: flex;
      padding: 6upx 0;
      justify-content: center;
      align-items: center;
      border-radius: 12upx;
    		border:1px solid #ddd;
    		box-sizing: content-box;
    		color:#3366ff;
    }

      .sms-code-resend {
        color: #666;
      }
    }
	
</style>
