<template>
	<view class="page-e tn-safe-area-inset-bottom">


		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>客户交易统计</text>
			</view>
			<view slot="right">

			</view>
		</tn-nav-bar>


		<view :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>


		<block v-if="rqkstime">
			<view class="tjtiaox" style="z-index: 0;">
				<view class="ssgjc">
					按日期查询 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
					<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> </view>
				</view>
			</view>
		</block>




		<view class="maxcon" style="margin-top:10upx;padding-top:0;">

			<view class="qhdps">
				<view class="tline"></view>
				<view class="logo">
					<image :src="staticsfile + khdata.avatar"></image>
				</view> 
				<view class="tit">{{khdata.realname}} <text class="khbhs">客户编号：{{khdata.id}}</text></view>
				<view class="clear"></view>
			</view> 

			<view class="tjzong">
				<view class="sz" >{{tjdata.jyjine}}</view>
				<view class="txt">累计交易金额 </view>
				<!-- <view class="fyzcbtn" @click="gotodpzz">查看详情</view> -->
			</view>
			<view class="tjitem ">
				<view class="ite gx">
					<view class="cons" >
						<view class="txt">非出入库交易次数 </text>
						</view>
						<view class="sz" >{{tjdata.xxjycs}}</view>

					</view>
				</view>
				<view class="ite">
					<view class="cons" >
						<view class="txt">非出入库交易金额 </text>
						</view>
						<view class="sz" >{{tjdata.xxjyjine}}</view>
					</view>
				</view>
				<view class="clear"></view>
			</view>
			<view class="tjitem noline">
				<view class="ite gx">
					<view class="cons" >
						<view class="txt">累计交易次数 </text>
						</view>
						<view class="sz" >{{tjdata.jycs}}</view>
			
					</view>
				</view>
				<view class="ite">
					<view class="cons" >
						<view class="txt">累计盈利金额 </text>
						</view>
						<view class="sz" style="color:#ff0000">{{tjdata.yljine}}</view>
					</view>
				</view>
				<view class="clear"></view>
			</view>
		</view>



		<view class="ddfltj">
			<!-- 入库 -->
			<view class="item">
				<view class="con" @click="gotojymx1">
					<view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
					<view class="icon">
						 入库交易
					</view>
					<view class="tjtxt">单量(单)</view>
					<view class="tjnum">{{tjdata.rknum1}}</view>
					<view class="tjtxt">订单金额(元)</view>
					<view class="tjnum">{{tjdata.rknum2}}</view>
				</view>
			</view>
			<!-- 出库 -->
			<view class="item">
				<view class="con" @click="gotojymx2">
					<view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
					<view class="icon">
						出库交易
					</view>
					<view class="tjtxt">单量(单)</view>
					<view class="tjnum">{{tjdata.cknum1}}</view>
					<view class="tjtxt">订单金额(元)</view>
					<view class="tjnum">{{tjdata.cknum2}}</view>
				</view>
			</view>


			<view class="clear"></view>
		</view>

		<view style="height:60upx"></view>


		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				khid: 0,
				khname: '',
				khdata: [],
				rqshow: false,
				rqkstime: 0,
				rqjstime: 0,
				tjdata: [],

			}
		},

		onLoad(options) {
			let that = this;
			let khid = options.khid ? options.khid : 0;
			this.khid=khid;
			this.loadData();
		},
		onShow() {
			let that = this;
		},

		methods: {

			gotojymx1(){
				let khid=this.khdata.id;
				let khname=this.khdata.realname;
				uni.navigateTo({
					url:'./jylist?khid='+khid+'&khname='+khname
				})
			},
			gotojymx2(){
				let khid=this.khdata.id;
				let khname=this.khdata.realname;
				uni.navigateTo({
					url:'./jycklist?khid='+khid+'&khname='+khname
				})
			},
			loadData() {
				let that = this;
				let da = {
					khid: that.khid ? that.khid : 0,
					dpid: that.dpid ? that.dpid : 0,
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/khgl/jytj', da)
					.then(res => {
						console.log(1222, res);
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.khdata = res.data.khdata;
							that.tjdata = res.data.tjdata;
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})
			},

			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					this.thkw = '';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					this.loadData();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				this.loadData();
			},


		}
	}
</script>

<style lang="scss" scoped>
	.ddfltj {
		margin: 0 20upx
	}

	.ddfltj .item {
		float: left;
		width: 50%;
	}

	.ddfltj .item .more {
		position: absolute;
		right: 20upx;
		top: 20upx;
		color: #888
	}

	.ddfltj .item .con {
		padding: 30upx;
		text-align: center;
		background: #fff;
		margin: 10upx;
		border-radius: 16upx;
		position: relative;
	}

	.ddfltj .item .icon {margin-bottom:35upx;font-size:35upx;}


	.ddfltj .item .tjtxt {
		font-size: 24rpx;
		height: 30upx;
		line-height: 30upx;
		color: #888;
		margin-top: 10upx
	}

	.ddfltj .item .tjnum {
		font-size: 36rpx;
		height: 70upx;
		line-height: 70upx;
	}

	.top-backgroup {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 450upx;
		z-index: -1;

		.backgroud-image {
			width: 100%;
			height: 450upx;
		}
	}

	.ttrrss {
		margin-right: 30upx;
		font-size: 24upx
	}

	.ttrrss .iconfont {
		margin-right: 10upx;
	}

	.tjtiao {
		background: #fff;
		color: #333;
		font-size: 24upx;
		padding: 10upx 30upx;
		margin-bottom: 20upx;
		border-radius: 10upx;
	}

	.tjtiao .ssgjc {
		font-size: 24upx;
	}

	.maxcon {
		background: #fff;
		margin:32upx;
		border-radius: 20rpx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon.pdum0 {
		padding: 0 20upx 0 20upx
	}

	.tjzong {
		text-align: center;
		border-bottom: 1px solid #F3F3F3;
		padding: 40upx 0;
		// padding-left: 80upx
	}

	.tjzong .sz {
		font-weight: 600;
		font-size: 56upx;
		// color:#ff0000;
	}

	.tjzong .txt {
		color: #7F7F7F;
		margin-top: 15upx;
		font-size: 28upx
	}

	.tbtjcon {}

	.tbtjcon .tjxm {
		float: left;
		padding-left: 70upx;
	}

	.tbtjcon .tjxm .xmcon {
		padding: 15upx 0
	}

	.tbtjcon .tjtb {
		float: right;
		padding-top: 20upx;
		padding-right: 20upx
	}

	.tbtjcon .tjtb image {
		width: 210upx;
	}

	.tbtjcon .sz {
		font-weight: 600;
		font-size: 36upx
	}

	.tbtjcon .txt {
		color: #7F7F7F;
		margin-bottom: 15upx;
		font-size: 28upx;
		position: relative;
	}

	.tbtjcon .txt .iconfont {
		font-size: 45upx;
		position: absolute;
		left: -45upx;
		top: -4upx
	}

	.tbtjcon .txt .ys1 {
		color: #1677ff
	}

	.tbtjcon .txt .ys2 {
		color: #54d216
	}

	.tbtjcon .txt .ys3 {
		color: #ff2828
	}

	.tbtjcon .txt .ys4 {
		color: #ffd427
	}


	.qhdps {
		background: #fff;
		padding: 20upx 40upx;
		position: relative;
		font-size: 32upx;
		border-bottom: 1px solid #F3F3F3;
		border-radius: 20upx 20upx 0 0;
	}

	.qhdps .icon {
		position: absolute;
		right: 40upx;
		top: 40upx;
	}

	.qhdps .icon .iconfont {
		font-size: 40upx
	}

	.qhdps .logo {
		float: left;
	}

	.qhdps .tit {
		float: left;
		height: 80upx;
		line-height: 80upx;
		margin-left: 20upx;
	}
	.khbhs{color:#888;font-size:24upx;position: absolute;right:30upx;top:20upx;}

	.qhdps .logo image {
		width: 80upx;
		height: 80upx;
		border-radius: 100upx;
		display: block;
	}


	.tjitem {
		border-bottom: 1px solid #F3F3F3;
		padding: 20upx 0;
	}

	.tjitem .ite {
		width: 50%;
		float: left;
		position: relative;
		text-align: center;
	}

	.tjitem .ite.gx:after {
		position: absolute;
		top: 35%;
		right: 0;
		content: '';
		width: 1px;
		height: 45%;
		-webkit-transform: scaleX(0.5);
		transform: scaleX(0.5);
		border-right: 1px solid #D9D9D9;
	}

	.tjitem .txt {
		color: #7F7F7F;
		margin-bottom: 15upx;
		font-size: 28upx
	}

	.tjitem .sz {
		font-weight: 600;
		font-size: 36upx
	}

	.tjitem.noline {
		border-bottom: 0
	}

	.tjitem .cons {
		// padding-left: 40upx;
	}

	.mxicon {
		// margin-left: 10upx;
	}



	.fyzcbtn {
		position: absolute;
		top: 50upx;
		right: 40upx;
		height: 70upx;
		line-height: 70upx;
		border-radius: 100upx;
		padding: 0 20upx;
		background: #FFD427;
		font-size: 24upx;
	}

	.tline {
		position: absolute;
		left: 0;
		top: 43upx;
		width: 10upx;
		height: 30upx;
		background: #FFD427;
	}
</style>