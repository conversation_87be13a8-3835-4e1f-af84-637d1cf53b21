<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{djdata.ddtypetxt}}详情</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" style="background:#fff;" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="ddtopcon">
				<view class="ddbh">
					{{djdata.ordernum}}
				</view>

				<block v-if="djdata.status==1">
					<view class="ddstacon" :class="'fwyssta'+djdata.ddsta ">{{djdata.ddstatusname}}</view>
				</block>
				<block v-else>
					<view class="ddstacon" :class="'ys'+djdata.status">{{statusname}}</view>
				</block>

				<view class="ddtticon">
					<image src="/static/images/ddtoicon1.png" style="width:105px;height:90px;"></image>
				</view>
			</view>

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">{{djdata.ddtypetxt}}信息</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx" style="display: none;">
							<view class="tit"><text class="redst"></text>单据编号</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ordernum}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户姓名</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户电话</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khtel}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<block v-if="djdata.ddtype==1">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==2 && djdata.ywtime>0">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>回收时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


						<block v-if="djdata.ddtype==4">

							<view class="rowx">
								<view class="tit"><text class="redst"></text>预付金额</view>
								<view class="inpcon">
									<view class="inp">￥{{djdata.yfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>预付时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="icon" style="color:#1677FF;z-index:200;">{{djdata.ywzqdwtxt}}</view>
								<view class="tit"><text class="redst"></text>还款周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>利率</view>
								<view class="inpcon">
									<view class="inp">{{djdata.lilv}}%</view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx">
								<view class="tit"><text class="redst"></text>总利息</view>
								<view class="inpcon">
									<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>总还款金额</view>
								<view class="inpcon">
									<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<block v-if="djdata.hkfs==2">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>到期时间</view>
									<view class="inpcon">
										<view class="inp">
											{{djdata.dqtimetxt}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>

							<block v-if="djdata.hkfs==1">
								<view class="rowx" @click="ckhkjh">
									<view class="icon"><text class="iconfont icon-jiantou"></text></view>
									<view class="tit"><text class="redst"></text>还款计划</view>
									<view class="inpcon">
										<view class="inp">
											{{hkjhtip}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>

						</block>

					</view>



					<block v-if="djdata.ddtype!=4">
						<view class="maxcon" style="padding-bottom:5upx">

							<view class="vstit" style="margin-bottom:30upx">物品列表</view>

							<block v-if="djshop">
								<view class="splist">
									<block v-for="(item,index) in djshop" :key="index">
										<view class="spitem">
											<view class="spname">
												<view class="tline"></view>
												{{item.title}}
												<text style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.id}})</text>
											</view>
											<view class="spbjbtn" @click="spedit" :data-spid="item.id"
												v-if="djdata.status==2"><text class="iconfont icon-bianji"></text> 编辑
											</view>

										<!-- 	<view class="rowa">
												<view class="tip">物品编号</view>
												<view class="nr colorls">{{item.id}}</view>
												<view class="clear"></view>
											</view> -->

											<view class="rowa">
												<view class="tip">物品价格</view>
												<view class="nr">
													¥{{item.danjia}} x {{item.shuliang}} = ¥<text
														>{{item.zongjia}}</text>
												</view>
												<view class="clear"></view>
											</view>
											<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
												<view class="rowa">
													<view class="tip" @click="qtfymx" :data-spid="item.id"
														:data-spname="item.title">其他收费 <text
															class="iconfont icon-yewucaozuo"
															style="margin-left:10upx;color:#1677FF"></text> </view>
													<view class="nr">
														<text @click="qtfymx" :data-spid="item.id"
															:data-spname="item.title">¥{{item.fjfjine}}</text>
														<text class="addotfybtn" @click="qtfyedit" :data-spid="item.id"
															:data-spname="item.title" data-fyid="0"
															v-if="djdata.status==2">+新增</text>
													</view>
													<view class="clear"></view>
												</view>
											</block>

									<!-- 		<block v-if="djdata.ddtype==2">
												<view class="rowa">
													<view class="tip" @click="qtcbmx" :data-spid="item.id"
														:data-spname="item.title">其他成本 <text
															class="iconfont icon-yewucaozuo"
															style="margin-left:10upx;color:#1677FF"></text> </view>
													<view class="nr">
														<text @click="qtcbmx" :data-spid="item.id"
															:data-spname="item.title">¥{{item.cbfjine}}</text>
														<text class="addotfybtn" @click="qtcbedit" :data-spid="item.id"
															:data-spname="item.title" data-fyid="0"
															v-if="djdata.status==2">+新增</text>
													</view>
													<view class="clear"></view>
												</view>
											</block> -->

											<block v-if="djdata.ddtype==1">
												<view class="rowa">
													<view class="tip">服务费比例</view>
													<view class="nr">{{item.fwfbl}} %</view>
													<view class="clear"></view>
												</view>
											</block>
											<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
												<view class="rowa">
													<view class="tip">服务费金额</view>
													<view class="nr" style="color:#ff0000">¥{{item.fwfjine}}</view>
													<view class="clear"></view>
												</view>
											</block>

											<view class="rowa">
												<view class="tip">验货员</view>
												<view class="nr">{{item.yhrystr ? item.yhrystr : '--' }}</view>
												<view class="clear"></view>
											</view>

											<block v-if="djdata.ddtype==1 || djdata.ddtype==2">
												<view class="rowa">
													<view class="tip">上架价格</view>
													<view class="nr">¥{{item.sjjiage}}</view>
													<view class="clear"></view>
												</view>
												<view class="rowa">
													<view class="tip">上架状态</view>
													<view class="nr">

														<block v-if="djdata.status==2">
															<view @click="setspsta" :data-spid='item.id'
																style="padding-top:15upx;">
																<tn-switch v-model="item.sjsta==1 ? true : false "
																	activeColor="#FFD427" inactiveColor="#dfdfdf"
																	leftIcon="success" right-icon="close"
																	change="ss"></tn-switch>
															</view>
														</block>
														<block v-else>
															{{item.sjstatusname}}
														</block>
													</view>
													<view class="clear"></view>
												</view>
											</block>
										
											<view class="rowa">
												<view class="tip">仓库</view>
												<view class="nr">{{item.ckname ? item.ckname : '--' }}</view>
												<view class="clear"></view>
											</view>

											<block v-if="item.vxq==1">
												<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
													<view class="rowa">
														<view class="tip">{{itemx.title}}</view>
														<view class="nr">{{itemx.value}}</view>
														<view class="clear"></view>
													</view>
												</block>

												<block v-if="item.picturesarr">
													<view class="rowxmttp">
														<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
															<block v-for="(itemxx,indexxx) in item.picturesarr"
																:key="indexxx">
																<view class='item'>
																	<image :src="staticsfile+itemxx"
																		:data-src='staticsfile + itemxx '
																		@click='previewImage' :data-picarr="item.picturesarr"></image>
																</view>
															</block>
															<view class="clear"></view>
														</view>
													</view>
												</block>

												<block v-if="item.beizhu">
													<view class="bzcon">
														<view class="bztit">物品备注：</view>
														<view class="bznr">{{item.beizhu}}</view>
													</view>
												</block>

											</block>


											<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
												<block v-if="item.vxq==0">
													展开详情<text class="iconfont icon-jtdown2"></text>
												</block>
												<block v-else>
													收起详情<text class="iconfont icon-jtup2"></text>
												</block>
											</view>


											<block v-if="djdata.ddtype==2">
												<view class="czfrcon">
													<view class="cstit">
														<view class="tit"><text class="iconfont icon-renminbi"></text>
															出资</view>
														<block v-if="djdata.status==2">
															<view class="tadd" @click="addczry" :data-spid="item.id"
																:data-isofr="0" :data-rytype="8" data-rytypetxt="出资人"
																style="color:#333">
																<text class="iconfont icon-jia"
																	style="margin-right:10upx;color:#1677FF"></text>
																新增出资
															</view>
														</block>
														<view class="clear"></view>

													</view>
												</view>

												<block v-if="item.czfrrydata">
													<block v-for="(itemx,indexxxx) in item.czfrrydata" :key="indexxxx">
														<view class="czrli">
															<block v-if="djdata.status==2 && itemx.rytype==8">
																<view class="del" @click="delczry"
																	:data-czryid="itemx.id">删除</view>
															</block>
															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm">出资金额：<text
																		:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
																</view>
																<view class="czxm2" @click="czryedit"
																	:data-spid="item.id" :data-czryid="itemx.id"
																	:data-czygid="itemx.ygid"
																	:data-czygname="itemx.ygname"
																	:data-czjine="itemx.czjine"
																	:data-rytype="itemx.rytype"
																	:data-rytypetxt="itemx.rytypetxt"
																	:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
																	:data-isdpczzh="itemx.isdpczzh"
																	v-if="djdata.status==2" :data-isofr="0">修改</view>
																<view class="clear"></view>
															</view>
														</view>
													</block>
													<view class="czrli">
														<view class="xx">
															<view class="czxm hj">已出资金额：<text
																	class="">{{item.czjinehj}}</text> <text
																	class="gx">,</text> 未出资金额：<text
																	class="hs">{{item.czjinehj2}}</text></view>
															<view class="clear"></view>
														</view>
													</view>
												</block>
											</block>




											<block v-if="djdata.ddtype==3">



												<view class="czfrcon">
													<view class="cstit">
														<view class="tit"><text class="iconfont icon-renminbi"></text>出资
														</view> 
														<block v-if="djdata.status==2">
															<view class="tadd" @click="addczry" :data-spid="item.id"
																:data-isofr="0" :data-rytype="8" data-rytypetxt="出资人" 
																style="color:#333">
																<text class="iconfont icon-jia"
																	style="margin-right:10upx;color:#1677FF"></text>
																新增出资
															</view>
														</block>
														<view class="clear"></view>

													</view>
												</view>

												<block v-if="item.czfrrydata">
													<block v-for="(itemx,indexx) in item.czfrrydata" :key="indexx">
														<view class="czrli">
															<block v-if="djdata.status==2 && itemx.rytype==8">
																<view class="del" @click="delczry"
																	:data-czryid="itemx.id">删除</view>
															</block>
															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm">出资金额：<text
																		:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
																</view>
																<view class="czxm">分润金额：<text
																		:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
																</view>
																<view class="czxm2" @click="czryedit"
																	:data-spid="item.id" :data-czryid="itemx.id"
																	:data-czygid="itemx.ygid"
																	:data-czygname="itemx.ygname"
																	:data-czjine="itemx.czjine"
																	:data-rytype="itemx.rytype"
																	:data-rytypetxt="itemx.rytypetxt"
																	:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
																	:data-isdpczzh="itemx.isdpczzh"
																	v-if="djdata.status==2" :data-isofr="0">修改</view>
																<view class="clear"></view>
															</view>
														</view>
													</block>
													<view class="czrli last">
														<view class="xx">
															<view class="czxm hj">已出资金额：<text
																	class="">{{item.czjinehj}}</text> <text
																	class="gx">,</text> 未出资金额：<text
																	class="hs">{{item.czjinehj2}}</text></view>
															<view class="clear"></view>
														</view>
													</view>
												</block>




												<view class="czfrcon">
													<view class="cstit">
														<view class="tit"><text class="iconfont icon-renminbi"></text>
															分润</view>
														<view class="clear"></view>
													</view>
												</view>

												<block v-if="item.czfrrydata3.length > 0">
													<block v-for="(itemx,indexx3) in item.czfrrydata3" :key="indexx3">
														<view class="czrli">
															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm">分润金额：<text
																		:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
																</view>
																<view class="czxm2" @click="czryedit"
																	:data-spid="item.id" :data-czryid="itemx.id"
																	:data-czygid="itemx.ygid"
																	:data-czygname="itemx.ygname"
																	:data-czjine="itemx.czjine"
																	:data-rytypetxt="itemx.rytypetxt"
																	:data-rytype="itemx.rytype"
																	:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
																	:data-isdpczzh="itemx.isdpczzh"
																	v-if="djdata.status==2" :data-isofr="1">修改</view>
																<view class="clear"></view>
															</view>
														</view>
													</block>
												</block>
											</block>



										</view>
									</block>
								</view>
							</block>

						</view>
					</block>




					<!-- 信用预付 -->
					<block v-if="djdata.ddtype==4">
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资</view>
							<block v-if="djdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="0" :data-isofr="0" :data-rytype="8"
									data-rytypetxt="出资人" style="color:#333"><text class="iconfont icon-jia"
										style="margin-right:10upx;color:#1677FF"></text> 新增出资
								</view>
							</block>
							<block v-if="djdata.czfrrydata"> 
								<view class="czry4info">
									<block v-for="(itemx,indexx) in djdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<block v-if="djdata.status==2 && itemx.rytype==8">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>

											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">出资金额：<text <text
														:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
												</view>
												<block v-if="czfrisfr==1">
													<view class="czxm">分润金额：<text
															:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
													</view>
												</block>
												<view class="czxm2" @click="czryedit" :data-spid="0"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"  :data-isdpczzh="itemx.isdpczzh"
													v-if="djdata.status==2" :data-isofr="0">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>

								</view>

							</block>

						</view>
					</block>
					<block v-if="djdata.ddtype==4 || djdata.ddtype==3">
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
							<block v-if="djdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="0" :data-isofr="1" :data-rytype="9"
									data-rytypetxt="分润人" style="color:#333"><text class="iconfont icon-jia"
										style="margin-right:10upx;color:#1677FF"></text> 新增分润
								</view>
							</block>
							<block v-if="djdata.czfrry2data.length > 0">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in djdata.czfrry2data" :key="indexx">
										<view class="czrli">
											<block v-if="djdata.status==2 && itemx.rytype==9">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>

											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">分润金额：<text
														:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
												</view>
												<view class="czxm2" @click="czryedit" :data-spid="0"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-frbl="itemx.frbl" :data-isdpczzh="itemx.isdpczzh"
													v-if="djdata.status==2" :data-isofr="1">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>

								</view>

							</block>


						</view>


						<view class="maxcon" style="padding-bottom:10upx">
							<block v-if="djdata.ddtype==4">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj">已出资金额：<text class="">{{djdata.czjinehj}}</text> <text
												class="gx">,</text> 未出资金额：<text class="hs">{{djdata.czjinehj2}}</text>
										</view>
										<view class="clear"></view>
									</view>
								</view>
							</block>
							<block v-if="czfrisfr==1">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">已分润金额：<text
												class="">{{djdata.frjinehj}}</text> <text class="gx">,</text>
											未分润金额：<text class="hs">{{djdata.frjinehj2}}</text></view>
										<view class="clear"></view>
									</view>
								</view>
							</block>
						</view>
					</block>
					
					
					<block v-if="djdata.ddtype!=4">
					
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit">费用信息</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>物品总价</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">{{djdata.spzongjia}}</view>
								</view>
								<view class="clear"></view>
							</view>
					
							<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>服务费</view>
									<view class="inpcon">
										<view class="inp">{{djdata.fwfzjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
						 <block v-if="djdata.ddtype==1 || djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他收费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fjfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>	
					
					<!-- 		<block v-if="djdata.ddtype==2">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>其他成本</view>
									<view class="inpcon">
										<view class="inp">{{djdata.cbfzjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block> -->
					
							<block v-if="djdata.ddtype==2">
								<view class="rowx" >
									<view class="tit"><text class="redst"></text>单据总价</view>
									<view class="inpcon">
										<view class="inp" style="color:#ff0000">¥{{djdata.zongjia}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
							<block v-else>
								<view class="rowx" >
									<view class="tit"><text class="redst"></text>总费用</view>
									<view class="inpcon">
										<view class="inp" style="color:#ff0000">¥{{djdata.zongfy}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
					
					
						</view>
					
					

					
					</block>
					
					
					<view class="maxcon" style="padding-bottom:10upx">

						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务人员</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywrytxtstr}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>录单人员</view>
							<view class="inpcon">
								<view class="inp">{{djdata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view>

					</view>

					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
							<view class="zjzkicon" @click="setvzz">{{vsfnr==0 ? '展开' : '收起'}} <text
									class="iconfont icon-jiantou_liebiaozhankai"></text></view>

							<block v-if="vsfnr==1">
								<view class="uprzimgcon">
									<view class="zzx sf1">
										<view class="con">
											<view class="vt1">
												<block v-if="djdata.rzimg1">
													<image :src='staticsfile + djdata.rzimg1'></image>
												</block>
												<block v-else>
													<image src="/static/images/rzupimg1.png"></image>
												</block>
											</view>
											<view class="vt2">身份证头像面</view>
											<view class="clear"></view>
										</view>
									</view>
									<view class="zzx sf2">
										<view class="con">
											<view class="sf2">
												<block v-if="djdata.rzimg2">
													<image :src='staticsfile + djdata.rzimg2'></image>
												</block>
												<block v-else>
													<image src="/static/images/rzupimg2.png"></image>
												</block>
											</view>
											<view class="vt2">身份证国徽面</view>
										</view>
									</view>
									<view class="clear"></view>
								</view>


								<view class="rowx" style="margin-top:20upx;" v-if="djdata.khsfzhao">
									<view class="tit"><text class="redst"></text>身份证号</view>
									<view class="inpcon">
										<view class="inp">{{djdata.khsfzhao}}</view>
									</view>
									<view class="clear"></view> 
								</view>


								<view class="rowx" v-if="djdata.isrlsb==1">
									<view class="tit"><text class="redst"></text>人脸识别</view>
									<view class="inpcon">
										<view class="inp">
											{{djdata.isrlsb==1 ? '需要' : '不需要'}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
								<block v-if="djdata.isrlsb==1">
									<view class="rowx">
										<view class="icon" style="z-index:200;" @click="gotorlsbewm"><text
												class="iconfont icon-qrcode-1-copy"
												style="color:#1677FF;margin-right:10upx"></text> 认证二维码</view>
										<view class="tit"><text class="redst"></text>识别状态</view>
										<view class="inpcon">
											<view class="inp">
												<text :class="'rlsbsta'+ djdata.rlsbsta ">{{djdata.rlsbstatxt}}</text>
											</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>


							</block>


						</view>
					</view>

					<block v-if="djdata.remark">
						<view class="maxcon">
							<view class="vstit" style="font-weight:400;margin-bottom:25upx">单据备注说明 <text
									style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

							<view class="bzcon">
								{{djdata.remark}}
							</view>

						</view>
					</block>

				</view>


				<block v-if="djdata.status==1 && shremark">
					<view class="maxcon">
						<view class="vstit" style="font-weight:400;margin-bottom:25upx">审核备注 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

						<view class="bzcon">
							{{shremark}}
						</view>
					</view>
				</block>

				<block v-if="djdata.status==2">

					<view class="maxcon">
						<view class="vstit" style="font-weight:400;margin-bottom:25upx">审核备注 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

						<view class="bzcon">
							<textarea name="shremark" class="beizhu" v-model="shremark"></textarea>
						</view>
					</view>


					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<view class="bomsfleft">
								<button class="bomsubbtn" @click="jjtgsh" style="background:#ddd;">
									<text>驳回</text>
								</button>
							</view>
							<view class="bomsfright">
								<button class="bomsubbtn" form-type="submit">
									<text>审核通过</text>
								</button>
							</view>
							<view class="clear"></view>
						</view>
					</view>
				</block>

			</form>



		</view>

		<view style="height:120upx"></view>

		<tn-modal v-model="jjtgshow" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">驳回理由</view>
				<view class="bhsmcon">
					<textarea name="bhsm" class="beizhu" v-model="bhsm" placeholder="请输入驳回的理由"
						style="height:350upx"></textarea>
				</view>

				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" @click="jjsh">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>


		<block v-if="hkjhdata">
			<tn-popup v-model="hkjhshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
				<view class="popuptit" style="border-bottom:0">还款计划</view>

				<view class="hkjh-time-smtip">借款{{djdata.ywzqnum}}{{djdata.ywzqdwtxt}}，应还总额</view>
				<view class="hkjh-time-smjine">¥{{djdata.hkzjine}}</view>

				<scroll-view scroll-y="true" style="height: 700rpx;">

					<view class="khjhcon">
						<view class="hkjh-timeline">
							<view class="hkjh-timeline-content">
								<block v-for="(item,index) in hkjhdata" :key="index">
									<view class="item hkjh-timeline-item" :class="'hksta'+item.status">
										<em class="yd-timeline-icon"></em>
										<view class="hkjine">
											¥{{item.yhkzjine}}
											<block v-if="item.bqsyhkjine > 0">
												<text class="yhtip">已还 {{item.bqyhkjine}}</text>
											</block>
											<text class="yhqtxt" v-if="item.status==1">已还清</text>
											<text class="yyqtxt" v-if="item.status==3">已逾期</text>
										</view>
										<!-- <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view> -->
										<view class="qbcon">
											<view class="t">{{item.xhtit}}</view>
											<view class="r">{{item.rq}}</view>
										</view>
									</view>
								</block>
							</view>
						</view>
					</view>

				</scroll-view>
			</tn-popup>
		</block>

		<tn-popup v-model="czryeditshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">
				<view class="rowx">
					<view class="tit"><text class="redst"></text>{{addrytypetxt}}</view>
					<view class="inpcon">
						<view class="inp">
							{{thczygname}}
							<text style="color:#888;margin-left:10upx;display: none;" v-if="thczygid > 0">({{thczygid}})</text>
						</view>
					</view>
					<view class="clear"></view>
				</view>

				<block v-if="isofr==0">


					<view class="rowx">
						<view class="tit"><text class="redst"></text>可用出资</view>
						<view class="inpcon">
							<view class="inp" style="color:#1677FF">{{thygczkyjine}} </view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx">
						<view class="tit"><text class="redst"></text>出资金额</view>
						<view class="inpcon">
							<view class="inp"><input class="input" type="number" name="title" v-model="thczjine"
									placeholder="请输入出资金额" placeholder-class="placeholder" /></view>
						</view>
						<view class="clear"></view>
					</view>
				</block>

				<block v-if="czfrisfr==1">
					<view class="rowx">
						<view class="tit"><text class="redst"></text>分润金额</view>
						<view class="inpcon">
							<view class="inp">
								<input class="input" type="number" name="frjine" v-model="thfrjine" placeholder=""
									placeholder-class="placeholder" style="width:120upx;" />
							</view> 
						</view>
						<view class="clear"></view>
						<view class="rowxff">
							<view class="wz">比例</view>
							<view class="srk"><input class="input2" type="number" name="frbl" v-model="thfrbl"
									placeholder="请输入" placeholder-class="placeholder" @input="frjscl()" />
							</view>
							<view class="tb"  style="color:#1677FF;z-index:200;">%</view>
							<view class="clear"></view>
						</view>
					</view>
					<view class="smtxt">说明：分润比例只用做辅助计算,不保存!</view>
				</block>

				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="czrysettj">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>


		<tn-popup v-model="qtfymxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他收费明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtfymxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtfymxspname}}
				</view>
				<view class="qtfylist">

					<block v-if="qtfydata.length>0">

						<block v-for="(item,index) in qtfydata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="bjbtn" v-if="djdata.status==2">
											<text @click="qtfydel" :data-fyid="item.id">删除</text>
											<text @click="qtfyedit" :data-fyid="item.id" :data-spid="item.spid"
												style="color:#1677FF">编辑</text>
										</view>


										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他收费</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>

		<tn-popup v-model="qtcbmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他成本明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtcbmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtcbmxspname}}
				</view>
				<view class="qtcblist">

					<block v-if="qtcbdata.length>0">

						<block v-for="(item,index) in qtcbdata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="bjbtn" v-if="djdata.status==2">
											<text @click="qtcbdel" :data-fyid="item.id">删除</text>
											<text @click="qtcbedit" :data-fyid="item.id" :data-spid="item.spid"
												style="color:#1677FF">编辑</text>
										</view>


										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtcbvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他成本</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="gzselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">选择分润规则</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="lrgzlist">
					<block v-if="frgzdata">
						<block v-for="(item,index) in frgzdata" :key="index">
							<view class="item" @click="selthfrgz" :data-gzid="item.id" :data-feilv="item.feilv">
								<view class="tit">{{item.title}}</view>
							</view>

						</block>
					</block>
				</view>
			</scroll-view>
		</tn-popup>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				spzongjia: 0,
				clicksta: false,
				remark: '',
				statusname: '',
				vsfnr: 0,
				hkjhshow: false,
				hkjhtip: '',
				hkjhdata: '',
				jjtgshow: false,
				bhsm: '',
				thspid: 0,

				thczjine: 0,
				thfrbl: 0,
				czfrspid: 0,
				czfrisfr: 0,
				isofr: 0,
				thczygid: 0,
				thczygname: '',
				thygczkyjine: '',

				czryeditshow: false,

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid: 0,

				shremark: '',
				gzselshow: false,
				thfrjine: 0,
				frgzdata: '',
				addrytype: 0,
				addrytypetxt: '人员姓名',

				qtcbmxshow: false,
				qtcbmxspname: '',
				qtcbdata: '',
				thqtcbvid: 0,

				isdpczzh: 0, //店铺出资



			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			this.clicksta = false;
			this.thczygid=0;
			let thczygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			// let thygczkyjine = uni.getStorageSync('thygczkyjine');
			if (thczygid) {
				console.log(thczygid);
				this.thczygid = thczygid;
				this.loadygcz();
				// this.thczygname = thygname;
				// this.thygczkyjine = thygczkyjine;
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
				// uni.setStorageSync('thygczkyjine', '');
				// this.czryeditshow = true;
			}

			if (uni.getStorageSync('isdpczzh') == 1) {
				this.isdpczzh = 1;
				this.loadygcz();
				uni.setStorageSync('isdpczzh', '');
			}


			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}


			if (uni.getStorageSync('thupspotfyspid') && uni.getStorageSync('thupspotfyspid') > 0) {
			 	this.loadData();
				this.getqyfylist(uni.getStorageSync('thupspotfyspid'));
				uni.setStorageSync('thupspotfyspid', 0)
			}

			if (uni.getStorageSync('thupspotcbspid') && uni.getStorageSync('thupspotcbspid') > 0) {
				this.getqycblist(uni.getStorageSync('thupspotcbspid'));
				uni.setStorageSync('thupspotcbspid', 0)
			}


		},

		methods: {

			loadygcz() {
				let that = this;

			
				that.thczygname = 0;
				that.thygczkyjine = 0;
				that.thczjine = 0;

				let da = {
					djid: this.djid,
					spid: this.czfrspid,
					czryid: this.thczygid,
					isdpczzh: this.isdpczzh,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/jsygcz', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.thczygid = res.data.thczygid;
							that.thczygname = res.data.thygname;
							that.thygczkyjine = res.data.thygczkyjine;
							that.thczjine = res.data.thczjine;
							that.czryeditshow = true;
						
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},

			sellxgz() {
				this.gzselshow = true;
			},
			selthfrgz(e) {
				let feilv = e.currentTarget.dataset.feilv;
				this.thfrbl = feilv;
				this.gzselshow = false;
				this.frjscl();
			},
			frjscl() {
				let that = this;
				let da = {
					djid: this.djid,
					spid: that.czfrspid ? that.czfrspid : 0,
					frbl: this.thfrbl ? this.thfrbl : 0,
				}
			
				this.$req.post('/v1/danju/frjscl', da)
					.then(res => {
					
						console.log(res);
						if (res.errcode == 0) {
							that.thfrbl = res.data.frbl;
							that.thfrjine = res.data.frjine;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},

			addczry(e) {
				let isfr = this.czfrisfr;
				let spid = e.currentTarget.dataset.spid;
				let isofr = e.currentTarget.dataset.isofr;
				let addrytype = e.currentTarget.dataset.rytype;
				let addrytypetxt = e.currentTarget.dataset.rytypetxt;
				this.isdpczzh = 0;
				console.log(isofr);
			
				this.addrytype = addrytype;
				this.addrytypetxt = addrytypetxt;
				this.isofr = isofr;
				this.czfrspid = spid;
				this.thczjine = 0;
				this.thfrbl = 0;
				this.thfrjine = 0;
				let yytitle = "选择出资人";
				if (isfr == 1) {
					yytitle = "选择出资/分润人";
				}
				if (isofr == 1) {
					yytitle = "选择分润人";
				}
				uni.navigateTo({
					url: '/pages/selyg/index?stype=7&xtitle=' + yytitle
				})
			},

			ckhkjh() {
				this.hkjhshow = true;
			},
			toback() {
				uni.navigateBack();
			},
			setvzz() {
				if (this.vsfnr == 0) {
					this.vsfnr = 1;
				} else {
					this.vsfnr = 0;
				}
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					et: 2,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdsheditx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.djshop = res.data.djshop;
							that.statusname = res.data.statusname;
							that.remark = res.data.djdata.remark;
							that.shremark = res.data.djdata.shremark;
							that.spzongjia = res.data.spzongjia;
							that.hkjhdata = res.data.hkjhdata;
							that.hkjhtip = res.data.hkjhtip;
							that.frgzdata = res.data.frgzdata;
							if (res.data.djdata.ddtype == 3 || res.data.djdata.ddtype == 4) {
								that.czfrisfr = 1;
							}

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			previewImage: function (e) {
					  let that = this;
					  let src = e.currentTarget.dataset.src;
					  let picarr=e.currentTarget.dataset.picarr;
					  let imgarr=[];
					  picarr.forEach(function(item,index,arr){
						  imgarr[index] = that.staticsfile+item;
					  });
					  wx.previewImage({
						  current: src,
						  urls: imgarr
					  });
			},
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},
			setspsta(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let da = {
					'spid': spid
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/danju/rkdshsetspsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})

			},


			formSubmit(e) {
				let that = this;
				let pvalue = e.detail.value;
				uni.showModal({
					title: '审核确认',
					content: '确认通过审核吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'djid': that.djid,
								'shremark': that.shremark ? that.shremark : '',
								'sta': 1
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdshsta', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.setStorageSync('thuprkdshlist', 1);
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			jjtgsh() {
				this.jjtgshow = true;
			},

			jjsh(e) {
				let that = this;

				if (!that.bhsm) {
					uni.showToast({
						icon: 'none',
						title: '请输入驳回理由',
					});
					return false;
				}

				let da = {
					'djid': that.djid,
					'bhsm': that.bhsm ? that.bhsm : ''
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/danju/rkdshbh', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thuprkdshlist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										uni.navigateBack()
									}, 1000);
								}
							});
							that.jjtgshow = false;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})

			},
			spedit(e) {
				let spid = e.currentTarget.dataset.spid
				let djid = this.djid;
				uni.navigateTo({
					url: '../rukudan/spedit?djid=' + djid + '&spid=' + spid
				})
			},

			czryedit(e) {

				let that = this;
				let czfrspid = e.currentTarget.dataset.spid;
				let czryid = e.currentTarget.dataset.czryid;
				let czygid = e.currentTarget.dataset.czygid;
				let czygname = e.currentTarget.dataset.czygname;
				let czjine = e.currentTarget.dataset.czjine;
				let frbl = e.currentTarget.dataset.frbl;
				let frjine = e.currentTarget.dataset.frjine;
				let isofr = e.currentTarget.dataset.isofr;
				let addrytype = e.currentTarget.dataset.rytype;
				let addrytypetxt = e.currentTarget.dataset.rytypetxt;
				let isdpczzh = e.currentTarget.dataset.isdpczzh;
			
				// this.czryeditshow = true;
				this.thczjine = czjine;
				this.thfrbl = 0;
				this.addrytype = addrytype;
				this.addrytypetxt = addrytypetxt;
				this.isdpczzh = isdpczzh;
				this.isofr = isofr;
				this.thfrjine = frjine;
				this.czfrspid = czfrspid;
				this.thczygid = czygid;
				this.thczygname = czygname;
				this.loadygcz();

			},
			delczry(e) {
				let that = this;
				let czryid = e.currentTarget.dataset.czryid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'czryid': czryid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdshdelczry', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},

			czrysettj() {
				let that = this;
				let djid = that.djid;
				let thczygid = that.thczygid;
				let czfrspid = that.czfrspid;
				let thczjine = that.thczjine;
				let thfrbl = that.thfrbl;
				let thfrjine = that.thfrjine;
				let isofr = that.isofr;
				let addrytype = that.addrytype;
				let isdpczzh = that.isdpczzh;
				let da = {
					djid: djid,
					spid: czfrspid,
					czryid: thczygid,
					czjine: thczjine ? thczjine : 0,
					frbl: thfrbl ? thfrbl : 0,
					frjine: thfrjine ? thfrjine : 0,
					isofr: isofr ? isofr : 0,
					addrytype: addrytype ? addrytype : 0,
					isdpczzh: isdpczzh ? isdpczzh : 0,
				}
				
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdshsetczry', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {

							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.czryeditshow = false;
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},






			qtfyedit(e) {

				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if (spname) {
					this.qtfymxspname = spname;
				}
				let djid = this.djid;

				uni.navigateTo({
					url: '/pages/work/rukudan/qtfyedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtfydel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtfydel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqyfylist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			qtfymx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtfymxspname = spname;
				this.getqyfylist(spid);
			},
			qtfysetvxq(e) {
				let thqtfyvid = e.currentTarget.dataset.id;
				if (thqtfyvid == this.thqtfyvid) {
					this.thqtfyvid = 0;
				} else {
					this.thqtfyvid = thqtfyvid;
				}

			},
			getqyfylist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtfylist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtfydata = res.data.qtfydata;
							that.qtfymxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},

			gotorlsbewm() {
				uni.navigateTo({
					url: '/pages/work/rukudan/rlsbewm?djid=' + this.djid
				})
			},




			qtcbedit(e) {

				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if (spname) {
					this.qtcbmxspname = spname;
				}
				let djid = this.djid;

				uni.navigateTo({
					url: '/pages/work/rukudan/qtcbedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtcbdel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtcbdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqycblist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			qtcbmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtcbmxspname = spname;
				this.getqycblist(spid);
			},
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}

			},
			getqycblist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtcblist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtcbdata = res.data.qtcbdata;
							that.qtcbmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},










		}
	}
</script>

<style lang="scss">
	.sqbdcon {}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
		overflow: hidden;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inpcon .colorhs {
		color: #ff0000
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .vstit .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		position: relative;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
	}

	.spitem .spbjbtn {
		position: absolute;
		right: 20upx;
		top: 20upx;
		color: #1677FF
	}

	.spitem .spbjbtn .iconfont {
		margin-right: 10upx
	}

	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		padding-right: 100upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #F0F0F0;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .viewxqcon {
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		margin-bottom: 10upx;
		margin-top: 15upx;
		text-align: right;
		font-size: 24upx;
	}

	.spitem .czfrcon {
		border-top: 1px dashed #FFD427;
		margin-top: 25upx;
		padding: 25upx 0
	}

	.spitem .czfrcon .tit {
		float: left;
		font-size: 28upx;
		font-weight: 600
	}

	.spitem .czfrcon .tit .iconfont {
		margin-right: 10upx;
		color: #ff0000
	}

	.spitem .czfrcon .tadd {
		float: right;
	}

	.spitem .czfrcon .tadd .iconfont {
		margin-right: 10upx;
		color: #1677FF
	}






	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 0upx;
		position: relative;
	}

	.zjzkicon {
		position: absolute;
		right: 0;
		top: 0;
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}


	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}



	.cklist {}

	.cklist .item {
		padding: 28upx;
		position: relative;
		border-bottom: 1px solid #eee;
	}

	.cklist .item .xzbtn {
		position: absolute;
		right: 30upx;
		top: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		font-size: 24upx;
	}

	.cklist .item .tx {
		float: left
	}

	.cklist .item .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.cklist .item .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 10upx;
	}

	.cklist .item .name {
		font-size: 28rpx;
		height: 40upx;
		line-height: 40upx;
	}

	.cklist .item .dizhi {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.cklist .item .dizhi text {
		color: #F7B52C;
		font-size: 24rpx;
		margin-top: 10upx;
		margin-right: 5upx
	}

	.cklist .item:last-child {
		border-bottom: 0
	}


	.addotfybtn {
		color: #1677FF;
		border-radius: 0upx;
		font-size: 24upx;
		margin-left: 25upx
	}

	.czry4info {
		margin-top: 20upx
	}
</style>