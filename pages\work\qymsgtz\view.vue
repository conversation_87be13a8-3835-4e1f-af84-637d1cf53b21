<template>
  <view class="template-news tn-safe-area-inset-bottom">
	
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >通知详情</text>
					</view>
					<view slot="right" >
					</view>
	</tn-nav-bar>
	
	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
	<view class="msinf">
		  <view style="position: absolute;right:40upx;top:20upx" >状态：<text class="vstatus" :class="'ys'+viewdata.vstatus">{{viewdata.vstatusname}}</text></view>
		  <view class="rq">
			  <text>创建时间：{{viewdata.addtime || ''}}</text>  
		  </view>
		  <view class="rq" v-if="viewdata.mdname">
		  	  <text>通知门店：{{viewdata.mdname}}</text>  
		  </view>
		  <view class="rq">
			  <text>通知对象：{{viewdata.dxtypetxt}}</text>  
		  </view>
		 
		  <block v-if="viewdata.jsdxnamestr">
			  <view class="rq">
				<text>接收人员：{{viewdata.jsdxnamestr}}</text>  
			  </view>
		  </block>
	</view>	
    <view class="viewcon">
		  <view class="title">{{viewdata.title}}</view>
    	  <view class="nrcon"> 
    		   <mp-html :content="viewdata.content" />
    	  </view>
    </view>
    

    
    <view class='tn-tabbar-height'></view>
    
  </view>
</template>

<script>
  export default {
    data(){
      return {
		 staticsfile: this.$staticsfile,
		 html:'',
		 id:0,
		 jstype:0,
		 prne:'',
		 viewdata:[]
      }
    },
	onLoad(options) {
		let id=options.id;
		this.id=id;
		this.loadData();
	},
	onShow(){
	
	},
    methods: {
		
		loadData(){
			  let that=this;
			 
			  let id=that.id; 
			  let da={  
				id:id
			  }
			 uni.showLoading({title: '处理中...'})
			 this.$req.post('/v1/qymsgtz/msgview', da)
			 .then(res => {
				uni.hideLoading();
				console.log(res);
				if(res.errcode==0){
				   that.viewdata=res.data.viewdata;
				}else{
					 uni.navigateBack()
				}
			 })
		},	
		
		gotoback(){
			uni.navigateBack();
		},
		gotoodview(){
			let that=this;
			let skurl=that.viewdata.skurl;
			uni.navigateTo({
				url:skurl
			})
		}
    
    }
  }
</script>

<style lang="scss" >

   page{background:#fff}
   
   .viewcon{margin:30upx 40upx 50upx 40upx;}
   .viewcon .title{font-size: 32upx; margin-bottom: 14px;margin-top:40upx}
   .viewcon .rq{font-size:13px;color:#333;margin:10upx 0 }
   .viewcon .nrcon{line-height:25px;padding-bottom:40upx;margin-top:30upx}
   .msinf{padding:20upx 40upx;background:#efefef;font-size:24upx;line-height:25px;position: relative;} 

	.vstatus{}
	.vstatus.ys1{color:#4aac09} 
	.vstatus.ys2{color:#ff0000}
</style>
