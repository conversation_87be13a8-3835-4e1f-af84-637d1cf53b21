<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{fydata.type == 1 ? '费用申请' : ''}}{{fydata.type == 2 ? '员工奖励' : ''}}{{fydata.type == 3 ? '员工惩罚' : ''}}审核</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">

				<block v-if="ygdata">
					<view class="maxcon">
						<view class="ttinfo">
							<view class="inf1">
								<view class="tf1">
									<view class="tx">
										<image :src="staticsfile + ygdata.avatar"></image>
									</view>
									<view class="xx">
										<view class="name">{{ygdata.realname}}</view>
										<view class="tel"><text>员工号:{{ygdata.zzhuid}}</text>
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>
					</view>
				</block>

				<view class="maxcon">

					<view class="vstit">基本信息</view>
					<view class="rowx">
						<view class="tit">所属门店：</view>
						<view class="inpcon">
							<view class="inp">{{fydata.mdname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">{{fydata.type == 1 ? '申请' : ''}}{{fydata.type == 2 ? '奖励' : ''}}{{fydata.type == 3 ? '惩罚' : ''}}金额：</view>
						<view class="inpcon">
							<view class="inp " :class="fydata.type == 3 ? 'jine2' : 'jine'">¥ {{fydata.jine}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">{{fydata.type == 1 ? '申请' : ''}}{{fydata.type == 2 ? '奖励' : ''}}{{fydata.type == 3 ? '惩罚' : ''}}时间：</view>
						<view class="inpcon">
							<view class="inp">{{fydata.addtime}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="bzcon">{{fydata.remark}}</view>

					<block v-if="fydata.picturescarr">
						<view class="pjimgcon">
							<block v-for="(itemx,indexx) in fydata.picturescarr" :key="indexx">
								<view class='item'>
									<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
										:data-picturescarr="fydata.picturescarr" @tap='previewImagex'>
									</image>
								</view>
							</block>
							<view class="clear"></view>
						</view>
					</block>

				</view>


				<block v-if="fydata.txstatus!=2">
					<view class="maxcon">
						<view class="vstit">审核信息</view>
						<view class="rowx">
							<view class="tit">审核人员：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核时间：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.shtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核状态：</view>
							<view class="inpcon">
								<view class="inp">
										<view class="stats " :class="'stacolor'+fydata.txstatus">{{fydata.statusname}}</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="bzcon">{{fydata.bhsm}}</view>
					</view>
				</block>


			</view>



			<block v-if="fydata.txstatus==2">
				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="jjtgsh" style="background:#ddd;">
								<text>拒绝</text>
							</button>
						</view>
						<view class="bomsfright">
							<button class="bomsubbtn" @click="tgsh">
								<text>审核通过</text>
							</button>
						</view>
						<view class="clear"></view>
					</view>
				</view>
			</block>



		</view>

		<view style="height:120upx"></view>


		<tn-modal v-model="jjtgshow" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">拒绝理由</view>
				<view class="bhsmcon">
					<textarea name="bhsm" class="beizhu" v-model="bhsm" placeholder="请输入拒绝输入的理由"
						style="height:350upx"></textarea>
				</view>

				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" @click="jjsh">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>



	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				djid: '',
				fydata: '',
				ygdata: '',
				bhsm: '',
				jjtgshow: false,
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			if (djid) {
				this.loadData();
			}
		},
		onShow() {

		},

		methods: {

			jjtgsh() {
				this.jjtgshow = true;
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/yggl/ygfyview', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.fydata = res.data.fydata;
							that.bhsm = res.data.fydata.bhsm;
							that.ygdata = res.data.ygdata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},


			tgsh(e) {
				let that = this;
				uni.showModal({
					title: '审核确认',
					content: '申请的费用审核通过后将直接增加到员工余额中，用户可以申请提现取出！确认通过审核吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'djid': that.djid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/yggl/ygfytgshsave', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.setStorageSync('thuplist',1);
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},


			jjsh(e) {
				let that = this;
				
				if(!that.bhsm){
				  uni.showToast({
					icon: 'none',
					title: '请输入拒绝理由',
				  });
				  return false;
				}
				
				let da = {
					'djid': that.djid,
					'bhsm': that.bhsm ? that.bhsm: ''
				}
				uni.showLoading({title: ''})
				that.$req.post('/v1/yggl/ygfyjjsave', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thuplist',1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
							that.jjtgshow = false;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
				
			},


		}
	}
</script>

<style lang="scss">
	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}
	.bzcon {
		margin-top:20upx;
	}
	.bhsmcon {
		margin: 10upx 0 30upx 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.pjimgcon {
		margin-top: 20upx;
	}

	.pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}
	
	.rowx .jine {
		color:#ff0000;
	}
	
	.rowx .jine2 {
		color:#0DD400;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
	
	
	.stats.stacolor1 {
		color: #0DD400;
	}
	
	.stats.stacolor2 {
		color: #FA6400;
	}
	
	.stats.stacolor3 {
		color: #3366ff;
	}
	
</style>