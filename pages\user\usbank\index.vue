<template>
	<view class="content b-t">
		<tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
			  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
			  		  <text  >我的银行卡</text>
			  		</view>
			  </tn-nav-bar>
		
		 
		
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		<block v-if="banklist.length>0">
		    <block v-for="(item, index) in banklist" :key="index">
				<view class="list b-b"   @click="dzcz" :data-bkid="item.id" >
					<view class="wrapper">
						<view class="address-box">
							<text class="address">{{item.b_name}}</text>
						</view>
						<view class="u-box"> 
							<text class="name">{{item.b_number}}</text>
							<text class="mobile">{{item.b_huming}}</text>
						</view>
					</view>
					<text class="iconfont icon-caozuo"  ></text>
				</view>
			</block>
		</block>
		<block v-else >
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-wushuju"></text></view>
				<view class="tit">暂无银行卡</view>
			</view>
		</block>
	
	
		
			<view class="tn-flex tn-footerfixed" style="z-index:100;">
			  <view class="tn-flex-1 justify-content-item tn-text-center">
				<button class="bomsubbtn"  @click="editka()">
				  <text>新增银行卡</text>
				</button>
			  </view>
			</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				source: 0,
				isxgshdz: 0,
				bkxgshdztip: '',
				banklist: []
			}
		},
		onLoad(option) {
			
			this.source = option.source;
		},
		onShow() {
			this.loadData();
		},
		methods: {

			async loadData() {
				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/muser/usbanklist', da)
					.then(res => {
						uni.hideNavigationBarLoading(); 
					
						if (res.errcode == 0) {
							that.banklist = res.data.banklist;
						} else {

						}
					})
			},
			dzcz: function(e) {
				let that = this;
				let bkid = e.currentTarget.dataset.bkid;
			
				uni.showActionSheet({
					itemList: ['编辑', '删除'],
					success(res) {
						if (res.tapIndex === 0) {
							uni.navigateTo({
								url: './edit?bkid=' + bkid
							})
						} else if (res.tapIndex === 1) {
							uni.showModal({
								title: '删除确认',
								content: '删除后不可恢复！确认删除吗？',
								success: function(e) {
									//点击确定
									if (e.confirm) {

										let da = {
											'bkid': bkid,
										}
										uni.showLoading({
											title: ''
										})
										that.$req.post('/v1/muser/delusbank', da)
											.then(res => {
												uni.hideLoading();
												if (res.errcode == 0) {
													uni.showToast({ 
														icon: 'none',
														title: res.msg,
														success() {
															setTimeout(function() {
																	that.loadData()
															}, 1000);
														}
													});
												} else {
													uni.showModal({
														content: res.msg,
														showCancel: false,
													})
												}
											})



									}




								}
							})
						}
					}
				})

			},

			//选择地址
			checkAddress(item) {
				if (this.source == 1) {
				
					//this.$api.prePage()获取上一页实例，在App.vue定义
					this.$api.prePage().addressData = item;
					uni.navigateBack()
				}
			},
			editka() {
				uni.navigateTo({
					url: '/pages/user/usbank/edit'
				})
				
			}
		}
	}
</script>

<style lang='scss'>
	page {
		background: #fff;
		padding-bottom: 120upx;
	}

	.bkscdtip {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
	}

	.bkscdtip .con {
		padding: 30upx;
		background: #ffe1e4;
		text-align: center;
		color: #fa436a
	}

	.content {
		position: relative;
	}

	.list {
		display: flex;
		align-items: center;
		padding: 20upx 30upx;
		background: #fff;
		position: relative;
		border-bottom:1px solid #eee;
	}

	.wrapper {
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.address-box {
		display: flex;
		align-items: center;

		.tag {
			font-size: 24upx;
			color: $base-color;
			margin-right: 10upx;
			background: #fffafb;
			border: 1px solid #ffb4c7;
			border-radius: 4upx;
			padding: 4upx 10upx;
			line-height: 1;
		}

		.address {
			font-size: 30upx;
			color: $font-color-dark;
		}
	}

	.u-box {
		font-size: 28upx;
		color: $font-color-light;
		margin-top: 16upx;

		.name {
			margin-right: 30upx;
		}
	}

	.icon-bianji {
		display: flex;
		align-items: center;
		height: 80upx;
		font-size: 40upx;
		color: $font-color-light;
		padding-left: 30upx;
	}
</style>
