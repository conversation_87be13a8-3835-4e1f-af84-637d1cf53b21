<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="3000">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >分类属性设置</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
			<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		


            <view class="pidcon">
				<view class="ss1">{{sortdata.sortname}}</view>
				<view class="ss2" @click="addsx" :data-sxid="0" ><text class="iconfont icon-tianjia2" style="margin-right:10upx"></text> 添加属性</view>
				<view class="clear"></view>
			</view>

		
			<block v-if="sxdata.length>0">
			
			    
					<view class="sxlist">
							<block v-for="(item,index) in sxdata" :key="index">
								 <view class="sxitem">
									<view class="dftit" >
										<view class="tit" @click="vmxx" :data-sxid="item.id" :data-sxtype="item.sxtype">{{item.sxtypetxt}} : <text>{{item.title}}</text></view>
										
										<block v-if="item.sxtype!=3">
										   <view class="more" @click="vmxx" :data-sxid="item.id" :data-sxtype="item.sxtype"> <text class="iconfont icon-jiantou-copy-copy"></text></view>
										</block>
										
										<view class="editaa" @click="editsxaa" :data-sxid="item.id" :data-thtitle="item.title" :data-thpx="item.px" :data-thsxtype="item.sxtype" :data-thsxtypetxt="item.sxtypetxt" v-if="item.sjid>0" > <text class="iconfont icon-icon-edit"></text></view>
										<view class="delaa" @click="delsxaa" :data-sxid="item.id" v-if="item.sjid>0"> <text class="iconfont icon-shanchu3"></text></view>
									
									</view>
									<view class="xxcon" v-if="item.sxtype!=3" :class="item.id==vthsxid ? 'hoverss' : '' " >
										<view class="xlis">
											<block v-for="(itemx,index) in item.xxdata" :key="index">
												 <view class="xxitem">
													 {{itemx.title}}
													 
													 <view class="xxset">
														 <text v-if="itemx.sjid>0" class="iconfont icon-cuohao02" @click="delxxaa" :data-xxid="itemx.id"  ></text>
														 <text v-if="itemx.sjid>0" class="iconfont icon-icon-edit" @click="editvalue"  :data-thxxsxid="itemx.sxid" :data-thxxid="itemx.id" :data-thxxtitle="itemx.title" :data-thxxpx="itemx.px"  ></text>
													 </view>
													 									
												 </view>
											</block>	
										</view>
										<view class="addvaluebtn" @click="addvalue" :data-thxxsxid="item.id" :data-thxxid="0" data-thxxtitle=""  ><text class="iconfont icon-tianjia2"></text> 添加选项</view>
									</view>
								</view>											
								
							</block>
					</view>
					
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关属性</view>
				</view>
			</block>
		    
	
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content">
	
			<view class="sxbdcon">
				
			        <view class="sxbdtit">{{thtitle ? '编辑属性' : '添加属性'}}</view>
				
					<view style="margin:20upx 0" >
					   <input type="text" :placeholder="'请输入属性名'" v-model="thtitle" name="title" maxlength="50" class="sxinput"  ></input>
					</view>
					
					<view style="margin:20upx 0" >
					   <input type="text" :placeholder="'请输入排序值'" v-model="thpx" name="px" maxlength="50" class="sxinput"  ></input>
					</view>
					
					<view style="text-align: center;margin:20upx 0;padding:10upx" >
						<tn-radio-group v-model="sxtypetxt" @change="setsxtype" >
							 <tn-radio v-for="(item, index) in radioList" :key="index" :name="item.name" :disabled="item.disabled" activeColor="#FFD427"  >
							   {{ item.name }}
							 </tn-radio>
						</tn-radio-group>
					</view>
					
					
					<view class="tn-flex-1 justify-content-item  tn-text-center">
					  <tn-button backgroundColor="#FFD427" padding="50rpx 0" width="100%"   @click="submitfl"  >
						<text >确认提交</text>
					  </tn-button>
					</view>
			</view>
			
		  </view>
		</tn-modal>
		
		
		<tn-modal v-model="show5" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content">
			<view class="sxbdcon">
			        <view class="sxbdtit">{{thxxtitle ? '编辑选项' : '添加选项'}}</view>
				
					<view style="margin:20upx 0" >
					   <input type="text" :placeholder="'请输入选项名称'" v-model="thxxtitle" name="title" maxlength="50" class="sxinput"  ></input>
					</view>
					
					<view style="margin:20upx 0" >
					   <input type="text" :placeholder="'请输入排序值'" v-model="thxxpx" name="px" maxlength="50" class="sxinput"  ></input>
					</view>
					
					<view class="tn-flex-1 justify-content-item  tn-text-center">
					  <tn-button backgroundColor="#FFD427" padding="50rpx 0" width="100%"   @click="submitflxx"  >
						<text >确认提交</text>
					  </tn-button>
					</view>
			</view>
		  </view>
		</tn-modal>
		
		

	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sortid:0,
				ScrollTop:0, 
				SrollHeight:200,
				show3:false,
				show5:false,
				sortdata:'',
				sxdata:'',
				sxid:0,
				thtitle:'',
				thpx:100,
				
				thsxid:0,
				vthsxid:0,
				
				thxxsxid:0,
				thxxid:0,
				thxxtitle:'',
				thxxpx:100,
	
				sxtype:1,
				sxtypetxt:'单选型',
				radioList: [{
				            name: '单选型',
				            disabled: false
				          },
				          {
				            name: '多选型',
				            disabled: false
				          },
				          {
				            name: '输入型',
				            disabled: false
				          }
				        ],
				
			}
		},
		onLoad(options) {
			let that=this;
		    let sortid=options.sortid ? options.sortid : 0
			this.sortid=sortid;
		},
		onShow(){
				let that=this;
				that.loadData(); 
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			
		
				loadData(){
					  let that=this;
					  let da={
						  sortid:that.sortid,
					  }
					 uni.showLoading({title: ''})
					 this.$req.post('/v1/sjgl/spfenleisx', da)
							 .then(res => {
								uni.hideLoading();
								 console.log(111,res);
								if(res.errcode==0){
									that.sortdata=res.data.sortdata;
									that.sxdata=res.data.sxdata;
								}else{
									uni.showModal({
										content: res.msg,
										showCancel: false,
										success() {
											uni.navigateBack();
										}
									})
								}
					})
		        },
				
				
				addsx(e){
					let that=this;
					this.sxtype=1;
					this.sxtypetxt='单选型';
					
					that.thsxid=0;
					that.thtitle=''; 
					that.thsxtype=this.sxtype;
					that.thpx= 100 ;
					
					this.show3=true;
				},
				editsxaa(e){
					let that=this;
					
					let thsxid= e.currentTarget.dataset.sxid;
					let thtitle= e.currentTarget.dataset.thtitle;
					let thpx= e.currentTarget.dataset.thpx;
					let thsxtype= e.currentTarget.dataset.thsxtype;
					let thsxtypetxt= e.currentTarget.dataset.thsxtypetxt;
					
					that.thsxid=thsxid;
					that.thtitle=thtitle;
					that.thsxtype=thsxtype;
					that.sxtypetxt=thsxtypetxt;
					that.thpx=thpx ? thpx : 100 ;
					
					this.show3=true;
				},
				
				addvalue(e){
					this.thxxsxid=e.currentTarget.dataset.thxxsxid ;
					this.show5=true;
				},
				
				editvalue(e){
					let that=this;
					
					let thxxsxid=e.currentTarget.dataset.thxxsxid ;
					let thxxid=e.currentTarget.dataset.thxxid ;
					let thxxtitle= e.currentTarget.dataset.thxxtitle;
					let thxxpx= e.currentTarget.dataset.thxxpx;
					
					that.thxxsxid=thxxsxid;
					that.thxxid=thxxid;
					that.thxxtitle=thxxtitle;
					that.thxxpx=thxxpx ? thxxpx : 100 ;
					
					this.show5=true;
				},
				
			
		
		        setsxtype(value){
					if(value=="单选型"){this.sxtype=1;this.sxtypetxt="单选型";}
					if(value=="多选型"){this.sxtype=2;this.sxtypetxt="多选型"}
					if(value=="输入型"){this.sxtype=3;this.sxtypetxt="输入型"}
				},
				
			
				vmxx(e){
					let thsxid=e.currentTarget.dataset.sxid;
					let sxtype=e.currentTarget.dataset.sxtype;
					
					if(sxtype==3){
						this.vthsxid=0;
						return false;
					}
					
					if(this.vthsxid==thsxid){
						this.vthsxid=0;
					}else{
						this.vthsxid=thsxid;	
					}
				},
		
					submitfl: function(e) {
										let that=this;
									
											if(!that.thtitle){
												uni.showToast({
													icon: 'none',
													title: '请输入属性名称',
												});
												return false;
											}
											if(!that.thpx){
												uni.showToast({
													icon: 'none',
													title: '请输入属性排序值',
												});
												return false;
											}
											
											  uni.showLoading({title: '处理中...'})
											  let da={
												'sxid': that.thsxid ? that.thsxid : 0,
												'sortid': that.sortid ? that.sortid : 0,
												'title': that.thtitle ? that.thtitle : '',
												'px': that.thpx ? that.thpx : 100,
												'sxtype': that.sxtype,
												'sxtypetxt': that.sxtypetxt,
											  }
											  
											  this.$req.post('/v1/sjgl/spfenleisxsave', da)
													  .then(res => {
														uni.hideLoading();
														console.log(res);
														if(res.errcode==0){
															uni.showToast({
																icon: 'none',
																title: res.msg,
																success() {
																	
																	that.show3=false
																	that.thtitle='';
																	that.thsxid=0;
																	that.sxtype=1;
																	that.thpx= 100 ;
																	
																	setTimeout(function(){
																		that.loadData();
																	},500)
																	
																}
															});
														}else{
															uni.showModal({
																content: res.msg,
																showCancel: false
															})
														}
														
													  })
													  .catch(err => {
													
													  })
											  
				    },
							
					delsxaa(e){
								   let that=this;
								   let sxid=e.currentTarget.dataset.sxid;
								   uni.showModal({
								   	title: '删除确认',
								   	content: '删除后不可恢复！确认删除吗？',
								   	success: function(e) {
								   		//点击确定
								   		if (e.confirm) {
								   
								   			let da = {
								   				sxid: sxid
								   			}
								   			uni.showLoading({title: ''})
								   			that.$req.post('/v1/sjgl/spfenleisxdel', da)
								   				.then(res => {
								   					uni.hideLoading();
								   					if (res.errcode == 0) {
								   						uni.showToast({
								   							icon: 'none',
								   							title: res.msg,
								   							success() {
								   								setTimeout(function() {
								   								   that.loadData();
								   								}, 1000);
								   							}
								   						});
								   					} else {
								   						uni.showModal({
								   							content: res.msg,
								   							showCancel: false,
								   						})
								   					}
								   				})
								   
								   
								   
								   		}
								   
								   	}
								   })
					},
					
					delxxaa(e){
								   let that=this;
								   let xxid=e.currentTarget.dataset.xxid;
								   uni.showModal({
								   	title: '删除确认',
								   	content: '删除后不可恢复！确认删除吗？',
								   	success: function(e) {
								   		//点击确定
								   		if (e.confirm) {
								   
								   			let da = {
								   				xxid: xxid
								   			}
								   			uni.showLoading({title: ''})
								   			that.$req.post('/v1/sjgl/spfenleixxdel', da)
								   				.then(res => {
								   					uni.hideLoading();
								   					if (res.errcode == 0) {
								   						uni.showToast({
								   							icon: 'none',
								   							title: res.msg,
								   							success() {
								   								setTimeout(function() {
								   								   that.loadData();
								   								}, 1000);
								   							}
								   						});
								   					} else {
								   						uni.showModal({
								   							content: res.msg,
								   							showCancel: false,
								   						})
								   					}
								   				})
								   
								   
								   
								   		}
								   
								   	}
								   })
					},
					
					
					
					submitflxx: function(e) {
							let that=this;
						
								if(!that.thxxtitle){
									uni.showToast({
										icon: 'none',
										title: '请输入选项名称',
									});
									return false;
								}
								if(!that.thxxpx){
									uni.showToast({
										icon: 'none',
										title: '请输入选项排序值',
									});
									return false;
								}
								
								  uni.showLoading({title: '处理中...'})
								  let da={
									'xxid': that.thxxid ? that.thxxid : 0,
									'sxid': that.thxxsxid ? that.thxxsxid : 0,
									'title': that.thxxtitle ? that.thxxtitle : '',
									'px': that.thxxpx ? that.thxxpx : 100,
								  }
								 
								  this.$req.post('/v1/sjgl/spfenleisxxxsave', da)
										  .then(res => {
											uni.hideLoading();
											console.log(res);
											if(res.errcode==0){
												uni.showToast({
													icon: 'none',
													title: res.msg,
													success() {
														
														that.show5=false
														that.thxxid=0;
														that.thxxsxid=0;
														that.thxxtitle='';
														that.thxxpx= 100 ;
														
														setTimeout(function(){
															that.loadData();
														},500)
														
													}
												});
											}else{
												uni.showModal({
													content: res.msg,
													showCancel: false
												})
											}
											
										  })
										  .catch(err => {
										
										  })
											  
					},
					
					
					
					
					
					
					
					
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}

.pidcon{background: #fff;padding:30upx 20upx;border-radius:20upx;margin:32upx 32upx 0 32upx;}
.pidcon .ss1{float:left;font-size:32upx;font-weight:700;}
.pidcon .ss2{float:right}

.sxbdcon{padding:0upx;}
.sxbdcon .sxbdtit{text-align: center;font-size:32upx;font-weight:600;margin:30upx 0}
.sxbdcon .sxinput{height: 88rpx;line-height:88upx;border-radius: 20rpx;border: 2rpx solid #F0F0F0;text-align: center;}

.sxlist{margin:32upx}
.sxitem{margin-bottom:20upx}
.sxitem .dftit{height: 96upx;line-height:96upx;background: #FFD427;border-radius: 20upx;padding:0 28upx;position: relative;z-index:100;}
.sxitem .dftit .tit{float:left;font-size: 28rpx;font-weight:600}
.sxitem .dftit .tit text{}
.sxitem .dftit .more{float:right;}
.sxitem .dftit .more .iconfont{font-size: 26rpx;}
.sxitem .dftit .editaa{position: absolute;right:90upx;top:0}
.sxitem .dftit .editaa .iconfont{font-size:28upx;margin-right:10upx}

.sxitem .dftit .delaa{font-size:26upx;position: absolute;right:180upx;top:0}
.sxitem .dftit .delaa .iconfont{font-size:28upx;margin-right:10upx}

 
.sxitem .xxcon{background:#fff;padding-top:20upx;margin-top:-20upx;border-radius: 0 0 20upx 20upx;display: none;}
.sxitem .xxcon .sjsm{font-size:24upx;padding:10upx 20upx 30upx 25upx ; color: #7F7F7F;}
.sxitem .xxcon .sjsm text{color: #1677FF;}
.sxitem .xxcon.hoverss{display: block;}

.xlis{padding:0 30upx}
.xlis .xxitem{height:100upx;line-height:100upx;overflow:hidden;position: relative;border-bottom:1px solid #efefef;}
.xlis .xxitem .xxset{position:absolute;right:0;top:0}
.xlis .xxitem .xxset text{margin-left:40upx;font-size:28upx;}

.addvaluebtn{text-align: center;height:110upx;line-height:110upx;}


</style>
