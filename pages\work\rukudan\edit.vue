<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">

					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx" @click="selkh">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>客户名称</view>
							<view class="inpcon">
								<view class="inp">
									{{khname ? khname : '请选择客户'}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>

					<block v-if="thkhdata">

						<view class="maxcon00">

							<view class="khinfo" @click="selkh">
								<view class="inf1">
									<view class="djcon">{{thkhdata.dengjitxt}}</view>
									<view class="tx">
										<view class="avatar">
											<image :src="staticsfile + thkhdata.avatar"></image>
										</view>
										<view class="sexicon" v-if="thkhdata.sex > 0">
											<image :src="'/static/images/sexicon'+thkhdata.sex+'.png'"></image>
										</view>
									</view>
									<view class="xx">
										<view class="tit">
											<text class="name">{{thkhdata.realname}}</text>
											<text class="uid">ID:{{thkhdata.id}}</text>
										</view>
										<view class="ssyg">电话号码：{{thkhdata.lxtel}}</view>
										<view class="ssyg" v-if="thkhdata.sfzhao">身份证号：{{thkhdata.sfzhao}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>

						</view>

					</block>


					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx" @click="selddtype">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp">
									{{thddtype ? thddtypetxt : '请选择业务类型'}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>



					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
							<!-- <view class="zjzkicon" @click="setvzz" style="display: none;">展开 <text class="iconfont icon-jiantou2"></text></view> -->




							<view class="uprzimgcon">
								<view class="zzx sf1">
									<view class="con">
										<view class="vt1" @click="uprzimg1">
											<block v-if="rzimg1">
												<image :src='staticsfile + rzimg1'></image>
											</block>
											<block v-else>
												<image src="/static/images/rzupimg1.png"></image>
											</block>
										</view>
										<view class="vt2">{{!thkhdata.rzimg1 ? '上传' : ''}}身份证头像面</view>
										<view class="clear"></view>
									</view>
								</view>
								<view class="zzx sf2">
									<view class="con">
										<view class="sf2" @click="uprzimg2">
											<block v-if="rzimg2">
												<image :src='staticsfile + rzimg2'></image>
											</block>
											<block v-else>
												<image src="/static/images/rzupimg2.png"></image>
											</block>
										</view>
										<view class="vt2">{{!thkhdata.rzimg2 ? '上传' : ''}}身份证国徽面</view>
									</view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx" style="margin-top:20upx;" :style="thkhdata.sfzhao ? 'display: none' : ''" >
								<view class="tit"><text class="redst"></text>身份证号</view>
								<view class="inpcon">
									<view class="inp"><input class="input" type="text" name="sfzhao" v-model="sfzhao"
											placeholder="请输入身份证号" placeholder-class="placeholder" maxlength="18"
											style="border-bottom:1px solid #eee" /></view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx" style="margin-top:15upx;position: relative;" v-if="sysrlsbsta==1">
								<view class="rlsbsmtip" @click="gotosqsm">
									<text class="iconfont icon-tishi2"></text>
									使用说明
								</view>
								<view class="tit"><text class="redst"></text>人脸识别</view>
								<view class="inpcon">
									<view class="inp">

										<radio-group @change="radioGroupChange">
											<label>
												<radio :value="1" :checked="isrlsb == 1" color="#FFD427" /> 需要
											</label>
											<label style="margin-left:10px">
												<radio :value="2" :checked="isrlsb == 2" color="#FFD427" /> 不需要
											</label>
										</radio-group>

									</view>
								</view>
								<view class="clear"></view>
							</view>



						</view>
					</view>


					<view class="maxcon">

						<view class="vstit" style="margin-bottom:0">业务人员</view>
						<view class="more vls" @click="selyg">新增 <text class="iconfont icon-tianjia2"></text></view>

                        <!-- 如果该客户没有负责人 -->
						<block v-if="thkhnofzr==1">
							<view class="nofrztipcon">
								<text class="iconfont icon-tishi5"></text> {{nofrztiptxt}}
							</view>
						</block>
						
							<block v-if="isdpywzh==1">
								<view class="ywrylist">
									<view class="xxitem">
										<text class="iconfont icon-renyuan" style="margin-right:10upx"></text> {{dpywtit}} 
										<view class="xxset" >
											<text class="iconfont icon-guanbi4" @click="delywry"
												:data-ygid="0"></text>
										</view>
									</view>
								</view>
							</block>
						
							<block v-if="ywrydata.length > 0">
								<view class="ywrylist">
									<block v-for="(itemx,index) in ywrydata" :key="index">
										<view class="xxitem">
											<text class="iconfont icon-renyuan" style="margin-right:10upx"></text> {{itemx.ygname}} 
											<!-- <text style="color:#888;margin-left:10px">({{itemx.ygid}})</text> -->
											<view class="xxset" v-if="itemx.isbd!=1">
												<text class="iconfont icon-guanbi4" @click="delywry"
													:data-ygid="itemx.ygid"></text>
											</view>
										</view>
									</block>
								</view>
							</block>
				


					</view>

					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>录单人员</view>
							<view class="inpcon">
								<view class="inp">
									{{opname}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>



				</view>

				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" form-type="submit">
							<text>下一步</text>
						</button>
					</view>
				</view>
			</form>








		</view>

		<view style="height:120upx"></view>





	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				html: '',
				status: 0,
				lng: '',
				lat: '',
				img_url: [],
				img_url_ok: [],
				dwlogo: '',
				formats: {},
				opname: '',
				xtitle: '',
				thygid: '',
				thygname: '',
				thkhdata: '',

				thddtype: 0,
				thddtypetxt: '',
				khid: '',
				khname: '',
				rzimg1: '',
				rzimg2: '',

				isrlsb: "2",
				vzz: 0,

				ywrydata: [],

				clicksta: false,
				sknexturl: '',

				sfzhao: '',
				sysrlsbsta: 0,
				
				thkhnofzr: 0,
				nofrztiptxt:'',
				isdpywzh:0,
				dpywtit:'',

			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			if (djid > 0) {
				this.xtitle = '编辑入库开单';
			} else {
				this.xtitle = '新增入库开单';
			}
			this.loadData();
		},

		onShow() {
			let thkhnofzr=0;
			let thygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			let thygisbdywy = uni.getStorageSync('thygisbdywy');
			
			this.clicksta = false;
				
			if (uni.getStorageSync('isdpywzh') == 1) {
			
				this.isdpywzh = 1;
				// this.ywrydata = [];
				uni.setStorageSync('isdpywzh', '');
			}

 
			let thkhdata = uni.getStorageSync('thkhdata');
		
			if (thkhdata) {
				this.ywrydata = [];
				this.thkhdata = thkhdata;
				this.khid = thkhdata.id;
				this.khname = thkhdata.realname;
				this.sfzhao = thkhdata.orsfzhao;
				this.rzimg1 = thkhdata.rzimg1;
				this.rzimg2 = thkhdata.rzimg2;
				
				if(thygid){
					this.thkhnofzr=0;
				}else{
					this.thkhnofzr=1;
				}
				
				uni.setStorageSync('thkhdata', '');
			}

			if (thygid) {
				let ywrydata = this.ywrydata;
				let isywrybh = 0;

				if (ywrydata.length > 0) {
					ywrydata.forEach((item, index) => {
						if (item.ygid == thygid) {
							isywrybh = 1;
						}
					})
				}
				if (isywrybh == 0) {
					ywrydata.push({
						ygid: thygid,
						ygname: thygname,
						isbd: thygisbdywy
					});
				}
				// this.isdpywzh=0;
				console.log(ywrydata);
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
				uni.setStorageSync('thygisbdywy', '')
			}else{
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
				uni.setStorageSync('thygisbdywy', '')
			}


		},

		methods: {
			selyg() {
				let thkhnofzr=this.thkhnofzr;
				if(!this.khid){
					uni.showToast({
						icon: 'none',
						title: '请先选择客户！'
					});
					return false;
				}
				
				uni.navigateTo({
					url: '/pages/selyg/index?stype=4&isdx=1&thkhnofzr='+thkhnofzr
				})
			},
			selkh() {
				if (this.djid > 0) {
					uni.showToast({
						icon: 'none',
						title: '客户不可更换'
					});
					return false;
				}

				uni.navigateTo({
					url: '/pages/selkh/index?stype=3'
				})
			},
			gotosqsm() {
				uni.navigateTo({
					url: '/pages/danye/index?id=8'
				})
			},

			//  setvzz(){
			// if(this.vzz==1){
			// 	this.vzz=0;
			// }else{
			// 	this.vzz=1;
			// }
			//  },

			delywry(e) {
				let ygid = e.currentTarget.dataset.ygid;
				let ywrydata = this.ywrydata;
				if(!ygid || ygid==0){
					this.isdpywzh=0;
					return false;
				}
				ywrydata.forEach((item, index) => {
					if (item.ygid == ygid) {
						ywrydata.splice(index, 1);
					}
				})
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdedit', da)
					.then(res => {
						uni.hideLoading();
					
						if (res.errcode == 0) {
							that.opname = res.data.opname;
							that.nofrztiptxt = res.data.nofrztiptxt;
							that.thkhnofzr = res.data.thkhnofzr;
							that.dpywtit = res.data.dpywtit;
							let djdata = res.data.djdata;
							that.djdata = djdata;
							if (res.data.djdata) {
								that.ywrydata = res.data.ywrydata;
								
								that.thkhdata = res.data.thkhdata;
								that.sysrlsbsta = res.data.sysrlsbsta;
								that.sfzhao = djdata.khsfzhao ? djdata.khsfzhao : '';
								that.rzimg1 = djdata.rzimg1 ? djdata.rzimg1 : '';
								that.rzimg2 = djdata.rzimg2 ? djdata.rzimg2 : '';
								that.khid = djdata.khid ? djdata.khid : '';
								that.khname = djdata.khname ? djdata.khname : '';
								that.isrlsb = String(djdata.isrlsb);
								that.thddtype = djdata.ddtype;
								that.thddtypetxt = djdata.ddtypetxt;
								 
								
								if(res.data.djdata.isdpyw==1){
									that.isdpywzh=1;
								}
							


							}
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			selddtype: function() {
				let that = this;

				if (this.djid > 0) {
					uni.showToast({
						icon: 'none',
						title: '业务类型不可修改'
					});
					return false;
				}

				uni.showActionSheet({
					itemList: ['质押暂存', '实物回收', '寄卖服务', '信用预付'],
					success: function(res) {
						var index = res.tapIndex;
						if (index == 0) {
							that.thddtype = 3;
							that.thddtypetxt = '质押暂存';
						}
						if (index == 1) {
							that.thddtype = 2;
							that.thddtypetxt = '实物回收';
						}
						if (index == 2) {
							that.thddtype = 1;
							that.thddtypetxt = '寄卖服务';
						}
						if (index == 3) {
							that.thddtype = 4;
							that.thddtypetxt = '信用预付';
						}

					},
				});
			},

			radioGroupChange(e) {
				this.isrlsb = e.detail.value;
			},
			uprzimg1() {
				let that = this;
				
				if(that.thkhdata.rzimg1){
					return false;
				}
				
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg1 = res.data.fname;
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},

			uprzimg2() {
				let that = this;
				if(that.thkhdata.rzimg2){
					return false;
				}
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg2 = res.data.fname;
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},


			formSubmit: function(e) {
				let that = this;

				let pvalue = e.detail.value;
				let khdata = that.thkhdata;
				let ddtype = that.thddtype;
				let isrlsb = that.isrlsb;
				let ywrydata = that.ywrydata;
				let ywryidarr = [];
				let ywryidstr = '';
				let sfzhao = pvalue.sfzhao;
				let sknexturl = '';

				if (ywrydata.length > 0) {
					ywrydata.forEach((item, index) => {
						ywryidarr.push(item.ygid);
					})
					ywryidstr = ywryidarr.toString();
				}
				console.log(ywryidstr);


				if (!khdata) {
					uni.showToast({
						icon: 'none',
						title: '请选择客户',
					});
					return false;
				}

				if (!ddtype || ddtype == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择业务类型',
					});
					return false;
				}

				// if(!sfzhao){
				//   uni.showToast({
				// 	icon: 'none',
				// 	title: '请输入客户身份证号',
				//   });
				//   return false;
				// }

				if (!ywryidstr && that.isdpywzh==0) {
					uni.showToast({
						icon: 'none',
						title: '请选择业务人员',
					});
					return false;
				}




				if (this.clicksta) return;
				this.clicksta = true;



				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'djid': that.djid,
					'khid': khdata.id,
					'isrlsb': isrlsb,
					'ddtype': ddtype,
					'ywryidstr': ywryidstr,
					'sfzhao': sfzhao ? sfzhao : '',
					'rzimg1': that.rzimg1 ? that.rzimg1 : '',
					'rzimg2': that.rzimg2 ? that.rzimg2 : '',
					'isdpywzh': that.isdpywzh ? that.isdpywzh : 0,

				}
				this.$req.post('/v1/danju/rkdeditsave', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							let djid = res.data.djid;
							that.djid=djid;
							if (ddtype == 1) {
								sknexturl = './editx1?djid=' + djid;
							}
							if (ddtype == 2) {
								sknexturl = './editx1?djid=' + djid;
							}
							if (ddtype == 3) {
								sknexturl = './editx1?djid=' + djid;
							}
							if (ddtype == 4) {
								sknexturl = './editx41?djid=' + djid;
							}
							uni.setStorageSync('thupdpgllist', 1);
							setTimeout(function() {
								uni.navigateTo({
									url: sknexturl
								})
							}, 500)

							// uni.showToast({
							// 	icon: 'none',
							// 	title: res.msg,
							// 	success() {

							// 	}
							// });
						} else {
							that.clicksta = false;
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},



		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}


	.rowxmttp {
		margn-top: 30upx;
		position: relative;
	}

	.rowxmttp .zjzkicon {
		position: absolute;
		right: 0;
		top: 0;
		font-size: 24upx
	}

	.rowxmttp .zjzkicon .iconfont {
		font-size: 20upx
	}

	.rlsbsmtip {
		position: absolute;
		right: 0;
		top: 32upx;
		font-size: 24upx;
		color: #888
	}

	.rlsbsmtip text {
		margin-right: 10upx;
		font-size: 24upx
	}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}




	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}


	.khinfo {
		background: #FFFFFF;
		border-radius: 20rpx;
		margin-bottom: 20upx;
		padding: 28upx 28upx;
		position: relative;
	}

	.khinfo .tx {
		float: left;
		position: relative;
	}

	.khinfo .tx .avatar image {
		width: 100upx;
		height: 100upx;
		border-radius: 100upx;
		display: block;
	}

	.khinfo .tx .sexicon {
		position: absolute;
		bottom: -20upx;
		left: 35upx
	}

	.khinfo .tx .sexicon image {
		width: 32upx;
		height: 32upx;
	}

	.khinfo .xx {
		float: left;
		margin-left: 30upx
	}

	.khinfo .xx .tit {
		height: 40upx;
		line-height: 40upx;
		font-size: 28upx;
		padding-top: 10upx;
		margin-bottom: 10upx;
	}

	.khinfo .xx .tit .name {
		font-size: 28upx;
		font-weight: 600
	}

	.khinfo .xx .tit .uid {
		font-size: 24rpx;
		margin-left: 15upx;
		color: #888
	}

	.khinfo .xx .ssyg {
		height: 40upx;
		line-height: 40upx;
		font-size: 24upx;
		color: #7F7F7F;
	}

	.khinfo .djcon {
		position: absolute;
		right: 0;
		top: 0;
		font-size: 24upx;
		background: #E7FFE2;
		border-radius: 0rpx 20rpx 0rpx 20rpx;
		height: 42upx;
		line-height: 42upx;
		padding: 0 15upx;
		color: #0EC45C
	}


	.ywrylist {
		padding-top: 10upx
	}

	.ywrylist .xxitem {
		height: 100upx;
		line-height: 100upx;
		overflow: hidden;
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.ywrylist .xxitem .xxset {
		position: absolute;
		right: 0;
		top: 0
	}

	.ywrylist .xxitem .xxset text {
		margin-left: 40upx;
		font-size: 28upx;
	}

	.ywrylist .xxitem:last-child {
		border-bottom: 0;
	}
	
	.nofrztipcon{background:#fafafa;border-radius:10upx;padding:15upx 20upx;font-size:24upx;margin-top:10upx}
	.nofrztipcon .iconfont{margin-right:10upx;color:#ff0000;font-size:24upx}
</style>