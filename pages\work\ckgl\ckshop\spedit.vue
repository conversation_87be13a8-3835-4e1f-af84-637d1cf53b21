<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex=100>
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon">

						<view class="vstit">物品信息</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品名称</view>
							<view class="inpcon">
								<view class="inp"><input class="input" type="text" name="title" v-model="spdata.title"
										placeholder="请输入物品名称" placeholder-class="placeholder" /></view>
							</view>
							<view class="clear"></view>
						</view>


					</view>


					<view class="maxcon">


						<view class="rowx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>物品分类</view>
							<view class="inpcon">
								<view class="inp">
									<view @click="selwpfl">{{sortid > 0 ? sortnamep : '请选择物品分类'}}</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>


						<block v-if="spflsxlist">
							<block v-for="(item,index) in spflsxlist" :key="index">
								<block v-if="item.sxtype==3">
									<view class="rowx">
										<view class="tit"><text class="redst"></text>{{item.title}}</view>
										<view class="inpcon">
											<view class="inp"><input class="input" type="text" :id="item.id"
													v-model="item.value" :placeholder="'请输入'+item.title"
													placeholder-class="placeholder" @blur="sxinplur" /></view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								<block v-else>
									<view class="rowx">
										<view class="icon"><text class="iconfont icon-jiantou"></text></view>
										<view class="tit"><text class="redst"></text>{{item.title}}</view>
										<view class="inpcon" @click="sxsel" :data-sxid="item.id"
											:data-sxname="item.title" :data-sxtype="item.sxtype"
											:data-sxvalue="item.sxvalue">
											<view class="inp">
												<block v-if="item.value">
													{{item.value}}
												</block>
												<block v-else>
													请选择{{item.title}}
												</block>
											</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
							</block>
						</block>





					</view>



					<view class="maxcon">
						<view class="vstit" style="font-weight:400;margin-bottom:25upx">备注说明 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户可见）</text></view>

						<view class="bzcon">
							<textarea name="beizhu" class="beizhu" v-model="beizhu"></textarea>
						</view>

						<view style="font-size:24upx;color:#888;margin-top:17upx">入库存档图片不可编辑，可在此处上传完善图片资料</view>
						<view class="rowxmttp">
							<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
								<block v-for="(item,index) in img_url_ok" :key="index">
									<view class='item'>
										<view class="del" @tap="deleteImg" :data-index="index"><i
												class="iconfont icon-shanchu2"></i></view>
										<image :src="staticsfile+item" :data-src='staticsfile + item '
											@tap='previewImage'></image>
									</view>
								</block>
								<view class='item' v-if="img_url_ok.length <= 8">
									<image @tap="chooseimage" src='/static/images/uppicaddx.png'></image>
								</view>
								<view class="clear"></view>
							</view>
						</view>


					</view>




				</view>

				<view class="tn-flex tn-footerfixed" style="z-index:10;">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" form-type="submit">
							<text>确认提交</text>
						</button>
					</view>
				</view>
			</form>








		</view>

		<view style="height:120upx"></view>

		<tn-modal v-model="fwfblshow" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">服务费比例</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入服务费比例'" v-model="fwfbl" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round" @click="jsfwjine">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>



		<tn-popup v-model="sxselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">请选择{{thsxname}}</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="sxvlist">

					<block v-if="thsxtype==1">
						<radio-group @change="zdysxChange">
							<block v-for="(item, index) in thsxvalue" :key="index">
								<view class="item">
									<label>
										<view class="icon">
											<radio :value="item" color="#FFD427" />
										</view>
										<view class="vstxt">{{item}}</view>
										<view class="clear"></view>
									</label>
								</view>
							</block>
						</radio-group>
					</block>
					<block v-if="thsxtype==2">
						<checkbox-group @change="zdysxmChange">
							<block v-for="(item, index) in thsxvalue" :key="index">
								<view class="item">
									<label>
										<view class="icon">
											<checkbox :value="item" color="#FFD427" />
										</view>
										<view class="vstxt">{{item}}</view>
										<view class="clear"></view>
									</label>
								</view>
							</block>
						</checkbox-group>

						<view style="height:160upx"></view>
						<view class="tn-flex tn-footerfixed">
							<view class="tn-flex-1 justify-content-item tn-text-center">
								<view class="bomsfleft">
									<button class="bomsubbtn" @click="closezdysxm" style="background:#ddd;">
										<text>关闭</text>
									</button>
								</view>
								<view class="bomsfright">
									<button class="bomsubbtn" @click="qrzdysxm">
										<text>确认选择</text>
									</button>
								</view>
								<view class="clear"></view>
							</view>
						</view>
					</block>

				</view>
			</scroll-view>
		</tn-popup>

		<tn-wpfl v-model="wpflshow" @qrxz="wpflqrxz" @close="wpflclose"></tn-wpfl>
	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				xtitle: '',
				spdata: [],
				ddtype: '',
				yhrydata: [],
				clicksta: false,
				dtptxt: '',
				dtpjgtxt: '',

				danjia: "",
				shuliang: "",
				zongjia: "0.00",
				sjjiage: "0.00",
				fwfjine: "0.00",
				fwfbl: '',
				ckdata: '',
				ckid: 0,
				ckname: '',
				ckselshow: false,
				sxselshow: false,
				fwfblshow: false,
				spsortdata: [],
				spflsxlist: '',
				thsxtype: 0,
				thsxid: 0,
				thsxname: '',
				thsxvalue: '',
				thsxmtemp: '',
				beizhu: '',

				wpflshow: false,
				sortid: 0,
				sortname: '',
				sortnamep: '',

			}
		},

		onLoad(options) {
			let that = this;
			let spid = options.spid ? options.spid : 0;
			this.spid = spid;
			this.loadData();

			this.xtitle = "编辑物品";


		},
		onShow() {

			this.clicksta = false;

		},

		methods: {


			loadData() {
				let that = this;
				let da = {
					spid: that.spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/ckspedit', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.spdata = res.data.spdata;
							that.sortid = parseInt(res.data.sortid);
							that.sortnamep = res.data.spdata.sortname;
							that.spflsxlist = res.data.spflsxlist ? res.data.spflsxlist : '';
							that.ckid = res.data.spdata ? res.data.spdata.ckid : 0;
							that.ckname = res.data.spdata ? res.data.spdata.ckname : '';
							that.ddtype = res.data.ddtype;
							that.dtptxt = res.data.dtptxt;
							that.dtpjgtxt = res.data.dtpjgtxt;
							that.danjia = res.data.spdata ? res.data.spdata.danjia : '';
							that.shuliang = res.data.spdata ? res.data.spdata.shuliang : '';
							that.zongjia = res.data.spdata ? res.data.spdata.zongjia : "0.00";
							that.sjjiage = res.data.spdata ? res.data.spdata.sjjiage : "0.00";
							that.fwfjine = res.data.spdata ? res.data.spdata.fwfjine : "0.00";
							that.spsortdata = res.data.spsortdata;
							that.beizhu = res.data.spdata.beizhu;
							that.img_url_ok = res.data.picturesarr;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}

					})

			},


			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},

			fwfblset() {

				if (!this.zongjia || this.zongjia == 0) {
					uni.showToast({
						icon: 'none',
						title: '请先完善物品总价',
					});
					return false;
				}

				this.fwfblshow = true;
			},
			jsfwjine() {
				let zongjia = this.zongjia;
				let fwfbl = this.fwfbl;
				let fwfjine = 0;
				if (!fwfbl || fwfbl == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入服务费比例',
					});
					return false;
				}

				if (fwfbl > 100) {
					uni.showToast({
						icon: 'none',
						title: '请输入正确的比例',
					});
					return false;
				}

				fwfjine = zongjia * fwfbl / 100;
				fwfjine = fwfjine.toFixed(2);
				this.fwfjine = fwfjine;
				this.fwfblshow = false;
			},
			sxsel(e) {
				let thsxtype = e.currentTarget.dataset.sxtype;
				let thsxid = e.currentTarget.dataset.sxid;
				let thsxname = e.currentTarget.dataset.sxname;
				let thsxvalue = e.currentTarget.dataset.sxvalue;

				this.thsxtype = thsxtype;
				this.thsxid = thsxid;
				this.thsxname = thsxname;
				this.thsxvalue = thsxvalue;
				this.sxselshow = true;

			},

			zdysxChange(e) {
				let that = this;
				let value = e.detail.value;

				let thsxid = that.thsxid;
				let spflsxlist = that.spflsxlist;

				if (value.length == 0) {
					value = '';
				}
				for (var i = spflsxlist.length - 1; i >= 0; i--) {
					if (spflsxlist[i].id == thsxid) {
						spflsxlist[i].value = value;
						break;
					}
				}
				that.spflsxlist = spflsxlist;
				this.sxselshow = false;
			},
			zdysxmChange(e) {
				let value = e.detail.value;
				this.thsxmtemp = value;
			},
			qrzdysxm() {
				let that = this;
				let value = that.thsxmtemp;
				let thsxid = that.thsxid;
				let spflsxlist = that.spflsxlist;

				if (value.length == 0) {
					value = '';
				} else {
					value = value.join(',');
				}
				for (var i = spflsxlist.length - 1; i >= 0; i--) {
					if (spflsxlist[i].id == thsxid) {
						spflsxlist[i].value = value;
						break;
					}
				}
				this.spflsxlist = spflsxlist;

				this.sxselshow = false;
			},
			closezdysxm() {
				this.sxselshow = false;
			},
			sxinplur(e) {

				let that = this;
				let thsxid = e.currentTarget.id;
				let value = e.detail.value;
				let spflsxlist = that.spflsxlist;
				for (var i = spflsxlist.length - 1; i >= 0; i--) {
					if (spflsxlist[i].id == thsxid) {
						spflsxlist[i].value = value;
						break;
					}
				}
				that.spflsxlist = spflsxlist;

			},
			jszongjia(e) {
				let that = this;
				setTimeout(() => {
					let danjia = that.danjia ? that.danjia : 0;
					let shuliang = that.shuliang ? that.shuliang : 0;
					let zongjia = 0;
					zongjia = parseFloat(danjia) * parseFloat(shuliang);
					this.zongjia = zongjia;
				}, 0)
			},

			selthck(e) {
				let ckid = e.currentTarget.dataset.ckid;
				let ckname = e.currentTarget.dataset.ckname;
				this.ckid = ckid;
				this.ckname = ckname;
				this.ckselshow = false;
			},

			onchange(e) {
				let that = this;
				let valuearr = e.detail.value;

				let vlength = valuearr.length;
				let sortid = 0;
				if (vlength > 0) {
					sortid = valuearr[vlength - 1].value;
				}


				let da = {
					sortid: sortid,
				}
				this.$req.post('/v1/com/getspflsx', da)
					.then(res => {
						console.log('131231', res);
						if (res.errcode == 0) {
							that.spflsxlist = res.data.spflsxlist;
						}
					})

			},


			selwpfl() {
				this.wpflshow = true;
			},
			wpflqrxz(node) {
				console.log(node);
				let sortid = node.sortid;
				let that = this;
				that.sortid = sortid;
				that.sortnamep = node.sortnamep;
				let da = {
					sortid: sortid,
				}
				this.$req.post('/v1/com/getspflsx', da)
					.then(res => {
						if (res.errcode == 0) {
							that.spflsxlist = res.data.spflsxlist;
						}
					})

				this.wpflshow = false;
			},
			wpflclose() {
				this.wpflshow = false;
			},



			formSubmit: function(e) {
				let that = this;
				let pvalue = e.detail.value;
				let khdata = that.thkhdata;
				let ddtype = that.thddtype;
				let isrlsb = that.isrlsb;
				let yhrydata = that.yhrydata;
				let yhryidarr = [];
				let yhryidstr = '';
				let sfzhao = pvalue.sfzhao;
				let sknexturl = '';
				let spflsxlist = that.spflsxlist;
				let spflsxarr = [];
				let spflsxstr = '';



				if (spflsxlist.length > 0) {
					spflsxlist.forEach((item, index) => {
						spflsxarr.push({
							id: item.id,
							title: item.title,
							value: item.value
						});
					})
					// spflsxstr=spflsxarr.toString();
				}


				if (!pvalue.title) {
					uni.showToast({
						icon: 'none',
						title: '请输入物品名称',
					});
					return false;
				}


				if (!that.sortid || that.sortid == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择物品分类',
					});
					return false;
				}

				if (!that.img_url_ok || that.img_url_ok.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请上传物品图片',
					});
					return false;
				}


				if (this.clicksta) return;
				this.clicksta = true;


				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'spid': that.spid,
					'sortid': that.sortid ? that.sortid : 0,
					'title': pvalue.title,
					'beizhu': pvalue.beizhu ? pvalue.beizhu : '',
					'pictures': that.img_url_ok ? that.img_url_ok : '',
					'spflsxarr': spflsxarr,
				}


				this.$req.post('/v1/ckgl/ckspeditsave', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							uni.setStorageSync('thupcksplist', 1)
							uni.setStorageSync('thupsplist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										uni.navigateBack()
									}, 500)
								}
							});
						} else {
							that.clicksta = false;
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch(err => {

					})

			},




		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}




	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 10
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}


	.yhrylist {
		padding-top: 30upx
	}

	.yhrylist .xxitem {
		height: 80upx;
		line-height: 80upx;
		overflow: hidden;
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.yhrylist .xxitem .xxset {
		position: absolute;
		right: 0;
		top: 0
	}

	.yhrylist .xxitem .xxset text {
		margin-left: 40upx;
		font-size: 28upx;
	}

	.yhrylist .xxitem:last-child {
		border-bottom: 0;
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #eee;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
		overflow: hidden;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #888
	}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}






	.sxvlist {}

	.sxvlist .item {
		padding: 0 30upx;
		border-bottom: 1px solid #efefef;
		height: 100upx;
		line-height: 100upx
	}

	.sxvlist .item .icon {
		float: left
	}

	.sxvlist .item .vstxt {
		float: left;
		mragin-left: 20upx
	}
</style>