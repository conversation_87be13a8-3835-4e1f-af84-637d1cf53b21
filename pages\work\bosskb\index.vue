<template>
  <view class="page-e tn-safe-area-inset-bottom">


	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
				 <view slot="back" class='tn-custom-nav-bar__back'  >
					  <text class='icon tn-icon-left'></text>
				 </view>
				<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				  <text  >BOSS看板</text>
				</view>
				<view slot="right" >
				</view>
				
	</tn-nav-bar>
	 <view class="tabs-fixed-blank" :style="{paddingTop: vuex_custom_bar_height + 10 + 'px'}"></view>

    
    <view class="kbmain" >
          
		 
		  	
		    <view class="maxcon " >
		  			   
					  <view class="qhdps" >
						<view class="tline"></view>     
						<view class="logo"><image :src="staticsfile + sjdata.logo"></image></view>
						<view class="tit">{{sjdata.title}}</view>
						<view class="clear"></view>
					  </view>
		  			   
		  			  <view class="tjzong">
		  				  	<view class="sz" @click="gotoqycwrcd" data-type="0">{{sjdata.zyue}}</view>
		  					<view class="txt">企业总账(企业收入余额)  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
		  			  </view>
		  			  <view class="tjitem noline">
		  				<view class="ite gx"> 
		  							<view class="cons" @click="gotoqycwrcd" data-type="1">
		  								<view class="txt">总收入  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
		  								<view class="sz">{{sjdata.syzjine}}</view>
		  							
		  							</view>
		  				</view>
		  				<view class="ite">
		  							<view class="cons" @click="gotoqycwrcd" data-type="2">
		  								<view class="txt">总支出  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
		  								<view class="sz">{{sjdata.zzhichu}}</view>
		  							</view>
		  				</view>
		  				<view class="clear"></view>
		  			  </view>
					  <view class="tjitem noline">
									<view class="ite gx"> 
												<view class="cons" @click="gotoqyzijinrcd" data-type="0">
													<view class="txt">可出资金额  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
													<view class="sz">{{sjdata.czkyjine}}</view>
												</view> 
									</view>
									<view class="ite">
												<view class="cons"  @click="gotoqyzijinrcd"  data-type="3">
													<view class="txt">当前出资  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
													<view class="sz">{{sjdata.czzjine}}</view>
												</view>
									</view>
									<view class="clear"></view>
					  </view>
		     </view>
		   
		   
	
		   
		   <view class="maxcon " >
		   			   
						<view class="qhdps" >
							<view class="tline"></view>    
							<view class="icon" v-if="dpdata.length > 1" @click="seldianpu"><text class="iconfont icon-qiehuandianpu1"></text></view>
							<view class="logo"><image :src="staticsfile + thdpdata.logo"></image></view>
							<view class="tit">{{dpname}}</view>
							<view class="clear"></view>
						</view>
		   			   
		   			  <view class="tjzong">
		   				  	<view class="sz" @click="gotodpcwrcd" data-type="0">{{thdpdata.dpyue}}</view>
		   					<view class="txt">店铺总账(店铺收入余额)  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
		   			  </view>
		   			  <view class="tjitem noline">
		   				<view class="ite gx"> 
		   							<view class="cons" @click="gotodpcwrcd" data-type="1">
		   								<view class="txt">总收入  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
		   								<view class="sz">{{thdpdata.dpsyzjine}}</view>
		   							
		   							</view>
		   				</view>
		   				<view class="ite">
		   							<view class="cons" @click="gotodpcwrcd" data-type="2">
		   								<view class="txt">总支出  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
		   								<view class="sz">{{thdpdata.dpzzhichu}}</view>
		   							</view>
		   				</view>
		   				<view class="clear"></view>
		   			  </view>
					  <view class="tjitem noline">
								<view class="ite gx"> 
											<view class="cons" @click="gotodpczzjrcd" data-type="0">
												<view class="txt">可出资金额  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
												<view class="sz">{{thdpdata.czkyjine}}</view>
											
											</view> 
								</view>
								<view class="ite">
											<view class="cons"  @click="gotodpczzjrcd"  data-type="3">
												<view class="txt">当前出资  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
												<view class="sz">{{thdpdata.czzjine}}</view>
											</view>
								</view>
								<view class="clear"></view>
					  </view>
		   </view>
		  
		   
    </view>
	

      <view style="height:60upx"></view>
	  
	  
	  
	<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
	   	<view class="popuptit">选择店铺</view>
	   	<scroll-view scroll-y="true" style="height: 800rpx;">
	   		<view class="seldplist">
	   			<block v-for="(item,index) in dpdata" :key="index">
	   
	   				<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
	   					<view class="xzbtn">选择</view>
	   					<view class="tx">
	   						<image :src="staticsfile + item.logo"></image>
	   					</view>
	   					<view class="xx">
	   						<view class="name">{{item.title}}</view>
	   						<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
	   						</view>
	   					</view>
	   					<view class="clear"></view>
	   				</view>
	   
	   			</block>
	   		</view>
	   	</scroll-view>
	</tn-popup>
	  
	  
	  
	  
	  
	  
	  
	  
	  
  </view>
</template>

<script>
	
  export default {
    data() {
      return {
		  staticsfile: this.$staticsfile,
		  ygid:0,
		  ygname:'',
		  ygdata:[],
		  sjdata:[],
		  
		  dpselshow:false,
		  dpdata:'',
		  dpid:0,
		  dpname:'',
		  thdpdata:[],
		  
		  rqshow:false,
		  rqkstime:0,
		  rqjstime:0,
		  tjdata:[],
		  
		  
		  
      }
    },
	
	onLoad(){
		let that=this;
		
	},
	onShow(){
		let that=this;
		this.loadData();
		this.getdpsj();
	},

	
    methods: {
	
	    gotoqycwrcd(e){
	    	let type=e.currentTarget.dataset.type;
	    	uni.navigateTo({
	    		url:'./dpcwrcd?type='+type
	    	})
	    },
	    gotoqyzijinrcd(e){
	    	let type=e.currentTarget.dataset.type;
	    	uni.navigateTo({
	    		url:'./dpzijinrcd?type='+type
	    	})
	    },
	
	
		gotodpcwrcd(e){
			let type=e.currentTarget.dataset.type;
			uni.navigateTo({
				url:'./dpcwrcd?dpid='+this.dpid+'&dpname='+this.dpname+'&type='+type
			})
		},
		gotodpczzjrcd(e){
			let type=e.currentTarget.dataset.type;
			uni.navigateTo({
				url:'./dpzijinrcd?dpid='+this.dpid+'&dpname='+this.dpname+'&type='+type
			})
		},
	
		seldianpu() {
			if(this.dpdata.length > 1){
				this.dpselshow = true;
			}
		},
		selthdp(e) {
			let that=this;
			let dpid = e.currentTarget.dataset.dpid;
			let dpname = e.currentTarget.dataset.dpname;
		    this.dpid=dpid;
		    this.dpname=dpname;
			this.getdpsj();
			this.dpselshow = false;
		},
		clsdpid(){
			let that=this;
			this.dpid=0;
			this.dpname='';
			this.getdpsj();
			this.dpselshow = false;
		},

		loadData() {
			let that=this;
			let da={
				thdpid:that.dpid ? that.dpid : 0,
			};
			uni.showNavigationBarLoading();
			this.$req.post('/v1/bosskb/index', da)
			      .then(res => {
			         console.log(222,res);
				    uni.hideNavigationBarLoading();
					if(res.errcode==0){
							that.dpdata = res.data.dpdata;
							that.sjdata = res.data.sjdata;
					}else{
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
					}
			      })
		},
		
	 
	 getdpsj() {
		let that=this;
		let da={
			thdpid:that.dpid ? that.dpid : 0,
		};
		uni.showNavigationBarLoading();
		this.$req.post('/v1/bosskb/getdpsj', da)
		      .then(res => {
		         console.log(222,res);
			    uni.hideNavigationBarLoading();
				if(res.errcode==0){
						that.thdpdata = res.data.thdpdata;
						that.dpid = res.data.thdpdata.id;
						that.dpname = res.data.thdpdata.title;
				}else{
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
				}
		      })
	},
	
	
	
	
    
    }
  }
</script>

<style lang="scss" scoped>
 
.kbmain{margin:10upx 32upx;}
 
.qhdps{background:#fff;padding:20upx 40upx;position: relative;font-size:32upx;border-bottom:1px solid #F3F3F3;border-radius:20upx 20upx 0 0;}
.qhdps .icon{position: absolute;right:40upx;top:40upx;}
.qhdps .icon .iconfont{font-size:40upx}
.qhdps .logo{float:left;}   
.qhdps .tit{float:left;height:80upx;line-height:80upx;margin-left:20upx;} 
.qhdps .logo image{width:80upx;height:80upx;border-radius:100upx;display: block;}

.maxcon{background:#fff;border-radius: 20rpx;margin-bottom:20upx;position: relative;}
.maxcon.pdum0{padding:0 20upx 0 20upx}

.tjitem{border-bottom:1px solid #F3F3F3;padding:20upx 0;}
.tjitem .ite{width:50%;float:left;position:relative}
.tjitem .ite.gx:after {position:absolute;top:35%;right:0;content:'';width:1px;height:45%;-webkit-transform: scaleX(0.5);transform: scaleX(0.5);border-right: 1px solid #D9D9D9;}

.tjitem .txt{color: #7F7F7F;margin-bottom:15upx;font-size:28upx}
.tjitem .sz{font-weight: 600;font-size:36upx}
.tjitem.noline{border-bottom:0}
.tjitem .cons{padding-left:40upx;}
 
.mxicon{margin-left:10upx;}
.tjzong{border-bottom:1px solid #F3F3F3;padding:40upx 0;padding-left:40upx;position: relative;}
.tjzong .sz{font-weight: 600;font-size:56upx}
.tjzong .txt{color: #7F7F7F;margin-top:15upx;font-size:28upx}
.fyzcbtn{position: absolute;top:50upx;right:40upx;height:70upx;line-height:70upx;border-radius:100upx;padding:0 20upx;background: #FFD427;font-size:24upx;}  

.tline{position: absolute;left:0;top:43upx;width:10upx;height:30upx;background:#FFD427 ;}

  
</style>
