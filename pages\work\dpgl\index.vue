<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>店铺管理</text>
			</view>
			<view slot="right">
			</view>

		</tn-nav-bar>
		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333"  :bold="true" :fontSize="28"
						:badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;">
					<view class="item btnimg" @click="dpedit" :data-dpid='0'>
						<image src="/static/images/addbtn.png"></image>
					</view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>



		</view>
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 20 + 'px'}"></view>



		<block v-if="listdata.length>0">



			<view class="dplist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="item " :class="item.status!=1 ? 'tystyle' : '' ">

						<view class="inf1">
							<view class="status" @click="setsta" :data-dpid='item.id'>
								<view class="aa">{{item.statusname}}</view>
								<view class="aa"><tn-switch v-model="item.status==1 ? true : false "
										activeColor="#FFD427" inactiveColor="#7F7F7F" leftIcon="success"
										right-icon="close" change="ss"></tn-switch></view>
							</view>
							<view class="ewms" @click="gotoyqewm" :data-dpid="item.id"><text
									class="iconfont icon-erweima"></text></view>
							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + item.logo"></image>
								</view>
								<view class="xx">
									<view class="dpid">{{item.title}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="tf2">
								<view class="n1">{{item.areatxt}}</view>
								<view class="n2">{{item.dizhi}}</view>
							</view>
						</view>
						<view class="inf2">
							<view class="tel">{{item.dptel}}</view>
							<view class="setbtn" @click="dpedit" :data-dpid='item.id'>门店设置</view>
							<view class="f1">
								店铺店长：
								<block v-if="item.dzuid > 0">
									<text @click="setdz" :data-dpid='item.id' :data-dpname='item.title'>
										<text style="color:#1677FF">{{item.dzname}}</text>
										<text class="iconfont icon-bianji bjbtn"></text>
									</text>
									<text class="iconfont icon-shanchu bjbtn2" @click="setdzjc" :data-dpid='item.id' :data-dpname='item.title' :data-dzname='item.dzname'></text>
								</block>
								<block v-else>
									<text @click="setdz" :data-dpid='item.id' :data-dpname='item.title'>
										<text style="color:#ff0000">设置店长</text>
										<text class="iconfont icon-bianji bjbtn"></text>
									</text>
								</block>
							</view>

							<view class="f1">
								店铺仓库：
								<block v-if="item.cknum > 0">
									<text @click="setck" :data-dpid='item.id' :data-dpname='item.title'>
										<text style="color:#4aac09;margin-right:5upx">{{item.cknum}}</text> 个
										<text class="iconfont icon-bianji bjbtn"></text>
									</text>
								</block>
								<block v-else>
									<text @click="setck" :data-dpid='item.id' :data-dpname='item.title'>
										<text style="color:#ff0000">设置仓库</text>
										<text class="iconfont icon-bianji bjbtn"></text>
									</text>
								</block>
							</view>

							<view class="f2">创建日期：{{item.addtime}}</view>
						</view>




					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无店铺</view>
			</view>
		</block>





		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">店铺搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入店铺名称/门店ID'" v-model="thkwtt" name="input"
							placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				index: 1,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						sta: 0,
						name: '全部'
					},
					{
						sta: 1,
						name: '开业'
					},
					{
						sta: 2,
						name: '停业'
					},
				],
				show3: false,
				checked: true,
				thdpid: 0,
				thdpname: '',

			}
		},
		onLoad(options) {
			let that = this;
			that.page = 0;
			that.listdata = [];
			that.loaditems();
		},
		onShow() {

			let that = this;
			if (uni.getStorageSync('thupdpgllist') == 1) {
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thupdpgllist', 0)
			}

			let thygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			if (thygid) {
				this.thygid = thygid;
				this.thygname = thygname;
				this.setdpdz();
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
			}


		},
		methods: {
 
			setdz(e) {
				let thdpid = e.currentTarget.dataset.dpid;
				let thdpname = e.currentTarget.dataset.dpname;
				this.thdpid = thdpid;
				this.thdpname = thdpname;
				uni.navigateTo({
					url: '/pages/selyg/index?stype=2&dpid=' + thdpid + '&dpname=' + thdpname
				})
			},
			setck(e) {
				let thdpid = e.currentTarget.dataset.dpid;
				let thdpname = e.currentTarget.dataset.dpname;
				uni.navigateTo({
					url: '/pages/work/qyck/index?dpid=' + thdpid + '&dpname=' + thdpname
				})
			},

			gotoyqewm(e) {
				let dpid = e.currentTarget.dataset.dpid;
				uni.navigateTo({
					url: '/pages/work/yggl/yqewm?dpid=' + dpid
				})
			},

			// switch打开或者关闭时触发，值为true或者false
			change(status) {
				console.log(status);
			},
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			dpedit(e) {
				let that = this;
				let dpid = e.currentTarget.dataset.dpid;
				uni.navigateTo({
					url: './edit?dpid=' + dpid
				})
			},


			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					sta: that.sta,
					kw: that.thkw ? that.thkw : '',
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/dpgl/dplist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},





			setsta(e) {
				let that = this;
				let dpid = e.currentTarget.dataset.dpid;
				let da = {
					'dpid': dpid
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/dpgl/setsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.page = 0;
										that.sta = 0;
										that.listdata = [];
										that.isloading = false;
										that.loadingType = -1;
										that.loaditems();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})


			},

			setdpdz() {
				let that = this;
				
				let thdpid= this.thdpid;
				let thdpname= this.thdpname;
				let thygid= this.thygid;
				let thygname= this.thygname;
				let xtip='确认将【'+thygname+'】设置为【'+thdpname+'】的店长吗？';
				
				uni.showModal({
					title: '',
					content: xtip,
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'dpid': thdpid,
								'dzuid': thygid,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/dpgl/setdpdz', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})



						}




					}
				})


			},
			
			setdzjc(e) {
				let that=this;
				let thdpid = e.currentTarget.dataset.dpid;
				let dzname = e.currentTarget.dataset.dzname;
				let xtip='确认解除【'+dzname+'】的店长职务吗？';
				uni.showModal({
					title: '',
					content: xtip,
					success: function(e) {
						//点击确定
						if (e.confirm) {
				
							let da = {
								'dpid': thdpid,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/dpgl/setdpdzjc', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
				
				
				
						}
				
				
				
				
					}
				})
			},








		}
	}
</script>

<style lang='scss'>
	page {}


	.ssnrtip {
		height: 124upx;
		line-height: 124upx;
		background: #f6f6f6;
		position: relative;
		padding: 0 32upx
	}

	.ssnrtip .con {}

	.ssnrtip .con text {
		color: #3366ff
	}

	.ssnrtip .cls {
		position: absolute;
		right: 32upx;
	}

	.ssnrtip .cls text {
		font-size: 45upx
	}



	.dplist {
		padding: 0 32upx 32upx 32upx
	}

	.dplist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx
	}

	.dplist .item .ewms {
		position: absolute;
		right: 40upx;
		top: 152upx
	}

	.dplist .item .ewms .iconfont {
		font-size: 55upx;
		color: #666
	}



	.dplist .item .status {
		position: absolute;
		right: 32upx;
		top: 52upx
	}

	.dplist .item .status .aa {
		float: left;
		margin-left: 10upx;
		line-height: 50upx;
	}

	.dplist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.dplist .item .inf1 .tx {
		float: left
	}

	.dplist .item .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 10upx;
	}

	.dplist .item .inf1 .xx {
		float: left;
		margin-left: 20upx
	}

	.dplist .item .inf1 .xx .dpid {
		height: 100upx;
		line-height: 100upx;
		font-size: 28rpx;
		font-weight:700;
	}

	.dplist .item .inf1 .tf2 {
		font-size: 28rpx;
		margin-top: 20upx;
		min-height:60upx; 
	}

	.dplist .item .inf1 .tf2 .n1 {
		font-weight: 600;
	}

	.dplist .item .inf1 .tf2 .n2 {
		margin-top: 10upx;
		color: #7F7F7F;
		font-size: 24upx
	}

	.dplist .item .inf2 {
		background: #fff;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 28upx;
		font-size: 24rpx;
		color: #333;
		position: relative;
		border-top: 1px solid #efefef
	}

	.dplist .item .inf2 .tel {
		position: absolute;
		right: 28upx;
		top: 28upx
	}

	.dplist .item .inf2 .f1 {
		margin-bottom: 10upx
	}

	.dplist .item .inf2 .f2 {}

	.dplist .item .inf2 .setbtn {
		position: absolute;
		right: 28upx;
		bottom: 30upx;
		width: 140rpx;
		height: 60upx;
		line-height: 60upx;
		background: #FFD427;
		border-radius: 80rpx;
		font-size: 24rpx;
		text-align: center;
	}

	.dplist .item .inf2 .bjbtn {
		font-size: 24upx;
		margin-left: 10upx;
	}
	.dplist .item .inf2 .bjbtn2 {
		font-size: 24upx;
		margin-left: 30upx;
		color:#ff6600
	}
	.dplist .item.tystyle .inf1 {
		background: #F0F0F0;
	}

	.dplist .item.tystyle .inf2 {
		background: #F0F0F0;
	}
</style>