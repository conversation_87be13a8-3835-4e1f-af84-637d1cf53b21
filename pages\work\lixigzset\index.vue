<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >利息规则设置</text>
					</view>
					<view slot="right" >
					</view>
					
		</tn-nav-bar>
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;" >
				<view class="item btnimg" @click="fredit" :data-lxid='0'><image src="../../../static/images/addbtn.png"></image></view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		</view>
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 20 + 'px'}" ></view>
	

		
			<block v-if="listdata.length>0">
			
			
			    
						    <view class="frlist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="item " :class="item.status!=1 ? 'tystyle' : '' " >
											
													<view class="inf1"  >
														<view class="tf2">
															<view class="tline"></view>
															<view class="n1">利率：{{item.feilv}}%</view>
														</view>
													</view>
													<view class="inf2">
														<view class="setbtn1" @click="fredit" :data-lxid='item.id'>编辑</view>
														<view class="setbtn2" @click="frdel" :data-lxid='item.id'>删除</view>
														
														<view  @click="setsta" :data-lxid='item.id' >
														    <view class="aa" style="float:left"><tn-switch v-model="item.status==1 ? true : false " activeColor="#FFD427"  inactiveColor="#dfdfdf" leftIcon="success" right-icon="close" change="ss"></tn-switch></view>
														</view>
														<view class="clear"></view>
														
													</view>
												
													
											</view>
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无利息规则</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">规则搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入规则名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 1,name: '正常'},
					{sta: 2,name: '停用'},
				],
				show3:false,
			    checked: true,
				
			}
		},
		onLoad(options) {
			let that=this;
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
		},
		onShow() {
		
			let that=this;
			if(uni.getStorageSync('thuplxsetlist')==1){
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems(); 
				uni.setStorageSync('thuplxsetlist',0)
			}
		
		},
		methods: {
			 // switch打开或者关闭时触发，值为true或者false
			change(status) {
				console.log(status);
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			fredit(e){
				let that=this;
				let lxid = e.currentTarget.dataset.lxid;
				uni.navigateTo({
					url:'./edit?lxid='+lxid
				})
			},
			
			
			// tab选项卡切换
			tabChange(index) {
				let that=this;
				let sta=that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
				
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							sta:that.sta,
							kw:that.thkw ? that.thkw : '' ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/sjgl/lxsetlist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
		
				frdel(e){
							   let that=this;
							   let lxid=e.currentTarget.dataset.lxid;
							   uni.showModal({
							   	title: '删除确认',
							   	content: '删除后不可恢复！确认删除吗？',
							   	success: function(e) {
							   		//点击确定
							   		if (e.confirm) {
							   			let da = {
							   				'lxid': lxid
							   			}
							   			uni.showLoading({title: ''})
							   			that.$req.post('/v1/sjgl/lxsetdel', da)
							   				.then(res => {
							   					uni.hideLoading();
							   					if (res.errcode == 0) {
							   						uni.showToast({
							   							icon: 'none',
							   							title: res.msg,
							   							success() {
							   								setTimeout(function() {
							   										that.page = 0;
							   										that.listdata = [];
							   										that.isloading = false;
							   										that.loadingType = -1;
							   										that.loaditems();
							   								}, 1000);
							   							}
							   						});
							   					} else {
							   						uni.showModal({
							   							content: res.msg,
							   							showCancel: false,
							   						})
							   					}
							   				})
							   		}
							   
							   	}
					})
							   						
							   
				},
				
						
			setsta(e){
				let that=this; 
				let lxid = e.currentTarget.dataset.lxid;
				let da = {
					'lxid': lxid
				}
				uni.showLoading({title: ''})
				that.$req.post('/v1/sjgl/lxsetsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
											that.page = 0;
											that.sta = 0;
											that.listdata = [];
											that.isloading = false;
											that.loadingType = -1;
											that.loaditems();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
				})
				
				
			},
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}




.frlist{padding:0 32upx 32upx 32upx}
.frlist .item{margin-bottom:30upx;background:#fff;border-radius:20upx;padding:30upx 40upx;position: relative;}
.frlist .item .tline{position: absolute;left:0;top:37upx;width:10upx;height:30upx;background:#FFD427 ;}
.frlist .n1{font-size: 28rpx;font-weight:600;margin-bottom:20upx}
.frlist .n2{font-size:28rpx;margin-bottom:20upx}
.frlist .setbtn1{width: 100rpx;height: 50rpx;line-height:50rpx;background: #FFD427;border-radius: 8rpx;font-size:24upx;text-align: center;position: absolute;right:40upx;top:30upx;}
.frlist .setbtn2{width: 100rpx;height: 50rpx;line-height:50rpx;background: #F6F6F6;border: 2rpx solid #B2B2B2;border-radius: 8rpx;font-size:24upx;text-align: center;position: absolute;right:40upx;top:100upx;}



</style>
