<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{djdata.ddtypetxt}}信息确认</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">{{djdata.ddtypetxt}}信息</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户姓名</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户电话</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khtel}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<block v-if="djdata.ddtype==1">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==2">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>回收时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

					</view>




					<view class="maxcon">

						<view class="vstit" style="margin-bottom:30upx">物品列表</view>

						<block v-if="djshop">
							<view class="splist">
								<block v-for="(item,index) in djshop" :key="index">
									<view class="spitem">
										<view class="spname">
											<view class="tline"></view>
											{{item.title}}
											<text	style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.id}})</text>
										</view>
										<!-- 	 <view class="rowa">
																 	<view class="tip">物品编号</view>
																 	<view class="nr colorls" >{{item.id}}</view>
																 	<view class="clear"></view>
																 </view> -->
										<view class="rowa">
											<view class="tip">物品价格</view>
											<view class="nr">
												¥{{item.danjia}} x {{item.shuliang}} = ¥<text>{{item.zongjia}}</text>
											</view>
											<view class="clear"></view>
										</view>

										<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
											<view class="rowa">
												<view class="tip" @click="qtfymx" :data-spid="item.id"
													:data-spname="item.title">其他收费 <text
														class="iconfont icon-yewucaozuo"
														style="margin-left:10upx;color:#1677FF"></text> </view>
												<view class="nr"><text @click="qtfymx" :data-spid="item.id"
														:data-spname="item.title">¥{{item.fjfjine}}</text></view>
												<view class="clear"></view>
											</view>
										</block>
										<!-- 		<block v-if="djdata.ddtype==2">
																	<view class="rowa">
																		<view class="tip" @click="qtcbmx" :data-spid="item.id" :data-spname="item.title">其他成本 <text 	class="iconfont icon-yewucaozuo" style="margin-left:10upx;color:#1677FF"></text> </view>
																		<view class="nr">
																			<text @click="qtcbmx" :data-spid="item.id" :data-spname="item.title">¥{{item.cbfjine}}</text>
																		</view>
																		<view class="clear"></view>
																	</view>
																</block> -->

										<block v-if="djdata.ddtype==1">
											<view class="rowa">
												<view class="tip">服务费比例</view>
												<view class="nr">{{item.fwfbl}} %</view>
												<view class="clear"></view>
											</view>
										</block>
										<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
											<view class="rowa">
												<view class="tip">{{djdata.ddtype == 1 ? '寄卖服务费' : '服务费金额'}}</view>
												<view class="nr" style="color:#ff0000">¥{{item.fwfjine}}</view>
												<view class="clear"></view>
											</view>
										</block>




										<view class="rowa">
											<view class="tip">验货员</view>
											<view class="nr">{{item.yhrystr ? item.yhrystr : '--' }}</view>
											<view class="clear"></view>
										</view>

										<block v-if="djdata.ddtype==1 || djdata.ddtype==2">
											<view class="rowa">
												<view class="tip">上架价格</view>
												<view class="nr">¥{{item.sjjiage}}</view>
												<view class="clear"></view>
											</view>
										</block>


										<block v-if="item.vxq==1">
											<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
												<view class="rowa">
													<view class="tip">{{itemx.title}}</view>
													<view class="nr">{{itemx.value}}</view>
													<view class="clear"></view>
												</view>
											</block>

											<view class="rowa">
												<view class="tip">仓库</view>
												<view class="nr">{{item.ckname}}</view>
												<view class="clear"></view>
											</view>


											<block v-if="item.picturesarr">
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
														<block v-for="(itemxx,indexx) in item.picturesarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemxx"
																	:data-src='staticsfile + itemxx '
																	@click='previewImage' :data-picarr="item.picturesarr"></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</view>
											</block>

											<block v-if="item.beizhu">
												<view class="bzcon">
													<view class="bztit">备注：</view>
													<view class="bznr">{{item.beizhu}}</view>
												</view>
											</block>

										</block>


										<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
											<block v-if="item.vxq==0">
												展开详情<text class="iconfont icon-jtdown2"></text>
											</block>
											<block v-else>
												收起详情<text class="iconfont icon-jtup2"></text>
											</block>
										</view>


									</view>
								</block>
							</view>
						</block>

					</view>



					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">费用信息</view>


						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品总价</view>
							<view class="inpcon">
								<view class="inp">{{djdata.spzongjia}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>服务费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fwfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他收费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fjfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<!-- 		<block v-if="djdata.ddtype==2">
												<view class="rowx">
													<view class="tit"><text class="redst"></text>其他成本</view>
													<view class="inpcon">
														<view class="inp">{{djdata.cbfzjine}}</view>
													</view>
													<view class="clear"></view>
												</view>
											</block> -->
						<block v-if="djdata.ddtype==2">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>单据总价</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{djdata.zongjia}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-else>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>总费用</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{djdata.zongfy}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

					</view>


					<view class="maxcon" style="padding-bottom:10upx">

						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务人员</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywrytxtstr}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>录单人员</view>
							<view class="inpcon">
								<view class="inp">{{djdata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view>

					</view>

					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
							<view class="zjzkicon" @click="setvzz">{{vsfnr==0 ? '展开' : '收起'}} <text
									class="iconfont icon-jiantou_liebiaozhankai"></text></view>


							<block v-if="vsfnr==1">
										<view class="uprzimgcon">
											<view class="zzx sf1">
												<view class="con">
													<view class="vt1">
														<block v-if="djdata.rzimg1">
															<image :src='staticsfile + djdata.rzimg1'></image>
														</block>
														<block v-else>
															<image src="/static/images/rzupimg1.png"></image>
														</block>
													</view>
													<view class="vt2">身份证头像面</view>
													<view class="clear"></view>
												</view>
											</view>
											<view class="zzx sf2">
												<view class="con">
													<view class="sf2">
														<block v-if="djdata.rzimg2">
															<image :src='staticsfile + djdata.rzimg2'></image>
														</block>
														<block v-else>
															<image src="/static/images/rzupimg2.png"></image>
														</block>
													</view>
													<view class="vt2">身份证国徽面</view>
												</view>
											</view>
											<view class="clear"></view>
										</view>


										<view class="rowx" style="margin-top:20upx;" v-if="djdata.khsfzhao">
											<view class="tit"><text class="redst"></text>身份证号</view>
											<view class="inpcon">
												<view class="inp">{{djdata.khsfzhao}}</view>
											</view>
											<view class="clear"></view>
										</view>

										<view class="rowx" style="margin-top:15upx;position: relative;" v-if="djdata.isrlsb==1">

											<view class="tit"><text class="redst"></text>人脸识别</view>
											<view class="inpcon">
												<view class="inp">
													{{djdata.isrlsb==1 ? '需要' : '不需要'}}

												</view>
											</view>
											<view class="clear"></view>
										</view>
                            </block>


						</view>
					</view>

					<view class="maxcon">
						<view class="vstit" style="font-weight:400;margin-bottom:25upx">单据备注说明 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

						<view class="bzcon">
							<textarea name="remark" class="beizhu" v-model="remark"></textarea>
						</view>

					</view>

				</view>




				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="toback" style="background:#ddd;">
								<text>上一步</text>
							</button>
						</view>
						<view class="bomsfright">
							<button class="bomsubbtn" form-type="submit">
								<text>提交审核</text>
							</button>
						</view>
						<view class="clear"></view>
					</view>
				</view>


			</form>








		</view>

		<view style="height:120upx"></view>


		<tn-popup v-model="qtfymxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他收费明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtfymxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtfymxspname}}
				</view>
				<view class="qtfylist">

					<block v-if="qtfydata.length>0">

						<block v-for="(item,index) in qtfydata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">

										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他收费</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="qtcbmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他成本明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtcbmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtcbmxspname}}
				</view>
				<view class="qtcblist">

					<block v-if="qtcbdata.length>0">
						<block v-for="(item,index) in qtcbdata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtcbvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他成本</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>

	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				spzongjia: 0,
				clicksta: false,
				remark: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid: 0,

				qtcbmxshow: false,
				qtcbmxspname: '',
				qtcbdata: '',
				thqtcbvid: 0,
				vsfnr: 0,
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}
		},

		methods: {

			toback() {
				uni.navigateBack();
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					et: 2,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdeditx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.djshop = res.data.djshop;
							that.remark = res.data.djdata.remark;
							that.spzongjia = res.data.spzongjia;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},


			previewImage: function (e) {
					  let that = this;
					  let src = e.currentTarget.dataset.src;
					  let picarr=e.currentTarget.dataset.picarr;
					  let imgarr=[];
					  picarr.forEach(function(item,index,arr){
						  imgarr[index] = that.staticsfile+item;
					  });
					  wx.previewImage({
						  current: src,
						  urls: imgarr
					  });
			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},


			formSubmit(e) {
				let that = this;
				let pvalue = e.detail.value;
				uni.showModal({
					title: '提交确认',
					content: '确认提交审核吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'djid': that.djid,
								'remark': pvalue.remark,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdtjsh', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													// uni.redirectTo({
													// 	url:'/pages/work/rukudan/index'
													// })
													uni.setStorageSync('thupdpgllist', 1);
													uni.navigateBack({
														delta: 3
													});

												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},





			qtfymx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtfymxspname = spname;
				this.getqyfylist(spid);
			},
			qtfysetvxq(e) {
				let thqtfyvid = e.currentTarget.dataset.id;
				if (thqtfyvid == this.thqtfyvid) {
					this.thqtfyvid = 0;
				} else {
					this.thqtfyvid = thqtfyvid;
				}
			},
			getqyfylist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtfylist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtfydata = res.data.qtfydata;
							that.qtfymxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			qtcbmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtcbmxspname = spname;
				this.getqycblist(spid);
			},
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}

			},
			getqycblist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtcblist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtcbdata = res.data.qtcbdata;
							that.qtcbmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			setvzz() {
				if (this.vsfnr == 0) {
					this.vsfnr = 1;
				} else {
					this.vsfnr = 0;
				}
			},



		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
	}

	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .viewxqcon {
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		margin-bottom: 10upx;
		margin-top: 15upx;
		text-align: right;
		font-size: 24upx;
	}

	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 0upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}


	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.zjzkicon {
		position: absolute;
		right: 25upx;
		top: 25upx;
	}
</style>