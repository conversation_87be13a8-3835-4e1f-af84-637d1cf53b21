<template>
	<view class="pages-a tn-safe-area-inset-bottom" >

    
    
    </view>
</template>

<script>

  export default {
    data(){
      return {
		 
      }
    },
	
	beforeCreate() {
	   let isloing = uni.getStorageSync('tokendata')
	   if (isloing){
	   	  uni.reLaunch({ url: '/pages/index/index' });
	   }else{
		  uni.reLaunch({ url: '/pages/public/login' });
	   }
	},
	
	mounted() {
		
	},
	
    methods: {
	
    }
  }
</script>

<style lang="scss" scoped>

  .pages-a {
  	  max-height: 100vh;
  }
  
</style>

