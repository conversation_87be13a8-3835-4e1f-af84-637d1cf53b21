<template>
	<view class="app">
		
		<tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
				<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				  <text  >收银台</text>
				</view>
		</tn-nav-bar>
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		<view class="price-box">
			<text>支付金额</text>
			<text class="price">{{zongjia}}</text>
			<view style="margin-top:10px">{{odnum}}</view>
		</view>

		<view class="pay-type-list">
			
			    <view class="type-item b-b" @click="changefkfs(5)">
			    	<text class="icon iconfont icon-zhifubao1" style="color:#06b4fd"></text>
			    	<view class="con">
			    		<text class="tit">支付宝</text>
			    		<text>支付宝支付</text>
			    	</view>
			    	<label class="radio">
			    		<radio value="" color="#FFD427" :checked='fkfs == 5' />
			    		</radio>
			    	</label>
			    </view>
			
				<view class="type-item b-b" @click="changefkfs(4)">
					<text class="icon iconfont icon-weixinzhifu2" style="color:#07c160"></text>
					<view class="con">
						<text class="tit">微信支付</text>
						<text>使用微信支付</text>
					</view>
					<label class="radio">
						<radio value="" color="#FFD427" :checked='fkfs == 4'  />
						</radio>
					</label>
				</view>
			
			
				
				<view class="type-item b-b" @click="changefkfs(6)">
					<text class="icon iconfont icon-yuezhifu" style="color:#ff6600"></text>
					<view class="con">
						<text class="tit">线下支付</text>
						<text>使用线下支付</text>
					</view>
					<label class="radio">
						<radio value="" color="#FFD427" :checked='fkfs == 6' />
						</radio>
					</label>
				</view>
				
	
		</view>
		

		
		<view class="tn-flex tn-footerfixed" style="z-index:900;">
		  <view class="tn-flex-1 justify-content-item tn-text-center">
			<button class="bomsubbtn"  @click="confirm">
			  <text>确认支付</text>
			</button>
		  </view>
		</view>
		
		<view style="height:160upx"></view>
			
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				fkfs:0,
				odid:0,
				oddata: '',
				zongjia: 0,
				ispaytype4: 0,
				odtb: 0,
				isbdxcx: 0,
				odnum:''
			};
		},
		computed: {
		
		},
		onLoad(options) {
			 let that=this;
			 let odnum=options.odnum;
			 that.odnum=odnum;
			
			// #ifdef MP-WEIXIN
			  that.MploadData();
			// #endif
			
			// #ifdef APP-PLUS
			  that.ApploadData();
			// #endif
			
		},
		onShow(){
			let that=this;
		},
		methods: {
			//选择支付方式
			changefkfs(type) {
				this.fkfs = type;
			},
			
			MploadData(){
				let that=this;
				let da={
					odnum:that.odnum,
					ptsys:2
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/cash/index', da)
					  .then(res => {
						
						uni.hideNavigationBarLoading();
						if(res.errcode==0){
								that.fkfs = res.data.fkfs;
								that.zongjia = res.data.zongjia;
								that.odtb = res.data.odtb;
								that.isbdxcx = res.data.isbdxcx;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack()
								}
							})
						}
					  })
			},
			
			ApploadData(){
				let that=this;
				let da={
					odnum:that.odnum,
					ptsys:1
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/cash/index', da)
					  .then(res => {
						
						uni.hideNavigationBarLoading();
						if(res.errcode==0){
								that.fkfs = res.data.fkfs;
								that.zongjia = res.data.zongjia;
								that.odtb = res.data.odtb;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack()
								}
							})
						}
					  })
			},
			
			
			
			//确认支付
			confirm: async function() {
				let that=this;
				let fkfs=that.fkfs;
				
				
				if(fkfs==3){
					  let da={
					  	odnum:that.odnum,
					  	fkfs:fkfs 
					  };
					  uni.showNavigationBarLoading();
					  this.$req.post('/v1/cash/topay', da)
					  	  .then(res => {
					  		
					  		uni.hideNavigationBarLoading();
					  		if(res.errcode==0){
					  				uni.redirectTo({
					  					url: '/pages/cash/paysuccess'
					  				})
					  		}else{
					  			uni.showModal({
					  				content: res.msg,
					  				showCancel: false,
					  				success() {
					  				 
					  				}
					  			})
					  		}
					  })
				}
				
				if(fkfs==4){
		          
					if(that.isbdxcx!=1){
						uni.login({
						        provider: 'weixin',
						        success: function (loginRes) {
										  let da={
											code:loginRes.code
										  };
										  uni.showNavigationBarLoading();
										  that.$req.post('/v1/muser/paybdopenid', da)
											  .then(res => {
												uni.hideNavigationBarLoading();
												if(res.errcode==0){
													that.wxpay();
												}else{
													uni.showModal({
														content: res.msg,
														showCancel: false,
														success() {
														 
														}
													})
												}
										  })
						        }
						});
						
					}else{
						that.wxpay();
					}
					
				}
				
				if(fkfs==5){
					that.alipay();
				}
				
				
				
			},
			
			
			wxpay(){
				let that=this;
				let fkfs=that.fkfs;
				let da={
					odnum:that.odnum,
					fkfs:fkfs 
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/cash/topay', da)
					  .then(res => {
					
						uni.hideNavigationBarLoading();
						if(res.errcode==0){
							    let odnum=res.data.odnum;
								let odtb=res.data.odtb;
								let paypara=res.data.paypara;
							    uni.requestPayment({
									'timeStamp': paypara.timeStamp,
									'nonceStr': paypara.nonceStr,
									'package': paypara.package,
									'signType': 'MD5',
									'paySign': paypara.paySign,
									'success': function (res) {
									
										uni.showToast({
											title: '支付成功',
											icon: 'none',
											duration: 1000,
											success: function () {
												setTimeout(function () {
													uni.redirectTo({
														url: '/pages/cash/paysuccess'
													})
												}, 1000);
											}
										});
									
									
									},
									'fail': function (res) {
										
									},
									'complete': function (res) {
									
									}
								});
							
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
								 
								}
							})
						}
				})
				
			},
			
			alipay(){
				let that=this;
				let fkfs=that.fkfs;
				let da={
					odnum:that.odnum,
					fkfs:fkfs 
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/cash/topay', da)
					  .then(res => {
					
						uni.hideNavigationBarLoading();
						if(res.errcode==0){
							    let odnum=res.data.odnum;
								let odtb=res.data.odtb;
								let orderInfo=res.data.orderInfo;
							   
							   uni.requestPayment({
							     provider: 'alipay',
							     orderInfo: orderInfo,
							     success: (e) => {
							       if (e.errMsg == 'requestPayment:ok') {
							         uni.showToast({
							         	title: '支付成功',
							         	icon: 'none',
							         	duration: 1000,
							         	success: function () {
							         		setTimeout(function () {
							         			uni.redirectTo({
							         				url: '/pages/cash/paysuccess'
							         			})
							         		}, 1000);
							         	}
							         });
							       }
							     },
							     fail: (e) => {
							       if (e.errMsg == 'requestPayment:fail canceled') {
									 uni.showToast({
									 	title: '取消支付',
									 	icon: 'none'
									 });
							       } else {
							         // this.$message.info("eeerrr: " + e.errMsg);
									 uni.showToast({
									 	title: '支付失败,请稍后重试',
									 	icon: 'none'
									 });
							       }
							     },
							     complete: () => {
							  
							     },
							   });
							
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
								 
								}
							})
						}
				})
			},
			
			
			
		}
	}
</script>

<style lang='scss'>
	.app {
		width: 100%;
	}

	.price-box {
		background-color: #fff;
		height: 265upx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		font-size: 28upx;
		color: #909399;

		.price{
			font-size: 50upx;
			color: #303133;
			margin-top: 12upx;
			&:before{
				content: '￥';
				font-size: 40upx;
			}
		}
	}

	.pay-type-list {
		margin-top: 20upx;
		background-color: #fff;
		padding-left: 60upx;
		
		.type-item{
			height: 120upx;
			padding: 20upx 0;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-right: 60upx;
			font-size: 30upx;
			position:relative;
		}
		
		.icon{
			width: 100upx;
			font-size: 52upx;
		}
		.icon-erjiye-yucunkuan {
			color: #fe8e2e;
		}
		.icon-weixinzhifu {
			color: #36cb59;
		}
		.icon-alipay {
			color: #01aaef;
		}
		.tit{
			font-size: $font-lg;
			color: $font-color-dark;
			margin-bottom: 4upx;
		}
		.con{
			flex: 1;
			display: flex;
			flex-direction: column;
			font-size: $font-sm;
			color: $font-color-light;
		}
	}
	.mix-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 630upx;
		height: 80upx;
		margin: 80upx auto 30upx;
		font-size: $font-lg;
		color: #fff;
		background-color: $base-color;
		border-radius: 10upx;
		box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);
	}

</style>
