import Vue from 'vue'
import store from './store'
import App from './App'
import apconfig from './common/apconfig.js'
import { req } from './common/request/index.js' // 全局挂载引入，配置相关在该index.js文件里修改
import  * as tool from './common/tool.js'

import { addPermisionInterceptor, removePermisionInterceptor } from '@/uni_modules/x-perm-apply-instr/js_sdk/index.js'

addPermisionInterceptor('chooseImage', '为了修改个人头像和发布信息图片视频等, 我们需要申请您设备的相机和存储权限')
addPermisionInterceptor('chooseVideo', '为了发布信息图片视频等, 我们需要申请您设备的相机和存储权限')
addPermisionInterceptor('saveImageToPhotosAlbum', '为了保存图片到手机相册, 我们需要申请您设备的存储权限')
addPermisionInterceptor('getLocation', '为了根据您的位置展示职位, 我们需要申请您设备的位置权限')
addPermisionInterceptor('makePhoneCall', '为了联系客服/用户/咨询等, 我们需要申请您设备的拨打电话权限')
addPermisionInterceptor('getRecorderManager', '为了使用语言消息功能等, 我们需要申请您设备的麦克风权限')
addPermisionInterceptor('startLocationUpdate', '为了根据您的位置展示信息, 我们需要申请您设备的位置权限')
addPermisionInterceptor('scanCode', '为了拍照上传头像等, 我们需要申请您设备的相机权限')

const prePage = ()=>{
	let pages = getCurrentPages();
	let prePage = pages[pages.length - 2];
	// #ifdef H5
	return prePage;
	// #endif
	return prePage.$vm; 
}


// 引入全局TuniaoUI
import TuniaoUI from 'tuniao-ui' 
Vue.use(TuniaoUI)

// 引入TuniaoUI提供的vuex简写方法
let vuexStore = require('@/store/$tn.mixin.js')
Vue.mixin(vuexStore)

// 引入TuniaoUI对小程序分享的mixin封装
let mpShare = require('tuniao-ui/libs/mixin/mpShare.js')
Vue.mixin(mpShare)

Vue.config.productionTip = false
Vue.prototype.$fire = new Vue();
Vue.prototype.$store = store;
Vue.prototype.$api = { prePage};


Vue.prototype.$apconfig = apconfig;
Vue.prototype.$apiurl = apconfig.apiurl;
Vue.prototype.$staticsfile = apconfig.staticsfile;
Vue.prototype.$hybridserverurl = apconfig.hybridserverurl;
Vue.prototype.$req = req
Vue.prototype.$tool = tool

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()