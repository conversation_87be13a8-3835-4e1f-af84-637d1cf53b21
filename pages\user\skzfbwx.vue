<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>收款支付宝</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">



					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit" style="font-weight:600"><text class="redst"></text>支付宝收款相关</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>账号名称</view>
								<view class="inpcon">
									<view class="inp"><input class="input" type="text" name="skzfbname"
											v-model="zfbwxdata.skzfbname" placeholder="请输入收款账号名称"
											placeholder-class="placeholder" /></view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>收款账号</view>
								<view class="inpcon">
									<view class="inp"><input class="input" type="text" name="skzfbhao"
											v-model="zfbwxdata.skzfbhao" placeholder="请输入收款支付宝账号"
											placeholder-class="placeholder" /></view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx">
								<view class="tit"><text class="redst"></text>支付宝收款码</view>
								<view class="inpcon">
									<view class="inp">
										<view class="delth" @click="delewm" data-type="1" v-if="skzfbewm">清空图片</view>
										<view class="skewmcon" @click="upskzfbewm">
											<block v-if="skzfbewm">
												<image :src='staticsfile + skzfbewm' mode="widthFix"></image>
											</block>
											<block v-else>
												<image src="/static/images/uppicaddx.png" mode="widthFix"></image>
											</block>
										</view>
									</view>
								</view>
								<view class="clear"></view>
							</view>




						</view>
					</view>




				</view>

				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" form-type="submit">
							<text>确认提交</text>
						</button>
					</view>
				</view>
			</form>








		</view>

		<view style="height:120upx"></view>





	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				html: '',
				status: 0,
				img_url: [],
				img_url_ok: [],
				skzfbewm: '',
				skwxewm: '',
				sfzhao: '',
				zfbwxdata: [],
			}
		},

		onLoad(options) {
			let that = this;
			this.loadData();
		},

		onShow() {
		
		},

		methods: {

			loadData() {
				let that = this;
				let da = {}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/muser/skzfbwx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.zfbwxdata = res.data.zfbwxdata;
							that.skzfbewm = res.data.zfbwxdata.skzfbewm;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			delewm(e){
				let type=e.currentTarget.dataset.type;
			
					this.skzfbewm='';
			
						
			},
			upskzfbewm() {
				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.skzfbewm = res.data.fname;
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},

			formSubmit: function(e) {
				let that = this;

				let pvalue = e.detail.value;
				
				if (!pvalue.skzfbname) {
					  uni.showToast({
						title: '请输入支付宝名称',
						icon: 'none',
						duration: 1000
					  });
					  return false;
				}
				if (!pvalue.skzfbhao) {
					  uni.showToast({
						title: '请输入支付宝账号',
						icon: 'none',
						duration: 1000
					  });
					  return false;
				}

				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'skzfbhao': pvalue.skzfbhao ? pvalue.skzfbhao : '',
					'skzfbname': pvalue.skzfbname ? pvalue.skzfbname : '',
					'skzfbewm': that.skzfbewm ? that.skzfbewm : '',
				}
				this.$req.post('/v1/muser/skzfbwxsave', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							uni.showToast({
								 title: res.msg,
								 icon: 'none',
								 duration: 1000,
								 success(res) {
									 setTimeout(function() {
										uni.navigateBack();
									 }, 1000);
								 }
							})
						} else {
							that.clicksta = false;
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},



		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #eee
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 115px);
		font-size: 28upx;
		min-height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}
	.rowx:last-child{border-bottom:0;}
    .rowx .delth{position: absolute;right:30upx;top:30upx;color:#888}
 


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}

    .skewmcon{padding-top: 15upx;float:left}
	.skewmcon image {
		width: 160upx;
		display: block;
		border-radius: 10upx;
	}
</style>