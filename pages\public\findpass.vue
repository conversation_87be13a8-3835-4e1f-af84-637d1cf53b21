<template>
	<view class="login">
		
		<tn-nav-bar fixed alpha customBack>
		  <view slot="back" class='tn-custom-nav-bar__back2' >
			<text class='icon iconfont icon-fanhui' @click="goBack()"></text>
		  </view>
		</tn-nav-bar>
		
		<view class="login">
		
				  <view class="login__bg login__bg--top">
					<image class="bg" src="/static/images/loginbg.jpg" mode="widthFix"></image>
				  </view>
		
		          <view class="topk"></view>
		
		
					<view class="login-type-content">
						
						
						<view class="titwz">修改登录密码</view>
						
						
						<view class="main">
						
							
					
								<view class="login-type-form">
									<view class="input-item">
										<input
												class="login-type-input"
												type="text"
												name="mobile"
												v-model="mobile"
												placeholder="手机号码"
												maxlength="11"
										/>
									</view>
								
									
									<view class="input-item input-item-sms-code" >
										<input class="login-type-input"	type="number"  name="mscode"	v-model="mscode"  placeholder="手机验证码"	maxlength="6"	/>
										<button :disabled='disabled'	class="sms-code-btn" @tap.stop="getVerificationCode()">
											<text>{{codename}}</text>
										</button>
									</view>
									
								
									<view class="input-item" >
										<input	class="login-type-input"	:password="!showPassword" v-model="password"	placeholder="新密码"	maxlength="20" /> 
										<view class="tricon" @click="showPassword = !showPassword">
										  <view :class="[showPassword ? 'iconfont icon-xianshimima' : 'iconfont icon-buxianshimima']"></view>
										</view>
									</view>
									<view class="input-item" >
										<input	class="login-type-input"	:password="!showPassword" v-model="repassword"	placeholder="确认密码"	maxlength="20" /> 
										<view class="tricon" @click="showPassword = !showPassword">
										  <view :class="[showPassword ? 'iconfont icon-xianshimima' : 'iconfont icon-buxianshimima']"></view>
										</view>
									</view>
									
								</view>
						
							<view>
								<button
										class="confirm-btn bg-active tn-main-gradient-indigo" 
										:disabled="btnLoading"
										:loading="btnLoading"
										@tap="toLogin"
								>
									确认修改密码
								</button>
							</view>
							
							<view class="dlmmcon" @click="gotologin">
									账号登录
							</view>
							
							
						    
							
						</view>
					</view>
				
		 </view>		
			
	</view>
</template>
<script>
	
	
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				logininfo:'',
				mobile: '',
				password: '',
				repassword: '',
				disabled:false,
				btnLoading: false,
				userInfo: null,
				appName: '',
				ydxysta:false,
				clientid:'',
				appver:'',
				apptype:0,
				loginByPass:true,
				codename:'获取验证码',
				mscode:'',
				codeSeconds: 0, // 验证码发送时间间隔
				smsCodeBtnDisabled: true,
				// 是否显示密码
				showPassword: false,
				// 倒计时提示文字
				tips: '获取验证码',
			};
		},
		
		
		onLoad(options) {
			// this.loadData();
		},
		
		watch: {
		
		},
		onShow() {
			let that=this;
			
			// #ifdef APP-PLUS
			  let clientid=plus.push.getClientInfo().clientid;
			   that.clientid=clientid ? clientid : ''; 
			  
			   plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
			   that.appver=widgetInfo.version;
			   let platform = uni.getSystemInfoSync().platform
			     if(platform=='android'){
					 that.apptype=1;
				 }else{
					 that.apptype=2;
				 }
			  });
			  
			// #endif
		},
		methods: {
			
			goBack(){
				 uni.navigateBack()
			},
			
			// 切换登录方式
			gotologin() {
		         uni.navigateBack()
			},
		
			// 统一跳转路由
			navTo(url) {
		      uni.navigateTo({ url });
			},
			
			// 提交表单
			async toLogin() {
			      let that=this;
				  
				
					if (this.mobile.length != 11) {
						uni.showToast({ 
							icon: 'none',
							title: '请输入手机号'
						});
						return false;
					   }
						if (this.mscode.length <= 0) {
							uni.showToast({
								icon: 'none',
								title: '请输入验证码'
							});
							return false;
						}
								
								  
					
					if (this.password.length < 6  ) {
						uni.showToast({
							icon: 'none',
							title: '密码长度不应少于6位'
						});
						return;
					}	
					
					
					if (this.repassword.length <= 0) {
						uni.showToast({
							icon: 'none',
							title: '请再次输入登录密码'
						});
						return;
					}
					if (this.password != this.repassword) {
						uni.showToast({
							icon: 'none',
							title: '两次输入的密码不匹配！'
						});
						return;
					}
					
				  uni.showLoading({title: ''})
				  
				  let da={
						mobile: this.mobile,
						mscode: this.mscode,
						password: this.password,
				  };
				  this.$req.post('/v1/login/findpassvalidata', da)
				  		.then(res => {
				  			uni.hideLoading();
							console.log(res);
					
				  			if(res.errcode!=0){
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg
				  				});
				  			}else{
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg,
				  					success() {
				  						setTimeout(function(){
				  							uni.redirectTo({
				  							   url: '/pages/public/login',
				  							})
				  						},1000)
				  					}
				  				});
				  			}
				  			
				  		})
				  		.catch(err => {
				  		
				  		})
				  
			},
			
			getCode(){
			    let mobile = this.mobile;
			    let that = this;
			    // var myreg = /^(14[0-9]|13[0-9]|15[0-9]|17[0-9]|18[0-9])\d{8}$$/;
			    if (mobile == "") {
			      uni.showToast({
			        title: '手机号不能为空',
			        icon: 'none',
			        duration: 1000
			      });
			      return false;
			    }
			    if (mobile.length!=11) {
			      uni.showToast({
			        title: '请输入正确的手机号',
			        icon: 'none',
			        duration: 1000
			      });
			      return false;
			    }
			
			      // 发起网络请求
			      let da = {
			        'mobile': mobile,
			        'fstype': 3
			      };
				  uni.showLoading({ 'title': '' });
				  this.$req.post('/v1/login/getmscode', da)
				  	.then(res => {
				  
						uni.hideLoading();
				  		if (res.errcode == 0) {
							
				  			 that.disabled= true;
				  			 uni.showToast({
				  			   title: res.msg,
				  			   icon: 'none', 
				  			   duration: 1000,
				  			   success: function () {
				  			     var num = 61;
				  			     var timer = setInterval(function () {
				  			       num--;
				  			       if (num <= 0) {
				  			         clearInterval(timer);
				  			           that.codename='重新发送';
				  			           that.disabled= false;
				  			       } else {
									    that.codename= num + "s"
				  			       }
				  			     }, 1000)
				  			 			
				  			 			
				  			   }
				  			 })
				  				
				  		} else {
				  			uni.showToast({
				  			  title: res.msg,
				  			  icon: 'none'
				  			})
				  		}
				  
				  	})
			 
			  },
			  //获取验证码
			  getVerificationCode() {
			    this.getCode();
			  },
			  
			
		}
	};
</script>
<style lang="scss">
	page {
		background: #fff;
	}

	.titwz{font-size:44upx;font-weight: 600;margin-bottom:100upx}
	.login {
		
		position: relative;
		height: 100%;
		z-index: 1;
		
		
		/* 背景图片 start */
		&__bg {
		  z-index: -1;
		  position: fixed;
		  
		  &--top {
		    top: 0;
		    left: 0;
		    right: 0;
		    width: 100%;
		    
		    .bg {
		      width: 750upx;
		      will-change: transform;
		    }
		  }
		
		}
		/* 背景图片 end */
		
		.topk{height:280upx}
		
		.login-type-content {
			margin:0 56upx;
			position: relative;
			.main {
				position: absolute;
				width: 100%;
				top: 160upx;
			    .login-type-form {
							.input-item {
								position: relative;
								margin-bottom: 32upx;
								font-size:32upx;
								color:#7F7F7F;
								.login-type-input {
									height: 112rpx;
									line-height: 112rpx;
									background: #fff;
									border-radius: 24rpx;
									padding:0 30upx;
								}
							}
							.tricon{position: absolute;right:32upx;top:35upx;z-index:100;}
							.tricon .iconfont{font-size:44upx}
				}
				.confirm-btn {
				  margin-top: 70upx;
				  height: 112upx;
				  line-height: 112upx;
				  border-radius:24upx;
				  background: #FFD427;
				  color:#333;
				}
			}
			
			.dlmmcon{height: 28upx;font-size: 26upx;line-height:28upx;color: #7F7F7F;margin-top:53upx;text-align:center}
		}
	
	}
	
	
	// 发送验证码样式
	.input-item-sms-code {
	
	  .sms-code-btn {
	      position: absolute;right:28upx;top:22upx;z-index: 200;background:#fff;color: #3366FF;font-size:28upx;padding:0
	  }
	
	  .sms-code-resend {
	    color: #666;background:#fff;
	  }
	}
	
	
	
	/* 胶囊*/
	 .tn-custom-nav-bar__back2 {
	
	   height: 100%;
	   position: relative;
	   display: flex;
	   justify-content: space-evenly;
	   align-items: center;
	   box-sizing: border-box;
	   font-size: 18px;
	      width: 140upx;
	   .icon {
	     display: block;
	     flex: 1;
	     margin: auto;
	     text-align: center;
		  font-size:45upx
	   }
	   
	  
	 }
		
	
	
</style>
