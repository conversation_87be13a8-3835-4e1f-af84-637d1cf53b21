<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>企业通知管理</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="jstypedata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333"  :bold="true" :fontSize="28"
						:gutter="20" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF"
						:height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;">
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
				</view>
			</view>
		</view>

		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>



		<block v-if="listdata.length>0">



			<view class="cwcon">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="cwli  ">
						<view class="nrcon">
							<view class="info">
							    <view class="vstatus" :class="'ys'+item.vstatus">{{item.vstatusname}}</view>
								<view class="title"></text>{{item.title}}</view>
							</view>
							<view class="inf2">
								<view >创建时间：{{item.addtime}}</view>
								<view >通知对象：{{item.dxtypetxt}}</view>
								<view v-if="item.mdname">通知店铺：{{item.mdname}}</view>
								<block v-if="item.vstatus==1"> 
									<view class="setbtn" @click="chehui" :data-tzid='item.id'  :data-jstype='item.jstype'  >撤回</view>
									<view class="setbtn2" @click="chakan" :data-tzid='item.id'  :data-jstype='item.jstype' >查看</view>
								</block>
								<block v-else>	
									<view class="setbtn" @click="edittz" :data-tzid='item.id'  :data-jstype='item.jstype'  >编辑</view>
									<view class="setbtn2" @click="deltz" :data-tzid='item.id'  :data-jstype='item.jstype' >删除</view>
								</block>
							</view>
							
				 
							<view class="clear"></view>
						</view>
					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关数据</view>
			</view>
		</block>





		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">通知搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入通知标题'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>

	
	     <view class="tn-flex tn-footerfixed">
	     	<view class="tn-flex-1 justify-content-item tn-text-center">
	     		<button class="bomsubbtn" style="width:50%;margin:0 auto;border-radius:100px;font-size:28upx"
	     			@click="addkhfs"   >
	     			<text class="iconfont icon-jia" style="margin-right:10upx;font-size:28upx"></text>
	     			<text>发通知</text>
	     		</button>
	     	</view>
	     </view>
	

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				jstype: 1,
				index: 1,
				thvid: 0,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				jstypedata: [
					{
						jstype: 1,
						name: '员工通知'
					},
					{
						jstype: 2,
						name: '客户通知'
					},
					
				],
				show3: false,
			}
		},
		onLoad(options) {
			let that = this;
			that.page = 0;
			that.listdata = [];
			that.isloading = false;
			that.loadingType = -1;
			that.loaditems();
		},
		onShow() {
			let that = this;
			if(uni.getStorageSync('thuplist')==1){
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thuplist',0)
			}
		},
		onReady() {

		},
	
		methods: {
			chakan(e) {
				let tzid=e.currentTarget.dataset.tzid;
				let jstype=e.currentTarget.dataset.jstype;
				uni.navigateTo({
					url: './view?id='+tzid
				})
			},
			edittz(e) {
				let tzid=e.currentTarget.dataset.tzid;
				let jstype=e.currentTarget.dataset.jstype;
				if(jstype==1){
					uni.navigateTo({
						url: './edit?tzid='+tzid+'&jstype='+jstype
					})
				}else{
					uni.navigateTo({
						url: './editkh?tzid='+tzid+'&jstype='+jstype
					})
				}
			
			},
			
			chehui(e){
				let that=this;
				let tzid=e.currentTarget.dataset.tzid;
				let jstype=e.currentTarget.dataset.jstype;
				let tipx='该操作将从员工接收者消息箱中撤回！确认撤回吗？';
				if(jstype==2){
					tipx='该操作将从客户接收者消息箱中撤回！确认撤回吗？';
				}
				uni.showModal({
					title: '撤回确认',
					content: tipx,
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								tzid: tzid,
								jstype: jstype,
							}
							uni.showLoading({title: ''})
							that.$req.post('/v1/qymsgtz/chehui', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
														that.page = 0;
														that.listdata = [];
														that.isloading = false;
														that.loadingType = -1;
														that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
						}
					}
				})
				
			},
			
			deltz(e){
				let that=this;
				let tzid=e.currentTarget.dataset.tzid;
				let jstype=e.currentTarget.dataset.jstype;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								tzid: tzid,
								jstype: jstype,
							}
							uni.showLoading({title: ''})
							that.$req.post('/v1/qymsgtz/tzdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
														that.page = 0;
														that.listdata = [];
														that.isloading = false;
														that.loadingType = -1;
														that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
						}
					}
				})
			},
		
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let jstype = that.jstypedata[index].jstype;
				console.log(jstype);
				that.thkw = '';
				that.current = index;
				that.jstype = jstype;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					jstype: that.jstype,
					kw: that.thkw ? that.thkw : '',
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/qymsgtz/tzlist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},

			addkhfs:function(){
					  let that=this;
					  uni.showActionSheet({
						  itemList: ['新增员工通知','新增客户通知'],
						  success: function (res)
						  {
							var index = res.tapIndex;
							if(index==0){
								uni.navigateTo({
									url:'./edit'
								})
							}
							if(index==1){
								uni.navigateTo({
									url:'./editkh'
								})
							}
						 
						  },
					  });
			},






		}
	}
</script>

<style lang='scss'>
	.cwcon {
		padding: 10upx 20upx;
	}

	.cwli {
		position: relative;
		border-bottom: 1px solid #efefef;
		padding: 30upx;
		background: #fff;
		margin-bottom: 20upx;
		border-radius: 20upx;
	}

	.cwli .info {
		font-size: 28upx;
		line-height: 45upx;
		position: relative;
		padding-right:80upx;
	}
	.cwli .info .vstatus{position: absolute;right:0upx;}
	.cwli .info .vstatus.ys1{color:#4aac09}
	.cwli .info .vstatus.ys2{color:#ff0000}
	.cwli .zkbtn {
		color: #888
	}

	.cwli .des {
		border-top: 1px solid #eee;
		margin-top: 10upx;
		padding-top: 10upx;
		color:#888
	}
	
	.cwli .inf2{border-top:1px solid #eee;padding-top:20upx;margin-top:20upx;line-height:40upx;position: relative;color:#888;font-size:24upx}
	.cwli .setbtn{position: absolute;right:0;top:35upx;width: 100rpx;height: 50upx;line-height:50upx;background: #FFD427;border-radius: 100rpx;text-align: center;color:#333}
	.cwli .setbtn2{position: absolute;right:120upx;top:35upx;width: 100rpx;height: 50upx;line-height:50upx;background: #efefef;border-radius: 100rpx;text-align: center;color:#333}
	
	
</style>