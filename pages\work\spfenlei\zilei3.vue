<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="3000">
					 <view slot="back" class='tn-custom-nav-bar__back' >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >物品分类（四级）</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :gutter="30" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;" >
				<view class="item btnimg" @click="fladd"  :data-pid='0'><image src="/static/images/addbtn.png"></image></view>
			</view>
		  </view>  
		</view>
		
		
	    <view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 20 + 'px'}" ></view>

            <view class="pidcon">{{piddata.sortname}}</view>

		
			<block v-if="sortdata.length>0">
			
			
			    
						    <view class="fllist">
			
									<block v-for="(item,index) in sortdata" :key="index">
										
											<view class="item " >
											
												  <view class="status" @click="setsta" :data-sortid='item.id' v-if="item.sjid>0">
												      <view class="aa"><tn-switch v-model="item.status==1 ? true : false " activeColor="#FFD427"  inactiveColor="#7F7F7F" leftIcon="success" right-icon="close" change="ss"></tn-switch></view>
												  </view>
												
													<view class="tf1">
														<view class="tx"><image :src="staticsfile + item.smallpic"></image></view>
														<view class="xx">
															<view class="name">{{item.sortname}}</view>
															<view class="czcc">
																<view class="ite t2" @click="flsxedit" :data-sortid='item.id' >
																	<text class="iconfont icon-a-shezhi-shucaidanshezhi"></text> 属性
																</view>
																<view class="ite t3" @click="fldel" :data-sortid='item.id'   v-if="item.sjid>0">
																	<text class="iconfont icon-shanchu4"></text> 删除
																</view>
																<view class="ite t4" @click="fledit" :data-thsortid='item.id'  :data-thsortname='item.sortname'  :data-thsortsmallpic='item.smallpic'  :data-thsortpx='item.px' style="margin-right:0" v-if="item.sjid>0">
																	<text class="iconfont icon-bianji"></text> 编辑
																</view>
																<view class="clear"></view>
															</view>
														</view>
														<view class="clear"></view>
													</view>	
									
										
													
											</view>
																					
										
									</block>
							</view>
			
					
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关四级分类</view>
				</view>
			</block>
		    
	
	
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
			<view class="">
			  <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">分类搜索</view>
			  <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
				<input :placeholder="'请输入分类名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
			  </view>
			</view>
			<view class="tn-flex-1 justify-content-item  tn-text-center">
			  <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
				<text >确认提交</text>
			  </tn-button>
			</view>
		  </view>
		</tn-modal>
	
		
		<tn-modal v-model="show4" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
				
				<view class="imgconmt"  >
					   <view class='item' @click="uplogo" >
						  <block v-if="thsortsmallpic" >
							   <image :src='staticsfile + thsortsmallpic'></image>
						  </block>
						  <block v-else>
							   <image src='/static/images/uppicaddx.png'></image>
						  </block>	  
					   </view>
					   <view class="clear"></view>
				</view>
				
		     <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 30rpx 0 30rpx 0;">
		       <input type="text" :placeholder="'分类名称'" v-model="thsortname" name="sortname" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		     </view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 30rpx 0 30rpx 0;">
		        <input type="number" :placeholder="'分类排序'" v-model="thsortpx" name="px" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="submitfl" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				pid:0,
				piddata:'',
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				listdata:[],
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 1,name: '启用'},
					{sta: 2,name: '停用'},
				],
				show3:false,
				show4:false,
				sortdata:'',
				
				thsortid:'',
				thsortname:'',
				thsortsmallpic:'',
				thsortpx:100,
				
			}
		},
		onLoad(options) {
			let that=this;
		    let pid=options.pid ? options.pid : 0
			this.pid=pid;
		},
		onShow(){
				let that=this;
				that.loadData(); 
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				this.show3=false;
				that.loadData();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.loadData();
			},
			
			flsxedit(e){
				let sortid = e.currentTarget.dataset.sortid;
				uni.navigateTo({
					url:"./flsx?sortid="+sortid
				})
			},
			
			flzilei(e){
				let that=this;
				let sortid = e.currentTarget.dataset.sortid;
				uni.navigateTo({
					url:"./zilei2?pid="+sortid
				})
			},
		
			fladd(e){
				let that=this;
				that.thsortid = 0;
				that.thsortname = '';
				that.thsortsmallpic = '';
				that.thsortpx = 100;
				this.show4=true;
			},
			
			
			fledit(e){
				let that=this;
				
				let thsortid= e.currentTarget.dataset.thsortid;
				let thsortname= e.currentTarget.dataset.thsortname;
				let thsortsmallpic= e.currentTarget.dataset.thsortsmallpic;
				let thsortpx= e.currentTarget.dataset.thsortpx;
				
				that.thsortid=thsortid;
				that.thsortname=thsortname;
				that.thsortsmallpic=thsortsmallpic ? thsortsmallpic : '' ;
				that.thsortpx=thsortpx ? thsortpx : 100 ;
				
				this.show4=true;
			},
			
		
			
			// tab选项卡切换
				tabChange(index) {
					let that=this;
					let sta=that.stadata[index].sta;
					console.log(sta);
					that.thkw = '';
					that.current = index;
					that.sta = sta;
					that.loadData();
				},
				

				loadData(){
					  let that=this;
					  let da={
						  pid:that.pid,
						  sta:that.sta,
						  kw:that.thkw ? that.thkw : '' ,
					  }
					 uni.showLoading({title: ''})
					 this.$req.post('/v1/sjgl/spfenlei', da)
							 .then(res => {
								uni.hideLoading();
								 console.log(111,res);
								if(res.errcode==0){
									that.sortdata=res.data.sortdata;
									that.piddata=res.data.piddata ? res.data.piddata : '';
								}else{
									uni.showModal({
										content: res.msg,
										showCancel: false,
										success() {
											uni.navigateBack();
										}
									})
								}
					})
		        },
		
				uplogo(){
						   let that = this;
						   uni.chooseImage({
							   count: 1, //默认9
							   sizeType: ['original', 'compressed'],
							   success: (resx) => {
								  const tempFilePaths = resx.tempFilePaths;
								  
												  let da={
													  filePath:tempFilePaths[0],
													  name: 'file'
												  } 
												  uni.showLoading({title: '上传中...'})
												  this.$req.upload('/v1/upload/upfile', da)
														  .then(res => {
															uni.hideLoading();
															// res = JSON.parse(res);
															console.log(res);
															if(res.errcode==0){
																that.thsortsmallpic=res.data.fname;
																uni.showToast({
																	icon: 'none',
																	title: res.msg
																});
															}else{
																uni.showModal({
																	content: res.msg,
																	showCancel: false
																})
															}
												  
												  })
										
								   
							   }
						   });
					   
				},
		
		
		
					submitfl: function(e) {
										let that=this;
									
											if(!that.thsortname){
												uni.showToast({
													icon: 'none',
													title: '请输入分类名称',
												});
												return false;
											}
											if(!that.thsortpx){
												uni.showToast({
													icon: 'none',
													title: '请输入分类排序值',
												});
												return false;
											}
											
											  uni.showLoading({title: '处理中...'})
											  let da={
												'pid': that.pid ? that.pid : 0,
												'sortid': that.thsortid ? that.thsortid : 0,
												'sortpx': that.thsortpx ? that.thsortpx : 100,
												'sortname': that.thsortname ? that.thsortname : '',
												'smallpic': that.thsortsmallpic ? that.thsortsmallpic  : '',
											  }
											  
											  this.$req.post('/v1/sjgl/spfenleisave', da)
													  .then(res => {
														uni.hideLoading();
														console.log(res);
														if(res.errcode==0){
															uni.showToast({
																icon: 'none',
																title: res.msg,
																success() {
																	that.show4=false
																	that.thsortid='';
																	that.thsortname='';
																	that.thsortsmallpic='';
																	that.thsortpx= 100 ;
																	
																	setTimeout(function(){
																		that.loadData();
																	},500)
																	
																}
															});
														}else{
															uni.showModal({
																content: res.msg,
																showCancel: false
															})
														}
														
													  })
													  .catch(err => {
													
													  })
											  
				    },
							
					setsta(e){
						let that=this; 
						let sortid = e.currentTarget.dataset.sortid;
						let da = {
							'sortid': sortid
						}
						uni.showLoading({title: ''})
						that.$req.post('/v1/sjgl/spfenleisetsta', da)
							.then(res => {
								uni.hideLoading();
								if (res.errcode == 0) {
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function() {
												that.loadData();
											}, 1000);
										}
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false,
									})
								}
						})
						
						
					},
					
					fldel(e){
								   let that=this;
								   let sortid=e.currentTarget.dataset.sortid;
								   uni.showModal({
								   	title: '删除确认',
								   	content: '删除后不可恢复！确认删除吗？',
								   	success: function(e) {
								   		//点击确定
								   		if (e.confirm) {
								   
								   			let da = {
								   				sortid: sortid
								   			}
								   			uni.showLoading({title: ''})
								   			that.$req.post('/v1/sjgl/spfenleidel', da)
								   				.then(res => {
								   					uni.hideLoading();
								   					if (res.errcode == 0) {
								   						uni.showToast({
								   							icon: 'none',
								   							title: res.msg,
								   							success() {
								   								setTimeout(function() {
								   								   that.loadData();
								   								}, 1000);
								   							}
								   						});
								   					} else {
								   						uni.showModal({
								   							content: res.msg,
								   							showCancel: false,
								   						})
								   					}
								   				})
								   
								   
								   
								   		}
								   
								   	}
								   })
					},
					
					
					
					
					
					
					
					
					
					
					
					
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}


.imgconmt{width: 160upx;height:160upx;margin:0 auto;}
.imgconmt image{width: 160upx;height:160upx;border-radius:100px;}

.fllist{padding:32upx}
.fllist .item{margin-bottom:32upx;padding:28upx;border-radius:20upx;background:#fff;position: relative;}
.fllist .item .tx{float:left;}
.fllist .item .xx{float:right;width:calc(100% - 140upx);}
.fllist .item .tx image{width:120upx;height:120upx;display: block;border-radius:100px;}
.fllist .item .name{font-size: 28rpx;font-weight:600;height:60upx;line-height:60upx;}

.fllist .item .status{position: absolute;right:20upx;top:20upx;font-size:24upx}
.fllist .item .status .aa{float:left;margin-left:10upx;line-height:50upx;}

.fllist .czcc{text-align: center;font-size:24upx;background: #fafafa;padding:10upx;border-radius:3px 50px 50px 3px;margin-top:10upx}
.fllist .czcc .ite{float:left;width:33.33%;text-align: center;}
.fllist .czcc .ite .iconfont{font-size:24upx;margin-right:5upx;}
.fllist .czcc .ite.t1{}
.fllist .czcc .ite.t2{}
.fllist .czcc .ite.t3{}

.pidcon{background: #fff;padding:30upx 20upx;text-align: center;font-size:32upx;font-weight:700;border-radius:20upx;margin:0 32upx;}

</style>
