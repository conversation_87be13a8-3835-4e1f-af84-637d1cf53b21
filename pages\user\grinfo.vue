<template>
	<view class="tn-safe-area-inset-bottom">


		<tn-nav-bar fixed :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>个人设置</text>
			</view>
		</tn-nav-bar>

		<view>

			<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>



			<!-- #ifdef APP-PLUS -->
			<view class="tn-flex tn-flex-row-between tn-strip-bottom tn-padding" @click="upavatar">
				<view class="justify-content-item">
					<view class="tn-text-bold tn-text-lg">
						我的头像
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						点击更新头像
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="logo-pic tn-shadow">
						<view class="logo-image">
							<view class="tn-shadow-blur" style="width: 80rpx;height: 80rpx;">
								<image :src="mudata.avatar ? staticsfile + mudata.avatar : '/static/images/ava_def.png'"
									style="width: 80rpx;height: 80rpx;"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- #endif -->


			<!-- #ifdef MP -->
			<button class="avatarbutton" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
				<view class="tn-flex tn-flex-row-between tn-strip-bottom tn-padding">
					<view class="justify-content-item">
						<view class=" tn-text-lg">
							我的头像
						</view>
						<view class="tn-color-gray tn-padding-top-xs tn-text-md">
							点击更新头像
						</view>
					</view>
					<view class="justify-content-item tn-text-lg tn-color-grey">
						<view class="logo-pic tn-shadow">
							<view class="logo-image">
								<view class="tn-shadow-blur" style="width: 80rpx;height: 80rpx;">
									<image
										:src="mudata.avatar ? staticsfile + mudata.avatar : '/static/images/ava_def.png'"
										style="width: 80rpx;height: 80rpx;"></image>
								</view>
							</view>
						</view>
					</view>
				</view>
			</button>
			<!-- #endif -->





			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding" @click="showModal3"
				data-zdtype="1">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						昵称
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						{{mudata.nickname ? mudata.nickname : '未设置'}}
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>

			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding" @click="showModal3"
				data-zdtype="2">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						姓名
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						{{mudata.realname ? mudata.realname : '未设置'}}
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>
			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding" @click="setsex">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						性别
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						{{mudata.sex ? mudata.sex : '未设置'}}
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>




			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding" @click="showModal3"
				data-zdtype="4">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						详细地址
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						{{mudata.dizhi ? mudata.dizhi : '未设置'}}
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>

			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding"
				@click="tn('/pages/user/usbank/index')">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						收款银行卡
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						用于提现、报销收款
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>

			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding" @click="tn('/pages/user/skzfbwx')">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						收款支付宝
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						用于提现、报销收款
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>
			<view class="tn-flex tn-flex-row-between tn-strip-bottom-min tn-padding"
				@click="tn('/pages/user/skzfbwxm')">
				<view class="justify-content-item">
					<view class=" tn-text-lg">
						收款微信
					</view>
					<view class="tn-color-gray tn-padding-top-xs">
						用于提现、报销收款
					</view>
				</view>
				<view class="justify-content-item tn-text-lg tn-color-grey">
					<view class="tn-icon-right tn-padding-top"></view>
				</view>
			</view>


			<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
				<view class="custom-modal-content">
					<view class="">
						<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">请输入{{zdtxt}}</view>
						<view class="tn-bg-gray--light"
							style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
							<input :placeholder="'请输入'+zdtxt" v-model="zdvalue" name="input"
								placeholder-style="color:#AAAAAA" maxlength="50"></input>
						</view>
					</view>
					<view class="tn-flex-1 justify-content-item  tn-text-center">
						<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
							@click="upzdvalue">
							<text style="color:#333">保 存</text>
						</tn-button>
					</view>
				</view>
			</tn-modal>




		</view>


	</view>
</template>

<script>
	import template_page_mixin from '@/libs/mixin/template_page_mixin.js'

	export default {
		name: 'TemplateSet',
		mixins: [template_page_mixin],
		data() {
			return {
				staticsfile: this.$staticsfile,
				mudata: '',
				dmudata: '',
				showAuthorizationModal: false,
				show3: false,
				zdvalue: '',
				zdtxt: '',
				zdtype: '',
				areatxt: '请选择 所在 地区',
				areaid: '0,0,0',
				multiArray: [],
				objectDiqudata: [],
				multiIndex: [0, 0, 0],
				sqxctip: '',
			}
		},
		onLoad() {

		},
		onShow() {
			let that = this;
			that.loadData();
		},

		computed: {

		},
		methods: {
			loadData() {
				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/muser/grinfo', da)
					.then(res => {
						console.log(res);
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.mudata = res.data.mudata;
							that.sqxctip = res.data.sqxctip;
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									uni.navigateBack()
								}
							});
						}
					})
			},

			upavatar() {

				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/muser/upavatar', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								if (res.errcode == 0) {
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function() {
												that.loadData();
											}, 1000);
										}
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})


					}
				});


			},


			upavatar1() {

				
				
				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/muser/upavatar', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								if (res.errcode == 0) {
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function() {
												that.loadData();
											}, 1000);
										}
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}
				
							})
				
				
					}
				});




			},

			onChooseAvatar(e) {
				let that = this;
				let avatarUrl = e.detail.avatarUrl;
				let da = {
					filePath: avatarUrl,
					name: 'file'
				}
				uni.showLoading({
					title: '上传中...'
				})
				this.$req.upload('/v1/muser/upavatar', da)
					.then(res => {
						uni.hideLoading();
						// res = JSON.parse(res);
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
			},









			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},


			// 弹出模态框
			showModal3(e) {
				console.log(e);
				let that = this;
				let dataset = e.currentTarget.dataset;
				let zdtype = dataset.zdtype;

				if (zdtype == 2) {
					uni.showToast({
						icon: 'none',
						title: '请联系管理人员修改',
					});
					return false;
				}

				if (zdtype == 1) {
					that.zdtype = 1;
					that.zdtxt = '昵称';
					that.zdvalue = that.mudata.nickname;
				}
				if (zdtype == 2) {
					that.zdtype = 2;
					that.zdtxt = '姓名';
					that.zdvalue = that.mudata.realname;
				}

				if (zdtype == 4) {
					that.zdtype = 4;
					that.zdtxt = '地址';
					that.zdvalue = that.mudata.dizhi;
				}

				console.log(zdtype, that.zdvalue);
				this.openModal3()
			},
			// 打开模态框
			openModal3() {
				this.show3 = true
			},


			upzdvalue() {
				let that = this;
				let zdtype = that.zdtype;
				let zdvalue = that.zdvalue;
				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'zdtype': zdtype,
					'zdvalue': zdvalue,
				}
				this.$req.post('/v1/muser/upgrinfo', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
										that.show3 = false;
									}, 1000)
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},



			setsex() {
				let that = this;
				uni.showActionSheet({
					title: '选择性别',
					itemList: ['男', '女'],
					success: (e) => {
						let sex = 1;
						if (e.tapIndex == 1) {
							sex = 2;
						}

						that.zdtype = 6;
						that.zdvalue = sex;
						that.upzdvalue();

					},
					fail: (e) => {
						console.log(e);

					}
				})
			},

			bindDateChange(e) {
				let that = this;
				let shengri = e.detail.value;
				that.zdtype = 7;
				that.zdvalue = shengri;
				that.upzdvalue();
			},


			getdiqudata() {
				let that = this;
				let da = {};
				this.$req.post('/v1/com/getdiqu', da).then(res => {
					uni.hideLoading();

					if (res.errcode == 0) {
						var array = []; //array用来给multiArray赋值
						var first = res.data.diqudata.crdata;
						var second = first[0].sub;
						var third = second[0].sub ? second[0].sub : [];
						array.push(first);
						array.push(second);
						array.push(third);

						that.objectDiqudata = res.data.diqudata.crdata;
						that.multiArray = array;

					} else {
						uni.showModal({
							content: res.msg,
							showCancel: false
						})
					}

				})
			},

			bindMultiPickerColumnChange(e) {
				let that = this;
				let tcode = '';
				let areaid = '';
				let areatxt = '';
				let objectDiqudata = that.objectDiqudata;

				var multiArray = this.multiArray;
				var multiIndex = this.multiIndex;
				multiIndex[e.detail.column] = e.detail.value;
				switch (e.detail.column) {
					case 0:
						multiIndex[1] = 0;
						multiIndex[2] = 0;
						multiArray[1] = objectDiqudata[multiIndex[0]].sub;
						multiArray[2] = objectDiqudata[multiIndex[0]].sub[0].sub ? objectDiqudata[multiIndex[0]].sub[0]
							.sub : [];

						break;
					case 1:
						multiIndex[2] = 0;
						multiArray[2] = objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub ? objectDiqudata[multiIndex[
							0]].sub[multiIndex[1]].sub : [];

						break;
				}

				if (objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub) {
					tcode = objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub[multiIndex[2]].code;
					areaid = objectDiqudata[multiIndex[0]].code + ',' + objectDiqudata[multiIndex[0]].sub[multiIndex[1]]
						.code + ',' + objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub[multiIndex[2]].code;
					areatxt = objectDiqudata[multiIndex[0]].name + ' ' + objectDiqudata[multiIndex[0]].sub[multiIndex[1]]
						.name + ' ' + objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub[multiIndex[2]].name;
				} else {
					tcode = objectDiqudata[multiIndex[0]].sub[multiIndex[1]].code;
					areaid = objectDiqudata[multiIndex[0]].code + ',' + objectDiqudata[multiIndex[0]].sub[multiIndex[1]]
						.code;
					areatxt = objectDiqudata[multiIndex[0]].name + ' ' + objectDiqudata[multiIndex[0]].sub[multiIndex[1]]
						.name;
				}


				that.multiArray = multiArray;
				that.multiIndex = multiIndex;
				that.code = tcode;
				that.areaid = areaid;
				that.areatxt = areatxt;

				console.log(areaid, areatxt);

			},

			upareatxt() {
				let that = this;
				let areaid = that.areaid;
				let areatxt = that.areatxt;


				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'areatxt': areatxt,
					'areaid': areaid
				}
				this.$req.post('/v1/muser/upareatxt', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000)
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},









		}
	}
</script>

<style>
	page {
		background: #fff;
	}
</style>

<style lang="scss" scoped>
	.toplinex {
		border-top: 1px solid #efefef
	}

	.avatarbutton {
		background-color: transparent;
		text-align: left;
		padding: 0;
		line-height: 22px;
		font-size: 32rpx;
	}



	/* 间隔线 start*/
	.tn-strip-bottom-min {
		width: 100%;
		border-bottom: 1rpx solid #eee;
	}

	.tn-strip-bottom {
		width: 100%;
		border-bottom: 1rpx solid #eee;
	}

	/* 间隔线 end*/


	/* 用户头像 start */
	.logo-image {
		width: 80rpx;
		height: 80rpx;
		position: relative;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border: 2rpx solid rgba(255, 255, 255, 0.05);
		box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 50%;
		overflow: hidden;
		// background-color: #FFFFFF;
	}
</style>