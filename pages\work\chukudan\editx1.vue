<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>填加物品</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon">

						<view class="vstit" style="margin-bottom:30upx">物品列表</view>
						<view class="more vls" @click="selwp" >添加物品 <text
								class="iconfont icon-tianjia2"></text></view>



						<block v-if="djshop">
							<view class="splist">
								<block v-for="(item,index) in djshop" :key="index">
									<view class="spitem">
										<view class="spname">
											<view class="tline"></view>
											{{item.title}}
											<text style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.spid}})</text>
										</view>
									
									<!-- 	<view class="rowa">
											<view class="tip">物品编号</view>
											<view class="nr colorls" >{{item.id}}</view>
											<view class="clear"></view>
										</view> -->
										<view class="rowa">
											<view class="tip">物品价格</view>
											<view class="nr">¥{{item.ckjiage}} <text class="iconfont icon-bianji slwpbjicon"  @click="addckd" :data-spid='item.spid' :data-spname='item.title' :data-sjjiage='item.ckjiage'  :data-kucun='item.kucun'></text></view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">物品数量</view>
											<view class="nr">{{item.cknum}} <text class="iconfont icon-bianji slwpbjicon"  @click="addckd" :data-spid='item.spid' :data-spname='item.title' :data-sjjiage='item.ckjiage' :data-kucun='item.kucun' ></text></view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">物品总价</view>
											<view class="nr">
												¥<text style="color:#ff0000">{{item.zongjia}}</text>
											</view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">所在门店</view>
											<view class="nr">{{item.mdname}}</view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">所在仓库</view>
											<view class="nr">{{item.ckname}}</view>
											<view class="clear"></view>
										</view>
										<block v-if="item.vxq==1">
											<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
												<view class="rowa">
													<view class="tip">{{itemx.title}}</view>
													<view class="nr">{{itemx.value}}</view>
													<view class="clear"></view>
												</view>
											</block>

											<block v-if="item.picturesarr">
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
														<block v-for="(itemxx,indexx) in item.picturesarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemxx"
																	:data-src='staticsfile + itemxx '
																	@click='previewImagev' :data-picarr="item.picturesarr"></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</view>
											</block>

											<block v-if="item.beizhu">
												<view class="bzcon">
													<view class="bztit">备注：</view>
													<view class="bznr">{{item.beizhu}}</view>
												</view>
											</block>

										</block>

								<!-- 	<view class="spbjbtn" @click="spedit" :data-spid="item.id">
									   <text class="iconfont icon-bianji"></text> 编辑
									</view> -->
									<view class="spbjbtn2" @click="spdel" :data-id="item.id" :data-spid="item.spid">
									   <text class="iconfont icon-shanchu3"></text> 删除
									</view>

										<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
											<block v-if="item.vxq==0">
												展开详情<text class="iconfont icon-jtdown2"></text>
											</block>
											<block v-else>
												收起详情<text class="iconfont icon-jtup2"></text>
											</block>
										</view>


									</view>
								</block>
							</view>
						</block>

					</view>



					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">费用信息</view>


						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品总价</view>
							<view class="inpcon">
								<view class="inp" style="color:#ff0000">{{djdata.spzongjia}}</view>
							</view>
							<view class="clear"></view>
						</view>
					

					</view>

				</view>



					<view class="tn-flex tn-footerfixed">
					  <view class="tn-flex-1 justify-content-item tn-text-center">
						  <view class="bomsfleft">
							  <button class="bomsubbtn"  @click="toback"  style="background:#ddd;" >
								  <text>上一步</text>
							  </button>
						  </view>
						  <view class="bomsfright">
							  <button class="bomsubbtn"  form-type="submit">
								<text>提交审核</text>
							  </button>
						  </view> 	  
						  <view class="clear"></view>	
					  </view>
					</view>


			</form>

		</view>
		
		
		<tn-popup v-model="spsxshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">
		
				<view class="rowx">
					<view class="tit">出库价格</view>
					<view class="inpcon">
						<view class="inp"><input class="input" type="number" name="ckjiage" v-model="thckjiage"
								placeholder="请输入出库价格" placeholder-class="placeholder" /></view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="rowx">
					<view class="tit">出库数量</view>
					<view class="inpcon">
						<view class="inp">
							<tn-number-box v-model="thcknum" :min="1" :max="thkucun" :step="1"></tn-number-box>
						</view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="qrsel">
							<text>确认提交</text>
						</button>
					</view>
				</view>
		
			</view>
		
		</tn-popup>
		

		<view style="height:120upx"></view>


		

	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				ddtype: 0,
				dtptxt: '',
				ywzqnum: '',
				ywzqdw: '',
				ywzqdwtxt: '',
				ywdqtimetxt: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid:0,
				
				thspid: 0,
				thspname: '',
				thsjjiage: 0,
				thkucun: 0,
				thcknum: 1,
				thckjiage: 0,
				spsxshow: false,
				
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			this.clicksta = false;
		
		},

		methods: {

			toback() {
				uni.navigateBack();
			},
			selwp(e) {
				uni.navigateTo({
					url: '/pages/selwp/index?stype=1&djid='+this.djid
				})
			},
			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					et: 1,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danjuck/ckdeditx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.ddtype = res.data.djdata.ddtype;
							that.ywzqnum = res.data.djdata.ywzqnum != 0 ? res.data.djdata.ywzqnum : '';
							that.ywzqdwtxt = res.data.djdata.ywzqdwtxt;
							that.ywzqdw = res.data.djdata.ywzqdw;
							that.ywdqtimetxt = res.data.djdata.ywdqtime;

							that.ywtime = res.data.ywtime;
							that.djshop = res.data.djshop;
							that.dtptxt = res.data.dtptxt;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
 
		 previewImagev: function (e) { 
				  let that = this;
				  let src = e.currentTarget.dataset.src;
				  let picarr=e.currentTarget.dataset.picarr;
				  let imgarr=[];
				  picarr.forEach(function(item,index,arr){
					  imgarr[index] = that.staticsfile+item;
				  });
				  wx.previewImage({
					  current: src,
					  urls: imgarr
				  });
		 },
					
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},

			formSubmit: function(e) {
				let that = this;
				let pvalue = e.detail.value;
				let djid = that.djid;
		
				if (that.djshop == '' || !that.djshop) {
					uni.showToast({
						icon: 'none',
						title: '还未添加物品',
					});
					return false;
				}

               uni.navigateTo({
                 url: './editx2?djid=' + djid
               })
	           return false;

				// if (this.clicksta) return;
				// this.clicksta = true;

				// uni.showLoading({
				// 	title: '处理中...'
				// })
				// let da = {
				// 	'djid': djid,
				// }
				// this.$req.post('/v1/danjuck/ckdeditxsave', da)
				// 	.then(res => {
				// 		uni.hideLoading();
				// 		console.log(res);
				// 		if (res.errcode == 0) {
				// 			setTimeout(function() {
				// 				uni.navigateTo({
				// 					url: './editx2?djid=' + djid
				// 				})
				// 			}, 500)
				// 		} else {
				// 			that.clicksta = false;
				// 			uni.showModal({
				// 				content: res.msg,
				// 				showCancel: false
				// 			})
				// 		}
				// 	})
				// 	.catch(err => {

				// 	})

			},


			spdel(e) {
				let that = this;
				let id = e.currentTarget.dataset.id;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'id': id,
								'djid':that.djid,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danjuck/ckdspdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			
			
			addckd(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				let thsjjiage = e.currentTarget.dataset.sjjiage;
				let thkucun = e.currentTarget.dataset.kucun;
				let thcknum = e.currentTarget.dataset.cknum;
				this.thspid = spid;
				this.thspname = spname;
				this.thsjjiage = thsjjiage;
				this.thckjiage = thsjjiage;
				this.thkucun = thkucun;
				this.thcknum = thcknum;
				this.spsxshow = true;
				console.log(thkucun);
			},
			qrsel() {
			    let that=this;
				let da = {
					seltype: 1,
					djid: this.djid,
					spid: this.thspid,
					cknum: this.thcknum,
					ckjiage: this.thckjiage,
				}
				uni.showLoading({title: ''})
				this.$req.post('/v1/selwp/qrsel', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							 that.spsxshow = false;
							 that.loadData();
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									
								}
							})
						}
					})
			
			},
		
		
		
		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		position: relative;
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
		padding-bottom:70upx
	}

	
	.spitem .spbjbtn {
		position: absolute;
		left: 140upx;
		bottom: 20upx; 
		color: #1677FF;
		height: 40upx;
		line-height: 40upx;
		font-size:24upx;
	}
	.spitem .spbjbtn .iconfont {
		margin-right: 10upx;font-size:24upx;
	}
	.spitem .spbjbtn2 {
		position: absolute;
		left: 20upx;
		bottom: 20upx; 
		color: #1677FF;
		height: 40upx;
		line-height: 40upx;
		font-size:24upx;
	}
	.spitem .spbjbtn2 .iconfont {
		margin-right: 10upx;font-size:24upx;
	}
	
	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		// padding-right: 100upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .viewxqcon {
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		text-align: right;
		font-size: 24upx;
		position: absolute;right:20upx;bottom:20upx;
	}

	.spitem:last-child {
		padding-botom: 0;
		margin-bottom: 0
	}
 
	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}
	.rowa .nr .slwpbjicon{margin-left:10upx;color:#1677FF}
	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.cznrcon {
		padding: 40upx;
	}
	.addotfybtn {
		color: #1677FF;
		border-radius: 0upx;
		font-size: 24upx;
		margin-left: 25upx
	}
</style>