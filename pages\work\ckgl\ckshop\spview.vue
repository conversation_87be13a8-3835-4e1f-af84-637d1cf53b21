<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>物品详情</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

	
	
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 'px'}" ></view>

        <block v-if="vtype==0">
				<view class="">

					<form @submit="formSubmit">
						<view class="sqbdcon">

                               <view class="maxcon">
                               	<view class="ttinfo">
                               		<view class="wpstatus" :class="'sta'+spdata.status">{{spdata.statusname}}</view>
                               		<view class="inf1">
                               			<view class="tf1">
                               				<!-- <view class="spbh"></view> -->
                               				<view class="tx"> 
                               					<image :src="staticsfile + spdata.smallpic" :data-src='staticsfile + spdata.smallpic ' @tap='previewImagex'></image>
                               				</view>
                               				<view class="xx"> 
                               					<view class="name">{{spdata.title}}</view>
                               					<view class="tel">
                               					编号：{{spdata.id}}
                               					<!-- 库存：<text class="lanse">{{spdata.kucun}}</text> -->
                               				
                               			
                               					</view>
                               				</view>
                               				<view class="clear"></view>
                               			</view>
                               		</view>
                               	</view>
                               </view>


							<view class="maxcon">

							
								<view class="rowx">
									<view class="tit">物品类型</view>
									<view class="inpcon">
										<view class="inp" style="color:#1677FF">
										{{spdata.ddtypename}}
										   <block v-if="spdata.otbstxt">
											   ，<text class="inp" style="color:#ff6600;" >{{spdata.otbstxt}}</text>
										   </block>
										</view>
									</view>
									<view class="clear"></view>
								</view>
								


							
							
								<view class="rowx">
									<view class="tit">库存数量</view>
									<view class="inpcon">
										<view class="inp">{{spdata.kucun}}</view>
									</view>
									<view class="clear"></view>
								</view>
						
								<view class="rowx">
									<view class="tit">{{dtptxt}}{{dtpjgtxt}}</view>
									<view class="inpcon">
										<view class="inp">¥{{spdata.danjia}}</view>
									</view>
									<view class="clear"></view>
								</view>
								
								<block v-if="spdata.ddtype!=3 || spdata.status==12"> 
									<view class="rowa">
										<view class="tip" @click="sjjgmx" :data-spid="spdata.id" :data-spname="spdata.title">上架价格 
										<text
											class="iconfont icon-yewucaozuo"
											style="margin-left:10upx;color:#1677FF"></text>
										</view>
										<view class="nr" >¥{{spdata.sjjiage}} </view>
										<view class="clear"></view>
									</view>
									<view class="rowa">
										<view class="tip">上架状态</view>
										<view class="nr">
											{{spdata.sjstatusname}}
										</view>
										<view class="clear"></view>
									</view>
								</block>
								
							   
								
								<block v-if="spdata.rksta_cf==1">
									<view class="rowa">
										<view class="tip">所属店铺</view>
										<view class="nr">
											{{spdata.mdname}} 
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowa">
										<view class="tip">所在仓库</view>
										<view class="nr">
											{{spdata.ckname_cf}}
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowa" v-if="spdata.rktime_cf">
										<view class="tip">入库时间</view>
										<view class="nr">
											{{spdata.rktime_cf}}
										</view>
										<view class="clear"></view>
									</view>
								</block>
								<block  v-else>
									<view class="rowx">
										<view class="tit">所在仓库</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ckname}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<block v-if="spdata.rktime"> 
										<view class="rowx">
											<view class="tit">入库时间</view>
											<view class="inpcon">
												<view class="inp">{{spdata.rktime}}</view>
											</view>
											<view class="clear"></view>
										</view>
									</block>
								</block>
								
							
							
								<block v-if="spdata.ddtype==1">
									<!-- <view class="rowx" >
										<view class="tit"><text class="redst"></text>寄卖周期</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywzqnum}} {{spdata.ywzqdwtxt}}</view>
										</view>
										<view class="clear"></view>
									</view> -->
							<!-- 		<view class="rowx" v-if="spdata.ywtime" >
										<view class="tit"><text class="redst"></text>寄卖时间</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywtime}}</view>
										</view>
										<view class="clear"></view>
									</view> -->
									<view class="rowx" v-if="spdata.ywdqtime" >
										<view class="tit"><text class="redst"></text>到期时间</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywdqtime}}</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								<block v-if="spdata.ddtype==2 && spdata.ywtime>0">
									<view class="rowx" >
										<view class="tit"><text class="redst"></text>回收时间</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywtime}}</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								
								<block v-if="spdata.ddtype==3">
							<!-- 		<view class="rowx" >
										<view class="tit"><text class="redst"></text>暂存周期</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywzqnum}} {{spdata.ywzqdwtxt}}</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx" >
										<view class="tit"><text class="redst"></text>暂存时间</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywtime}}</view>
										</view>
										<view class="clear"></view>
									</view> -->
									<view class="rowx" v-if="spdata.ywdqtime" >
										<view class="tit"><text class="redst"></text>到期时间</view>
										<view class="inpcon">
											<view class="inp">{{spdata.ywdqtime}}</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
							
							

								<block v-if="vxq==1">
									<block v-for="(itemx,indexx) in spdata.zdysxarr" :key="indexx">
										<view class="rowa">
											<view class="tip">{{itemx.title}}</view>
											<view class="nr">{{itemx.value}}</view>
											<view class="clear"></view>
										</view>
									</block>

									<block v-if="picturesarr">
										<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
									
										<!-- 	<view class='item' v-if="spdata.smallpic">
												<image :src="staticsfile+spdata.smallpic"
													:data-src='staticsfile + spdata.smallpic ' @tap='previewImagex'></image>
											</view> -->
											<block v-if="picturesarr">
												<block v-for="(item,index) in picturesarr" :key="index">
													<view class='item'>
														<image :src="staticsfile+item" :data-src='staticsfile + item '
															@tap='previewImagev' :data-picarr="picturesarr"></image>
													</view>
												</block>
											</block>
											<view class="clear"></view>
										</view>
									</block>

									<block v-if="spdata.beizhu">
										<view class="bzcon">
											<view class="bztit">物品备注：</view>
											<view class="bznr">{{spdata.beizhu}}</view>
										</view>
									</block>

								</block>


								<view class="viewxqcon" @click="setvxq" >
									<block v-if="vxq==0">
										展开详情<text class="iconfont icon-jtdown2"></text>
									</block>
									<block v-else>
										收起详情<text class="iconfont icon-jtup2"></text>
									</block>
								</view>

						
							</view>


                            <view class="maxcon" style="padding-bottom:10upx">
                            	<view class="vstit">费用信息</view>
                            	<view class="rowx">
                            		<view class="tit"><text class="redst"></text>{{dtptxt}}总价</view>
                            		<view class="inpcon">
                            			<view class="inp" style="color:#ff0000">{{spdata.zongjia}}</view>
                            		</view>
                            		<view class="clear"></view>
                            	</view>
								
							<block v-if="spdata.ddtype==1 || spdata.ddtype==3">
									<block v-if="spdata.status==8">
										<view class="rowx">
											<view class="tit"><text class="redst"></text>其他收费</view>
											<view class="inpcon">
												<view class="inp">{{spdata.fjfjine}}</view>
											</view>
											<view class="clear"></view>
										</view>
									</block>
									<block v-else>
										<view class="rowa">
											<view class="tip" @click="qtfymx" >其他收费 <text class="iconfont icon-yewucaozuo"	style="margin-left:10upx;color:#1677FF"></text> </view>
											<view class="nr">
												<text @click="qtfymx" >¥{{spdata.fjfjine}}</text>
											</view>
											<view class="clear"></view>
										</view>
									</block>
							</block>
							
								<view class="rowa"> 
									<view class="tip" @click="qtcbmx" :data-spid="spdata.id" :data-spname="spdata.title">其他成本 <text 	class="iconfont icon-yewucaozuo" style="margin-left:10upx;color:#1677FF"></text> </view>
									<view class="nr">
										<text @click="qtcbmx" :data-spid="spdata.id" :data-spname="spdata.title">¥{{spdata.cbfjine}}</text>
										
									</view>
									<view class="clear"></view>
								</view>
							                            
								
                            	<block v-if="spdata.ddtype==1 || spdata.ddtype==3">
                            		<view class="rowx">
                            			<view class="tit"><text class="redst"></text>服务费</view>
                            			<view class="inpcon">
                            				<view class="inp">{{spdata.fwfjine}}</view>
                            			</view>
                            			<view class="clear"></view>
                            		</view>
                            	</block>
								
								
								
                            </view>


							<view class="maxcon">

								<view class="vstit">其他信息</view>
								<view class="zjzkicon" @click="setvzz2">{{vsfnr2==0 ? '展开' : '收起'}} <text class="iconfont icon-jiantou_liebiaozhankai"></text></view>
								<block v-if="vsfnr2==1">
						
										<view class="rowx">
											<view class="tit">录入店铺</view>
											<view class="inpcon">
												<view class="inp">{{spdata.mdname}}</view>
											</view>
											<view class="clear"></view>
										</view>
										<view class="rowx">
											<view class="tit">录入人员</view>
											<view class="inpcon">
												<view class="inp">{{spdata.lrrystr}}</view>
											</view>
											<view class="clear"></view>
										</view>
								
										<view class="rowx">
											<view class="tit">业务人员</view>
											<view class="inpcon">
												<view class="inp">{{spdata.ywrystr}}</view>
											</view>
											<view class="clear"></view>
										</view>
										<view class="rowx">
											<view class="tit">验货人员</view>
											<view class="inpcon">
												<view class="inp">{{spdata.yhrystr}}</view>
											</view>
											<view class="clear"></view>
										</view>
										<view class="rowx">
											<view class="tit">单据审核</view>
											<view class="inpcon">
												<view class="inp">{{spdata.shrystr}}</view>
											</view>
											<view class="clear"></view>
										</view>
								</block>		
										
							</view>

							



						</view>

					
					</form>
				</view>
		
		
		</block>
		
		<block v-if="vtype==1">
		   
		            <block v-if="spdtdata.length>0">
		            
		            	<view class="cwcon">
		            
		            		<block v-for="(item,index) in spdtdata" :key="index">
		            
		            			<view class="cwli  ">
		            				<view class="nrcon">
		            					<view class="info">
											<view class="cztype">{{item.typetxt}}</view>
		            						<view><text class="tit">操作时间：</text>{{item.addtime}}</view>
		            						<view v-if="item.ordernum"><text class="tit">相关单号：</text>{{item.ordernum}}</view>
		            						<view v-if="item.opname"><text class="tit">操作人员：</text>{{item.opname}}</view>
											
											<block v-if="item.type==10">
												<view><text class="tit">原上架价格：</text>￥{{item.orsjjiage}}</view>
												<view><text class="tit">新上架价格：</text><text class="colorls">￥{{item.sjjiage}}</text></view>
											</block>
											<block v-if="item.type==11">
												<view><text class="tit">上架价格：</text><text class="colorls">￥{{item.sjjiage}}</text></view>
											</block>
											
											<block v-if="item.type==1">
												<view><text class="tit">转出仓库：</text>{{item.orckname}}</view>
												<view><text class="tit">转入仓库：</text>{{item.zrckname}}</view>
												<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text></view>
											</block>
											<block v-if="item.type==2">
												<view><text class="tit">调用人员：</text>{{item.dyygname}}</view>
												<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text></view>
											</block>
											<block v-if="item.type==3">
												<view><text class="tit">入库仓库：</text>{{item.zrckname}}</view>
												<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text></view>
											</block>
											<block v-if="item.type==4">
												<view><text class="tit">出库仓库：</text>{{item.orckname}}</view>
												<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text></view>
											</block>
											<block v-if="item.type==5">
												<view><text class="tit">入库仓库：</text>{{item.zrckname}}</view>
												<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text></view>
											</block>
											<block v-if="item.type==6">
												<view><text class="tit">出库仓库：</text>{{item.orckname}}</view>
												<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text></view>
											</block>
											
											<block v-if="item.remark || item.picturescarr">
												<view class="zkbtn" @click="setvxqdt" :data-id="item.id">
													详情 <text class="iconfont icon-jtdown2"></text>
												</view>
												<block v-if="item.id==thviddt">
													<view class="des">
														<view v-if="item.remark">备注说明：{{item.remark}}</view>
														<block v-if="item.picturescarr">
															<view class="pjimgcon">
																<block v-for="(itemx,indexx) in item.picturescarr" :key="indexx">
																	<view class='item'>
																		<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
																			:data-picturescarr="item.picturescarr" @tap='previewImagex'>
																		</image>
																	</view>
																</block>
																<view class="clear"></view>
															</view>
														</block>
													</view>
												</block>
											</block>
											
											
		            					</view>
		            		
		            					<view class="clear"></view>
		            				</view>
		            			</view>
		            
		            		</block>
		            	</view>
		            
		            </block>
		            <block v-else>
		            	<view class="gwcno">
		            		<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
		            		<view class="tit">暂无物品动态</view>
		            	</view>
		            </block>
		   
		   
		</block>


		<view style="height:50upx"></view>



        <tn-popup v-model="qtfymxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
         	<view class="popuptit">其他收费明细</view>
         	<scroll-view scroll-y="true" style="height: 800rpx;">
         	
         		<view class="qtfylist">
         
         			<block v-if="qtfydata.length>0">
         
         				<block v-for="(item,index) in qtfydata" :key="index">
         					<view class="lis-item  ">
         						<view class="nrcon">
         							<view class="info">
         
         								<block v-if="item.type==1">
         									<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
         								</block>
         								<block v-else>
         									<view><text>金额：</text> <text class="jine"
         											style="color:#4aac09">￥{{item.orjine}}</text></view>
         								</block>
         								<view><text>时间：</text>{{item.addtime}}</view>
         								<view><text>说明：</text>{{item.remark}}</view>
         							    <block v-if="item.picturescarr">
         									<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
         										详情 <text class="iconfont icon-jtdown2"></text>
         									</view>
         								</block>
         								<block v-if="item.id==thqtfyvid">
         									<view class="des">
         										<block v-if="item.picturescarr">
         											<view class="pjimgcon">
         												<block v-for="(itemx,indexx) in item.picturescarr"
         													:key="indexx">
         													<view class='item'>
         														<image :src="staticsfile+itemx"
         															:data-src='staticsfile + itemx '
         															:data-picturescarr="item.picturescarr"
         															@tap='previewImagex'></image>
         													</view>
         												</block>
         												<view class="clear"></view>
         											</view>
         										</block>
         									</view>
         								</block>
         
         							</view>
         							<view class="clear"></view>
         						</view>
         					</view>
         
         				</block>
         
         
         			</block>
         			<block v-else>
         				<view class="gwcno">
         					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
         					<view class="tit">暂无其他收费</view>
         				</view>
         			</block>
         
         
         		</view>
         	</scroll-view>
        </tn-popup>


		<tn-popup v-model="qtcbmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
        	<view class="popuptit">其他成本明细</view>
        	<scroll-view scroll-y="true" style="height: 800rpx;">
        		<view class="qtcbmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
        			{{qtcbmxspname}}
        		</view>
        		<view class="qtcblist">
        
        			<block v-if="qtcbdata.length>0">
        
        				<block v-for="(item,index) in qtcbdata" :key="index">
        					<view class="lis-item  ">
        						<view class="nrcon">
        							<view class="info">
        								<!-- <view class="bjbtn" >
        									<text @click="qtcbdel" :data-fyid="item.id">删除</text>
        									<text @click="qtcbedit" :data-fyid="item.id" :data-spid="item.spid"
        										style="color:#1677FF">编辑</text>
        								</view> -->
        
        
        								<block v-if="item.type==1">
        									<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
        								</block>
        								<block v-else>
        									<view><text>金额：</text> <text class="jine"
        											style="color:#4aac09">￥{{item.orjine}}</text></view>
        								</block>
        								<view><text>时间：</text>{{item.addtime}}</view>
        								<view><text>说明：</text>{{item.remark}}</view>
        								<block v-if="item.picturescarr">
        									<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
        										详情 <text class="iconfont icon-jtdown2"></text>
        									</view>
        								</block>
        								<block v-if="item.id==thqtcbvid">
        									<view class="des">
        										<block v-if="item.picturescarr">
        											<view class="pjimgcon">
        												<block v-for="(itemx,indexx) in item.picturescarr"
        													:key="indexx">
        													<view class='item'>
        														<image :src="staticsfile+itemx"
        															:data-src='staticsfile + itemx '
        															:data-picturescarr="item.picturescarr"
        															@tap='previewImagex'></image>
        													</view>
        												</block>
        												<view class="clear"></view>
        											</view>
        										</block>
        									</view>
        								</block>
        
        							</view>
        							<view class="clear"></view>
        						</view>
        					</view>
        
        				</block>
        
        
        			</block>
        			<block v-else>
        				<view class="gwcno">
        					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
        					<view class="tit">暂无其他成本</view>
        				</view>
        			</block>
        
        
        		</view>
        	</scroll-view>
        </tn-popup>

		<tn-popup v-model="sjjgmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
         	<view class="popuptit">上架价格调整记录</view>
         	<scroll-view scroll-y="true" style="height: 800rpx;">
         		<view class="sjjgmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
         			{{sjjgmxspname}}
         		</view>
         		<view class="sjjglist">
         
         			<block v-if="sjjgdata.length>0">
         
         				<block v-for="(item,index) in sjjgdata" :key="index">
         					<view class="lis-item  ">
         						<view class="nrcon">
         							<view class="info">
										<view class="cztype">{{item.typetxt}}</view>
										<view><text class="tit">操作时间：</text>{{item.addtime}}</view>
										<view v-if="item.opname"><text class="tit">操作人员：</text>{{item.opname}}</view>
										<block v-if="item.type==10">
											<view><text class="tit">原上架价格：</text>￥{{item.orsjjiage}}</view>
											<view><text class="tit">新上架价格：</text><text class="colorls">￥{{item.sjjiage}}</text></view>
										</block> 
										<block v-if="item.type==11">
											<view><text class="tit">上架价格：</text><text class="colorls">￥{{item.sjjiage}}</text></view>
										</block>
									
         								<view v-if="item.remark"><text>调整说明：</text>{{item.remark}}</view>
         								<block v-if="item.picturescarr">
         									<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
         										详情 <text class="iconfont icon-jtdown2"></text>
         									</view>
         								</block>
         								<block v-if="item.id==thqtfyvid">
         									<view class="des">
         										<block v-if="item.picturescarr">
         											<view class="pjimgcon">
         												<block v-for="(itemx,indexx) in item.picturescarr"
         													:key="indexx">
         													<view class='item'>
         														<image :src="staticsfile+itemx"
         															:data-src='staticsfile + itemx '
         															:data-picturescarr="item.picturescarr"
         															@tap='previewImagex'></image>
         													</view>
         												</block>
         												<view class="clear"></view>
         											</view>
         										</block>
         									</view>
         								</block>
         
         							</view>
         							<view class="clear"></view>
         						</view>
         					</view>
         				</block>
         
         			</block>
         			<block v-else>
         				<view class="gwcno">
         					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
         					<view class="tit">暂无调整价格记录</view>
         				</view>
         			</block>
         
         		</view>
         	</scroll-view>
        </tn-popup>
		 





	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				spdata: '',
				spdtdata: '',
				status: 0,
				img_url: [],
				img_url_ok: [],
				spid: 0,
				picturesarr: '',
				kgpicturesarr: '',
				vxq:0,
				kgremark:'',
				vsfnr:0,
				vsfnr2:0,
				vtype:0,
				index: 1,
				current: 0,
				vtypedata:[
					{vtype: 0,name: '基本信息'},
					{vtype: 1,name: '物品动态'},
				],
				
				qtfymxshow:false,
				qtfydata: '',
				thqtfyvid:0,
				dtptxt:'',
				dtpjgtxt:'',
				thviddt:0,
				
				sjjgmxshow: false,
				sjjgmxspname: '',
				sjjgdata: '',
				thsjjgvid: 0,
				
				qtcbmxshow: false,
				qtcbmxspname: '',
				qtcbdata: '',
				thqtcbvid: 0,
				
			}
		},

		onLoad(options) {
			let that = this;
			let spid = options.spid ? options.spid : 0;
			this.spid = spid;
			this.loadData();
		},
		onShow() {
			if (uni.getStorageSync('thupspotfyspid')==1) {
				this.loadData();
				this.getqyfylist();
				uni.setStorageSync('thupspotfyspid', 0)
			}
			
			uni.setStorageSync('thupspotcbspid', 0)
		
			if (uni.getStorageSync('thupsplist')==1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}
		},

		methods: {
			
			setvzz() {
				if (this.vsfnr == 0) {
					this.vsfnr = 1;
				} else {
					this.vsfnr = 0;
				}
			},
			setvzz2() {
				if (this.vsfnr2 == 0) {
					this.vsfnr2 = 1;
				} else {
					this.vsfnr2 = 0;
				}
			},
			spedit(e) {
				let spid = e.currentTarget.dataset.spid
				uni.navigateTo({
					url: './spedit?spid=' + spid
				})
			},
			tabChange(index) {
				let that=this;
				let vtype=that.vtypedata[index].vtype;
				that.current = index;
				that.vtype = vtype;
			},
			
			loadData() {
				let that = this;
				let spid = that.spid;
				let da = {
					spid: spid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/ckshopedit', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.spdata = res.data.spdata;
							that.spdtdata = res.data.spdtdata;
							that.kgremark = res.data.spdata.kgremark;
							that.img_url_ok=res.data.kgpicturesarr;
							that.picturesarr = res.data.picturesarr;
							
							that.dtptxt = res.data.dtptxt;
							that.dtpjgtxt = res.data.dtpjgtxt;
							
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			setvxq(){
			
				let that = this;
                if(this.vxq==1){
					this.vxq=0;
				}else{
					this.vxq=1;
				}			   
			},
			setvxqdt(e) {
				let thvid = e.currentTarget.dataset.id;
				if (thvid == this.thviddt) {
					this.thviddt = 0;
				} else {
					this.thviddt = thvid;
				}
			
			},
			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImagev: function (e) {
					  let that = this;
					  let src = e.currentTarget.dataset.src;
					  let picarr=e.currentTarget.dataset.picarr;
				
					  let imgarr=[];
					  picarr.forEach(function(item,index,arr){
						  imgarr[index] = that.staticsfile+item;
					  });
					  wx.previewImage({
						  current: src,
						  urls: imgarr
					  });
			},
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},



			formSubmit: function(e) {
				let that = this;
				var formdata = e.detail.value


				let pvalue = e.detail.value;


				uni.showModal({
					title: '',
					content: '确认提交吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							uni.showLoading({
								title: '处理中...'
							})
							let da = {
								'spid': that.spid,
								'kgremark': that.kgremark ? that.kgremark : '',
								'kgremarkfile': that.img_url_ok ? that.img_url_ok : '',
							}
						
							that.$req.post('/v1/ckgl/upglbeizhu', da)
								.then(res => {
									uni.hideLoading();
									console.log(res);
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000)
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}
								})
						}
					}
				})
			},


            setspsta(e) {
            	let that = this;
            	let spid = this.spid;
            	let da = {
            		'spid': spid
            	}
            	uni.showLoading({
            		title: ''
            	})
            	that.$req.post('/v1/ckgl/setspsta', da)
            		.then(res => {
            			uni.hideLoading();
            			if (res.errcode == 0) {
							uni.setStorageSync('thupcksplist',1)
            				uni.showToast({
            					icon: 'none',
            					title: res.msg,
            					success() {
            						setTimeout(function() {
            							that.loadData();
            						}, 1000);
            					}
            				});
            			} else {
            				uni.showModal({
            					content: res.msg,
            					showCancel: false,
            				})
            			}
            		})
            
            },


			tiaobo(e){
				let that=this;
				let spid = that.spid;
				uni.navigateTo({
					url:'./tiaobo?spid='+spid
				})
			},
			
			qtfyedit(e) {
				let spid = this.spid;
				let fyid = e.currentTarget.dataset.fyid;
				uni.navigateTo({
					url: '/pages/work/ckgl/ckshop/qtfyedit?spid=' + spid + '&fyid=' + fyid
				})
			},
			
			qtfymx(e) {
				this.getqyfylist();
			},
			qtfysetvxq(e) {
				let thqtfyvid = e.currentTarget.dataset.id;
				if (thqtfyvid == this.thqtfyvid) {
					this.thqtfyvid = 0;
				} else {
					this.thqtfyvid = thqtfyvid;
				}
				
			},
			getqyfylist() {
				let that = this;
				let spid=this.spid;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/ckspqtfylist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtfydata = res.data.qtfydata;
							that.qtfymxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			qtfydel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/ckgl/ckspqtfydel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqyfylist();
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
			
						}
			
					}
				})
			},

			
			setsjjg(e){
				let spid=e.currentTarget.dataset.spid;
				uni.navigateTo({
					url:'/pages/work/ckgl/ckshop/sjjgset?spid='+spid
				})
			},
			sjjgmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.sjjgmxspname = spname;
				this.getsjjglist(spid);
			},
			sjjgsetvxq(e) {
				let thsjjgvid = e.currentTarget.dataset.id;
				if (thsjjgvid == this.thsjjgvid) {
					this.thsjjgvid = 0;
				} else {
					this.thsjjgvid = thsjjgvid;
				}
			
			},
			getsjjglist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/spsjjglist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.sjjgdata = res.data.sjjgdata;
							that.sjjgmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			
			
			qtcbedit(e) {
			
				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if (spname) {
					this.qtcbmxspname = spname;
				}
				let djid = this.spdata.djid;
			
				uni.navigateTo({
					url: '/pages/work/rukudan/qtcbedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtcbdel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtcbdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqycblist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
			
						}
			
					}
				})
			},
			qtcbmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtcbmxspname = spname;
				this.getqycblist(spid);
			},
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}
			
			},
			getqycblist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtcblist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtcbdata = res.data.qtcbdata;
							that.qtcbmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			



		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

.ttinfo{position: relative;}
.ttinfo .icon{position: absolute;right:0upx;top:30upx}
.ttinfo .icon .iconfont{color:#ddd}
.ttinfo .inf1{}
.ttinfo .lanse{color:#1677FF;font-size:24upx}
.ttinfo .inf1 .tx{float:left}
.ttinfo .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
.ttinfo .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:10upx;}
.ttinfo .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.ttinfo .inf1 .tel{color: #333;font-size: 24rpx;margin-top:10upx}
.ttinfo .inf1 .tel text{margin-right:30upx}
.ttinfo .spbh{position: absolute;right:0;top:0;font-size:20upx;color:#1677FF}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		margin-bottom: 20upx;
		font-weight: 700
	}
	.maxcon .spbjbtn {
		position: absolute;
		right: 20upx;
		top: 20upx;
		color: #1677FF
	}

	.sqbdcon {
		margin: 32upx;
	}
 .wpstatus{position: absolute;right:28upx;top:28upx;border-radius:30upx;padding:5upx 15upx;color:#fff;background:#ccc;font-size:24upx}
 .wpstatus.sta1{background:#54D216;}
 .wpstatus.sta12{background:#54D216;}
 .wpstatus.sta2{background:#FF6600;color:#fff}
 .wpstatus.sta3{background:#FF2828;color:#fff}
 .wpstatus.sta8{background:#888;}
 .wpstatus.sta9{background:#c76096;color:#fff}
 
 
 .slnum{color:#ff0000}
 
 
 
 
 
	.wpsmcon {
		background: #fafafa;
		overflow-y: scroll;
		border: 1px solid #efefef;
		padding: 20upx 10upx 20upx 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
		text-align: right;
		overflow: hidden;
	}

	.rowx .inp {
		color: #7F7F7F;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
	.addotfybtn {
		color: #1677FF;
		border-radius: 0upx;
		font-size: 24upx;
		margin-left: 25upx
	}
	.viewxqcon{color:#ff6600;height:40upx;line-height:40upx;margin-bottom:10upx;margin-top:15upx;text-align: right;font-size:24upx;}
	
	.rowa{height:90upx;line-height:90upx;border-bottom:1px dashed #efefef;overflow: hidden;}
	.rowa .tip{float:left;}
	.rowa .nr{float:right}
	.rowa:last-child{border-bottom:0;}
	.bzcon{color:#7F7F7F;margin-bottom:20upx}
	
	.bomsubbtn3{
		height: 80rpx;
		line-height: 80rpx;
		border:1px solid #ddd;
		margin-top:10upx;
		background:#efefef;
	}
	.bomsubbtn2{
		height: 80rpx;
		line-height: 80rpx;
		background: #FFD427;
		border-radius: 10rpx;
		margin:0 32upx;
		color:#333;
		text-align: center;
	}
	.bomsubbtn2 .iconfont{margin-right:20upx;}
	.zjzkicon { 
		position: absolute;
		right: 28upx;
		top: 28upx;
	}
	
	
	
	
	.cwcon {
		padding: 30upx 30upx;
	}
	
	.cwli {
		position: relative;
		border-bottom: 1px solid #efefef;
		padding: 30upx;
		background: #fff;
		margin-bottom: 20upx;
		border-radius: 20upx;
	}
	.cwli .cztype{position: absolute;right:30upx;top:30upx;color:#1677FF}
	
	.cwli .info {
		font-size: 24upx;
		line-height: 45upx;
	}
	.cwli .pjimgcon {
		margin-top: 10upx;
	}
	
	.cwli .pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}
	
	.cwli .pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}
	.cwli .ygidcc{font-size:24upx;color:#888;margin-left:20upx}
	
	.cwli .zkbtn {
		color: #888;
		position:absolute;
		right:20upx;top:70upx;font-size:24upx
	}
	
	.cwli .des {
		border-top: 1px solid #eee;
		margin-top: 10upx;
		padding-top: 10upx;
		color:#888
	}
	

	
	
	
	
	
	
	
	
</style>