<template>
	<view class="login">
		
		<tn-nav-bar fixed alpha customBack >
		  <view slot="back" class='tn-custom-nav-bar__back2'  >
			<text class='icon iconfont icon-fanhui' @click="goBack()"></text>
		  </view>
		</tn-nav-bar>
		
		<view class="login">
		
				  <view class="login__bg login__bg--top">
					<image class="bg" src="/static/images/regbg.jpg" mode="widthFix"></image>
				  </view>
		
		          <view class="topk"></view>
		
		
					<view class="login-type-content">
						
						<view class="logoimg"><image src="../../static/images/logo.png"></image></view>
						<view class="titwz">企业注册-企业信息</view>
						
						<view class="main">
						
							
					
								<view class="login-type-form">
									<view class="input-item">
										<input
												class="login-type-input"
												type="text"
												name="title"
												v-model="title"
												placeholder="企业名称/商号名称"
												maxlength="50"
										/>
									</view>
								
									<view class="input-item">
										<input
												class="login-type-input"
												type="text"
												name="lxren"
												v-model="lxren"
												placeholder="联系人"
												maxlength="50"
										/>
									</view>
								
									<view class="input-item">
										<input
												class="login-type-input"
												type="text"
												name="lxtel"
												v-model="lxtel"
												placeholder="联系手机号"
												maxlength="50"
										/>
									</view>
									
									<view class="input-item">
										<input
												class="login-type-input"
												type="text"
												name="dizhi"
												v-model="dizhi"
												placeholder="联系地址"
												maxlength="150"
										/>
									</view>
								
								</view>
						
					
						
							<view>
								<button
										class="confirm-btn bg-active tn-main-gradient-indigo" 
										:disabled="btnLoading"
										:loading="btnLoading"
										@tap="toTj"
								>
									确认提交
								</button>
							</view>
							
						
							
						    
							
						</view>
					</view>
				
		 </view>		
			
	</view>
</template>
<script>
	
	
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				title: '',
				lxren: '',
				lxtel: '',
				dizhi: '',
				disabled:false,
				btnLoading: false,
			};
		},
		
		
		onLoad(options) {
			// this.loadData();
		},
		
		watch: {
		
		},
		onShow() {
			let that=this;
		},
		methods: {
			
			goBack(){
				 uni.navigateBack()
			},
			

		
			// 统一跳转路由
			navTo(url) {
		      uni.navigateTo({ url });
			},
			
			// 提交表单
			async toTj() {
			      let that=this;
				
					if (this.title.length <= 0) {
						uni.showToast({ 
							icon: 'none',
							title: '请输入企业名称、商号名称'
						});
						return;
					}
					
					if (this.lxren.length <= 0) {
						uni.showToast({ 
							icon: 'none',
							title: '请输入联系人'
						});
						return;
					}
					
					if (this.lxtel.length <= 0) {
						uni.showToast({ 
							icon: 'none',
							title: '请输入联系手机号'
						});
						return;
					}
					if (this.dizhi.length <= 0) {
						uni.showToast({ 
							icon: 'none',
							title: '请输入联系地址'
						});
						return;
					}
					
							
				  uni.showLoading({title: ''})
				  
				  let da={
					title: this.title,
					lxren: this.lxren,
					lxtel: this.lxtel,
					dizhi: this.dizhi,
				  };
				  this.$req.post('/v1/selsf/sjregsave', da)
				  		.then(res => {
				  			uni.hideLoading();
							console.log(res);
					
				  			if(res.errcode!=0){
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg
				  				});
				  			}else{
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg,
				  					success() {
				  						setTimeout(function(){
											uni.navigateBack()
				  							// uni.redirectTo({
				  							//    url: '/pages/user/rzgl/sjrz',
				  							// })
				  						},1000)
				  					}
				  				});
				  			}
				  			
				  		})
				  		.catch(err => {
				  		
				  		})
				  
			},
			
			
			  
			
		}
	};
</script>
<style lang="scss">
	page {
		background: #fff;
	}

.logoimg{}
.logoimg image{width: 148upx;height:148upx}
	.titwz{font-size:36upx;font-weight: 600;margin-top:20upx}
	.login {
		
		position: relative;
		height: 100%;
		z-index: 1;
		
		
		/* 背景图片 start */
		&__bg {
		  z-index: -1;
		  position: fixed;
		  
		  &--top {
		    top: 0;
		    left: 0;
		    right: 0;
		    width: 100%;
		    
		    .bg {
		      width: 750upx;
		      will-change: transform;
		    }
		  }
		
		}
		/* 背景图片 end */
		
		.topk{height:180upx}
		
		.dltype{}
		.dltype .item{float:left}
		.dltype .item .text{
			width: 104upx;
			height: 74upx;
			font-size: 52upx;
			font-weight: 600;
			color: #333333;
			line-height: 74upx;
		}
		.dltype .item .line{
			width: 60upx;
			height: 8upx;
			background: #3366FF;
			border-radius: 4upx;
			margin:10upx auto;
		}
		
		
		
		.login-type-content {
			margin:0 56upx;
			position: relative;
			.main {
				position: absolute;
				width: 100%;
				top: 270upx;
			    .login-type-form {
							.input-item {
								position: relative;
								margin-bottom: 32upx;
								font-size:32upx;
								color:#7F7F7F;
								.login-type-input {
									height: 112rpx;
									line-height: 112rpx;
									background: #fff;
									border-radius: 24rpx;
									padding:0 30upx;
								}
							}
							.tricon{position: absolute;right:0;top:0;z-index:100;}
							.tricon .iconfont{font-size:44upx}
				}
				.confirm-btn {
				  margin-top:30upx;
				  height: 112upx;
				  line-height: 112upx;
				  border-radius:24upx;
				  background: #FFD427;
				  color:#333;
				}
			}
			
			.dlmmcon{height: 28upx;font-size: 26upx;line-height:28upx;color: #7F7F7F;margin-top:53upx;text-align:center;margin-bottom:30upx}
		}
	
	}
	
	
	// 发送验证码样式
	.input-item-sms-code {
	
	  .sms-code-btn {
	      position: absolute;right:28upx;top:22upx;z-index: 200;background:#fff;color: #3366FF;font-size:28upx;padding:0
	  }
	
	  .sms-code-resend {
	    color: #666;background:#fff;
	  }
	}
	
	
	/* 胶囊*/
	 .tn-custom-nav-bar__back2 {
	
	   height: 100%;
	   position: relative;
	   display: flex;
	   justify-content: space-evenly;
	   align-items: center;
	   box-sizing: border-box;
	   font-size: 18px;
	   width: 140upx;
	   .icon {
	     display: block;
	     flex: 1;
	     margin: auto;
	     text-align: center;
		  font-size:45upx
	   }
	   
	  
	 }
		
	
	
	
</style>
