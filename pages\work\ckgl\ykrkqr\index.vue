<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>移库入库确认</text>
			</view>
			<view slot="right">
			</view>

		</tn-nav-bar>
		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333" :bold="true" :fontSize="28" :gutter="20" :badgeOffset="[20, 50]"
						@change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;">

					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text>
					</view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>
		</view>
 
			<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>
			<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block> 


		



		<block v-if="listdata.length>0">


			<view class="splist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="cwli  ">
						<view class="nrcon">
							<view class="info">
								<view class="cztype">{{item.typetxt}}</view>
								<view v-if="item.ordernum"><text class="tit">相关单号：</text>{{item.ordernum}}</view>
						<view><text class="tit">操作时间：</text>{{item.addtime}}</view>
									<view><text class="tit">商品名称：</text>{{item.spname}}</view>
									<view><text class="tit">移出仓库：</text>{{item.orckname}}</view>
									<view><text class="tit">移入仓库：</text>{{item.zrckname}}</view>
									
									<view>
										<text class="tit">移库状态：</text>
										<text class="dysta" :class="'ys'+item.yksta">{{item.ykstatxt}}</text>
									</view>
									<view><text class="tit">物品数量：</text><text class="slnum">{{item.czkcnum}}</text>
									</view>
									
									<block v-if="item.yksta==2">
									       <view class="ghbtn" @click="ykqrrk" data-sta=1 :data-rcdid="item.id">确认入库</view>
									</block>
								
								    <view class="ghbtn2" @click="viewsp" :data-spid="item.spid">商品详情</view>
					
							</view>
					
							<view class="clear"></view>
						</view>
					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关物品</view>
			</view>
		</block>





		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">物品搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入物品名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>
		
		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 2,
				statxt: '待确认',
				ddtype: 0,
				ddtypetxt: '类型',
				index: 1,
				current: 1,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
			stadata: [{
					sta: 0,
					name: '全部'
				},
				{
					sta: 2,
					name: '待确认'
				},
				{
					sta: 1,
					name: '已确认'
				},
				{
					sta: 6,
					name: '已收移回'
				},
				{
					sta: 5,
					name: '未收撤回'
				},
			],
				
				pxfs: 0,
				pxfstxt: '排序',

				show3: false,
				checked: true,
				tbdhdownshow: false,
				dwtype: 2,

				tjdata: [],
				spsortdata: [],

				rqshow: false,
				rqkstime: 0,
				rqjstime: 0,

				sortid: 0,

				
				thspid: 0,
		

			}
		},
		onLoad(options) {
			let that = this;


			that.page = 0;
			that.listdata = [];
			that.loaditems();
		},
		onShow() {

		},
		methods: {
			previewImagev: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;
			
				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			viewsp(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				console.log(spid);
				uni.navigateTo({
					url: '../ckshop/spview?spid=' + spid
				})
			},
			selcangku() {
				this.ckselshow = true;
			},

			
		
			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					// this.thkw = '';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},




			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					ckid: that.ckid ? that.ckid : 0,
					sortid: that.sortid ? that.sortid : 0,
					pxfs: that.pxfs ? that.pxfs : 0,
					sta: that.sta ? that.sta : 0,
					ddtype: that.ddtype ? that.ddtype : 0,
					kw: that.thkw ? that.thkw : '',
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
				}
				if (page != 1) {
					that.isloading = true;
				}
				console.log(that.sta);

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/ckgl/ykrkqrlist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},

			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
		
			ykqrrk(e) {
				let that = this;
				let rcdid = e.currentTarget.dataset.rcdid;
				uni.showModal({
					title: '操作确认',
					content: '确认已入库吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
 
							let da = {
								'rcdid': rcdid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/ckgl/ykrkqrczsave', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},



		}
	}
</script>





<style lang='scss'>
	.splist {
		padding: 15upx 32upx 32upx 32upx
	}

	.splist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx
	}
	.splist .item .spbh{position: absolute;right:28upx;top:28upx;font-size:20upx;color:#1677FF}
	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .status {
		position: absolute;
		right: 28upx;
		bottom: 50upx;
		border-radius: 30upx;
		padding: 5upx 15upx;
		color: #fff;
		background: #ddd;
		font-size: 24upx
	}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 240upx;
		height: 240upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: left;
		margin-left: 20upx;
		width: calc(100% - 265upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 45upx;
		line-height: 45upx;
		overflow: hidden;
		margin-bottom: 15upx;
		font-size: 28upx
	}

	.splist .item .inf1 .xx .n2 {
		margin-top: 10upx;
	}

	.splist .item .inf1 .xx .n2 text {
		color: #ff0000
	}

	.splist .item .inf1 .xx .n2 .zj {
		font-weight: 600
	}

	.splist .item .inf2 {
		background: #F0F0F0;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 15upx 28upx;
		font-size: 20rpx;
		color: #333;
		position: relative;
		line-height: 40upx;
	}

	.splist .item .inf2 .rktime {}

	.splist .item .inf2 .f1 {}
	.splist .item .inf2 .f1 .bjbtn{font-size:24upx;margin-left:10upx;color: #1677FF}

	.splist .item .inf2 .f1 .lanse {
		color: #1677FF
	}

	.splist .item .inf2 .f1 .hongse {
		color: #FF2828
	}

	.splist .item .inf2 .setbtn {
		position: absolute;
		right: 15upx;
		top: 45upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .inf2 .setbtn2 {
		position: absolute;
		right: 130upx;
		top: 45upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fff;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .inf2 .setbtn2.mr0 {
		right: 15upx
	}

	.splist .item .inf2 .setbtn3 {
		position: absolute;
		right: 15upx;
		top: 45upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #54D216;
		border-radius: 8rpx;
		text-align: center;
		color: #fff
	}



	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}

	.splist .item .status.sta1 {
		background: #54D216;
	}

	.splist .item .status.sta2 {
		background: #FF2828;
		color: #fff
	}


	.cwcon {
		padding: 30upx 30upx;
	}

	.cwli {
		position: relative;
		border-bottom: 1px solid #efefef;
		padding: 30upx;
		background: #fff;
		margin-bottom: 20upx;
		border-radius: 20upx;
	}

	.cwli .cztype {
		position: absolute;
		right: 30upx;
		top: 30upx;
		color: #1677FF
	}

	.cwli .ghbtn {
		height: 45upx;
		line-height: 45upx;
		background: #FFD427;
		border-radius: 5upx;
		padding: 0 20upx;
		position: absolute;
		right: 30upx;
		bottom: 40upx
	}
	.cwli .ghbtn2{
		height: 45upx;
		line-height: 45upx;
		padding: 0 20upx;
		position: absolute;
		right: 30upx;
		bottom: 120upx;
		color: #1677FF
	}
	.cwli .info {
		font-size: 24upx;
		line-height: 45upx;
	}

	.cwli .pjimgcon {
		margin-top: 10upx;
	}

	.cwli .pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.cwli .pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}

	.cwli .ygidcc {
		font-size: 24upx;
		color: #888;
		margin-left: 20upx
	}

	.cwli .zkbtn {
		color: #888;
		position: absolute;
		right: 20upx;
		top: 70upx;
		font-size: 24upx
	}

	.cwli .des {
		border-top: 1px solid #eee;
		margin-top: 10upx;
		padding-top: 10upx;
		color: #888
	}

	.cwli .dysta {}

	.cwli .dysta.ys1 {
		color: #4aac09
	}

	.cwli .dysta.ys2 {
		color: #ff6600
	}

	.cwli .dysta.ys3 {
		color: #ff0000
	}
</style>