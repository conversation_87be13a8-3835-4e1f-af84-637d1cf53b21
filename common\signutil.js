import MD5 from './md5.js';
 
/**
 * 返回随机字符串
 */
export const createNonceStr=(num)=>{
			
	let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
	let maxPos = $chars.length;
	let str = '';
	for (let i = 0; i < num; i++) {
		str += $chars.charAt(Math.floor(Math.random() * maxPos));
	}
	return str;
 
}
 
/**
 * 返回根据时间戳随机川经过MD5加密后的签名
 */
export const arithmetic = (timeStamp,randomStr,TOKEN,url,data) => {
	let arr = [timeStamp, randomStr, TOKEN,url];
	 let dataStr = dataSerialize(dataSort(data))
	//按照首字母大小写顺序排序
	arr.sort();
	//拼接成字符串
	let str = arr.join("");
	//进行加密
 
	let signature = MD5(str);
	//转换成大写
	signature = signature.toUpperCase();
	return signature;
}

export function signatureGenerate(timeStamp,randomStr,TOKEN,url,data){
	    
	
		data.url=url;	
		data.timestamp=timeStamp;
		data.nonce=randomStr;
    // post参数
    let dataStr = dataSerialize(dataSort(data))

    // 生成签名
    // let str = dataStr + "&timestamp=" + timeStamp + "&url=" + url + "&nonce="+randomStr+'&key='+TOKEN
    let str = dataStr + 'key='+TOKEN
    let sign = MD5(str)

	
    return {
        signature: sign.toUpperCase(), // 将签名字母转为大写
    }
}




// 参数排序
function dataSort(obj){
    if (JSON.stringify(obj) == "{}" || obj == null) {
        return {}
    }
    let key = Object.keys(obj)?.sort()
    let newObj = {}
    for (let i = 0; i < key.length; i++) {

		if(obj[key[i]] && obj[key[i]]!=0 && obj[key[i]] !='' && !Array.isArray(obj[key[i]]) ){
		
			  newObj[key[i]] = obj[key[i]]   
		}
    }
    return newObj
}
 
// 参数序列化
function dataSerialize(sortObj){
    let strJoin = ''
    for(let key in sortObj){
        strJoin += key + "=" + sortObj[key] + "&"
    }
 
    // return strJoin.substring(0, strJoin.length - 1)
    return strJoin
}
