<template>
	<view class="pages-a tn-safe-area-inset-bottom">
		
	  <tn-nav-bar fixed  :alpha="true"  :bottomShadow="false" backgroundColor="#FFD427" >
		  <view slot="back" class='tn-custom-nav-bar__back' >
		  	  <text class='icon tn-icon-left'></text>
		  </view>
		  <view class="tn-flex tn-flex-col-center tn-flex-row-center ">
		    <text  >购买套餐</text>
		  </view>
	  </tn-nav-bar>
	  <view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		  
	
	 <view class="topbg" >
	    <view class="usinfo" >
	        <view class="tx"><image :src="staticsfile + sjdata.logo"></image></view>
	        <view class="xx">
	 				 <view class="name">
	 				   <view class="t1">{{sjdata.title}}</view>
	 				 </view>
	 				 <view class="nid">
	 					 <view class="idv">当前版本：<text class="bb1">{{sjdata.tctxt}}</text></view> 
	 					 <view class="idv" style="margin-left:20upx">到期时间：<text class="bb2">{{sjdata.dqdate}}</text></view>
	 					 <view class="clear"></view>
	 				 </view>
	 			 </view>
	 		 </view>
	 </view>
	 
	 	<view class="tcliscon">
	 
	 			<scroll-view scroll-x="true" style="white-space: nowrap;" >
	 				<block v-for="(item,index) in tcdata" :key="index">
	 					 <view class="tc-item" @click="selthtc" :data-tcid="item.id" :data-tcindex="index" :class="thtcid==item.id ? 'hoverss' : '' " >
	 							<view class="con"  >
									<block v-if="item.ismr==1">
									    <view class="tit">{{item.title}}</view>
	 								    <view class="jiage"><text class="fh">￥</text><text class="jg">{{item.jiage}}</text><text class="fh">/年</text></view>
	 								    <view class="yhjine">￥<text>{{item.yhjine}}</text></view>
									</block>
									<block v-else>
										<view class="tit">{{item.title}}</view>
							            <view class="jiage"><text class="fh">￥</text><text class="jg">{{item.jiage}}</text><text class="fh">/年</text></view>
										<view class="yhjine">￥<text>{{item.yhjine}}</text></view>
									</block>
	 							</view>
	 					 </view>
	 				</block>
	 			</scroll-view>
	 		
	 	</view>
		
		<view class="tcsmcon">
			 <mp-html :content="thtcdata.content" />
		</view>
			
		<block v-if="thtcdata.isgm==1">	
			<view class="tn-flex tn-footerfixed" style="z-index:3000;">
			  <view class="tn-flex-1 justify-content-item tn-text-center">
				<button class="bomsubbtn"  @click="gototj">
				  <text style="font-weight:700">确认提交</text>
				</button>
			  </view>
			</view>	
		</block>
			
		<view style="height:120upx"></view>	
	</view>
</template>
<script>
	
	
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				tcdata:'',
				sjdata:'',
				thtcid:0,
				thtcdata:'',
				clicksta:false,
			};
		},
		
		
		onLoad(options) {
			this.loadData();
		},
		
		onShow() {
			let that=this;
			this.clicksta = false;
		},
		methods: {
		    selthtc(e){
				let tcid=e.currentTarget.dataset.tcid;
				let tcindex=e.currentTarget.dataset.tcindex;
				let thtcdata=this.tcdata[tcindex];
				this.thtcid=tcid;
				this.thtcdata=thtcdata;
			},
		    loadData() {
		    	let that=this;
		    	let da={};
		    	uni.showNavigationBarLoading();
		    	this.$req.post('/v1/taocanbb/index', da)
		    	      .then(res => {
		    	         console.log(222,res);
		    		    uni.hideNavigationBarLoading();
		    			if(res.errcode==0){
		    				that.sjdata = res.data.sjdata;
		    				that.thtcid = res.data.thtcdata.id;
		    				that.thtcdata = res.data.thtcdata;
		    				that.tcdata = res.data.tcdata;
		    			}else{
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack()
								}
							})
		    			}
		    	      })
		    },
			
	
			gototj() {
			      let that=this;
				  uni.showLoading({title: '处理中...'})
				  let da={
					  tcid: this.thtcid,
				  };
		          if (this.clicksta) return;this.clicksta = true;
				  this.$req.post('/v1/taocanbb/gmtcsave', da)
				  		.then(res => {
				  			uni.hideLoading();
							console.log(res);
					   
				  			if(res.errcode==0){
								let odnum=res.data.odnum;
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg,
				  					success() {
				  						setTimeout(function(){
				  							uni.navigateTo({
				  								url:'/pages/cash/pay?odnum='+odnum
				  							})
				  						},1000)
				  					}
				  				});
				  			}else{
								that.clicksta = false;
								uni.showModal({
									content: res.msg,
									showCancel: false,
									success() {
										uni.navigateBack();
									}
								})
				  			}
				  			
				  		})
				  		.catch(err => {
				  		
				  		})
				  
			},
			
			
			
		}
	};
</script>
<style lang="scss">
	
page{background:url("/static/images/loginbg.jpg");background-position: top left;background-size: 100%;}	
	
.pagebg{}
.pagebg image{width:100%;}

.topbg{position: relative;}
.usinfo{margin:0 40upx 0 40upx;height:200upx;padding-top:50upx}
.usinfo .tx{float:left}
.usinfo .tx image{width: 120rpx;height: 120rpx;border: 8upx solid #fff;display: block;border-radius:100px;background:#fff;  }
.usinfo .xx{float:left;margin-left:30upx;padding-top:15upx}
.usinfo .xx .name{ height: 50rpx;font-size: 36rpx;font-weight: 500;color: #333;line-height: 50rpx;}
.usinfo .xx .name .t1{}
.usinfo .xx .nid{margin-top:5upx}
.usinfo .xx .nid .idv{float:left;height: 34rpx;font-size: 24rpx;font-weight: 500;color: #333;line-height: 34rpx;}
.usinfo .xx .nid .idv .bb1{font-weight:700}
.usinfo .xx .nid .idv .bb2{font-weight:700;color:#ff0000;}
	
	
.tcliscon{background: #FFFFFF;border-radius: 20rpx;margin:0 40upx;padding:20upx}	

.tc-item{display: inline-block;margin-right:20upx;position: relative;width: 240rpx;height: 270rpx;background: #FFFAF1;border-radius: 20rpx;border: 2rpx solid #FFF1BA;text-align:center}
.tc-item:last-child{margin-right:0}
.tc-item .tit{height: 50upx;font-size: 32upx;line-height: 50rpx;margin-bottom:20upx;margin-top:48upx}
.tc-item .jiage{font-size:24upx;margin-bottom:20upx} 
.tc-item .jiage .jg{font-size:40upx;font-weight: bold;} 
.tc-item .yhjine{font-size:24upx;text-decoration: line-through}
.tc-item.hoverss{
	background: linear-gradient(180deg, #FBE4AE 0%, #FFFCF7 100%);
	border-radius: 20rpx;
	border: 2rpx solid #FFD427;
	color:#7E4C14;
}

.tcsmcon{background: #FFFFFF;border-radius: 20rpx;margin:30upx 40upx;padding:20upx;min-height:500upx;line-height:50upx;}	
	
</style>
