<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>我的消息</text>
			</view>
			<view slot="right">
				<view class="clsmsg" @click="clsmsgcz"><text class="iconfont icon-qingkong"></text></view>
			</view>
		</tn-nav-bar>

		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 80vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333" :bold="true" :fontSize="28" :badgeOffset="[30, 15]" @change="tabChange"
						backgroundColor="#FFFFFF" :height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 20vw;overflow: hidden;">
					<!-- <text class="iconfont icon-qingkong" @click="clsmsgcz"></text> -->
				</view>
			</view>
		</view>
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 5 + 'px'}"></view>

		<block v-if="listdata.length>0">

			<view class="msglist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="item" @click="gotoview" :data-id="item.id" @longpress="cabtn">
						<view class="tline"></view>
						<block v-if="item.status==2">
							<view class="wdd"><text class="iconfont icon-dian"></text></view>
						</block>
						<view class="rq">
							<text class="iconfont icon-xiaoxi1"></text> <text class="dm">{{item.mdname}}</text>
							<text class="sj">{{item.addtime}}</text>
						</view>
						<view class="tit">{{item.title}}</view>
					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>

		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-wushuju"></text></view>
				<view class="tit">暂无相关消息</view>
			</view>
		</block>

		<view class='tn-tabbar-height'></view>

	</view>


</template>

<script>
	export default {
		data() {
			return {

				staticsfile: this.$staticsfile,
				sta: 0,
				kw: '',
				page: 0,
				current: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						sta: 0,
						name: '全部'
					},
					{
						sta: 2,
						name: '未读消息'
					},
					{
						sta: 1,
						name: '已读消息'
					},
				],

			}
		},

		onLoad() {
			let that = this;
			//    that.page = 0;
			//    that.listdata = [];
			//    that.isloading = false;
			//    that.loadingType = -1;
			//    that.loaditems();

		},
		onShow() {
			let that = this;
			this.loadData();

			setTimeout(function() {
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			}, 100);

		},

		methods: {

			loadData() {
				let that = this;
				let da = {
					tjtype: 21
				}
				this.$req.post('/v1/muser/gettjnum', da)
					.then(res => {
						console.log(111, res);
						if (res.errcode == 0) {
						    that.stadata=res.data.tjdata;
						}
					})
			},
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			onReachBottom() {
				this.loaditems();
			},


			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					sta: that.sta,
					kw: that.thkw ? that.thkw : '',
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/muser/msglist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},



			gotoview(e) {
				let dataset = e.currentTarget.dataset;
				let id = dataset.id;
				let skurl = '/pages/user/msg/view?id=' + id;
				uni.navigateTo({
					url: skurl,
				});
			},


			sortChange(e) {
				let that = this;
				let sortid = e.currentTarget.dataset.sortid;
				that.sortid = sortid;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			cabtn(e) {
				let that = this;
				let id = e.currentTarget.dataset.id;

				uni.showModal({
					title: '删除提示',
					content: '确认删除该条消息吗？',
					success: function(res) {
						if (res.confirm) {
							let da = {
								id: id
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/muser/msgcls', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
						} else if (res.cancel) {

						}
					}
				});
			},

			clsmsgcz() {
				let that = this;
				uni.showModal({
					title: '清理确认',
					content: '清理后不可恢复！确认清理吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/muser/msgcls', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
						}
					}
				})
			}



		}
	}
</script>


<style lang="scss" scoped>
	.pages-a {
		max-height: 100vh;
	}

	.clsmsg {
		height: 40px;
		line-height: 40px;
		margin-right: 32upx
	}

	.clsmsg text {
		font-size: 22px;
	}



	.msglist {}

	.msglist .item {
		position: relative;
		margin: 30upx;
		border-radius: 10upx;
		background: #fff;
		padding: 20upx 40upx 30upx 40upx;
	}

	.msglist .item .wdd {
		position: absolute;
		right: 10px;
		top: 15px;
	}

	.msglist .item .wdd text {
		color: #ff0000;
		font-size: 25px
	}

	.msglist .item .tit {
		font-size: 28rpx;
		height: 34upx;
		line-height: 34upx;
		overflow: hidden;
		margin-top: 10upx
	}

	.msglist .item .rq {
		font-size: 24rpx;
		line-height: 40rpx;
		color: #333
	}

	.msglist .item .rq .sj {
		font-size: 20rpx;
		color: #888;
		margin-left: 20upx
	}

	.msglist .item .rq .iconfont {
		margin-right: 10upx
	}

	.msglist .item .tline {
		position: absolute;
		left: 0;
		top: 28upx;
		width: 10upx;
		height: 30upx;
		background: #FFD427;
		border-radius: 10px;
	}
</style>