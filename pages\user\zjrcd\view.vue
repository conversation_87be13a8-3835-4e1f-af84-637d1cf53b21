<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>出资详情</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">

 
				<view class="maxcon" style="padding-top:28upx">
					<view class="vstit">出资信息<text style="font-size:24upx;font-weight:400;color:#888">（出资编号:{{czdata.id}}）</text></view>
					<view class="wpstatus" :class="'sta'+czdata.status">{{czdata.statusname}}</view>
				
					<view class="rowx">
						<view class="tit">出资时间</view>
						<view class="inpcon">
							<view class="inp">{{czdata.addtime}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">所属门店</view>
						<view class="inpcon">
							<view class="inp">{{czdata.mdname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">出资人</view>
						<view class="inpcon">
							<view class="inp">{{czdata.ygname}}</view>
						</view>
						<view class="clear"></view>
					</view>
				
					<view class="rowx">
						<view class="tit">出资金额</view>
						<view class="inpcon">
							<view class="inp"><text class="jine" style="color:#1677FF">￥{{czdata.jine_change}}</text></view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx">
						<view class="tit">回本金额</view>
						<view class="inpcon">
							<view class="inp">
								<text class="jine" style="color:#ff0000" >￥{{czdata.yhbjine}}</text>
								，待回本:<text class="jine" style="color:#1677FF">￥{{czdata.whbjine}}</text>
							</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">累计分润</view>
						<view class="inpcon">
							<view class="inp">
								<text class="jine" >￥{{czdata.zfenrun}}</text>
							</view>
						</view>
						<view class="clear"></view>
					</view>
					
					<block v-if="czdata.status==3">
						<view class="rowx">
							<view class="tit">亏损金额</view>
							<view class="inpcon">
								<view class="inp">
									<text class="jine" style="color:#4aac09" >-￥{{czdata.ksjine}}</text>
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</block>
					
					<view class="rowx" v-if="czdata.sm">
						<view class="tit">出资说明</view>
						<view class="inpcon">
							<view class="inp">{{czdata.sm}}</view>
						</view>
						<view class="clear"></view>
					</view>

				</view>
 
				<view class="maxcon" >
						<block v-if="djdata">
							
							<view class="rowx" v-if="djdata.ordernum">
								<view class="tit"><text class="redst"></text>原单单据</view>
								<view class="inpcon">
									<view class="icon" @click="gotodjxq" :data-djid="djdata.id" style="position: absolute;right:0;top:0;"><text class="iconfont icon-tiaobodan" style="color:#1677FF"></text></view>
									<view class="inp" @click="gotodjxq" :data-djid="djdata.id" style="color:#1677FF">{{djdata.ordernum}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" v-if="djdata.ddtypetxt">
								<view class="tit"><text class="redst"></text>单据类型</view>
								<view class="inpcon">
									<view class="inp">
										<text class="ddtypeaa" :class="'ys'+djdata.ddtype">{{djdata.ddtypetxt}}</text> 
									</view>
								</view>
								<view class="clear"></view>
							</view>
							
							
						</block>	
						<block v-if="spdata"> 
								<view class="rowx">
									<view class="tit"><text class="redst"></text>物品名称</view>
									<view class="inpcon">
										<view class="inp" style="color:#1677FF" @click="gotospxq" :data-spid="spdata.id">({{spdata.id}}){{spdata.title}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<view class="rowx">
									<view class="tit"><text class="redst"></text>所属类别</view>
									<view class="inpcon">
										<view class="inp">{{spdata.sortname}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<view class="rowx">
									<view class="tit"><text class="redst"></text>物品总价</view>
									<view class="inpcon">
										<view class="inp">
											¥{{spdata.danjia}} x {{spdata.shuliang}} = <text
												style="color:#ff0000">¥{{spdata.orzongjia}}</text>
										</view>
									</view>
									<view class="clear"></view>
								</view>
						</block>
						<block v-if="djdata.ddtype==1">
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>寄卖周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>寄卖时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==2 && djdata.ywtime>0">
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>回收时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						
						<block v-if="djdata.ddtype==3">
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>暂存周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>暂存时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==4"> 
								<view class="rowx"> 
									<view class="tit"><text class="redst"></text>预付金额</view>
									<view class="inpcon">
										<view class="inp">￥{{djdata.yfjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<view class="rowx" >
									<view class="tit"><text class="redst"></text>利率</view>
									<view class="inpcon">
										<view class="inp">{{djdata.lilv}}%</view>
									</view>
									<view class="clear"></view>
								</view> 
								<view class="rowx">
									<view class="tit"><text class="redst"></text>总利息</view>
									<view class="inpcon">
										<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<view class="rowx">
									<view class="tit"><text class="redst"></text>总还款金额</view>
									<view class="inpcon">
										<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<view class="rowx" >
									<view class="tit"><text class="redst"></text>还款方式</view>
									<view class="inpcon">
										<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<view class="rowx" >
									<view class="icon" style="color:#1677FF;z-index:200;">{{djdata.ywzqdwtxt}}</view>
									<view class="tit"><text class="redst"></text>还款周期</view>
									<view class="inpcon">
										<view class="inp">{{djdata.ywzqnum}}</view>
									</view>
									<view class="clear"></view>
								</view>
								<block v-if="djdata.hkfs==2">
									<view class="rowx" >
										<view class="tit"><text class="redst"></text>到期时间</view>
										<view class="inpcon">
											<view class="inp">
												{{djdata.dqtimetxt}}
											</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								
								<block v-if="djdata.hkfs==1">
									<view class="rowx" @click="ckhkjh">
										<view class="icon"><text class="iconfont icon-jiantou"></text></view>
										<view class="tit"><text class="redst"></text>还款计划</view>
										<view class="inpcon">
										   <view class="inp">
											  {{hkjhtip}}
										   </view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
								
					    </block>
									
					
						
				</view>
				
				
				<block v-if="hbrcddata.length > 0">
					<view class="maxcon" style="padding-top:28upx">
						<view class="vstit">资金记录</view>
					    <view class="zjrrlist">
					
							<block v-for="(item,index) in hbrcddata" :key="index">
								<view class="lis-item  ">
									<view class="nrcon">
										<view class="info">
					
											<block v-if="item.type==1">
												<view><text>金额：</text> <text class="jine" >+￥{{item.jine_change}}</text></view>
											</block>
											<block v-else>
												<view><text>金额：</text> <text class="jine"	 style="color:#4aac09">￥{{item.jine_change}}</text></view>
											</block>
											<view><text>时间：</text>{{item.addtime}}</view>
											<view v-if="item.glodnum"><text>单号：</text>{{item.glodnum}}</view>
											<view><text>事项：</text>{{item.sm}}</view>
										
										
											<view class="zkbtn" @click="setvxq" :data-id="item.id" v-if="item.remark || item.picturescarr"> 展开详情 <text class="iconfont icon-jtdown2"></text>
											</view>
											<block v-if="item.id==thvid">
												<view class="des">
													<view v-if="item.remark">备注：{{item.remark}}</view>
													<block v-if="item.picturescarr">
														<view class="spjimgcon">
															<block v-for="(itemx,indexx) in item.picturescarr" :key="indexx">
																<view class='item'>
																	<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
																		:data-picturescarr="item.picturescarr" @tap='previewImagex'>
																	</image>
																</view>
															</block>
															<view class="clear"></view>
														</view>
													</block>
												</view>
											</block>
					
										</view>
										<view class="clear"></view>
									</view>
								</view>
					
							</block>
					
					
					
					
					
					
					</view>
				    </view>
				</block>



				<!-- <block v-if="czdata.status==3">
					<view class="maxcon" style="padding-top:30upx">
						<view class="vstit" style="margin-bottom:25upx">亏损说明</view>

						<view class="rowx"> 
							<view class="tit"><text class="redst"></text>亏损金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#ff0000">{{czdata.ksjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="bzcon" v-if="czdata.ksremark" style="margin-top:20upx">
							{{czdata.ksremark}}
						</view>

						<block v-if="czdata.kspicturesarr">
							<view class="rowxmttp">
								<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
									<block v-for="(itemxx,indexx) in czdata.kspicturesarr" :key="indexx">
										<view class='item'>
											<image :src="staticsfile+itemxx" :data-src='staticsfile + itemxx '
												@click='previewImagexf'></image>
										</view>
									</block>
									<view class="clear"></view>
								</view>
							</view>
						</block>

					</view>
				</block> -->

			</view>





		</view>

        <tn-popup v-model="hkjhshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
        	<view class="popuptit" style="border-bottom:0">还款计划</view>
        	
        	<view class="hkjh-time-smtip">借款{{djdata.ywzqnum}}{{djdata.ywzqdwtxt}}，应还总额</view>
        	<view class="hkjh-time-smjine">¥{{djdata.hkzjine}}</view>
        	
                <scroll-view scroll-y="true" style="height: 700rpx;">
        			
        		  <view class="khjhcon">
        		     <view class="hkjh-timeline">
        				  <view class="hkjh-timeline-content">
        					<block v-for="(item,index) in hkjhdata" :key="index">
        						<view class="item hkjh-timeline-item" :class="'hksta'+item.status">
        							<em class="yd-timeline-icon"></em>
        							<view class="hkjine">
        								¥{{item.yhkzjine}}  
        								<block v-if="item.bqsyhkjine > 0">
        									<text class="yhtip" >已还 {{item.bqyhkjine}}</text>
        								</block>
        								<text class="yhqtxt" v-if="item.status==1">已还清</text>
        								<text class="yyqtxt" v-if="item.status==3">已逾期</text>
        							</view>
        							<!-- <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view> -->
        							<view class="qbcon">
        								<view class="t">{{item.xhtit}}</view>
        								<view class="r">{{item.rq}}</view>
        							</view>
        						</view>
        					</block>
        				  </view>
        		     </view>
        		  </view>
        		  
                </scroll-view>
        </tn-popup>


		<view style="height:120upx"></view>

	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				czrcdid: '',
				czdata: '',
				djdata: '',
				spdata: '',
				bhsm: '',
				jjtgshow: false,
				hkjhshow:false,
				hkjhtip:'',
				hkjhdata:'',
				hbrcddata:'',
				thvid:0,
			}
		},

		onLoad(options) {
			let that = this;
			let czrcdid = options.czrcdid ? options.czrcdid : 0;
			this.czrcdid = czrcdid;
		},
		onShow() {
			this.loadData();
		},

		methods: {
			setvxq(e) {
				let thvid = e.currentTarget.dataset.id;
				if (thvid == this.thvid) {
					this.thvid = 0;
				} else {
					this.thvid = thvid;
				}
			
			},
			gotodjxq(e){
				let djid=e.currentTarget.dataset.djid;
				uni.navigateTo({
					url:'/pages/work/rukudan/view?djid='+djid
				})
			},
            gotospxq(e){
				let spid=e.currentTarget.dataset.spid;
				uni.navigateTo({
					url:'/pages/work/ckgl/ckshop/spview?spid='+spid
				})
			},
			loadData() {
				let that = this;
				let czrcdid = that.czrcdid;
				let da = {
					czrcdid: czrcdid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/muser/zjrcdview', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.czdata = res.data.czdata;
							that.djdata = res.data.djdata;
							that.spdata = res.data.spdata;
							that.hkjhtip = res.data.hkjhtip;
							that.hkjhdata = res.data.hkjhdata;
							that.hbrcddata = res.data.hbrcddata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			}, 
			
			previewImagexf: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			ckhkjh(){
				this.hkjhshow=true; 
			},
			


		}
	}
</script>

<style lang="scss">
	.wpstatus{position: absolute;right:28upx;top:28upx;border-radius:30upx;padding:5upx 15upx;color:#fff;background:#ccc;font-size:24upx}
	.wpstatus.sta1{background:#FF0000;color:#fff}
	.wpstatus.sta2{background:#1677FF;color:#fff}
	.wpstatus.sta3{background:#4aac09;color:#fff}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding:10upx 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .vstit .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}

	.sqbdcon {
		margin: 20upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.pjimgcon {
		margin-top: 20upx;
	}

	.pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #333;
	}

	.rowx:last-child {
		border-bottom: 0;
	}


	.stats.stacolor1 {
		color: #43b058;
	}

	.stats.stacolor2 {
		color: #FA6400;
	}

	.stats.stacolor3 {
		color: #888;
	}
	
	.zjrrlist .lis-item{position: relative;border-bottom: 1px solid #efefef;padding:20upx 0upx;}
	.zjrrlist .lis-item .qtfyzkbtn{position: absolute;right:30upx;top:60upx;font-size:24upx;color:#888}
	.zjrrlist .lis-item .bjbtn{position: absolute;right:30upx;top:20upx;font-size:24upx;}
	.zjrrlist .lis-item .bjbtn text{margin-left:30upx}
	.zjrrlist .lis-item .info {font-size: 28upx;line-height: 45upx;	}
	.zjrrlist .lis-item .jine {color: #ff0000;	}
	.zjrrlist .lis-item .time {font-size: 12px;color: #888;line-height: 15px;margin-top: 10upx;	}
	.zjrrlist .lis-item .spjimgcon {margin-top: 10upx;}
	.zjrrlist .lis-item .spjimgcon .item {float: left;margin-right: 15upx;margin-bottom: 15upx;position: relative	}
	.zjrrlist .lis-item .spjimgcon image {width: 120upx;height: 120upx;display: block;border-radius: 8upx	}
	.zjrrlist .lis-item:last-child{border-bottom:0;}
	.zjrrlist .lis-item .des {}
	.zjrrlist .lis-item .inf2{border-top:1px solid #eee;padding-top:20upx;margin-top:20upx;line-height:40upx;position: relative;color:#888}
	.zjrrlist .zkbtn {color: #888; position:absolute;right:0upx;top:60upx;font-size:24upx}
	
	.imgconmt {}
	
	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}
	
	.imgconmt .item .del {
		position: absolute; 
		right: -7px;
		top: -7px;
		z-index: 200
	}
	
	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}
	
	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}
</style>