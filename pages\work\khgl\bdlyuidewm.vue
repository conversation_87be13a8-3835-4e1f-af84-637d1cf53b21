<template>
  <view class="tn-safe-area-inset-bottom">

    <tn-nav-bar fixed alpha="" backTitle=' '   :bottomShadow="false" :zIndex="100" :isBack="true"  backgroundColor="#FFD427" >
    		<view slot="back" class='tn-custom-nav-bar__back'  >
    			  <text class='icon tn-icon-left'></text>
    		</view>
    		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
    		  <text  >关联客户账号</text>
    		</view>
    </tn-nav-bar>
	<view class="tabs-fixed-blank" ></view>
	
	<view class="fxewmcon">
		<view class="fxinfo1">
			
			<view class="sjname">{{sjdata.title}}</view>
			<view class="ygname">客户：{{khdata.realname}}</view>
			
			<view class="ewmcon">
				  <image class="codeimg " :src="staticsfile + ewmfname"  @click='previewImage' />
			</view>
			
			<view class="smsm">客户使用微信扫一扫绑定该客户</view>
			
			<button class="xzbtn" @click="appfxewm">分享保存</button>
			
		</view>
    </view>
	 <view style="height:120upx"></view>
	<view class="tn-flex tn-footerfixed">
	
	  
	  

	  <!-- #ifdef APP-PLUS -->
	  <!-- #endif -->
	  <!-- #ifdef MP-WEIXIN -->
	  <button class="xzbtn" open-type="share" style="display: none;">分享二维码</button>
	  <!-- #endif -->
	  
	</view>
	
		<tn-popup v-model="fwpopsta" mode="bottom" :borderRadius="20" >
		  <view class="fwxzcon" >
			<view class="closebtn" @click="appfxewmclose"  ><text class="iconfont icon-guanbi4"></text></view>
			   <view class="tsx1">分享邀请</view>
			
			   	<view class="adddtcon">
						 <view class="fxvcon">
								 <view class="fxli" @tap="fxwxhy">
									  <view class="ficon"><image src="/static/images/fximg1.png" /></view>
									  <view class="ftit">微信</view>
								 </view>
								 <view class="fxli"  @tap="fxwxpyq">
									 <view class="ficon"><image src="/static/images/fximg2.png" /></view>
									 <view class="ftit">朋友圈</view>
								 </view>
								 <view class="fxli" @tap="saveImage">
									 <view class="ficon"><image src="/static/images/fximg3.png" /></view>
									 <view class="ftit">保存图片</view>
								 </view>
								 <view class="clear"></view>
						  </view>
			   	</view>
			
			   
		  </view>
		</tn-popup>
	 
				
	</view>
</template>

<script>
	
	export default {

		data() {
			return {
		        staticsfile: this.$staticsfile,
		        sjdata:'',
		        mudata:'',
		        sbma:'',
		        ewmfname:'',
				fwpopsta:false,
				khid:0,
				khdata:'',
			}
		},
		onLoad(options) {
			let that=this;
			let khid=options.khid ? options.khid : 0;
			this.khid=khid;
			that.loadData(); 
		},
		onShow() {
			
		},
		onReady() {
			
		},
		onPullDownRefresh() {
			this.loadData(); 
		},
		methods: {
		
		 loadData(){
		 	  let that=this;
		 	  let da={ 
				  khid:that.khid
		 	  }
		 	  uni.showLoading({title: ''})
		 	 this.$req.post('/v1/khgl/bdlyuidewm', da)
		 	 .then(res => {
				uni.hideLoading();
		 		console.log(res);
		 		if(res.errcode==0){
		 		   that.khdata=res.data.khdata;
		 		   that.sjdata=res.data.sjdata;
		 		   that.mudata=res.data.mudata;
		 		   that.sbma=res.data.sbma;
		 		   that.ewmfname=res.data.ewmfname;
		 		}else{
		 			uni.showToast({
		 				icon: 'none',
		 				title: res.msg,
		 				success() {
		 					setTimeout(function(){
		 						uni.navigateBack();
		 					},1000)
		 				}
		 			});
		 		}
		 	 })
		 },	
		 			
		 previewImage() {
		   let igrr=this.staticsfile + this.ewmfname;
		     uni.previewImage({
				urls: [igrr],
				current: igrr,
		     })
			 
			 uni.showToast({
			 	icon: 'none',
			 	title: '长按保存二维码'
			 });
			 
		  },	
				  
				  
		  onShareAppMessage() {
			let khyqma=this.sbma;
			return {
			  title: '邀请客户',
			  path: '/pages/index/index?khyqma='+khyqma
			};
		  },
		  
		  appfxewm(){
			  this.fwpopsta=true;
		  },
		  appfxewmclose(){
			  this.fwpopsta=false;
		  },
		  
		  togglePopup(type, open) {
		  				let that=this;
		  				this.type = type
		  				this.$nextTick(() => {
		  					this.$refs['show' + open].open()
		  				})
		  			},
		  			Popupcancel(open) {
		  				this.$refs['show' + open].close();
		  			},
		  			Popupchange(e) {
		  				console.log(e);
		  				if(!e.show){
		  				
		  				}
		  			},
		  			fxwxhy:function(){
		  				let that=this;
		  				uni.share({
		  				                    provider: "weixin",
		  				                    scene: "WXSceneSession",
		  				                    type: 2,
		  				                    imageUrl: this.staticsfile + this.ewmfname,
		  				                    success: function (res) {
		  				                        console.log(JSON.stringify(res));
		  				                        uni.showToast({
		  				                            title: '已分享',
		  				                            duration: 2000
		  				                        });
		  				                    },
		  				                    fail: function (err) {
		  				    
		  				                      
		  				                    }
		  				                });
		  			},
		  			fxwxpyq:function(){
		  				let that=this;
		  				uni.share({
		  							provider: "weixin",
		  							scene: "WXSenceTimeline",
		  							type: 2,
		  							imageUrl: this.staticsfile + this.ewmfname,
		  							success: function (res) {
		  								console.log(JSON.stringify(res));
		  								uni.showToast({
		  									title: '已分享',
		  									duration: 2000
		  								});
		  							},
		  							fail: function (err) {
		  			
		  							  
		  							}
		  						});
		  			},
		  			saveImage() {
		  			        this.isShowPopup = false
		  			        uni.saveImageToPhotosAlbum({
		  			            filePath: this.staticsfile + this.ewmfname,
		  			            success(res) {
		  			                uni.showToast({
		  			                    title: '已保存到相册',
		  			                    icon: 'success',
		  			                    duration: 2000
		  			                })
		  			            }
		  			        })
		  			},
		  
		  
		   
		}
	}
</script>

<style lang='scss'>
	
	.pages-a { 
		  max-height: 100vh;
	}
	
	page{
	    background-image:url('/static/images/yqmbg.jpg');
		background-size: cover;
	}
	
	
	
	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 150upx;
		height: calc(140upx + env(safe-area-inset-bottom) / 2);
	}
	
	 
	.tabs-fixed{
	  position: fixed;
	  top: 0;
	  width: 100%;
	  transition: all 0.25s ease-out;
	  z-index: 1;
	}
	
	
	.codeimg {
	     width: 400upx;
	     height: 400upx;
	   }
	   
	.fxewmcon{margin:140upx 80upx 100upx 80upx}   
  .fxewmcon .fxinfo1{
        background:#fff;
        border-radius: 10px;
        padding:80upx 30upx;
        text-align: center;
        position: relative;
		box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(98,80,4,0.21);
   }
   .fxewmcon .fxinfo1 .sjname{font-size: 40rpx;font-weight: 600;margin-bottom:40upx}
   .fxewmcon .fxinfo1 .ygname{font-size: 30rpx;font-weight: 600;margin-bottom:40upx}
   .fxewmcon .fxinfo1 .smsm{font-size: 28rpx;margin-top:50upx}
  
  .fxewmcon .dptit{
	  margin-top:60upx;
	  margin-bottom:40upx;
	  height: 25px;
	  font-size: 18px;
	  font-weight: 500;
	  color: #333333;
	  line-height: 25px;
  }

.xzbtn{
	height: 50px;
	line-height: 50px;
	background: #FFD427;
	border-radius: 100px;
	width: 100%;
	text-align: center;
	font-size: 14px;
	font-weight: 400;
	margin-top:70upx;
}

.fwxzcon{padding:32upx;}
.fwxzcon .closebtn{position: absolute;right:32upx;top:32upx}
.fwxzcon .tsx1{font-weight: 500;color: #333333;line-height: 40rpx;}
.fxvcon{display: flex;text-align: center;padding-top:30upx;}
.fxvcon .fxli{flex: 1;}
.fxvcon .ficon{height:120upx;}
.fxvcon .ficon image{width:120upx;height:120upx;}
.fxvcon .ftit{height:20px;line-height:20px;font-size:14px;margin-top:10upx;}
.fxvqxbtn{text-align: center;border-top:1px solid #efefef;height:100upx;line-height:100upx;font-size:15px;margin-top:30upx}

</style>
