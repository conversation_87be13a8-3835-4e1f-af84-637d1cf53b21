<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
   <tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100" >
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >{{djdata.ddtypetxt}}信息确认</text>
					</view>
					<view slot="right" >
					</view>
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
   
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
										
										
										<view class="maxcon" style="padding-bottom:10upx">
												<view class="vstit">{{djdata.ddtypetxt}}信息</view>
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>业务类型</view>
													<view class="inpcon">
														<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>客户姓名</view>
													<view class="inpcon">
														<view class="inp">{{djdata.khname}}</view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>客户电话</view>
													<view class="inpcon">
														<view class="inp">{{djdata.khtel}}</view>
													</view>
													<view class="clear"></view>
												</view>
											
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>预付金额</view>
													<view class="inpcon">
														<view class="inp">￥{{djdata.yfjine}}</view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>预付时间</view>
													<view class="inpcon">
														<view class="inp">{{djdata.ywtime}}</view>
													</view>
													<view class="clear"></view>
												</view>
												
												
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>还款方式</view>
													<view class="inpcon">
														<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx" >
													<view class="icon" style="color:#1677FF;z-index:200;">{{djdata.ywzqdwtxt}}</view>
													<view class="tit"><text class="redst"></text>还款周期</view>
													<view class="inpcon">
														<view class="inp">{{djdata.ywzqnum}}</view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>利率</view>
													<view class="inpcon">
														<view class="inp">{{djdata.lilv}}%</view>
													</view>
													<view class="clear"></view>
												</view> 
												
										
										
										
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>总利息</view>
													<view class="inpcon">
														<view class="inp">￥{{djdata.lxzjine}}</view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx" >
													<view class="tit"><text class="redst"></text>总还款金额</view>
													<view class="inpcon">
														<view class="inp">￥{{djdata.hkzjine}}</view>
													</view>
													<view class="clear"></view>
												</view>
												
												<block v-if="djdata.hkfs==2">
													<view class="rowx" >
														<view class="tit"><text class="redst"></text>到期时间</view>
														<view class="inpcon">
															<view class="inp">
																{{djdata.dqtimetxt}}
															</view>
														</view>
														<view class="clear"></view>
													</view>
												</block>
												
												<block v-if="djdata.hkfs==1">
													<view class="rowx" @click="ckhkjh">
														<view class="icon"><text class="iconfont icon-jiantou"></text></view>
														<view class="tit"><text class="redst"></text>还款计划</view>
														<view class="inpcon">
														   <view class="inp">
															  {{hkjhtip}}
														   </view>
														</view>
														<view class="clear"></view>
													</view>
												</block>
												
										</view>	
										
								
									
									
									<view class="maxcon" style="padding-bottom:10upx">
										
											<view class="rowx" >
												<view class="tit"><text class="redst"></text>业务人员</view>
												<view class="inpcon">
													<view class="inp">{{djdata.ywrytxtstr}}</view>
												</view>
												<view class="clear"></view>
											</view>
											<view class="rowx" >
												<view class="tit"><text class="redst"></text>录单人员</view>
												<view class="inpcon">
													<view class="inp">{{djdata.opname}}</view>
												</view>
												<view class="clear"></view>
											</view>
										
									</view>	
															
									<view class="maxcon">
										<view class="rowxmttp">
											  <view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
												<view class="zjzkicon" @click="setvzz">{{vsfnr==0 ? '展开' : '收起'}} <text
																				class="iconfont icon-jiantou_liebiaozhankai"></text></view>											 
																		
											<block v-if="vsfnr==1">
											
													 <view class="uprzimgcon" >
																		 <view class="zzx sf1" >
																			 <view class="con">
																				 <view class="vt1"   >	  
																						<block v-if="djdata.rzimg1" >
																							<image :src='staticsfile + djdata.rzimg1'></image>
																						</block>
																						<block v-else>
																							<image src="/static/images/rzupimg1.png"></image>
																						</block>
																				 </view>
																				  <view class="vt2">身份证头像面</view>
																				 <view class="clear"></view>
																			 </view>
																		 </view>
																		 <view class="zzx sf2"   >
																			 <view class="con"  >
																				 <view class="sf2" >	 
																						<block v-if="djdata.rzimg2" >
																							<image :src='staticsfile + djdata.rzimg2'></image>
																						</block>
																						<block v-else>
																							<image src="/static/images/rzupimg2.png"></image>
																						</block>	
																				  </view>	
																				  <view class="vt2">身份证国徽面</view>
																			 </view>
																		 </view>
																	<view class="clear"></view>								
													 </view>
													 
													 
													 <view class="rowx" style="margin-top:20upx;" v-if="djdata.khsfzhao">
													 	<view class="tit"><text class="redst"></text>身份证号</view>
													 	<view class="inpcon">
													 	   <view class="inp">{{djdata.khsfzhao}}</view>
													 	</view>
													 	<view class="clear"></view>
													 </view>
													 
													 <view class="rowx" style="margin-top:15upx;position: relative;" v-if="djdata.isrlsb==1">
													
													 	<view class="tit"><text class="redst"></text>人脸识别</view>
													 	<view class="inpcon">
													 	   <view class="inp" >
																{{djdata.isrlsb==1 ? '需要' : '不需要'}}
																
													 	   </view>
													 	</view>
													 	<view class="clear"></view>
													 </view>
													 
									        </block>	
											   												   
										</view>		 
									</view>		 
									
									<view class="maxcon">
											<view class="vstit" style="font-weight:400;margin-bottom:25upx">单据备注说明 <text style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>
											
											<view class="bzcon">
												  <textarea name="remark" class="beizhu" v-model="remark"></textarea>
											</view>
											 
									</view>		
									
				  			</view>	
				  			
				  			
							
							
							<view class="tn-flex tn-footerfixed">
							  <view class="tn-flex-1 justify-content-item tn-text-center">
								  <view class="bomsfleft">
									  <button class="bomsubbtn"  @click="toback"  style="background:#ddd;" >
										  <text>上一步</text>
									  </button>
								  </view>
								  <view class="bomsfright">
									  <button class="bomsubbtn"  form-type="submit">
									    <text>提交审核</text>
									  </button>
								  </view> 	  
								  <view class="clear"></view>	
							  </view>
							</view>
							
							
				  </form>
				  
				
				
				
		
			

	
	</view>
  
	<view style="height:120upx"></view>
    
	
	
	
	<tn-popup v-model="hkjhshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
		<view class="popuptit" style="border-bottom:0">还款计划</view>
		
		<view class="hkjh-time-smtip">借款{{djdata.ywzqnum}}{{djdata.ywzqdwtxt}}，应还总额</view>
		<view class="hkjh-time-smjine">¥{{djdata.hkzjine}}</view>
		
	        <scroll-view scroll-y="true" style="height: 700rpx;">
				
			  <view class="khjhcon">
			     <view class="hkjh-timeline">
					  <view class="hkjh-timeline-content">
						  <block v-for="(item,index) in hkjhdata" :key="index">
							  <view class="item hkjh-timeline-item">
								  <em class="yd-timeline-icon"></em>
								  <view class="hkjine">¥{{item.yhkzjine}}</view>
								  <!-- <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view> -->
								  <view class="qbcon">
								     <view class="t">{{item.xhtit}}</view>
								     <view class="r">{{item.rq}}</view>
								  </view>
							  </view>
						  </block>
					  </view>
			     </view>
			  </view>
			  
	        </scroll-view>
	</tn-popup>
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 djid:0,
			 staticsfile: this.$staticsfile,
			 img_url: [],
			 img_url_ok:[],
			 ywtime:'',
			 djdata:'',
			 vspxq:0,
			 clicksta:false,
		     remark:'',
			 hkjhshow:false,
			 hkjhtip:'',
			 hkjhdata:'',
			 vsfnr: 0,
		  }
	},

	onLoad(options) {
		let that=this;
		let djid=options.djid ? options.djid : 0;
		this.djid=djid;
		this.loadData();
	},
	
	onShow() {
		
	},

    methods: {
		 	  ckhkjh(){
				this.hkjhshow=true; 
			  },
			  toback(){
				 uni.navigateBack(); 
			  },
			
		     loadData(){
				  let that=this;
				  let djid=that.djid; 
				  let da={  
				 	djid:djid,
					et:2,
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/danju/rkdeditx41', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log('232',res);
				 			if(res.errcode==0){
				 		        that.djdata=res.data.djdata;
				 		        that.remark=res.data.djdata.remark;
				 		        that.hkjhtip=res.data.hkjhtip;
								that.hkjhdata=res.data.hkjhdata;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
		
	setvzz() {
		if (this.vsfnr == 0) {
			this.vsfnr = 1;
		} else {
			this.vsfnr = 0;
		}
	},
		
		
		previewImagex: function (e) {
				  let that = this;
				  let src = e.currentTarget.dataset.src;
				  let imgarr=[src];
				  wx.previewImage({
					  current: src,
					  urls: imgarr
				  });
		},
		
		
		formSubmit(e){
			let that=this;
			let pvalue = e.detail.value;
			uni.showModal({
				title: '提交确认',
				content: '确认提交审核吗？',
				success: function(e) {
					//点击确定
					if (e.confirm) {
			
						let da = {
							'djid': that.djid,
							'remark': pvalue.remark,
						}
						uni.showLoading({title: ''})
						that.$req.post('/v1/danju/rkdtjsh', da)
							.then(res => {
								uni.hideLoading();
								if (res.errcode == 0) {
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function() {
												// uni.redirectTo({
												// 	url:'/pages/work/rukudan/index'
												// })
												uni.setStorageSync('thupdpgllist',1);
												uni.navigateBack({
													delta:3});
											}, 1000);
										}
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false,
									})
								}
							})
			
					}
			
				}
			})
		},
	
    }
  }
</script>

<style lang="scss" >

.sqbdcon{margin:32upx;}
.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
	border-bottom:1px solid #efefef;
}
.rowx .icon{position: absolute;right:2upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 85px);font-size:28upx;height:90upx;line-height:90upx;overflow: hidden;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{}
.rowx .inp .input{height:90upx;line-height:90upx;}
.rowx:last-child{border-bottom:0;}

.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx;position: relative;}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .more{position: absolute;right:28upx;top:28upx;color:#888;}
.maxcon .more .t1{color: #7F7F7F;}
.maxcon .more.vls{color: #1677FF;}
.maxcon .more .iconfont{margin-left:10upx}


.spitem{margin-bottom:20upx;padding-bottom:10upx;border-bottom:1px solid #efefef;}
.spitem .spname{font-weight:600;margin-bottom:20upx;font-size:32upx;position: relative;padding-left:20upx;line-height:40upx;}
.spitem .spname .tline{width:10upx ;background: #FFD427;height:32upx;border-radius:6upx;position: absolute;left:0;top:3upx;}
.spitem .czcon{margin-top:30upx}
.spitem .czcon .czbtn{width: 48%;height: 80upx;line-height:80upx;border-radius: 8rpx;border: 1rpx solid #F0F0F0;text-align: center}
.spitem .czcon .btn1{color:#ff0000;float:left}
.spitem .czcon .btn2{float:right}
.spitem .viewxqcon{color:#7F7F7F;height:50upx;line-height:50upx;margin-bottom:10upx;text-align: left;margin-top:20upx}
.spitem:last-child{border-bottom:0;padding-botom:0;margin-bottom:0}

.rowa{height:90upx;line-height:90upx;border-bottom:1px solid #efefef;overflow: hidden;}
.rowa .tip{float:left;color:#7F7F7F}
.rowa .nr{float:right}
.rowa:last-child{border-bottom:0;}
.bzcon{color:#7F7F7F;margin-bottom:20upx}

.rowxmttp{margin-top:0upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:200}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}


.uprzimgcon{margin-top:25upx}
.uprzimgcon .zzx{background: #EBEBEB;border-radius:10upx;width:48%;text-align: center;}
.uprzimgcon .zzx.sf1{float:left;}
.uprzimgcon .zzx.sf2{float:right;}
.uprzimgcon .zzx .vt1{font-size: 36rpx;}
.uprzimgcon .zzx .vt2{font-size: 24rpx;color: #7F7F7F;margin-top:25upx}
.uprzimgcon .con{padding:20upx}
.uprzimgcon image{width: 100%;height:180upx;display: block;border-radius:10upx;}

.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

	.zjzkicon {
		position: absolute;
		right: 25upx;
		top: 25upx;
	}

</style>

