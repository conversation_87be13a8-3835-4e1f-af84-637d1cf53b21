<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >客户等级</text>
					</view>
					<view slot="right" ></view>
	</tn-nav-bar>
	
	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
	
    
   	<view class="" >
   
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
				  				  <view class="maxcon" @click="sethydjmax">
									  客户等级上限 <text style="margin-left:50upx">{{omaxhydj}}</text>
								  </view>
				  			</view>	
							
							<view class="hjdjcon">
								<view class="hydjitem">
									<view class="dftit" @click="vmxx" :data-dj="1" ><view class="tit">{{hydengji.title1}}</view><view class="more">默认等级 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
									<view class="xxcon" :class="thdj==1 ? 'hoverss' : '' " style="padding-bottom:20upx">
										<view class="rowx">
											<view class="tit">等级名称</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="title1"  v-model="hydengji.title1" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
									</view>
								</view>
							
							
							
							
							
							
								<view class="hydjitem" v-show="omaxhydj > 1" >
									<view class="dftit" @click="vmxx" :data-dj="2" ><view class="tit">{{hydengji.title2}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
									<view class="xxcon" :class="thdj==2 ? 'hoverss' : '' " >
										<view class="rowx">
											<view class="tit">等级名称</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="title2"  v-model="hydengji.title2" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易次数</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jycs2"  v-model="hydengji.jycs2" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jyjine2"  v-model="hydengji.jyjine2" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">盈利金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="yljine2"  v-model="hydengji.yljine2" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该客户等级</view>
										
									</view>
								</view>
							
							
							
								<view class="hydjitem" v-show="omaxhydj > 2" >
									<view class="dftit" @click="vmxx" :data-dj="3" ><view class="tit">{{hydengji.title3}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
									<view class="xxcon" :class="thdj==3 ? 'hoverss' : '' " >
										<view class="rowx">
											<view class="tit">等级名称</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="title3"  v-model="hydengji.title3" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易次数</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jycs3"  v-model="hydengji.jycs3" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jyjine3"  v-model="hydengji.jyjine3" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">盈利金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="yljine3"  v-model="hydengji.yljine3" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该客户等级</view>
										
									</view>
								</view>
							
							
							
							
							
							
								<view class="hydjitem" v-show="omaxhydj > 3" >
									<view class="dftit" @click="vmxx" :data-dj="4" ><view class="tit">{{hydengji.title4 ? hydengji.title4 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
									<view class="xxcon" :class="thdj==4 ? 'hoverss' : '' " >
										<view class="rowx">
											<view class="tit">等级名称</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="title4"  v-model="hydengji.title4" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易次数</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jycs4"  v-model="hydengji.jycs4" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jyjine4"  v-model="hydengji.jyjine4" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">盈利金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="yljine4"  v-model="hydengji.yljine4" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该客户等级</view>
										
									</view>
								</view>
							
							
							
								<view class="hydjitem" v-show="omaxhydj > 4" >
									<view class="dftit" @click="vmxx" :data-dj="5" ><view class="tit">{{hydengji.title5 ? hydengji.title5 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
									<view class="xxcon" :class="thdj==5 ? 'hoverss' : '' " >
										<view class="rowx">
											<view class="tit">等级名称</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="title5"  v-model="hydengji.title5" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易次数</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jycs5"  v-model="hydengji.jycs5" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">交易金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="jyjine5"  v-model="hydengji.jyjine5" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="rowx">
											<view class="tit">盈利金额</view>
											<view class="inpcon">
											   <view class="inp"><input class="input" type="text" name="yljine5"  v-model="hydengji.yljine5" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
										</view>
										
										<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该客户等级</view>
										
									</view>
								</view>
							
							
							
							
							      <view class="hydjitem" v-show="omaxhydj > 5" >
							      	<view class="dftit" @click="vmxx" :data-dj="6" ><view class="tit">{{hydengji.title6 ? hydengji.title6 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
							      	<view class="xxcon" :class="thdj==6 ? 'hoverss' : '' " >
							      		<view class="rowx">
							      			<view class="tit">等级名称</view>
							      			<view class="inpcon">
							      			   <view class="inp"><input class="input" type="text" name="title6"  v-model="hydengji.title6" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
							      			</view>
							      			<view class="clear"></view>
							      		</view>
							      		
							      		<view class="rowx">
							      			<view class="tit">交易次数</view>
							      			<view class="inpcon">
							      			   <view class="inp"><input class="input" type="text" name="jycs6"  v-model="hydengji.jycs6" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
							      			</view>
							      			<view class="clear"></view>
							      		</view>
							      		
							      		<view class="rowx">
							      			<view class="tit">交易金额</view>
							      			<view class="inpcon">
							      			   <view class="inp"><input class="input" type="text" name="jyjine6"  v-model="hydengji.jyjine6" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
							      			</view>
							      			<view class="clear"></view>
							      		</view>
							      		
							      		<view class="rowx">
							      			<view class="tit">盈利金额</view>
							      			<view class="inpcon">
							      			   <view class="inp"><input class="input" type="text" name="yljine6"  v-model="hydengji.yljine6" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
							      			</view>
							      			<view class="clear"></view>
							      		</view>
							      		
							      		<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该会员等级</view>
							      		
							      	</view>
							      </view>
							
							
							
									
									<view class="hydjitem" v-show="omaxhydj > 6" >
										<view class="dftit" @click="vmxx" :data-dj="7" ><view class="tit">{{hydengji.title7 ? hydengji.title7 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
										<view class="xxcon" :class="thdj==7 ? 'hoverss' : '' " >
											<view class="rowx">
												<view class="tit">等级名称</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="title7"  v-model="hydengji.title7" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易次数</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jycs7"  v-model="hydengji.jycs7" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jyjine7"  v-model="hydengji.jyjine7" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">盈利金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="yljine7"  v-model="hydengji.yljine7" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该会员等级</view>
											
										</view>
									</view>
							
									<view class="hydjitem" v-show="omaxhydj > 7" >
										<view class="dftit" @click="vmxx" :data-dj="8" ><view class="tit">{{hydengji.title8 ? hydengji.title8 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
										<view class="xxcon" :class="thdj==8 ? 'hoverss' : '' " >
											<view class="rowx">
												<view class="tit">等级名称</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="title8"  v-model="hydengji.title8" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易次数</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jycs8"  v-model="hydengji.jycs8" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jyjine8"  v-model="hydengji.jyjine8" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">盈利金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="yljine8"  v-model="hydengji.yljine8" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该会员等级</view>
											
										</view>
									</view>
							
							
							
							
							
									<view class="hydjitem" v-show="omaxhydj > 8" >
										<view class="dftit" @click="vmxx" :data-dj="9" ><view class="tit">{{hydengji.title9 ? hydengji.title9 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
										<view class="xxcon" :class="thdj==9 ? 'hoverss' : '' " >
											<view class="rowx">
												<view class="tit">等级名称</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="title9"  v-model="hydengji.title9" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易次数</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jycs9"  v-model="hydengji.jycs9" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jyjine9"  v-model="hydengji.jyjine9" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">盈利金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="yljine9"  v-model="hydengji.yljine9" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该会员等级</view>
											
										</view>
									</view>
							
							
							
							
									<view class="hydjitem" v-show="omaxhydj > 9" >
										<view class="dftit" @click="vmxx" :data-dj="10" ><view class="tit">{{hydengji.title10 ? hydengji.title10 : '未设置'}}</view><view class="more">升级条件 <text class="iconfont icon-jiantou_liebiaozhankai"></text></view></view>
										<view class="xxcon" :class="thdj==10 ? 'hoverss' : '' " >
											<view class="rowx">
												<view class="tit">等级名称</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="title10"  v-model="hydengji.title10" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易次数</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jycs10"  v-model="hydengji.jycs10" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">交易金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="jyjine10"  v-model="hydengji.jyjine10" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit">盈利金额</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="yljine10"  v-model="hydengji.yljine10" placeholder="请输等级名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="sjsm"><text>*</text> 满足以上任意条件即可达到该会员等级</view>
											
										</view>
									</view>
							
							
							
							
							
							
							
							
							
							
							</view>
							
							
				  			
				  			<view class="tn-flex tn-footerfixed">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>完成</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
	</view>
  
	<view style="height:120px"></view>
    
	
	<tn-modal v-model="hydjmaxmodal" :custom="true" :showCloseBtn="true">
	  <view class="custom-modal-content">
	    <view class="">
	      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">会员等级范围(1~10)</view>
	      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
	        <input placeholder="请输入会员等级范围1-10" v-model="maxhydj" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align:center"></input>
	      </view>
	    </view>
	    <view class="tn-flex-1 justify-content-item  tn-text-center">
	      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="uphydjmax" >
	        <text style="color:#333">确认提交</text>
	      </tn-button>
	    </view>
	  </view>
	</tn-modal>
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
		     hydjmaxmodal:false,
		     maxhydj:'',
		     omaxhydj:'',
		     hydengji:'',
		     thdj:1,
		  }
	},

	onLoad(options) {
		let that=this;
		this.loadData();
	},

    methods: {
		
		sethydjmax(){
			this.hydjmaxmodal=true;
		},
		vmxx(e){
			let dj=e.currentTarget.dataset.dj;
			this.thdj=dj;
		},
		
		loadData(){
				  let that=this;
				  let id=that.id; 
				  let da={  
				 	id:id,
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/sjgl/hydengji', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 		        that.hydengji=res.data.hydengji;
				 		        that.maxhydj=res.data.maxhydj;
				 		        that.omaxhydj=res.data.maxhydj;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			
		uphydjmax(){
				let that=this;
				let zdtype=that.zdtype;
				let maxhydj=that.maxhydj;
				if(maxhydj < 1 || maxhydj > 10 ){
					uni.showModal({
						content: '会员等级范围1~10',
						showCancel: false
					})
					return false;
				}
				
				uni.showLoading({title: '处理中...'})
				let da={
					  'maxhydj': maxhydj
				}
				this.$req.post('/v1/sjgl/uphydjmax', da)
						.then(res => {
							uni.hideLoading();
							if(res.errcode==0){
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function(){
												 that.loadData();
												 that.hydjmaxmodal=false;
											},1000)
										}
									});
							}else{
								uni.showModal({
									content: res.msg,
									showCancel: false
								})
							}
							
						})
						.catch(err => {
							
			         	})
		},
		
		
		
		
			formSubmit: function(e) {
						let that=this;
					   var formdata = e.detail.value
						
							
							 let pvalue = e.detail.value;
				
								if(!pvalue.title1){
								  uni.showToast({
									icon: 'none',
									title: '请输入默认等级名称',
								  });
								  return false;
								}
										
							  uni.showLoading({title: '处理中...'})
							  let da={
								'title1': pvalue.title1 ?  pvalue.title1 : '',
						
						        'title2': pvalue.title2 ?  pvalue.title2 : '',
						        'jycs2': pvalue.jycs2 ?  pvalue.jycs2 : 0,
						        'jyjine2': pvalue.jyjine2 ?  pvalue.jyjine2 : 0,
						        'yljine2': pvalue.yljine2 ?  pvalue.yljine2 : 0,
						
						        'title3': pvalue.title3 ?  pvalue.title3 : '',
						        'jycs3': pvalue.jycs3 ?  pvalue.jycs3 : 0,
						        'jyjine3': pvalue.jyjine3 ?  pvalue.jyjine3 : 0,
						        'yljine3': pvalue.yljine3 ?  pvalue.yljine3 : 0,
								
								
								'title4': pvalue.title4 ?  pvalue.title4 : '',
								'jycs4': pvalue.jycs4 ?  pvalue.jycs4 : 0,
								'jyjine4': pvalue.jyjine4 ?  pvalue.jyjine4 : 0,
								'yljine4': pvalue.yljine4 ?  pvalue.yljine4 : 0,
								
								
								'title5': pvalue.title5 ?  pvalue.title5 : '',
								'jycs5': pvalue.jycs5 ?  pvalue.jycs5 : 0,
								'jyjine5': pvalue.jyjine5 ?  pvalue.jyjine5 : 0,
								'yljine5': pvalue.yljine5 ?  pvalue.yljine5 : 0,
								
								
								'title6': pvalue.title6 ?  pvalue.title6 : '',
								'jycs6': pvalue.jycs6 ?  pvalue.jycs6 : 0,
								'jyjine6': pvalue.jyjine6 ?  pvalue.jyjine6 : 0,
								'yljine6': pvalue.yljine6 ?  pvalue.yljine6 : 0,
								
								
								'title7': pvalue.title7 ?  pvalue.title7 : '',
								'jycs7': pvalue.jycs7 ?  pvalue.jycs7 : 0,
								'jyjine7': pvalue.jyjine7 ?  pvalue.jyjine7 : 0,
								'yljine7': pvalue.yljine7 ?  pvalue.yljine7 : 0,
								
								
								'title8': pvalue.title8 ?  pvalue.title8 : '',
								'jycs8': pvalue.jycs8 ?  pvalue.jycs8 : 0,
								'jyjine8': pvalue.jyjine8 ?  pvalue.jyjine8 : 0,
								'yljine8': pvalue.yljine8 ?  pvalue.yljine8 : 0,
								
								
								'title9': pvalue.title9 ?  pvalue.title9 : '',
								'jycs9': pvalue.jycs9 ?  pvalue.jycs9 : 0,
								'jyjine9': pvalue.jyjine9 ?  pvalue.jyjine9 : 0,
								'yljine9': pvalue.yljine9 ?  pvalue.yljine9 : 0,
								
								
								'title10': pvalue.title10 ?  pvalue.title10 : '',
								'jycs10': pvalue.jycs10 ?  pvalue.jycs10 : 0,
								'jyjine10': pvalue.jyjine10 ?  pvalue.jyjine10 : 0,
								'yljine10': pvalue.yljine10 ?  pvalue.yljine10 : 0,
						
							  }
							  this.$req.post('/v1/sjgl/uphydengji', da)
									  .then(res => {
										uni.hideLoading();
										console.log(res);
										if(res.errcode==0){
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													setTimeout(function(){
														uni.navigateBack()
													},1000)
												}
											});
										}else{
											uni.showModal({
												content: res.msg,
												showCancel: false
											})
										}
										
									  })
									  .catch(err => {
									
									  })
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.sqbdcon{margin:32upx;}
.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
 
.hjdjcon{margin:20upx 32upx}
.hydjitem{margin-bottom:20upx}
.hydjitem .dftit{height: 96upx;line-height:96upx;background: #FFD427;border-radius: 20upx;padding:0 28upx;position: relative;z-index:100;}
.hydjitem .dftit .tit{float:left;font-size: 28rpx;font-weight:600}
.hydjitem .dftit .more{float:right;font-size: 28rpx;}
.hydjitem .dftit .more .iconfont{font-size: 28rpx;}
 
.xxcon{background:#fff;padding-top:40upx;margin-top:-20upx;border-radius: 0 0 20upx 20upx;display: none;}
.xxcon .sjsm{font-size:24upx;padding:10upx 20upx 30upx 25upx ; color: #7F7F7F;}
.xxcon .sjsm text{color: #1677FF;}
.xxcon.hoverss{display: block;}

.rowx{position: relative; padding:0 30upx}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inp{padding-top:10upx}
.rowx .inp .input{height:66upx;line-height:66upx;border: 1rpx solid #F0F0F0;border-radius:10upx;padding:0 20upx}
 
 
 
</style>

