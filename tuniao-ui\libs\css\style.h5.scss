/* H5的时候，隐藏滚动条 */
::-webkit-scrollbar {
	display: none;  
	width: 0 !important;  
	height: 0 !important;  
	-webkit-appearance: none;  
	background: transparent;  
}

/* 双标签 start*/
.capsule {
  display: inline-flex;
  vertical-align: middle;
  width: 20%;
  min-width: 136rpx;
  height: 45rpx;
  
  .capsule-tag {
    margin: 0;
    
    &:first-child {
      border-top-right-radius: 0rpx;
      border-bottom-right-radius: 0rpx;
    }
    
    &:last-child {
      
      &::after {
        border-top-left-radius: 0rpx;
        border-bottom-left-radius: 0rpx;
      }
    }
  }
}
/* 双标签 end*/