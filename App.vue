<script>
  import Vue from 'vue'
  import store from './store/index.js'
  import updateCustomBarInfo from './tuniao-ui/libs/function/updateCustomBarInfo.js'

	export default {
		methods: {
	
		},
		onLaunch: function() {
						uni.getSystemInfo({
						  success: function(e) {
							// #ifndef H5
							// 获取手机系统版本
							const system = e.system.toLowerCase()
							const platform = e.platform.toLowerCase()
							// 判断是否为ios设备
							if (platform.indexOf('ios') != -1 && (system.indexOf('ios') != -1 || system.indexOf('macos') != -1)) {
							  Vue.prototype.SystemPlatform = 'apple'
							} else if (platform.indexOf('android') != -1 && (system.indexOf('android') != -1)) {
							  Vue.prototype.SystemPlatform = 'android'
							} else {
							  Vue.prototype.SystemPlatform = 'devtools'
							}
							// #endif
						  }
						})
		
					  // 获取设备的状态栏信息和自定义顶栏信息
					  // store.dispatch('updateCustomBarInfo')
					  updateCustomBarInfo().then((res) => {
						store.commit('$tStore', {
						  name: 'vuex_status_bar_height',
						  value: res.statusBarHeight
						})
						store.commit('$tStore', {
						  name: 'vuex_custom_bar_height',
						  value: res.customBarHeight
						})
					  })
				
						// #ifdef MP-WEIXIN
								//更新检测
								if (wx.canIUse('getUpdateManager')) {
								  const updateManager = wx.getUpdateManager();
								  updateManager && updateManager.onCheckForUpdate((res) => {
									if (res.hasUpdate) {
									  updateManager.onUpdateReady(() => {
										uni.showModal({
										  title: '更新提示',
										  content: '新版本已经准备就绪，是否需要重新启动应用？',
										  success: (res) => {
											if (res.confirm) {
											  uni.clearStorageSync() // 更新完成后刷新storage的数据
											  updateManager.applyUpdate()
											}
										  }
										})
									  })
								
									  updateManager.onUpdateFailed(() => {
										uni.showModal({
										  title: '已有新版本上线',
										  content: '小程序自动更新失败，请删除该小程序后重新搜索打开哟~~~',
							  showCancel: false
										})
									  })
									} else {
									  //没有更新
									}
								  })
								} else {
								  uni.showModal({
									title: '提示',
									content: '当前微信版本过低，无法使用该功能，请更新到最新的微信后再重试。',
						showCancel: false
								  })
								}
					// #endif
					
					
					
					
					// #ifdef APP-PLUS
					 
					 			   //           let osname = plus.os.name; 
					 						//  if (osname == 'Android') {
					 						// 	 console.log('Android1');
					 						// 	var main = plus.android.runtimeMainActivity();
					 						// 	var pkName = main.getPackageName();
					 						// 	var uid = main.getApplicationInfo().plusGetAttribute("uid");
					 						// 	var NotificationManagerCompat = plus.android.importClass("androidx.core.app.NotificationManagerCompat");
					 						// 	var areNotificationsEnabled = NotificationManagerCompat.from(main).areNotificationsEnabled();
					 						// 	// 未开通‘允许通知’权限，则弹窗提醒开通，并点击确认后，跳转到系统设置页面进行设置  
					 						// 	if (!areNotificationsEnabled) { 
					 						// 	　　uni.showModal({
					 						// 	　　　　title: '提示',  
					 						// 	　　　　content: '为了接收新订单通知，请先打开APP通知权限',  
					 						// 	　　    showCancel: true,  
					 						// 	　　　　success: function (res) {  
					 						// 	　　　　　　if (res.confirm) {  
					 						// 	　　　　　　　　var Intent = plus.android.importClass('android.content.Intent');
					 						// 	　　　　　　　　var Build = plus.android.importClass("android.os.Build");
					 						// 	　　　　　　　　//android 8.0引导  
					 						// 	　　　　　　　　if (Build.VERSION.SDK_INT >= 26) {
					 						// 	　　　　　　　　　　var intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
					 						// 	　　　　　　　　　　intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
					 						// 	　　　　　　　　} else if (Build.VERSION.SDK_INT >= 21) { //android 5.0-7.0  
					 						// 	　　　　　　　　　　var intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
					 						// 	　　　　　　　　　　intent.putExtra("app_package", pkName);
					 						// 	　　　　　　　　　　intent.putExtra("app_uid", uid);
					 						// 	　　　　　　　　} else { //(<21)其他--跳转到该应用管理的详情页
					 						// 	　　　　　　　　　　var Settings = plus.android.importClass("android.provider.Settings");
					 						// 	　　　　　　　　　　var Uri = plus.android.importClass("android.net.Uri");
					 						// 	　　　　　　　　　　var intent = new Intent();
					 						// 	　　　　　　　　　　intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
					 						// 	　　　　　　　　　　var uri = Uri.fromParts("package", main.getPackageName(), null);
					 						// 	　　　　　　　　　　intent.setData(uri);
					 						// 	　　　　　　　　}
					 						// 	　　　　　　　　// 跳转到该应用的系统通知设置页  
					 						// 	　　　　　　　　main.startActivity(intent); 
					 						// 	　　　　　　}  
					 						// 	　　　　}
					 						// 	　　});
					 						// 	}
					 							
					 						// }
					 						
					 						//  if (osname == 'iOS') {
					 						//  			var UIApplication = plus.ios.import("UIApplication");  
					 						//  			var app = UIApplication.sharedApplication();  
					 						//  			var enabledTypes  = 0;  
					 						//  			if (app.currentUserNotificationSettings) {  
					 						//  			    var settings = app.currentUserNotificationSettings();  
					 						//  			    enabledTypes = settings.plusGetAttribute("types");  
					 						//  			} else {  
					 						//  			    //针对低版本ios系统  
					 						//  			    enabledTypes = app.enabledRemoteNotificationTypes();  
					 						//  			}  
					 						//  			plus.ios.deleteObject(app);  
					 						//  			if ( 0 == enabledTypes ) {  
					 						//  			    uni.showModal({  
					 						//  			        title: '提示',  
					 						//  			        content: '请先打开APP通知权限',  
					 						//  			        showCancel: true,  
					 						//  			        success: function (res) {  
					 						//  			            if (res.confirm) {  
					 						//  			                var UIApplication = plus.ios.import("UIApplication");  
					 						//  			                var NSURL = plus.ios.import("NSURL");  
					 						//  			                var setting = NSURL.URLWithString("app-settings:");  
					 						//  			                var application = UIApplication.sharedApplication();  
					 						//  			                application.openURL(setting);  
					 						//  			                plus.ios.deleteObject(setting);  
					 						//  			                plus.ios.deleteObject(application);  
					 						//  			            }  
					 						//  			        }  
					 						//  			    });  
					 						//  			}
					 						//  }
					 							 
					 				 //  	var options = {cover:false, sound: 'system' };   
					 					// plus.push.createMessage('您有一条新订单,请及时处理！', {msgtype: 'newod', skipurl:'/pages/user/fuwuod/index'}, options); 
					 			        plus.push.addEventListener('click',function(res) {  
											   if (res.payload.msgtype == 'newod') {  
													   setTimeout(function() {
														  uni.navigateTo({
														     url: res.payload.skipurl
														  })  
													   }, 1000);
												}  
										}, false );
					 			       								   
					 			       								   
					 				    plus.push.addEventListener('receive',function(res){
											let payload=res.payload;
											if (payload.msgtype == 'newod' ) {
												// console.log(222,res.payload);
												// uni.$emit('getonfknewod',{msgmoney:payload.msgmoney});
												var options = {cover:false, sound: 'system', title: payload.msgtitle };
												plus.push.createMessage(payload.msgcontent,{msgtype: payload.msgtype, skipurl:payload.skipurl},options ); 
											}   
										},false );
										
										
										
					 
					 
					 
					// #endif
					
					
					
					
					
					
					
			},
		onShow: function() {
		
		},
		onHide: function() {
		
		},
	}
</script>
<style>
	@import './static/css/common.css';
</style>
<style lang="scss">
  /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
  @import './tuniao-ui/index.scss';
  @import './tuniao-ui/iconfont.css';
</style>
