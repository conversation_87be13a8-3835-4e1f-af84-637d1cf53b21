<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon">


						<view class="rowx">
							<view class="tit"><text class="redst"></text>角色名称</view>
							<view class="inpcon">
								<view class="inp"><input class="input" type="text" name="title" v-model="jsdata.title"
										placeholder="请输入角色名称" placeholder-class="placeholder" :disabled="issys==1" />
								</view>
							</view>
							<view class="clear"></view>
						</view>






					</view>


					<view class="masxtip">*请选择下方权限（可多选）</view>


					<view class="hjdjcon">
						<checkbox-group @change="checkboxGroupChange">
							<block v-for="(item,index) in xtjsdata" :key="index">
								<view class="hydjitem">
									<!-- @click="vmxx" :data-dj="item.id" -->
									<view class="dftit">
										<view class="tit">
											{{item.sortname}}
										</view>
										<!-- <view class="more"><text class="iconfont icon-jiantou_liebiaozhankai"></text></view> -->
									</view>
									<!-- :class="thdj==item.id ? 'hoverss' : '' " -->

									<view class="xxcon hoverss">
										<view class="item" v-for="(itemx, indexx) in item.ziqx" :key="indexx">
											<checkbox :value="itemx.id" color="#FFD427" :checked="itemx.ischecked">
												{{itemx.sortname}}</checkbox>
										</view>
										<view class="clear"></view>
									</view>
								</view>
							</block>
						</checkbox-group>
					</view>




				</view>

				<block v-if="jsid > 0">
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<view class="bomsfleft">
								<button class="bomsubbtn" @click="jsdel" :data-jsid="jsid" style="background:#ddd;">
									<text>删除角色</text>
								</button>
							</view>
							<view class="bomsfright">
								<button class="bomsubbtn" form-type="submit">
									<text>确认提交</text>
								</button>
							</view>
							<view class="clear"></view>
						</view>
					</view>
				</block>
				<block v-else>
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="bomsubbtn" form-type="submit">
								<text>确认提交</text>
							</button>
						</view>
					</view>
				</block>
			</form>








		</view>

		<view style="height:120upx"></view>





	</view>


</template>

<script>
	export default {
		data() {
			return {
				jsid: 0,
				staticsfile: this.$staticsfile,
				xtjsdata: '',
				jsdata: '',
				xtitle: '',
				thdj: 0,
				issys: 0,
				qxidstr: ''
			}
		},

		onLoad(options) {
			let that = this;
			let jsid = options.jsid ? options.jsid : 0;
			this.jsid = jsid;
			if (jsid > 0) {
				this.xtitle = '编辑员工角色';
			} else {
				this.xtitle = '添加员工角色';
			}
			this.loadData();
		},

		onShow() {

		},

		methods: {

			loadData() {
				let that = this;
				let jsid = that.jsid;
				let da = {
					jsid: jsid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/sjgl/jiaoseedit', da)
					.then(res => {
						uni.hideLoading();
						// console.log(res);
						if (res.errcode == 0) {
							that.xtjsdata = res.data.xtjsdata;
							that.jsdata = res.data.jsdata;
							that.issys = res.data.jsdata.issys;
							if (res.data.jsdata) {
								that.status = res.data.jsdata.status;
								that.qxidstr = res.data.jsdata.qxidarr;
							}
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			vmxx(e) {
				let dj = e.currentTarget.dataset.dj;
				this.thdj = dj;
			},


			radioGroupChange(e) {
				this.status = e.detail.value;
			},



			checkboxGroupChange(e) {
				// this.qxidstr=e;
				let value = e.detail.value;
				this.qxidstr = value;
				// console.log(value);
			},

			checkboxChange(e) {
				// console.log(e);
			},


			formSubmit: function(e) {
				let that = this;

				let pvalue = e.detail.value;
				let qxidstr = that.qxidstr;

				if (qxidstr.length == 0) {
					qxidstr = '';
				} else {
					qxidstr = qxidstr.join(',');
				}


				if (!pvalue.title) {
					uni.showToast({
						icon: 'none',
						title: '请输入角色名称',
					});
					return false;
				}

				// if(!qxidstr){
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请选择角色权限',
				// 	});
				// 	return false;
				// }

				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'jsid': that.jsid,
					'title': pvalue.title,
					'qxidstr': qxidstr
				}


				this.$req.post('/v1/sjgl/jiaoseeditsave', da)
					.then(res => {
						uni.hideLoading();
						// console.log(res);
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									uni.setStorageSync('thupjssetlist', 1);
									setTimeout(function() {
										uni.navigateBack()
									}, 1000)
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},


			jsdel(e) {
				let that = this;
				let jsid = e.currentTarget.dataset.jsid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'jsid': jsid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/sjgl/jiaosedel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
						}

					}
				})


			},



		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.masxtip {
		font-size: 24upx;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 10upx 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .dptit {
		text-align: center;
	}

	.hjdjcon {
		margin: 20upx 0
	}

	.hydjitem {
		margin-bottom: 20upx;
		width: 100%;
	}

	.hydjitem .dftit {
		height: 96upx;
		line-height: 96upx;
		background: #fff;
		border-radius: 20upx;
		padding: 0 28upx;
		position: relative;
		z-index: 100;
		border-bottom: 1px solid #eee;
	}

	.hydjitem .dftit .tit {
		float: left;
		font-size: 28rpx;
		font-weight: 600;
	}

	.hydjitem .dftit .more {
		float: right;
		font-size: 28rpx;
	}

	.hydjitem .dftit .more .iconfont {
		font-size: 28rpx;
	}


	.xxcon {
		background: #fff;
		padding-top: 40upx;
		margin-top: -20upx;
		border-radius: 0 0 20upx 20upx;
		display: none;
		padding-left: 30upx;
		padding-bottom: 10upx;
	}

	.xxcon .sjsm {
		font-size: 24upx;
		padding: 10upx 20upx 30upx 25upx;
		color: #7F7F7F;
	}

	.xxcon .sjsm text {
		color: #1677FF;
	}

	.xxcon.hoverss {
		display: block;
	}

	.xxcon .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx
	}
</style>