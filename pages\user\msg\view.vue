<template>
  <view class="template-news tn-safe-area-inset-bottom">
	
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >通知详情</text>
					</view>
					<view slot="right" >
					</view>
	</tn-nav-bar>
	
	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
	
    <view class="viewcon">
          <view class="title">{{viewdata.title}}</view>
    	  <view class="rq">
			  <text class="iconfont icon-mendian"></text> <text class="dm">{{viewdata.mdname}}</text>
			  <text class="iconfont icon-shijian1" ></text>
			  <text>{{viewdata.addtime || ''}}</text>
		  </view>
    	  <view class="nrcon">
    		   <mp-html :content="viewdata.content" />
    	  </view>
		  
		
		  
    </view>
    

    <view class="tn-flex tn-footerfixed" v-if="viewdata.skurl">
    	<view class="tn-flex-1 justify-content-item tn-text-center">
    		<button class="bomsubbtn" @click="gotoodview">
    			<text>查看单据详情</text>
    		</button>
    	</view>
    </view>
    <view class='tn-tabbar-height'></view>
    
  </view>
</template>

<script>
  export default {
    data(){
      return {
		 staticsfile: this.$staticsfile,
		 html:'',
		 id:0,
		 prne:'',
		 viewdata:[]
      }
    },
	onLoad(options) {
		let id=options.id;
		this.id=id;
		this.loadData();
	},
	onShow(){
	
	},
    methods: {
		
		loadData(){
			  let that=this;
			 
			  let id=that.id; 
			  let da={  
				id:id
			  }
			 uni.showLoading({title: '处理中...'})
			 this.$req.post('/v1/muser/msgview', da)
			 .then(res => {
				uni.hideLoading();
				console.log(res);
				if(res.errcode==0){
				   that.viewdata=res.data.viewdata;
				}else{
					 uni.navigateBack()
				}
			 })
		},	
		
		gotoback(){
			uni.navigateBack();
		},
		gotoodview(){
			let that=this;
			let skurl=that.viewdata.skurl;
			uni.navigateTo({
				url:skurl
			})
		}
    
    }
  }
</script>

<style lang="scss" >

   page{background:#fff}
   
   .viewcon{margin:30upx 40upx 50upx 40upx;}
   .viewcon .title{font-size: 32upx; margin-bottom: 14px;}
   .viewcon .rq{font-size:13px;color:#333;margin:30upx 0 }
   .viewcon .rq .dm{margin-right:40upx;}
   .viewcon .rq .iconfont{margin-right:5upx;font-size:24upx }
   .viewcon .nrcon{line-height:25px;padding-bottom:40upx; }


</style>
