<template>
	<view class="content">
		<text class="success-icon iconfont icon-zhifuchenggong1 "></text>
		<text class="tit">支付成功</text>
		
		<view class="btn-group">
			<view class="grzxco"><navigator url="/pages/mine/mine" open-type="switchTab" >个人中心</navigator></view>
			<view class="grzxco"><navigator url="/pages/index/index" open-type="switchTab">返回首页</navigator></view>
			<view class="clear"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onShow(){
			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				uni.setNavigationBarColor({
				  frontColor: mbjson.tbtxtcolor,
				  backgroundColor: mbjson.tbbgcolor
				});
			}
		},
		methods: {
			
		}
	}
</script>

<style lang='scss'>
	page{background: #fff;}
	.content{
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	.success-icon{
		font-size: 100upx;
		color: #3366FF;
		margin-top: 100upx;
		margin-bottom:100upx;
	}
	.tit{
		font-size: 38upx;
		color: #303133;
	}
	.btn-group{
		padding-top: 100upx;
	}
	.mix-btn {
		margin-top: 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 600upx;
		height: 80upx;
		font-size: $font-lg;
		color: #fff;
		background-color: $base-color;
		border-radius: 10upx;
		&.hollow{
			background: #fff;
			color: #303133;
			border: 1px solid #ccc;
		}
	}
	
	.grzxco{
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
	    text-align: center;
		width:250upx;
	    display:inline-block;
		border:1px solid #eee;border-radius:5px;
		margin:0 15px;
	}
	
</style>
