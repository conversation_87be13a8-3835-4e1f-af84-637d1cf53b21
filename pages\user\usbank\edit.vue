<template>
	<view class="content">
		<tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
			  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
			  		  <text  >编辑银行卡</text>
			  		</view>
			  </tn-nav-bar>
		
		 
		
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		<view class="ssconvvt">
				<form @submit="formSubmit" >
				
						<view class="row b-b">
							<text class="tit">所属银行：</text>
							<input class="input" type="text" name="b_name" v-model="bkinfo.b_name" placeholder="请输入所属银行" placeholder-class="placeholder" />
						</view>
						<view class="row b-b">
							<text class="tit">开户银行：</text>
							<input class="input" type="text" name="b_kaihu" v-model="bkinfo.b_kaihu" placeholder="请输入开户支行、分行" placeholder-class="placeholder" />
						</view>
						<view class="row b-b">
							<text class="tit">银行卡号：</text>
							<input class="input" type="text" name="b_number" v-model="bkinfo.b_number" placeholder="请输入银行卡号" placeholder-class="placeholder" />
						</view>
						<view class="row b-b">
							<text class="tit">开户姓名：</text>
							<input class="input" type="text" name="b_huming" v-model="bkinfo.b_huming" placeholder="请输入持卡人姓名" placeholder-class="placeholder" />
						</view>
						
						
						<view class="" style="margin-top:60upx">
						  <view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="bomsubbtn"  form-type="submit">
							  <text>确认提交</text>
							</button>
						  </view>
						</view>
				</form>
		</view>
				
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bkid:0,
				bkinfo:{},
		
			}
		},
		onLoad(option){
			let that=this;
			let bkid=option.bkid || 0 ;
			that.bkid=bkid;
			this.loadData();
		},
		onShow(){
	
		},
		methods: {
			
			loadData() {
				let that=this;
				let bkid=that.bkid;
				let da={bkid:bkid};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/muser/usbankinfo', da)
				      .then(res => {
					uni.hideNavigationBarLoading();
                 
					if(res.errcode==0){
						let bkinfo=res.data.bkinfo;
				        that.bkinfo = res.data.bkinfo;
					}else{
						uni.showModal({
						    title: '错误提示',
						    content: res.msg,
							showCancel: false,
							success() {
								uni.navigateBack()
							}
						});
						return false;
					}
				})
			},
			
			selbankChange(e) {
				let that=this;
				let theselindex=e.detail.value;
				let selthbank=that.selbankdata[theselindex];
		
				that.selthbank=selthbank;
				that.selbank= selthbank['title'];
			
			},
			
			formSubmit: function(e) { 
				let that=this;
			               var formdata = e.detail.value
										
								 let bkid=that.bkinfo.id;
								 let pvalue = e.detail.value;
							
							
								
								   if (!pvalue.b_name) {
									   uni.showToast({
									   	icon: 'none',
									   	title: '请输入银行卡所属银行！'
									   });
									   return false;
								   } 
									 if (!pvalue.b_kaihu) {
									   uni.showToast({
									   	icon: 'none',
									   	title: '请输入开户支行、分行！'
									   });
									   return false;
								   }
									 if (!pvalue.b_number) {
										 uni.showToast({
										 	icon: 'none',
										 	title: '请输入银行卡号！'
										 });
										 return false;
									 }
									 if (!pvalue.b_huming) {
										 uni.showToast({
										 	icon: 'none',
										 	title: '请输入开户姓名！'
										 });
										 return false;
									 }
								  uni.showLoading({title: '处理中...'})
								  let da={
									'bkid': bkid ? bkid : 0 ,
									'b_name': pvalue.b_name,
									'b_kaihu': pvalue.b_kaihu,
									'b_huming': pvalue.b_huming,
									'b_number': pvalue.b_number,
								  }
				
								  this.$req.post('/v1/muser/upusbank', da)
								          .then(res => {
								  		    uni.hideLoading();
								  			if(res.errcode==0){
												uni.showToast({
													icon: 'none',
													title: res.msg,
													success() {
														setTimeout(function(){
															 uni.navigateBack({
															 	
															 })
														},1000)
													}
												});
								  			}else{
								  				uni.showModal({
								  					content: res.msg,
								  					showCancel: false
								  				})
								  			}
								  			
								          })
								          .catch(err => {
								  		
								          })
								  
			   },
		},
		
		
		    
	
	    
	
	
	}
</script>

<style lang="scss">

 .radio-item{margin-right: 30upx;}
	.row{
		display: flex;
		align-items: center;
		position: relative;
		padding:0 30upx;
		height: 110upx;
		background: #fff;
		
		.tit{
			flex-shrink: 0;
			width: 150upx;
			font-size: 30upx;
			color: $font-color-dark;
		}
		.input{
			flex: 1;
			font-size: 30upx;
			color: $font-color-dark;
		}
		.icon-shouhuodizhi{
			font-size: 36upx;
			color: $font-color-light;
		}
	}
	.row.b-b{
		border-bottom:1px solid #eee;
	}


.ssconvvt{margin:32upx;padding:32upx;background:#fff;border-radius:20upx;}
</style>
