<template>
	<view class="mcn">
		<view class="viewcon">
		      <view class="title">{{viewdata.title}}</view>
			  <view class="rq">{{viewdata.addtime}}</view>
			  <view class="nrcon">
				  
				  <jyf-parser :html="html" ref="article"></jyf-parser>
				  
			  </view>
	    </view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				html:'',
				id:0,
		      	viewdata:[]
			}
		},
		onLoad(options) {
			let id=options.id;
			this.id=id;
			this.loadData();
		},
		onShow(){
			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				uni.setNavigationBarColor({
				  frontColor: mbjson.tbtxtcolor,
				  backgroundColor: mbjson.tbbgcolor
				});
			}
		},
		methods: {
			 loadData(){
				  let that=this;
				  let id=that.id; 
				  let da={  
				 	id:id
				  }
				 uni.showLoading({title: '处理中...'})
				 this.$req.post('/v2/dinghuo/notice/view', da)
				         .then(res => {
				 		    uni.hideLoading();
				 			if(res.errcode==0){
				 			   that.viewdata=res.data.viewdata;
				 			   this.$refs.article.setContent(res.data.viewdata.content);
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false
				 				})
				 			}
				         })
			 }	
		},
	
	
	}
</script>

<style>
	
     .viewcon{margin:30upx 40upx 30upx 40upx;}
     .viewcon .title{font-size:16px;line-height:50upx;}
     .viewcon .rq{font-size:11px;color:#888;margin:30upx 0 }
     .viewcon .nrcon{color:#888;line-height:22px; }

	
</style>
