<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">

					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx" @click="selkh">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>客户名称</view>
							<view class="inpcon">
								<view class="inp">
									{{khname ? khname : '请选择客户'}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>

					<block v-if="thkhdata">

						<view class="maxcon00">

							<view class="khinfo" @click="selkh">
								<view class="inf1">
									<view class="djcon">{{thkhdata.dengjitxt}}</view>
									<view class="tx">
										<view class="avatar">
											<image :src="staticsfile + thkhdata.avatar"></image>
										</view>
										<view class="sexicon" v-if="thkhdata.sex > 0">
											<image :src="'/static/images/sexicon'+thkhdata.sex+'.png'"></image>
										</view>
									</view>
									<view class="xx">
										<view class="tit">
											<text class="name">{{thkhdata.realname}}</text>
											<text class="uid">ID:{{thkhdata.id}}</text>
										</view>
										<view class="ssyg">电话号码：{{thkhdata.lxtel}}</view>
										<view class="ssyg" v-if="thkhdata.sfzhao">身份证号：{{thkhdata.sfzhao}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>

						</view>

					</block>


					<view class="maxcon">

						<view class="vstit" style="margin-bottom:30upx">物品列表</view>
						<view class="more vls" @click="selwp">添加物品 <text class="iconfont icon-tianjia2"></text></view>



						<block v-if="djshop">
							<view class="splist">
								<block v-for="(item,index) in djshop" :key="index">
									<view class="spitem">
										<view class="spname">
											<view class="tline"></view>
											{{item.title}}
											<text style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.spid}})</text>
										</view>
							<!-- 			<view class="rowa">
											<view class="tip">物品编号</view>
											<view class="nr colorls" >{{item.spid}}</view>
											<view class="clear"></view>
										</view> -->
										<view class="rowa">
											<view class="tip">物品类型</view>
											<view class="nr" style="color:#1677FF">{{item.ddtypename}}</view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">上架价格</view>
											<view class="nr">¥{{item.sjjiage}}</view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">出售价格</view>
											<view class="nr">¥{{item.ckjiage}} <text
													class="iconfont icon-bianji slwpbjicon" @click="addckd"
													:data-spid='item.spid' :data-spname='item.title'
													:data-cknum='item.cknum' :data-sjjiage='item.ckjiage'
													:data-kucun='item.kucun'></text></view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">物品数量</view>
											<view class="nr">{{item.cknum}} <text
													class="iconfont icon-bianji slwpbjicon" @click="addckd"
													:data-spid='item.spid' :data-spname='item.title'
													:data-cknum='item.cknum' :data-sjjiage='item.ckjiage'
													:data-kucun='item.kucun'></text></view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">物品总价</view>
											<view class="nr">
												¥<text style="color:#ff0000">{{item.zongjia}}</text>
											</view>
											<view class="clear"></view>
										</view>
										
										<block v-if="item.vxq==1">
											
											<view class="rowa">
												<view class="tip">所在门店</view>
												<view class="nr">{{item.mdname}}</view>
												<view class="clear"></view>
											</view>
											<view class="rowa">
												<view class="tip">所在仓库</view>
												<view class="nr">{{item.ckname}}</view>
												<view class="clear"></view>
											</view>
											
											<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
												<view class="rowa">
													<view class="tip">{{itemx.title}}</view>
													<view class="nr">{{itemx.value}}</view>
													<view class="clear"></view>
												</view>
											</block>

											<block v-if="item.picturesarr">
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
														<block v-for="(itemxx,indexx) in item.picturesarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemxx"
																	:data-src='staticsfile + itemxx '
																	@click='previewImagev' :data-picarr="item.picturesarr"></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</view>
											</block>

											<block v-if="item.beizhu">
												<view class="bzcon">
													<view class="bztit">备注：</view>
													<view class="bznr">{{item.beizhu}}</view>
												</view>
											</block>

										</block>

										<!-- 	<view class="spbjbtn" @click="spedit" :data-spid="item.id">
													   <text class="iconfont icon-bianji"></text> 编辑
													</view> -->
										<view class="spbjbtn2" @click="spdel" :data-id="item.id" :data-spid="item.spid">
											<text class="iconfont icon-shanchu3"></text> 删除
										</view>

										<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
											<block v-if="item.vxq==0">
												展开详情<text class="iconfont icon-jtdown2"></text>
											</block>
											<block v-else>
												收起详情<text class="iconfont icon-jtup2"></text>
											</block>
										</view>


									</view>
								</block>
							</view>
						</block>

					</view>



					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">费用信息</view>


						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品总价</view>
							<view class="inpcon">
								<view class="inp" style="color:#ff0000">{{djdata.spzongjia}}</view>
							</view>
							<view class="clear"></view>
						</view>


					</view>


					<view class="maxcon">

						<view class="vstit" style="margin-bottom:0">销售人员</view>
						<view class="more vls" @click="selyg">新增 <text class="iconfont icon-tianjia2"></text></view>

						<!-- 如果该客户没有负责人 -->
						<block v-if="thkhnofzr==1">
							<view class="nofrztipcon">
								<text class="iconfont icon-tishi5"></text> {{nofrztiptxt}}
							</view>
						</block>
						
						<block v-if="isdpywzh==0">
							<block v-if="xsrydata.length > 0">
								<view class="ywrylist">
									<block v-for="(itemx,index) in xsrydata" :key="index">
										<view class="xxitem">
											<text class="iconfont icon-renyuan" style="margin-right:10upx"></text> {{itemx.ygname}} 
											<!-- <text style="color:#888;margin-left:10px">({{itemx.ygid}})</text> -->
											<view class="xxset" v-if="itemx.isbd!=1">
												<text class="iconfont icon-guanbi4" @click="delywry"
													:data-ygid="itemx.ygid"></text>
											</view>
										</view>
									</block>
								</view>
							</block>
						</block>
						<block v-else>
							<view class="ywrylist">
								<view class="xxitem">
									<text class="iconfont icon-renyuan" style="margin-right:10upx"></text> {{dpywtit}} 
									<view class="xxset" >
										<text class="iconfont icon-guanbi4" @click="delywry"
											:data-ygid="0"></text>
									</view>
								</view>
							</view>
						</block>

					</view>

					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>录单人员</view>
							<view class="inpcon">
								<view class="inp">
									{{opname}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>


					<view class="maxcon">
						<view class="vstit">单据备注说明 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

						<view class="bzcon">
							<textarea name="remark" class="beizhu" v-model="remark"></textarea>
						</view>

						<view class="rowxmttp">
							<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
								<block v-for="(item,index) in img_url_ok" :key="index">
									<view class='item'>
										<view class="del" @tap="deleteImg" :data-index="index"><i
												class="iconfont icon-shanchu2"></i></view>
										<image :src="staticsfile+item" :data-src='staticsfile + item '
											@tap='previewImage'></image>
									</view>
								</block>
								<view class='item' v-if="img_url_ok.length <= 8">
									<image @tap="chooseimage" src='/static/images/uppicaddx.png'></image>
								</view>
								<view class="clear"></view>
							</view>
						</view>


					</view>


				</view>

				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="toback" style="background:#ddd;">
								<text>上一步</text>
							</button>
						</view>
						<view class="bomsfright">
							<button class="bomsubbtn" form-type="submit">
								<text>确认下一步</text>
							</button>
						</view>
						<view class="clear"></view>
					</view>
				</view>
			</form>

		</view>



		<tn-popup v-model="spsxshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">

				<view class="rowx">
					<view class="tit">出库价格</view>
					<view class="inpcon">
						<view class="inp"><input class="input" type="number" name="ckjiage" v-model="thckjiage"
								placeholder="请输入出库价格" placeholder-class="placeholder" /></view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="rowx">
					<view class="tit">出库数量</view>
					<view class="inpcon">
						<view class="inp">
							<!-- <tn-number-box v-model="thcknum" :min="1" :max="thkucun" :step="1"></tn-number-box> -->
							<view class="inp"><input class="input" type="number" name="thcknum" v-model="thcknum"  :placeholder="'请输入出库数量'" placeholder-class="placeholder"   /></view>
						</view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="qrsel">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>



		<view style="height:120upx"></view>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				html: '',
				status: 0,
				img_url: [],
				img_url_ok: [],
				dwlogo: '',
				formats: {},
				opname: '',
				xtitle: '',
				thygid: '',
				thygname: '',
				thkhdata: '',

				khid: '',
				khname: '',

				vzz: 0,
				xsrydata: [],
				clicksta: false,
				sknexturl: '',
				djshop: '',
				djdata: '',
				vsfnr: 0,
				vsfnr2: 0,
				remark: '',
				xzongjia: 0,

				djshop: '',
				vspxq: 0,
				ddtype: 0,
				dtptxt: '',
				ywzqnum: '',
				ywzqdw: '',
				ywzqdwtxt: '',
				ywdqtimetxt: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid: 0,

				thspid: 0,
				thspname: '',
				thsjjiage: 0,
				thkucun: 0,
				thcknum: 1,
				thckjiage: 0,
				spsxshow: false,

				thkhnofzr: 0,
				nofrztiptxt:'',
				isdpywzh:0,
				dpywtit:'',

			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.xtitle = '编辑售卖单';
			this.loadData();
		},

		onShow() {
			let thkhnofzr=0;
			let thygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			let thygisbdywy = uni.getStorageSync('thygisbdywy');
			this.clicksta = false;
			
			if (uni.getStorageSync('isdpywzh') == 1) {
				this.isdpywzh = 1;
				this.xsrydata = [];
				uni.setStorageSync('isdpywzh', '');
			}

			let thkhdata = uni.getStorageSync('thkhdata');
			if (thkhdata) {
				this.xsrydata = [];
				this.thkhdata = thkhdata;
				this.khid = thkhdata.id;
				this.khname = thkhdata.realname;
				
				if(thygid){
					this.thkhnofzr=0;
				}else{
					this.thkhnofzr=1;
				}
				
				uni.setStorageSync('thkhdata', '');
			}



		
			
			
			if (thygid) {
				let xsrydata = this.xsrydata;
				let isywrybh = 0;
			
				if (xsrydata.length > 0) {
					xsrydata.forEach((item, index) => {
						if (item.ygid == thygid) {
							isywrybh = 1;
						}
					})
				}
				if (isywrybh == 0) {
					xsrydata.push({
						ygid: thygid,
						ygname: thygname,
						isbd: thygisbdywy
					});
				}
				this.isdpywzh=0;
				console.log(xsrydata);
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
				uni.setStorageSync('thygisbdywy', '')
			}

			

			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}

		},

		methods: {
			toback() {
				uni.navigateBack();
			},
			selyg() {
				
				let thkhnofzr=this.thkhnofzr;
				if(!this.khid){
					uni.showToast({
						icon: 'none',
						title: '请先选择客户！'
					});
					return false;
				}
				
				uni.navigateTo({
					url: '/pages/selyg/index?stype=9&isdx=1&thkhnofzr='+thkhnofzr
				})
			},
			selwp(e) {
				uni.navigateTo({
					url: '/pages/selwp/index?stype=1&djid=' + this.djid
				})
			},
			selkh() {

				// if (this.djid > 0) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '客户不可更换'
				// 	});
				// 	return false;
				// }

				uni.navigateTo({
					url: '/pages/selkh/index?stype=4'
				})
			},
			gotosqsm() {
				uni.navigateTo({
					url: '/pages/danye/index?id=8'
				})
			},


			delywry(e) {
				let ygid = e.currentTarget.dataset.ygid;
				let xsrydata = this.xsrydata;
				
				if(!ygid || ygid==0){
					this.isdpywzh=0;
					return false;
				}
				
				xsrydata.forEach((item, index) => {
					if (item.ygid == ygid) {
						xsrydata.splice(index, 1);
					}
				})
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danjuck/ckdedit', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.opname = res.data.opname;
							that.nofrztiptxt = res.data.nofrztiptxt;
							that.thkhnofzr = res.data.thkhnofzr;
							that.dpywtit = res.data.dpywtit;
							
							let djdata = res.data.djdata;
							that.djdata = djdata;

							that.xsrydata = res.data.xsrydata;
							that.thkhdata = res.data.thkhdata;
							that.khid = djdata.khid ? djdata.khid : '';
							that.khname = djdata.khname ? djdata.khname : '';
							that.remark = res.data.djdata.remark;
							that.img_url_ok = res.data.picturesarr;
							that.picturesarr = res.data.picturesarr;


							that.ddtype = res.data.djdata.ddtype;
							that.ywzqnum = res.data.djdata.ywzqnum != 0 ? res.data.djdata.ywzqnum : '';
							that.ywzqdwtxt = res.data.djdata.ywzqdwtxt;
							that.ywzqdw = res.data.djdata.ywzqdw;
							that.ywdqtimetxt = res.data.djdata.ywdqtime;

							that.ywtime = res.data.ywtime;
							that.djshop = res.data.djshop;
							that.dtptxt = res.data.dtptxt;


							if(res.data.thkhnofzr==1){
								that.isdpywzh=1;
							}

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			previewImagev: function (e) {
					  let that = this;
					  let src = e.currentTarget.dataset.src;
					  let picarr=e.currentTarget.dataset.picarr;
					  let imgarr=[];
					  picarr.forEach(function(item,index,arr){
						  imgarr[index] = that.staticsfile+item;
					  });
					  wx.previewImage({
						  current: src,
						  urls: imgarr
					  });
			},
			
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},

			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},

			spdel(e) {
				let that = this;
				let id = e.currentTarget.dataset.id;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'id': id,
								'djid': that.djid,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danjuck/ckdspdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},

			addckd(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				let thsjjiage = e.currentTarget.dataset.sjjiage;
				let thkucun = e.currentTarget.dataset.kucun;
				let thcknum = e.currentTarget.dataset.cknum;
				this.thspid = spid;
				this.thspname = spname;
				this.thsjjiage = thsjjiage;
				this.thckjiage = thsjjiage;
				this.thkucun = thkucun;
				this.thcknum = thcknum;
				this.spsxshow = true;
				console.log(thkucun);
			},
			qrsel() {
				let that = this;
				let da = {
					stype: 1,
					djid: this.djid,
					spid: this.thspid,
					cknum: this.thcknum,
					ckjiage: this.thckjiage,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/selwp/qrsel', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
											that.spsxshow = false;
											that.loadData();
									}, 1000);
								}
							});
							
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {

								}
							})
						}
					})

			},

			formSubmit: function(e) {
				let that = this;

				let pvalue = e.detail.value;
				let khdata = that.thkhdata;
				let xsrydata = that.xsrydata;
				let xsryidarr = [];
				let xsryidstr = '';
				let sknexturl = '';

				if (xsrydata.length > 0) {
					xsrydata.forEach((item, index) => {
						xsryidarr.push(item.ygid);
					})
					xsryidstr = xsryidarr.toString();
				}
				console.log(xsryidstr);

				if (!khdata) {
					uni.showToast({
						icon: 'none',
						title: '请选择客户',
					});
					return false;
				}


				if (!xsryidstr  && that.isdpywzh==0) {
					uni.showToast({
						icon: 'none',
						title: '请选择销售人员',
					});
					return false;
				}

				if (that.djshop == '' || !that.djshop) {
					uni.showToast({
						icon: 'none',
						title: '还未添加物品',
					});
					return false;
				}


				if (this.clicksta) return;
				this.clicksta = true;



				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'djid': that.djid,
					'khid': khdata.id,
					'xsryidstr': xsryidstr,
					'remark': that.remark ? that.remark : '',
					'remarkfile': that.img_url_ok ? that.img_url_ok : '',
					'isdpywzh': that.isdpywzh ? that.isdpywzh : 0,
				}
				this.$req.post('/v1/danjuck/ckdeditsave', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {

							let djid = res.data.djid;
							sknexturl = './editx2?djid=' + djid;

							uni.setStorageSync('thupckdlist', 1)
							setTimeout(function() {
								uni.navigateTo({
									url: sknexturl
								})
							}, 500)

							// uni.showToast({
							// 	icon: 'none',
							// 	title: res.msg,
							// 	success() {

							// 	}
							// });
						} else {
							that.clicksta = false;
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},


		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 105px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}


	.rowxmttp {
		margn-top: 30upx;
		position: relative;
	}

	.rowxmttp .zjzkicon {
		position: absolute;
		right: 0;
		top: 0;
		font-size: 24upx
	}

	.rowxmttp .zjzkicon .iconfont {
		font-size: 20upx
	}

	.rlsbsmtip {
		position: absolute;
		right: 0;
		top: 32upx;
		font-size: 24upx;
		color: #888
	}

	.rlsbsmtip text {
		margin-right: 10upx;
		font-size: 24upx
	}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}




	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}


	.khinfo {
		background: #FFFFFF;
		border-radius: 20rpx;
		margin-bottom: 20upx;
		padding: 28upx 28upx;
		position: relative;
	}

	.khinfo .tx {
		float: left;
		position: relative;
	}

	.khinfo .tx .avatar image {
		width: 100upx;
		height: 100upx;
		border-radius: 100upx;
		display: block;
	}

	.khinfo .tx .sexicon {
		position: absolute;
		bottom: -20upx;
		left: 35upx
	}

	.khinfo .tx .sexicon image {
		width: 32upx;
		height: 32upx;
	}

	.khinfo .xx {
		float: left;
		margin-left: 30upx
	}

	.khinfo .xx .tit {
		height: 40upx;
		line-height: 40upx;
		font-size: 28upx;
		padding-top: 10upx;
		margin-bottom: 10upx;
	}

	.khinfo .xx .tit .name {
		font-size: 28upx;
		font-weight: 600
	}

	.khinfo .xx .tit .uid {
		font-size: 24rpx;
		margin-left: 15upx;
		color: #888
	}

	.khinfo .xx .ssyg {
		height: 40upx;
		line-height: 40upx;
		font-size: 24upx;
		color: #7F7F7F;
	}

	.khinfo .djcon {
		position: absolute;
		right: 0;
		top: 0;
		font-size: 24upx;
		background: #E7FFE2;
		border-radius: 0rpx 20rpx 0rpx 20rpx;
		height: 42upx;
		line-height: 42upx;
		padding: 0 15upx;
		color: #0EC45C
	}


	.ywrylist {
		padding-top: 10upx
	}
	
	.ywrylist .xxitem {
		height: 100upx;
		line-height: 100upx;
		overflow: hidden;
		position: relative;
		border-bottom: 1px solid #efefef;
	}
	
	.ywrylist .xxitem .xxset {
		position: absolute;
		right: 0;
		top: 0
	}
	
	.ywrylist .xxitem .xxset text {
		margin-left: 40upx;
		font-size: 28upx;
	}
	
	.ywrylist .xxitem:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}



	.spitem {
		position: relative;
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
		padding-bottom: 70upx
	}


	.spitem .spbjbtn {
		position: absolute;
		left: 140upx;
		bottom: 20upx;
		color: #1677FF;
		height: 40upx;
		line-height: 40upx;
		font-size: 24upx;
	}

	.spitem .spbjbtn .iconfont {
		margin-right: 10upx;
		font-size: 24upx;
	}

	.spitem .spbjbtn2 {
		position: absolute;
		left: 20upx;
		bottom: 20upx;
		color: #1677FF;
		height: 40upx;
		line-height: 40upx;
		font-size: 24upx;
	}

	.spitem .spbjbtn2 .iconfont {
		margin-right: 10upx;
		font-size: 24upx;
	}

	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		// padding-right: 100upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .viewxqcon {
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		text-align: right;
		font-size: 24upx;
		position: absolute;
		right: 20upx;
		bottom: 20upx;
	}

	.spitem:last-child {
		padding-botom: 0;
		margin-bottom: 0
	}


	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa .nr .slwpbjicon {
		margin-left: 10upx;
		color: #1677FF
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.cznrcon {
		padding: 40upx;
	}
	.nofrztipcon{background:#fafafa;border-radius:10upx;padding:15upx 20upx;font-size:24upx;margin-top:10upx}
	.nofrztipcon .iconfont{margin-right:10upx;color:#ff0000;font-size:24upx}
</style>