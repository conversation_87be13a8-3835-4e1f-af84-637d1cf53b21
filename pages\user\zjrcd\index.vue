<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				我的资金记录
			</view>
			<view slot="right">
				<view @click="gotozj" class="txbtn"><text class="iconfont icon-caozuojilu"></text> 资金增减</view>
			</view>
		</tn-nav-bar>

		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 75vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="typedata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333"  :bold="true" :fontSize="24"
						:gutter="10" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF"
						:height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 25vw;overflow: hidden;">

					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text>
					</view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>
		</view>

		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>
		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>

		<view class="tjtiao" >
			
				<view class="fless">
					<view class="ite">
						<view class="sz">{{tjdata.xjine2}}</view>
						<view class="tx">可用资金</view>
					</view>
					<view class="ite">
						<view class="sz">{{tjdata.xjine1}}</view>
						<view class="tx">当前出资</view>
					</view>
					<view class="ite">
						<view class="sz">{{tjdata.xjine3}}</view>
						<view class="tx">回本金额</view>
					</view>
				</view>
		
		</view>

		<block v-if="listdata.length>0">



			<view class="czrcdcon">

				<block v-for="(item,index) in listdata" :key="index">

                    <block v-if="type==4">
						<view class="czrcdli  ">
							<view class="nrcon"> 
								<view class="info"> 
									<view class="vxqbtn" @click="gotosyckxq"  style="top:60upx;"  :data-czrcdid="item.czrcdid">查看详情 <text class="iconfont icon-jtdown2"></text></view>
									<block v-if="item.type==1">
										<view><text class="tit">金额：</text><text class="jine" >+￥{{item.jine_change}}</text></view>
									</block>
									<block v-else>
										<view><text class="tit">金额：</text><text class="jine" style="color:#4aac09" >￥{{item.jine_change}}</text></view>
									</block>
									<view><text class="tit">时间：</text><text style="color:#888">{{item.addtime}}</text></view>
									<view><text class="tit">说明：</text><text >{{item.sm}}</text></view>

								</view>
								
								<view class="clear"></view>
							</view>
						</view>
					</block>
					<block v-else>
						
						<view class="czrcdli  ">
							<view class="nrcon">
								<view class="info">
									 
								
								<!-- 	<block v-if="item.glodnum">
										<view><text class="tit">单号：</text><text>{{item.glodnum}}</text></view>
									</block> -->
									<block v-if="item.cwlx==3">
										<view><text class="tit">事项：</text><text >{{item.sm}}</text></view>
									</block>
									<block v-else>
										<view><text class="tit">类型：</text><text >{{item.cwlxtxt}}</text></view>
									</block>	
								
									<block v-if="item.type==1"> 
										<view><text class="tit">金额：</text><text class="jine" >+￥{{item.jine_change}}</text></view>
									</block>
									<block v-else>
										<block v-if="item.cwlx==3">
											<view><text class="tit">出资：</text><text class="jine" style="color:#1677FF">￥{{item.jine_change}}</text></view>
										</block>
										<block v-else>
											<view><text class="tit">金额：</text><text class="jine" style="color:#4aac09">-￥{{item.jine_change}}</text></view>
										</block>	 
									</block>
									<!-- 如果是出资，则有回本相关 --> 
									<block v-if="item.cwlx==3"> 
										<view class="stats " :class="'stacolor'+item.status">{{item.statusname}}</view>
										<view class="dhbxg">
											<text class="tit">回本：</text>
											<text class="jine" >￥{{item.yhbjine}}</text>
											，待回本 ：<text class="jine" style="color:#4aac09">￥{{item.whbjine}}</text>
										</view>
										<view class="vxqbtn" @click="gotosyckxq"   :data-czrcdid="item.id">查看详情</view>
										<block v-if="item.djtype==1">
											<view class="vxqbtn2" @click="gotoyd"   :data-djtype="item.djtype" :data-djid="item.djid">查看原单</view>
										</block>
									
									</block>
									
									<view v-if="item.mdname"><text class="tit">门店：</text><text>{{item.mdname}}</text></view>
									<view><text class="tit">时间：</text><text style="color:#888">{{item.addtime}}</text></view>
								
								
									<block v-if="item.remark || item.picturescarr">
										<view class="zkbtn" @click="setvxq" :data-id="item.id">
											展开详情 <text class="iconfont icon-jtdown2"></text>
										</view>
										<block v-if="item.id==thvid">
											<view class="des">
												<view v-if="item.remark">备注：{{item.remark}}</view>
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr" :key="indexx">
										 					<view class='item'>
																<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr" @tap='previewImagex'>
																</image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>
									</block>
								
								
						
								</view>
								
								<view class="clear"></view>
							</view>
						</view>
						
					</block>	
					
				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关数据</view>
			</view>
		</block>
		



		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入备注内容'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>

	<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427" activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20" :closeBtn="true"></tn-calendar>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				type: 0,
				index: 1,
				thvid: 0,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				typedata: [],
				show3: false,
				
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				tjdata:[],
				
				ygid:0,
				ygname:'',
				ygdata:[],
				
			}
		},
		onLoad(options) {
			let that=this;
			let type=options.type ? options.type : 0
			this.type=type;
			this.loadData();
			
			setTimeout(function() {
				  that.page = 0;
				  that.listdata = [];
				  that.isloading = false;
				  that.loadingType = -1;
				  that.loaditems(); 
			}, 100);
			
			
		},
		onShow() {
			
		},
		onReady() {

		},
		onPullDownRefresh() {

		},
		methods: {
			gotoyd(e){
				let djtype=e.currentTarget.dataset.djtype;
				let djid=e.currentTarget.dataset.djid;
				if(djtype==1){
					uni.navigateTo({
						url:'/pages/work/rukudan/view?djid='+djid
					})
				}
			},
			gotosyckxq(e){
				let czrcdid=e.currentTarget.dataset.czrcdid;
				uni.navigateTo({
					url:'/pages/user/zjrcd/view?czrcdid='+czrcdid
				})
			},
			gotozj(){
				uni.navigateTo({
					url:'/pages/user/zjzj/index'
				})
			},
			loadData(){
					  let that=this;
					  let da={
						  tjtype:7,
						  
					  }
					
					 this.$req.post('/v1/muser/gettjnum', da)
					         .then(res => {
					 		   
							     console.log(111,res);
					 			if(res.errcode==0){
									that.tjdata= res.data.tjdata;
									that.typedata= res.data.typedata;
									if(that.type==3){
										that.current=3;
									}
								
					 			}else{
					 				uni.showModal({
					 					content: res.msg,
					 					showCancel: false,
										success() {
											uni.navigateBack();
										}
					 				})
					 			}
					         })
				
			},
			rqmodal(){
				this.rqshow=true
			},
			rqmodalchange(e){
				let that=this;
				if(e.startDate && e.endDate){
					// this.thkw='';
					this.rqkstime=e.startDate;
					this.rqjstime=e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch(){
				let that=this;
				this.rqkstime='';
				this.rqjstime='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
		
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;
				
			 //    this.rqkstime='';
				// this.rqjstime='';				

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			setvxq(e) {
				let thvid = e.currentTarget.dataset.id;
				if (thvid == this.thvid) {
					this.thvid = 0;
				} else {
					this.thvid = thvid;
				}

			},
			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let type = that.typedata[index].type;
				console.log(type);
				
				if(index==0){
					that.thkw = '';
					that.rqkstime='';
					that.rqjstime='';
				}
			
				that.current = index;
				that.type = type;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					type: that.type,
					ygid:that.ygid,
					kw: that.thkw ? that.thkw : '',
					rqkstime:that.rqkstime ? that.rqkstime : 0 ,
					rqjstime:that.rqjstime ? that.rqjstime : 0 ,
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/muser/ygzjrcd', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},








		}
	}
</script>

<style lang='scss'>

	.txbtn{padding-right:20upx;color:#1677FF}
</style>