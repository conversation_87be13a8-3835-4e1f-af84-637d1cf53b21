<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >物品调拨归还</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >

				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
								
								     	<view class="maxcon" >
								     		<view class="ttinfo">
								     			<view class="inf1"  >
								     				<view class="tf1">
													    <view class="spbh">编号：{{spdata.id}}</view>
								     					<view class="tx"><image :src="staticsfile + spdata.smallpic"></image></view>
								     					<view class="xx">
								     						<view class="name">{{spdata.title}}</view>
								     						<view class="tel">
																<text class="lanse">{{spdata.ckname}}</text>
															</view>
								     					</view>
								     					<view class="clear"></view>
								     				</view>	
								     			</view>
								     		</view>
								     	</view>
								   
											<view class="maxcon" style="padding:10upx 28upx">
												<view class="rowx" > 
								
													<view class="tit">调用人员</view>
													<view class="inpcon"><view class="inp">{{dbdata.dyygname}}</view></view>
													<view class="clear"></view>
												</view>
												<view class="rowx" >
										
													<view class="tit">调用数量</view>
													<view class="inpcon"><view class="inp">{{dbdata.czkcnum}}</view></view>
													<view class="clear"></view>
												</view>
												<view class="rowx" >
													<view class="tit">调用时间</view>
													<view class="inpcon"><view class="inp">{{dbdata.addtime}}</view></view>
													<view class="clear"></view>
												</view>
											</view>
											
											
											<view class="maxcon" style="padding:10upx 28upx">
											    <view class="rowx">
													<view class="tit"><text class="redst"></text>归还数量</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="number" name="ghnum"   placeholder="请输入物品数量" placeholder-class="placeholder"   /></view>
													</view>
													<view class="clear"></view>
											    </view>
											</view>
									 
								
									
										<view class="maxcon">
												<view class="vstit">归还说明</view>
												
												<view class="bzcon">
													  <textarea name="remark" class="beizhu"></textarea>
												</view>
									
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx"> 
																<block v-for="(item,index) in img_url_ok" :key="index">
																   <view class='item'  >
																	   <view class="del" @tap="deleteImg"   :data-index="index" ><i class="iconfont icon-shanchu2"></i></view>
																	   <image  :src="staticsfile+item" :data-src='staticsfile + item ' @tap='previewImage' ></image>
																   </view>
																</block>
															   <view class='item' v-if="img_url_ok.length <= 8" >
																   <image @tap="chooseimage"  src='/static/images/uppicaddx.png'></image>
															   </view>
															   <view class="clear"></view>
													 </view>
												</view>		
												 
												 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  			<view class="tn-flex tn-footerfixed">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>提交</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
				
				

			

	
	</view>
  
	<view style="height:120upx"></view>
    

	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 spid:0,
			 spdata:'',
			 status:0,
			 jine:0,
			 img_url: [],
			 img_url_ok:[],
			 systixian:'',
			 tipwztxt:'',
			
			  ckselshow:false,
			  thckdata:'',
			  ckdata:'',
			  ckid:0,
			  ckname:'',
			  
			  dysta:0,
			  tbtype:0,
			  tbtypetxt:'',
			  
			  dbid:0,
			  dbdata:'',
			  thygid:0,
			  thygname:'',
			  
		  }
	},

	onLoad(options) {
		let that=this;
		let dbid=options.dbid ? options.dbid : 0;
		this.dbid=dbid;
		this.loadData();
		
	},
	onShow() {
	
	},

    methods: {
		toback:function(){
		   uni.navigateBack();
		},
		radio_dysta(e){
			this.dysta=e.detail.value;
		},		   
		loadData(){
				  let that=this;
				  let da={
					  dbid:that.dbid
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/ckgl/tiaobogh', da)
				         .then(res => {
				 		    uni.hideLoading();
						    console.log(res);
				 			if(res.errcode==0){
								 that.dbdata=res.data.dbdata;
								 that.spdata=res.data.spdata;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			chooseimage(){
								   let that = this;
								   let img_url_ok=that.img_url_ok;
								   uni.chooseImage({
									   count: 9, //默认9
									   sizeType: ['original', 'compressed'],
									   success: (resx) => {
										  const tempFilePaths = resx.tempFilePaths;
										  
										
												  for(let i = 0;i < tempFilePaths.length; i++) {
														  let da={
															  filePath:tempFilePaths[i],
															  name: 'file'
														  } 
														  uni.showLoading({title: '上传中...'})
														  this.$req.upload('/v1/upload/upfile', da)
																  .then(res => {
																	uni.hideLoading();
																	// res = JSON.parse(res);
																	if(res.errcode==0){
																		img_url_ok.push(res.data.fname);
																		that.img_url_ok=img_url_ok;
																		
																		uni.showToast({
																			icon: 'none',
																			title: res.msg
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
														  
														  })
												}
										   
									   }
								   });
							   
							},
							previewImage: function (e) {
									  let that = this;
									  let src = e.currentTarget.dataset.src;
									  let img_url_ok=that.img_url_ok;
									  let imgarr=[];
									  img_url_ok.forEach(function(item,index,arr){
										  imgarr[index] = that.staticsfile+item;
									  });
									  wx.previewImage({
										  current: src,
										  urls: imgarr
									  });
							},
							deleteImg: function (e) {
								let that = this;
								let index = e.currentTarget.dataset.index;
								let img_url_ok = that.img_url_ok;
								img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
								that.img_url_ok=img_url_ok;
							},
		
		
		
			formSubmit: function(e) {
						let that=this;
					   var formdata = e.detail.value
						
							
									 let pvalue = e.detail.value;
										 
									
											 if(!pvalue.ghnum || pvalue.ghnum <= 0){
											 	   uni.showToast({
											 		icon: 'none',
											 		title: '请输入归还数量',
											 	   });
											 	   return false;
											 }
										
										
										if(!pvalue.remark){
										  uni.showToast({
											icon: 'none',
											title: '请输入归还说明',
										  });
										  return false;
										}
									  
								       uni.showModal({
													title: '',
													content: '以上信息已确认无误并提交吗？',
													success: function(e) {
														//点击确定 
														if (e.confirm) {
												                   uni.showLoading({title: '处理中...'})
																   let da={
																		'dbid': that.dbid,
																		'remark': pvalue.remark ? pvalue.remark : '',
																		'ghnum': pvalue.ghnum ? pvalue.ghnum : 0,
																		'pictures': that.img_url_ok ? that.img_url_ok  : '' ,
																   }
															
																   that.$req.post('/v1/ckgl/tiaoboghsave', da)
																  .then(res => {
																	uni.hideLoading();
																	console.log(res);
																	if(res.errcode==0){
																
																		uni.setStorageSync('thupsplist',1)
																		uni.showToast({
																			icon: 'none',
																			title: res.msg,
																			success() {
																				setTimeout(function(){
																					uni.navigateBack()
																				},1000)
																			}
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
																	
																  })
																  
															
														}
													}
								})	  
									  
									  
									  
									  
									  
									  
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.toplinex{border-top:1px solid #fff}

.matxcont{margin:32upx;padding:32upx;border-radius:20upx;background: #fff;min-height: 500upx;}
.notixiancon{text-align: center;}
.tipcon{padding:50px 0}
.tipcon text{font-size:55px;color:#FFD427}
.smlink{margin-top:40px}


.ttinfo{position: relative;}
.ttinfo .icon{position: absolute;right:0upx;top:30upx}
.ttinfo .icon .iconfont{color:#ddd}
.ttinfo .inf1{}
.ttinfo .lanse{color:#1677FF;font-size:24upx}
.ttinfo .inf1 .tx{float:left}
.ttinfo .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
.ttinfo .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:10upx;}
.ttinfo .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.ttinfo .inf1 .tel{color: #333;font-size: 24rpx;margin-top:10upx}
.ttinfo .inf1 .tel text{margin-right:30upx}
.ttinfo .spbh{position: absolute;right:0;top:0;font-size:20upx;color:#1677FF}

.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{margin-bottom:20upx}


.sqbdcon{margin:32upx;}


.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.rowxmttp{margin-top:30upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:200}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}

.jinecon{position: relative;margin:40upx 0}
.jinecon .icon{position: absolute;left:0;top:10upx;}
.jinecon .icon .iconfont{font-size:40upx}
.jinecon .inputss{font-size:40upx;color:#000;padding-left:40upx}



.rowx{
	position: relative;
	border-bottom:1px solid #F0F0F0;
}
.rowx .icon{position: absolute;right:0;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{color: #7F7F7F;}
.rowx .inp .input{height:90upx;line-height:90upx;}
.rowx:last-child{border-bottom:0;}

 
</style>

