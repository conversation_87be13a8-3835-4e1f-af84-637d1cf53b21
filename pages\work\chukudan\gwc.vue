<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>
					已选物品
				</text>
			</view>
			<view slot="right">
				<view class="jxxzwp" @click="selwp"><text class="iconfont icon-tianjia2"></text> 选择物品</view>
			</view>

		</tn-nav-bar>

		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 15 + 'px'}"></view>


		<block v-if="listdata.length>0">


			<view class="splist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="item ">

						<view class="xgbtn2" @click="addckd" :data-gwcid='item.id' :data-spid='item.spid'
							:data-spname='item.title' :data-sjjiage='item.ckjiage' :data-cknum='item.cknum'
							:data-kucun='item.kucun'><text class="iconfont icon-bianji"></text></view>

						<view class="inf1">
							<view class="spbh">编号：{{item.id}}</view>
							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + item.smallpic" @tap='previewImagev' :data-picarr="item.picturesarr"></image>
								</view>
								<view class="xx">
									<view class="n0"><text class="ddtype"
											:class="'ys'+item.ddtype">{{item.ddtypename}}</text> {{item.sortname}}
									</view>
									<view class="n1">{{item.title}}</view>
									<view class="n2">单价数量：￥{{item.ckjiage}} x {{item.cknum}}</view>
									<view class="n2">出库总价：<text class="zj">￥{{item.zongjia}}</text></view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
						<view class="inf2">
							<view class="f1">加入时间：{{item.addtime}}</view>
							<view class="f1">物品所在：{{item.mdname}} <text style="margin:0 15upx">-</text> {{item.ckname}}
							</view>
							<view class="setbtn" @click="gwcspyc" :data-id='item.id'>移除</view>
						</view>




					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂未选择任何物品</view>
				<view class="gotobtn" @click="selwp"><text class="iconfont icon-tianjia2"></text> 选择物品</view>

			</view>
		</block>



		<tn-popup v-model="spsxshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">

				<view class="rowx">
					<view class="tit">出库价格</view>
					<view class="inpcon">
						<view class="inp"><input class="input" type="number" name="ckjiage" v-model="thckjiage"
								placeholder="请输入出库价格" placeholder-class="placeholder" /></view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="rowx">
					<view class="tit">出库数量</view>
					<view class="inpcon">
						<view class="inp">
							<!-- <tn-number-box v-model="thcknum" :min="1" :max="thkucun" :step="1"></tn-number-box> -->
						   <view class="inp"><input class="input" type="number" name="thcknum" v-model="thcknum"  :placeholder="'请输入出库数量'" placeholder-class="placeholder"   /></view>
						
						</view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="qrsel">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>

		<view class="tn-flex tn-footerfixed">
			<view class="diczcon">
				<view class="sf1">
					<text class="iconfont icon-gouwucheman"></text>
					已选 <text class="tnum">{{gwctj.gwcspnum}}</text> 件,
					<text class="tnum">￥{{gwctj.gwcspzj}}</text>
				</view>
				<view class="sf2">
					<view class="qrbtn" @click="gotoqrxz">确认提交</view>
					<!-- <view class="qrbtn2" @click="selwp"   ><text class="iconfont icon-tianjia2"></text> 选择物品</view> -->
				</view>
				<view class="clear"></view>
			</view>

		</view>

		<view style="height:120upx"></view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				statxt: '状态',
				ddtype: 0,
				ddtypetxt: '类型',
				index: 1,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],

				gwctj: [],
				thspid: 0,
				thspname: '',
				thsjjiage: 0,
				thkucun: 0,
				thcknum: 1,
				thckjiage: 0,
				spsxshow: false,

				gwcid: 0,

			}
		},
		onLoad(options) {

		},
		onShow() {
			let that = this;
			this.loadData();
			that.page = 0;
			that.listdata = [];
			that.isloading = false;
			that.loadingType = -1;
			that.loaditems();
		},
		methods: {
			previewImagev: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;
			   
				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			selwp() {
				uni.navigateTo({
					url: '/pages/selwp/index?stype=2'
				})
			},
			addckd(e) {
				let that = this;
				let gwcid = e.currentTarget.dataset.gwcid;
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				let thsjjiage = e.currentTarget.dataset.sjjiage;
				let thkucun = e.currentTarget.dataset.kucun;
				let thcknum = e.currentTarget.dataset.cknum;
				this.gwcid = gwcid;
				this.thspid = spid;
				this.thspname = spname;
				this.thsjjiage = thsjjiage;
				this.thckjiage = thsjjiage;
				this.thkucun = thkucun;
				this.thcknum = thcknum;
				this.spsxshow = true;
				console.log(spid);
			},
			qrsel() {
				let that = this;
				let da = {
					gwcid: this.gwcid,
					spid: this.thspid,
					cknum: this.thcknum,
					ckjiage: this.thckjiage,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danjuck/gwcxgsel', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {

							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.spsxshow = false;
										that.gwctj = res.data.gwctj;
										that.page = 0;
										that.listdata = [];
										that.isloading = false;
										that.loadingType = -1;
										that.loaditems();
									}, 1000);
								}
							});

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {

								}
							})
						}
					})
			},


			gwcspyc(e) {
				let that = this;
				let id = e.currentTarget.dataset.id;
				uni.showModal({
					title: '',
					content: '确认移除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'id': id,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danjuck/gwcspdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},


			loadData() {
				let that = this;
				let da = {
					tjtype: 2,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danjuck/gwctj', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							that.gwctj = res.data.gwctj;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/danjuck/gwcsplist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},

			gotoqrxz() {
				let that = this;
				let da = {}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/danjuck/gwcsubmit', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							let djid = res.data.djid;
							uni.navigateTo({
								url:'./edit?djid='+djid
							})
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},


		}
	}
</script>





<style lang='scss'>
	.jxxzwp {
		margin-right: 20upx;
		color: #1677FF
	}

	.jxxzwp .iconfont {
		margin-right: 10upx;
	}




	.splist {
		padding: 0 32upx 32upx 32upx
	}

	.splist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx;
		position: relative;
	}

	.splist .item .xgbtn2 {
		position: absolute;
		right: 28upx;
		top: 150upx;
		font-size: 24upx;
		z-index: 200;
	}

	.splist .item .spbh {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 20upx;
		color: #1677FF
	}

	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .status {
		position: absolute;
		right: 28upx;
		bottom: 80upx;
		border-radius: 30upx;
		padding: 5upx 15upx;
		color: #fff;
		background: #ccc;
		font-size: 24upx
	}

	.splist .item .inf1 .sjstatus {
		position: absolute;
		right: 50upx;
		bottom: 30upx;
		font-size: 24upx;
	}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 200upx;
		height: 200upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: right;
		width: calc(100% - 226upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 45upx;
		line-height: 45upx;
		overflow: hidden;
		margin-bottom: 15upx;
		font-size: 28upx
	}

	.splist .item .inf1 .xx .n2 {
		margin-top: 10upx;
	}

	.splist .item .inf1 .xx .n2 text {
		color: #ff0000
	}

	.splist .item .inf1 .xx .n2 .zj {
		font-weight: 600
	}

	.splist .item .inf2 {
		background: #F0F0F0;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 15upx 28upx;
		font-size: 20rpx;
		color: #333;
		position: relative;
		line-height: 40upx;
	}

	.splist .item .inf2 .rktime {}

	.splist .item .inf2 .f1 {}

	.splist .item .inf2 .setbtn {
		position: absolute;
		right: 25upx;
		top: 29upx;
		width: 120rpx;
		height: 55upx;
		line-height: 55upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .inf2 .setbtn.btn22 {
		background: #ddd;
		color: #888;
	}

	.splist .item .inf2 .setbtn2 {
		position: absolute;
		right: 145upx;
		top: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fff;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}

	.splist .item .status.sta1 {
		background: #54D216;
	}

	.splist .item .status.sta2 {
		background: #FF6600;
		color: #fff
	}

	.splist .item .status.sta3 {
		background: #FF2828;
		color: #fff
	}

	.splist .item .status.sta8 {
		background: #888;
	}

	.splist .item .status.sta9 {
		background: #c76096;
		color: #fff
	}

	.splist .item .sjstatus.sta1 {
		color: #43b058;
	}

	.splist .item .sjstatus.sta2 {
		color: #888;
	}



	.flckselcon {
		padding: 0 30upx;
		height: 70upx;
		line-height: 70upx;
		width: 100%;
		z-index: 300;
		background: #f6f6f6;
		overflow: hidden;
		border-bottom: 1px solid #eaeaea;
	}

	.flckselcon .icon-jiantou2 {
		font-size: 20upx;
		color: #888
	}

	.flckselcon .sf1 {
		float: left
	}

	.flckselcon .sf2 {
		float: right;
	}

	.flckselcon .wpflsel {}

	.flckselcon .wpflsel .da0 {
		float: left;
		margin-right: 10upx
	}

	.flckselcon .wpflsel .da1 {
		float: left
	}

	.flckselcon .wpflsel .da2 {
		float: left
	}

	.flckselcon .wpcksel {}

	.cznrcon {
		padding: 40upx;
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inpcon .colorhs {
		color: #ff0000
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
</style>