<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex=100>
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >{{xtitle}}</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
   
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
										
									<view class="maxcon"  >
											
											<view class="vstit" style="margin-bottom:0">验货人员</view>
											<view class="more vls" @click="selyg">新增 <text class="iconfont icon-tianjia2" ></text></view>
											
											<block v-if="yhrydata.length > 0">
												<view class="yhrylist">
													<block v-for="(itemx,index) in yhrydata" :key="index">
														 <view class="xxitem">
															 {{itemx.ygname}} <text style="color:#888;margin-left:10px">({{itemx.ygid}})</text>
															 <view class="xxset">
																 <text class="iconfont icon-guanbi4" @click="delyhry" :data-ygid="itemx.ygid" ></text>
															 </view>
														 </view>
													</block>	
												</view>
											</block>
											
											
									</view>	
										
									
									<view class="maxcon">
											
											<view class="vstit">物品信息</view>
											
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>物品名称</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="text" name="title"  v-model="spdata.title" placeholder="请输入物品名称" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
											</view>
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>{{dtptxt}}{{dtpjgtxt}}</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="number" name="danjia" v-model="danjia"  :placeholder="'请输入'+dtptxt+dtpjgtxt" placeholder-class="placeholder" @input="jszongjia" /></view>
												</view>
												<view class="clear"></view>
											</view>
											<view class="rowx">
												<view class="tit"><text class="redst"></text>物品数量</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="number" name="shuliang" v-model="shuliang"  :placeholder="'请输入物品数量'" placeholder-class="placeholder"  @input="jszongjia" /></view>
												</view>
												<view class="clear"></view>
											</view>
											<view class="rowx">
												<view class="tit"><text class="redst"></text>{{dtptxt}}总价</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="number" name="zongjia" v-model="zongjia"  :placeholder="'请输入'+dtptxt+'总价'" placeholder-class="placeholder" @input="jsdanjia" /></view>
												</view>
												<view class="clear"></view>
											</view>
											<!-- 寄卖 -->
										  <block v-if="ddtype==1">
											
											<view class="rowx">
												<view class="tit"><text class="redst"></text>寄卖服务费</view>
												<view class="inpcon">
												   <view class="inp"><input class="input" type="number" name="fwfjine" disabled="" v-model="fwfjine"  placeholder="请输入服务费金额" placeholder-class="placeholder" /></view>
												</view>
												<view class="clear"></view>
												<view class="rowxff">
													<view class="wz">按比例</view>
													<view class="srk"><input class="input2" type="number" name="fwfbl" v-model="fwfbl" max="100" 
															placeholder="" placeholder-class="placeholder" @input="jsfwjine()" />
													</view>
													<view class="tb" style="color:#1677FF;z-index:200;">%</view>
													<view class="clear"></view>
												</view>
											</view>
											
										  </block>	 
										  <!-- 质押 -->
										  <block v-if="ddtype==3">
												<view class="rowx">
													<view class="tit"><text class="redst"></text>服务费金额</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="number" name="fwfjine" v-model="fwfjine"  placeholder="请输入服务费金额" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
													<view class="rowxff">
														<view class="wz">按比例</view>
														<view class="srk"><input class="input2" type="number" name="fwfbl" v-model="fwfbl"  max="100" 
																placeholder="" placeholder-class="placeholder" @input="jsfwjine()" />
														</view>
														<view class="tb" style="color:#1677FF;z-index:200;">%</view>
														<view class="clear"></view>
													</view>
												</view>
										  </block>	
										  
									</view>	
									
									
									<view class="maxcon">
											
											
											<view class="rowx">
												<view class="icon" ><text class="iconfont icon-jiantou"></text></view>
												<view class="tit"><text class="redst"></text>物品分类</view>
												<view class="inpcon">
													<view class="inp">
														<view @click="selwpfl">{{sortid > 0 ? sortnamep : '请选择物品分类'}}</view>
													</view>
												</view>
												<view class="clear"></view>
											</view>
											
											
											<block v-if="spflsxlist">
												<block v-for="(item,index) in spflsxlist" :key="index">
													<block v-if="item.sxtype==3">
														<view class="rowx">
															<view class="tit"><text class="redst"></text>{{item.title}}</view>
															<view class="inpcon">
															   <view class="inp"><input class="input" type="text" :id="item.id"  v-model="item.value"  :placeholder="'请输入'+item.title" placeholder-class="placeholder" @blur="sxinplur" /></view>
															</view>
															<view class="clear"></view>
														</view>
													</block>
													<block v-else>	
														<view class="rowx">
															<view class="icon"><text class="iconfont icon-jiantou"></text></view>
															<view class="tit"><text class="redst"></text>{{item.title}}</view>
															<view class="inpcon" @click="sxsel" :data-sxid="item.id"  :data-sxname="item.title"  :data-sxtype="item.sxtype" :data-sxvalue="item.sxvalue"  >
															   <view class="inp">
																   <block v-if="item.value">
																      {{item.value}}
																   </block>
																   <block v-else>
																	  请选择{{item.title}} 
																   </block>   
															   </view>
															</view>
															<view class="clear"></view>
														</view>
													</block>	
												</block>
											</block>
											
											
											
											<view class="rowx" >
												<view class="icon"><text class="iconfont icon-jiantou"></text></view>
												<view class="tit"><text class="redst"></text>入库选择</view>
												<view class="inpcon hs" >
													<view @click="selcangku">
														  <block v-if="ckname">
															 {{ckname}}
														  </block>
														  <block v-else>
															点击选择仓库
														  </block>	   
													</view>
													<text class="iconfont icon-cuohao02" v-if="ckname" style="position: absolute;right:0;top:0;z-index:200;" @click="clsthck"></text>
													
												</view>
												<view class="clear"></view>
											</view>
											
											<block v-if="ddtype!=3">
												<view class="rowx">
													<view class="tit"><text class="redst"></text>上架价格</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="number" name="sjjiage" v-model="sjjiage"  placeholder="请输入上架价格" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
											</block>
											
											
									</view>	
									
									
									
										<view class="maxcon">
												<view class="vstit" style="font-weight:400;margin-bottom:25upx">物品备注 <text style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户可见）</text></view>
												
												<view class="bzcon">
													  <textarea name="beizhu" class="beizhu" v-model="beizhu"></textarea>
												</view>
									
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx"> 
																<block v-for="(item,index) in img_url_ok" :key="index">
																   <view class='item'  >
																	   <view class="del" @tap="deleteImg"   :data-index="index" ><i class="iconfont icon-shanchu2"></i></view>
																	   <image  :src="staticsfile+item" :data-src='staticsfile + item ' @tap='previewImage' ></image>
																   </view>
																</block>
															   <view class='item' v-if="img_url_ok.length <= 8" >
																   <image @tap="chooseimage"  src='/static/images/uppicaddx.png'></image>
															   </view>
															   <view class="clear"></view>
													 </view>
												</view>		
												 
												 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  			<view class="tn-flex tn-footerfixed" style="z-index:10;">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>确认提交</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
				
				
				
		
			

	
	</view>
  
	<view style="height:120upx"></view>
	
	<tn-modal v-model="fwfblshow" :custom="true" :showCloseBtn="true">
	  <view class="custom-modal-content"> 
	    <view class="">
	      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">服务费比例</view>
	      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 10rpx 0 60rpx 0;position: relative;">
			  <view style="position: absolute;right:20upx;color:#1677FF;font-size:32upx">%</view> 
	        <input :placeholder="'请输入服务费比例'" v-model="fwfbl" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
	      </view> 
	    </view> 
	    <view class="tn-flex-1 justify-content-item  tn-text-center">
	      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="jsfwjine" >
	        <text >确认提交</text>
	      </tn-button>
	    </view>
	  </view>
	</tn-modal>
	
    
	<tn-popup v-model="ckselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
		<view class="popuptit">选择仓库</view>
	    <scroll-view scroll-y="true" style="height: 800rpx;">
	          <view class="xselcklist">
	              <block v-for="(item,index) in ckdata" :key="index">
					  
					  <view class="item" @click="selthck" :data-ckid="item.id"  :data-ckname="item.title"  >
						    <view class="xzbtn"  >选择</view>
					  		<view class="tx"><image :src="staticsfile + item.logo"></image></view>
					  		<view class="xx">
					  			<view class="name">{{item.title}}</view>
					  			<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
					  		</view>
					  		<view class="clear"></view>
					  </view>
					  
			      </block>		  
	          </view>
	        </scroll-view>
	</tn-popup>
	
	
	<tn-popup v-model="sxselshow" mode="bottom" :borderRadius="20" :closeBtn=true>
		<view class="popuptit">请选择{{thsxname}}</view>
	    <scroll-view scroll-y="true" style="height: 800rpx;">
	          <view class="sxvlist">
				  
				  <block v-if="thsxtype==1">
					  <radio-group @change="zdysxChange">
									<block  v-for="(item, index) in thsxvalue" :key="index">
										<view class="item">
											<label >
												<view class="icon">
													<radio :value="item"  color="#FFD427" />
												</view>
												<view class="vstxt">{{item}}</view>
												<view class="clear"></view>
											</label>
										</view>	
									</block>
					   </radio-group>
					</block>
					<block v-if="thsxtype==2">
						<checkbox-group @change="zdysxmChange">
								<block  v-for="(item, index) in thsxvalue" :key="index">
									<view class="item">
										<label >
											<view class="icon">
												<checkbox :value="item"  color="#FFD427" />
											</view>
											<view class="vstxt">{{item}}</view>
											<view class="clear"></view>
										</label>
									</view>	
								</block>
						 </checkbox-group>
						 
						 <view style="height:160upx"></view>
						 <view class="tn-flex tn-footerfixed">
						   <view class="tn-flex-1 justify-content-item tn-text-center">
							   <view class="bomsfleft">
								  <button class="bomsubbtn"  @click="closezdysxm"  style="background:#ddd;" >
									  <text>关闭</text>
								  </button>
							   </view>
						 	   <view class="bomsfright">
						 		  <button class="bomsubbtn" @click="qrzdysxm">
						 		    <text>确认选择</text>
						 		  </button>
						 	   </view> 	  
						 	   <view class="clear"></view>	
						   </view>
						 </view>
					</block>   
	           	  
	          </view>
	        </scroll-view>
	</tn-popup>
	
	
	<tn-wpfl v-model="wpflshow" @qrxz="wpflqrxz" @close="wpflclose"></tn-wpfl>
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 img_url: [],
			 img_url_ok:[],
			 xtitle:'',
			 spdata:[],
			 ddtype:'',
			 yhrydata:[],
			 clicksta:false,
			 dtptxt:'',
			 dtpjgtxt:'',

			 danjia:"",
			 shuliang:"",
			 zongjia:"0.00",
			 sjjiage:"0.00",
			 fwfjine:"0.00",
			 fwfbl:'',
			 ckdata:'',
			 ckid:0,
			 ckname:'',
			 ckselshow:false,
			 sxselshow:false,
			 fwfblshow:false,
			 spsortdata: [],
			 spflsxlist:'',
			 thsxtype:0,
			 thsxid:0,
			 thsxname:'',
			 thsxvalue:'',
			 thsxmtemp:'',
			 beizhu:'',
			 
			 
			 wpflshow: false,
			 sortid: 0,
			 sortnamep: '',
			
		  }
	},

	onLoad(options) {
		let that=this;
		let djid=options.djid ? options.djid : 0;
		let spid=options.spid ? options.spid : 0;
		this.djid=djid;
		this.spid=spid;
		this.loadData();
		
		if(spid > 0){
			this.xtitle="编辑物品";
		}else{
			this.xtitle="添加物品";
		}
		
	},
	onShow() {
	
		  this.clicksta=false;
		  
		  let thygid=uni.getStorageSync('thygid');
		  let thygname=uni.getStorageSync('thygname');
		  if(thygid){
			let yhrydata=this.yhrydata;
			let isyhrybh=0;
			if(yhrydata.length> 0){
				yhrydata.forEach( (item , index ) => {
				   if(item.id==thygid){
					   isyhrybh=1;
				   }
				})
			}
			if(isyhrybh==0){
				yhrydata.push({ygid:thygid,ygname:thygname});
			}
			  console.log(yhrydata);
			uni.setStorageSync('thygid','');
			uni.setStorageSync('thygname','');
		  }
	
	},

    methods: {
		
					   
		loadData(){
				  let that=this;
				  let da={
				 	djid:that.djid,
				 	spid:that.spid,
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/danju/rkdspedit', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 		        that.ckdata=res.data.ckdata;
				 		        that.spdata=res.data.spdata;
				 		        that.sortid=parseInt(res.data.sortid);
								that.sortnamep = res.data.spdata.sortname;
				 		        that.spflsxlist=res.data.spflsxlist ? res.data.spflsxlist : '';
				 		        that.ckid=res.data.spdata ? res.data.spdata.ckid : 0 ;
				 		        that.ckname=res.data.spdata ? res.data.spdata.ckname : '' ;
				 		        that.ddtype=res.data.ddtype;
								that.yhrydata = res.data.yhrydata;
								that.dtptxt = res.data.dtptxt;
								that.dtpjgtxt = res.data.dtpjgtxt;
								that.danjia=res.data.spdata ? res.data.spdata.danjia : '';
								that.shuliang=res.data.spdata ? res.data.spdata.shuliang : '';
								that.zongjia=res.data.spdata ? res.data.spdata.zongjia : "0.00";
								that.sjjiage=res.data.spdata ? res.data.spdata.sjjiage : "0.00";
								that.fwfjine=res.data.spdata ? res.data.spdata.fwfjine : "0.00";
								that.fwfbl=res.data.spdata ? res.data.spdata.fwfbl : "0";
								that.spsortdata=res.data.spsortdata;
								that.beizhu=res.data.spdata.beizhu;
								that.img_url_ok=res.data.picturesarr;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
					
				     })
			
		},
		selyg(){
			 uni.navigateTo({
				url:'/pages/selyg/index?stype=5&isdx=1'
			 })
		},
		delyhry(e){
			  let ygid=e.currentTarget.dataset.ygid;
			  let yhrydata=this.yhrydata;
			  yhrydata.forEach( (item , index ) => {
				 if(item.ygid==ygid){
				   yhrydata.splice(index, 1);
				 }
			  })
		},
		
			
		chooseimage(){
		   let that = this;
		   let img_url_ok=that.img_url_ok;
		   uni.chooseImage({
			   count: 9, //默认9
			   sizeType: ['original', 'compressed'],
			   success: (resx) => {
				  const tempFilePaths = resx.tempFilePaths;
				  
				
						  for(let i = 0;i < tempFilePaths.length; i++) {
								  let da={
									  filePath:tempFilePaths[i],
									  name: 'file'
								  } 
								  uni.showLoading({title: '上传中...'})
								  this.$req.upload('/v1/upload/upfile', da)
										  .then(res => {
											uni.hideLoading();
											// res = JSON.parse(res);
											if(res.errcode==0){
												img_url_ok.push(res.data.fname);
												that.img_url_ok=img_url_ok;
												
												uni.showToast({
													icon: 'none',
													title: res.msg
												});
											}else{
												uni.showModal({
													content: res.msg,
													showCancel: false
												})
											}
								  
								  })
						}
				   
			   }
		   });
							    
		},
		previewImage: function (e) {
				  let that = this;
				  let src = e.currentTarget.dataset.src;
				  let img_url_ok=that.img_url_ok;
				  let imgarr=[];
				  img_url_ok.forEach(function(item,index,arr){
					  imgarr[index] = that.staticsfile+item;
				  });
				  wx.previewImage({
					  current: src,
					  urls: imgarr
				  });
		},
		deleteImg: function (e) {
			let that = this;
			let index = e.currentTarget.dataset.index;
			let img_url_ok = that.img_url_ok;
			img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
			that.img_url_ok=img_url_ok;
		},
		selcangku(){
			this.ckselshow=true;
		},
		fwfblset(){
			
			if(!this.zongjia || this.zongjia==0){
				uni.showToast({
					icon: 'none',
					title: '请先完善物品总价',
				});
				return false;
			}
			
			this.fwfblshow=true;
		},
	
		sxsel(e){
			let thsxtype=e.currentTarget.dataset.sxtype;
			let thsxid=e.currentTarget.dataset.sxid;
			let thsxname=e.currentTarget.dataset.sxname;
			let thsxvalue=e.currentTarget.dataset.sxvalue;
		
			this.thsxtype=thsxtype;
			this.thsxid=thsxid;
			this.thsxname=thsxname;
			this.thsxvalue=thsxvalue;
			this.sxselshow=true;
			
		},
		
		zdysxChange(e){
			let that=this;
			let value=e.detail.value;
		
	        let thsxid=that.thsxid;
			let spflsxlist=that.spflsxlist;
			
			if(value.length == 0){value='';}
			for (var i = spflsxlist.length - 1; i >= 0; i--) {
				if(spflsxlist[i].id==thsxid){
					spflsxlist[i].value=value;
					break;
				}
			}
			that.spflsxlist=spflsxlist;
			this.sxselshow=false;
		},
		zdysxmChange(e){
			let value=e.detail.value;
		    this.thsxmtemp=value;
		},
		qrzdysxm(){
			let that=this;
			let value=that.thsxmtemp;
			let thsxid=that.thsxid;
			let spflsxlist=that.spflsxlist;
		
			if(value.length == 0){
				value='';
			}else{
				value=value.join(',');
			}
			for (var i = spflsxlist.length - 1; i >= 0; i--) {
				if(spflsxlist[i].id==thsxid){
					spflsxlist[i].value=value;
					break;
				}
			}
			this.spflsxlist=spflsxlist;
			
			this.sxselshow=false;
		},
		closezdysxm(){
			this.sxselshow=false;
		},
		sxinplur(e){
		
			let that=this;
			let thsxid=e.currentTarget.id;
			let value=e.detail.value;
			let spflsxlist=that.spflsxlist;
			for (var i = spflsxlist.length - 1; i >= 0; i--) {
				if(spflsxlist[i].id==thsxid){
					spflsxlist[i].value=value;
					break;
				}
			}
			that.spflsxlist=spflsxlist;
			
		},
		jszongjia(e){
			let that=this;
			setTimeout(() => {
					let danjia=that.danjia ? that.danjia : 0 ;
					let shuliang=that.shuliang ? that.shuliang : 0 ;
					let zongjia=0;
					let fwfbl=that.fwfbl;
					let fwfjine=0;
					zongjia=parseFloat(danjia) * parseFloat(shuliang);
					zongjia=zongjia.toFixed(2);
					this.zongjia=zongjia; 
					
					console.log(fwfbl);
					if(fwfbl && fwfbl > 0){
						fwfjine=zongjia * fwfbl / 100;
						fwfjine=fwfjine.toFixed(2);
						this.fwfjine=fwfjine;
					}
					
			},0)
		},
		jsdanjia(e){
			let that=this;
			setTimeout(() => {
					let zongjia=that.zongjia ? that.zongjia : 0 ;
					let shuliang=that.shuliang ? that.shuliang : 0 ;
					let danjia=0;
					let fwfbl=that.fwfbl;
					let fwfjine=0;
					
					if(zongjia > 0 ){
						if(shuliang>0){
							danjia=parseFloat(zongjia) / parseFloat(shuliang);
							danjia=danjia.toFixed(2);
							this.danjia=danjia;
							if(fwfbl && fwfbl > 0){
								fwfjine=zongjia * fwfbl / 100;
								fwfjine=fwfjine.toFixed(2);
								this.fwfjine=fwfjine;
							}
						}
					}else{
						this.danjia=0;
						this.fwfjine=0;
					}
				
			},0)
		},
		
		jsfwjine(){
			let that=this;
		    let zongjia=this.zongjia;
			let fwfbl=this.fwfbl;
			let fwfjine=0;
			
			if(zongjia<=0){
				this.zzfwf=0;
				return false;
			}
			
			if(!fwfbl){
				uni.showToast({
					icon: 'none',
					title: '请输入服务费比例',
				});
				setTimeout(function(){
					that.fwfbl=0
				},200)
				return false;
			}
			
			if(fwfbl > 100){
				uni.showToast({
					icon: 'none',
					title: '请输入正确的比例',
				});
				setTimeout(function(){
					that.fwfbl=0
				},200)
				return false;
			}
			
			fwfjine=zongjia * fwfbl / 100;
			fwfjine=fwfjine.toFixed(2);
		    this.fwfjine=fwfjine;
			this.fwfblshow=false;
		},
		
		selthck(e){
			let ckid=e.currentTarget.dataset.ckid;
			let ckname=e.currentTarget.dataset.ckname;
			this.ckid=ckid;
			this.ckname=ckname;
			this.ckselshow=false;
		},
		clsthck(){
			this.ckid=0;
			this.ckname='';
		},
		onchange(e) {
			 let that=this;
		     let valuearr = e.detail.value;
		
			 let vlength=valuearr.length;
			 let sortid=0;
			 if(vlength > 0){
				 sortid = valuearr[vlength - 1].value;
			 }
		
			 
			  let da={
				sortid:sortid,
			  }
			  this.$req.post('/v1/com/getspflsx', da)
					 .then(res => {
						 console.log('131231',res);
						if(res.errcode==0){
							that.spflsxlist=res.data.spflsxlist;
						}
			 })
			 
		 },
		 onnodeclick(node) {
			 // console.log(node.value);
			 // let sortid=node.value;
			 // let that=this;
			 		  
	
		 },
 
        formSubmit: function(e) {
				 let that=this;
				 let pvalue = e.detail.value;
				 let khdata = that.thkhdata ;
				 let ddtype = that.thddtype;
				 let isrlsb = that.isrlsb;
				 let yhrydata = that.yhrydata;
				 let yhryidarr = [];
				 let yhryidstr = '';
				 let sfzhao=pvalue.sfzhao;
				 let sknexturl='';
				 let spflsxlist=that.spflsxlist;
				 let spflsxarr = [];
				 let spflsxstr = '';
					
					
					
				
				if(yhrydata.length > 0){
					yhrydata.forEach( (item , index ) => {
					   yhryidarr.push(item.ygid);
					})
					yhryidstr=yhryidarr.toString();
				}
			
				 if(spflsxlist.length > 0){
				 	spflsxlist.forEach( (item , index ) => {
				 	   spflsxarr.push({id:item.id,title:item.title,value:item.value});
				 	})
				 	// spflsxstr=spflsxarr.toString();
				 }
			
				
				if(!yhryidstr){
				  uni.showToast({
					icon: 'none',
					title: '请选择验货人员',
				  });
				  return false;
				} 
				 
				if(!pvalue.title){
				  uni.showToast({
					icon: 'none',
					title: '请输入物品名称',
				  });
				  return false;
				}
				
				if(!pvalue.danjia){
				  uni.showToast({
					icon: 'none',
					title: '请输入价格',
				  });
				  return false;
				}
				
				if(!pvalue.shuliang || pvalue.shuliang==0){
				  uni.showToast({
					icon: 'none',
					title: '请输入数量',
				  });
				  return false;
				}
				
				if(!pvalue.zongjia || pvalue.zongjia==0){
				  uni.showToast({
					icon: 'none',
					title: '请输入总价',
				  });
				  return false;
				}
				
			    if(ddtype==1){
					if(!that.fwfbl){
					  uni.showToast({
						icon: 'none',
						title: '请输入服务费比例',
					  });
					  return false;
					}
				}
				if(ddtype==3){
					if(!that.fwfjine){
					  uni.showToast({
						icon: 'none',
						title: '请输入服务费金额',
					  });
					  return false;
					}
				}
				
				
				if(!that.sortid || that.sortid==0){
				  uni.showToast({
					icon: 'none',
					title: '请选择物品分类',
				  });
				  return false;
				}
					
				// if(!that.ckid || that.ckid==0){
				//   uni.showToast({
				// 	icon: 'none',
				// 	title: '请选择仓库',
				//   });
				//   return false;
				// }
				if(!that.sjjiage){
					that.sjjiage=0;
				 //  uni.showToast({
					// icon: 'none',
					// title: '请输入上架价格',
				 //  });
				 //  return false;
				}
				
				if(!that.img_url_ok || that.img_url_ok.length==0){
					uni.showToast({
						icon: 'none',
						title: '请上传物品图片',
					});
					return false;
				}
					
				
					
		 if (this.clicksta) return;this.clicksta = true;	
					
					
					
		  uni.showLoading({title: '处理中...'})
		  let da={
			'djid': that.djid,
			'spid': that.spid,
			'yhryidstr': yhryidstr,
			'sortid': that.sortid ? that.sortid : 0,
			'ckid': that.ckid ? that.ckid : 0,
			'ckname': that.ckname ? that.ckname : '',
			'title': pvalue.title,
			'sjjiage': pvalue.sjjiage ? pvalue.sjjiage : 0,
			'danjia': that.danjia,
			'shuliang': that.shuliang,
			'zongjia': that.zongjia,
			'fwfjine': that.fwfjine ? that.fwfjine : 0,
			'fwfbl': that.fwfbl ? that.fwfbl : 0,
			'beizhu': pvalue.beizhu ? pvalue.beizhu : '',
			'pictures': that.img_url_ok ? that.img_url_ok  : '' ,
			'spflsxarr': spflsxarr ,
		  }
		  
		
		  
		  this.$req.post('/v1/danju/rkdspeditsave', da)
				  .then(res => {
					uni.hideLoading();
					console.log(res);
					if(res.errcode==0){
						uni.setStorageSync('thupsplist',1);
						uni.showToast({ 
							icon: 'none',
							title: res.msg,
							success() {
								setTimeout(function(){
								   uni.navigateBack()
								},500)
							}
						});
					}else{
						that.clicksta = false;
						uni.showModal({
							content: res.msg,
							showCancel: false
						})
					}
				  })
				  .catch(err => {
				
				  })
         							  
         },
         			
         		selwpfl() {
         			this.wpflshow = true;
         		},
         		wpflqrxz(node) {
         			console.log(node);
         			let sortid = node.sortid;
         			let that = this;
         			that.sortid = sortid;
         			that.sortnamep = node.sortnamep;
         			let da = {
         				sortid: sortid,
         			}
         			this.$req.post('/v1/com/getspflsx', da)
         				.then(res => {
							console.log(res);
         					if (res.errcode == 0) {
         						that.spflsxlist = res.data.spflsxlist;
         					}
         				})
         		
         			this.wpflshow = false;
         		},
         		wpflclose() {
         			this.wpflshow = false;
         		},

	
    }
  }
</script>

<style lang="scss" >

.toplinex{border-top:1px solid #fff}




.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx;position: relative;}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .more{position: absolute;right:28upx;top:28upx;color:#888;}
.maxcon .more .t1{color: #7F7F7F;}
.maxcon .more.vls{color: #1677FF;}
.maxcon .more .iconfont{margin-left:10upx}


.sqbdcon{margin:32upx;}


.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.rowxmttp{margin-top:30upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:10}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}


.yhrylist{padding-top:30upx}
.yhrylist .xxitem{height:80upx;line-height:80upx;overflow:hidden;position: relative;border-bottom:1px solid #efefef;}
.yhrylist .xxitem .xxset{position:absolute;right:0;top:0}
.yhrylist .xxitem .xxset text{margin-left:40upx;font-size:28upx;}
.yhrylist .xxitem:last-child{border-bottom:0;}


.rowx{position: relative;border-bottom: 1px solid #eee;}
.rowx .icon{position: absolute;right:2upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 95px);font-size:28upx;height:90upx;line-height:90upx;overflow: hidden;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{color:#888}
.rowx .inp .input{height:90upx;line-height:90upx;}
.rowx:last-child{border-bottom: 0;}
 
 
 

 
 
.sxvlist{} 
.sxvlist .item{padding:0 30upx;border-bottom:1px solid #efefef;height:100upx;line-height:100upx} 
.sxvlist .item .icon{float:left} 
.sxvlist .item .vstxt{float:left;mragin-left:20upx} 

.maxcon .rowxff {margin-top: 10upx;position: absolute;right: 0upx;top: 0	}
.maxcon .rowxff .tb {height: 70upx;line-height: 70upx;float: left}
.maxcon .rowxff .wz {float: left;font-size: 28upx;color: #333;height: 70upx;line-height: 70upx;}
.maxcon .rowxff .srk {float: left;margin: 0 15upx}
.maxcon .rowxff .srk .input2 {height: 70upx;line-height: 70upx;border: 1px solid #ddd;text-align: center;width: 110upx;border-radius: 15upx;background: #fafafa;	}


 
</style>

