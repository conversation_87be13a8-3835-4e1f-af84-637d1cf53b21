<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="tabs-fixed-blank"></view>


		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 100;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 75vw;overflow: hidden;padding-left:10px;">

					<view class="tbdhdown">
						<view class="xite" :class="type==0 && sta==0    ? 'hoverss' : ''"
							@click="tbdhdownclk" :data-dwtype="0">
							<view class="con">全部</view>
						</view>
						<view class="xite" :class="dwtype==1 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="1">
							<view class="con">{{type!=0 ? typetxt : '类型'}} <text
									class="iconfont icon-jiantou-copy-copy"></text></view>
						</view>
						<view class="xite" :class="dwtype==2 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="2">
							<view class="con">{{sta!=0 ? statxt : '状态'}} <text
									class="iconfont icon-jiantou-copy-copy"></text></view>
						</view>
						<view class="xite" :class="dwtype==9 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="9">
							<view class="con">{{pxfs!=0 ? pxfstxt : '排序'}} <text
									class="iconfont icon-jiantou-copy-copy"></text></view>
						</view>
					</view>
				</view>
				<view class="justify-content-item gnssrr" style="width: 25vw;overflow: hidden;">
					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>
		</view>

 
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 5 + 'px'}"></view>

        <block v-if="thkw || rqkstime">
        		<view class="ssvtiao">
					<block v-if="rqkstime">
						<view class="ssgjc">
							 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
							<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
							</view>
						</view>
					</block>
        			<block v-if="thkw">
        				<view class="ssgjc">
        					 <text class="gjc">" {{thkw}} "</text>
        					<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
        					</view>
        				</view>
        			</block>
					<view class="clear"></view>
        		</view>
        </block>

		<view class="tjtiao">
			
				<view class="fless">
					<view class="ite"  @click="tbdhdownitemsel" :data-dwtype="2" :data-value="2" data-name="待审核">
						<view class="sz">{{tjdata.tjnum2}}</view>
						<view class="tx">待审核</view>
					</view>
					<view class="ite"  @click="tbdhdownitemsel" :data-dwtype="2" :data-value="1" data-name="已通过">
						<view class="sz">{{tjdata.tjnum1}}</view>
						<view class="tx">已通过</view>
					</view>
					<view class="ite"  @click="tbdhdownitemsel" :data-dwtype="2" :data-value="3" data-name="未通过">
						<view class="sz">{{tjdata.tjnum3}}</view>
						<view class="tx">未通过</view>
					</view>
				</view>
			
		</view>

		<block v-if="listdata.length>0">



			<view class="djlist">

				<block v-for="(item,index) in listdata" :key="index">

				    <view class="item ">
						<view class="con">
							<view class="status" :class="'ys'+item.status">{{item.statusname}}</view>
							<view class="inf0">
								<view class="tf1">
									<text class="ddtype" style="background:#333;color:#FFF;border-left:6upx solid #FFD427" >{{item.typetxt}}</text>
									{{item.ordernum}} 
								</view>
								<view class="tf2">
									<text class="rq">{{item.addtime}}</text>
								</view>
								<view class="clear"></view>
							</view>
							
							<view class="inf1">
								<!-- 1赎回2续期3质押转售4寄卖转回收5增资6减资7结清8亏损9等额本息还款10先息后本还款11押转卖撤回13寄转回收撤回,16多笔还款 -->
								<block v-if="item.status==2">
									<view class="setbtn" @click="caozuo" :data-shid='item.id'>审核</view>
								</block>
								<view>客户信息：<text class="khname">{{item.khname}}</text><text class="khid">（{{item.khid}})</text></view>
								<view v-if="item.spname"><text class="tit">物品名称：</text>{{item.spname}}</view>
								
								<block v-if="item.type==1">
								  <view><text class="tit">物品价格：</text><text >￥{{item.xzongjia}}</text></view>
								  <view><text class="tit">赎回费用：</text><text class="yfjine">￥{{item.shfeiyong}}</text></view>
								</block>
								<block v-if="item.type==2">
									<block v-if="item.ddtype==4">
									  <view><text class="tit">续期周期：</text><text >{{item.ywzqnum}}</text><text >{{item.ywzqdwtxt}}</text></view>
									  <view><text class="tit">续期利息：</text><text class="yfjine">￥{{item.xqlixi}}</text></view>
									</block>
									<block v-if="item.ddtype==1">
									  <view><text class="tit">续期周期：</text><text >{{item.ywzqnum}}</text><text >{{item.ywzqdwtxt}}</text></view>
									</block>
									<block v-if="item.ddtype==3">
									  <view><text class="tit">续期周期：</text><text >{{item.ywzqnum}}</text><text >{{item.ywzqdwtxt}}</text></view>
									  <view><text class="tit">续期费用：</text><text class="yfjine">￥{{item.xqfwf}}</text></view>
									</block>
								</block>
								
								<block v-if="item.type==5">
								  <view><text class="tit">增资金额：</text><text class="yfjine">￥{{item.zjzjine}}</text></view>
								  <view><text class="tit">增资服务费：</text><text class="yfjine">￥{{item.zzfwf}}</text></view>
								</block>
								<block v-if="item.type==6">
								  <view><text class="tit">减资金额：</text><text class="yfjine">￥{{item.zjzjine}}</text></view>
								</block>
								<block v-if="item.type==7">
								  <view><text class="tit">结清金额：</text><text class="yfjine">￥{{item.hkjine}}</text></view>
								</block>
								<block v-if="item.type==9">
								  <view><text class="tit">还款金额：</text><text class="yfjine">￥{{item.hkjine}}</text></view>
								</block>
								<block v-if="item.type==8">
								  <view><text class="tit">亏损金额：</text><text class="yfjine">￥{{item.ksjine}}</text></view>
								</block>
								<block v-if="item.type==10"> 
								  <view><text class="tit">还款金额：</text><text class="yfjine">￥{{item.hkjine}}</text></view>
								</block>
								<block v-if="item.type==15">
								  <view><text class="tit">增加成本：</text><text class="yfjine">￥{{item.shfeiyong}}</text></view>
								</block>
							
								<block v-if="item.type==16">
								  <view><text class="tit">还款笔数：</text><text class="yfjine">{{item.dbhkqs}}</text>笔</view>
								  <view><text class="tit">还款金额：</text><text class="yfjine">￥{{item.hkjine}}</text></view>
								</block>
							
								<block v-if="item.type==3 || item.type==11">
								  <view><text class="tit">物品价格：</text><text class="yfjine">￥{{item.xzongjia}}</text></view>
								</block>
								<block v-if="item.type==4 || item.type==13">
								  <view><text class="tit">物品价格：</text><text class="yfjine">￥{{item.xzongjia}}</text></view>
								</block>

								<block v-if="item.status!=2">
									<view class="inf2">
										<view>审核时间：{{item.shtime}}</view>
										<view>审核人员：{{item.opname}}</view>
										<view class="setbtn3" @click="caozuo" :data-shid='item.id'>查看</view>
									</view>
								</block>


							</view>
						
							<view class="clear"></view>
						</view>
					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关数据</view>
			</view>
		</block>


		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">

						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>

					</block>
				</view>
			</scroll-view>
		</tn-popup>


		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入单号或客户名称'" v-model="thkwtt" name="input"
							placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>

		<tn-popup v-model="tbdhdownshow" mode="top" :marginTop="vuex_custom_bar_height + 38" :zIndex=100
			:borderRadius="20">
			<block v-if="dwtype==1">
				<view class="tbdhdownxxcon2">
					<block v-for="(item,index) in typedata" :key="index">
						<view class="vitem1" :class="item.value==type ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="1" :data-value="item.value" :data-name="item.name">
							{{item.name}}
						</view>
					</block>
					<view class="clear"></view>
				</view>
			</block>
			<block v-if="dwtype==2">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in stadata" :key="index">
						<view class="vitem1" :class="item.value==sta ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="2" :data-value="item.value" :data-name="item.name">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==sta"></text>
						</view>
					</block>
				</view>
			</block>

			<block v-if="dwtype==9">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in pxdata" :key="index">
						<view class="vitem1" :class="item.value==pxfs ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="9" :data-value="item.value" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==pxfs"></text>
						</view>
					</block>
				</view>
			</block>

		</tn-popup>


		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 2,
				statxt: '待审核',
				index: 1,
				thvid: 0,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						value: 0,
						name: '全部'
					},
					{
						value: 2,
						name: '待审核'
					},
					{
						value: 1,
						name: '已审核'
					},
					{
						value: 3,
						name: '未通过'
					},
				],
			
				typedata: [],
				pxdata: [{
						value: 0,
						name: '默认排序',
						vname: '排序',
					},
					{
						value: 1,
						name: '申请时间升序',
						vname: '升序'
					},
					{
						value: 2,
						name: '申请时间降序',
						vname: '降序'
					},
				],
				pxfs: 0,
				pxfstxt: '排序',
				show3: false,

				rqshow: false,
				rqkstime: 0,
				rqjstime: 0,
				tjdata: [],
				type: 0,
				typetxt:'类型',
				xtitle: '',

				khid: 0,
				khname: '',
				khdata: [],

				dpselshow: false,
				dpdata: '',
				dpid: 0,
				dpname: '',

				tbdhdownshow: false,
				dwtype: 2,

			}
		},
		onLoad(options) {
			let that = this;
			// let type = options.type ? options.type : 1;
			// let khid = options.khid ? options.khid : 0;
			// let khname = options.khname ? options.khname : '';
			// let dpid = options.dpid ? options.dpid : 0;
			// let dpname = options.dpname ? options.dpname : '';
			// if (khid > 0) {
			// 	this.khid = khid;
			// 	this.khname = khname;
			// }
			// if (dpid > 0) {
			// 	this.dpid = dpid;
			// 	this.dpname = dpname;
			// }
			// this.type = type;

			this.loadData();

			that.page = 0;
			that.listdata = [];
			that.isloading = false;
			that.loadingType = -1;
			that.loaditems();
		},
		onShow() {
			let that = this;

			let thkhdata = uni.getStorageSync('thkhdata');
			if (thkhdata) {
				this.khid = thkhdata.id;
				this.khname = thkhdata.realname;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thkhdata', '');
			}

			if (uni.getStorageSync('thuplist') == 1) {
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thuplist', 0)
			}

		},
		onReady() {

		},
		onPullDownRefresh() {

		},
		methods: {

			selkh() {
				uni.navigateTo({
					url: '/pages/selkh/index?stype=1'
				})
			},

			seldianpu() {
				this.dpselshow = true;
			},
			selthdp(e) {
				let that = this;
				let dpid = e.currentTarget.dataset.dpid;
				let dpname = e.currentTarget.dataset.dpname;
				this.dpid = dpid;
				this.dpname = dpname;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.dpselshow = false;
			},

			clsdpid() {
				let that = this;
				this.dpid = 0;
				this.dpname = '';
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.dpselshow = false;
			},
			clskhid() {
				let that = this;
				this.khid = 0;
				this.khname = '';
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.dpselshow = false;
			},

			loadData() {
				let that = this;
				let da = {
					tjtype: that.type,
					khid: that.khid ? that.khid : 0,
					dpid: that.dpid ? that.dpid : 0,
				}

				this.$req.post('/v1/fuwu/getshtjnum', da)
					.then(res => {

						console.log(111, res);
						if (res.errcode == 0) {
							that.tjdata = res.data.tjdata;
							that.xtitle = res.data.xtitle;
							that.dpdata = res.data.dpdata;
							that.typedata = res.data.typedata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					// this.thkw = '';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			caozuo(e) {
				let shid = e.currentTarget.dataset.shid;
				console.log(shid);
				uni.navigateTo({
					url: './view?shid=' + shid
				})
			},
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				// this.rqkstime = '';
				// this.rqjstime = '';

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			setvxq(e) {
				let thvid = e.currentTarget.dataset.id;
				if (thvid == this.thvid) {
					this.thvid = 0;
				} else {
					this.thvid = thvid;
				}

			},
			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);

				if (index == 0) {
					that.thkw = '';
					that.rqkstime = '';
					that.rqjstime = '';
				}

				that.current = index;
				that.sta = sta;
				that.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					sta: that.sta,
					type: that.type,
					kw: that.thkw ? that.thkw : '',
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
					khid: that.khid ? that.khid : 0,
					dpid: that.dpid ? that.dpid : 0,
					pxfs: that.pxfs ? that.pxfs : 0,
				}
				console.log(that.pxfs);
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/fuwu/ddczshlist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},

			tbdhdownclk(e) {
				let that = this;
				let dwtype = e.currentTarget.dataset.dwtype;

				that.dwtype = dwtype;
				if (dwtype == 0) {
					that.tbdhdownshow = false;
					this.thkw = '';
					this.thkwtt = '';
					that.rqkstime = '';
					that.rqjstime = '';

					that.type = 0;
					// that.sortid = 0;
					// that.ckid = 0;
					that.ckname = '';

					that.thkw = '';
					that.sta = 0;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				} else {
					that.tbdhdownshow = true;
				}

			},
			tbdhdownitemsel(e) {
				let that = this;
				let dwtype = e.currentTarget.dataset.dwtype;
				let value = e.currentTarget.dataset.value;
				let name = e.currentTarget.dataset.name;
				let vname = e.currentTarget.dataset.vname;

				if (dwtype == 1) {
					that.type = value;
					that.typetxt = name;
					that.loadData();
				}

				if (dwtype == 2) {
					that.sta = value;
					that.statxt = name;
				}
				if (dwtype == 9) {
					that.pxfs = value;
					that.pxfstxt = vname;
				}
				that.dwtype = dwtype;
				that.tbdhdownshow = false;

				// that.thkw = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();

			},








		}
	}
</script>

<style lang='scss'>
	
	.djlist .inf1{min-height:130upx;}
</style>