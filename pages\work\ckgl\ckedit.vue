<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
   <tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >{{xtitle}}</text>
					</view>
					<view slot="right" >
					</view>
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
   
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
										
										<view class="maxcon">
											   <view class="dptit"><input class="input" type="text" name="title"  v-model="ckdata.title" placeholder="请输入仓库名称" placeholder-class="placeholder" /></view>
										</view>
								
										
										<view class="maxcon">
												
											
												
												<view class="rowx">
													<view class="icon"><text class="iconfont icon-jiantou"></text></view>
													<view class="tit"><text class="redst"></text>所在地区</view>
													<view class="inpcon">
														<view class="inp">
															<picker mode="multiSelector"  @columnchange="bindMultiPickerColumnChange" :value="multiIndex" :range="multiArray" range-key='name'>
																		<view class="input">
																			{{areatxt}}
																		</view>
															</picker>
														</view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx">
													<view class="tit"><text class="redst"></text>详细地址</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="dizhi"  v-model="ckdata.dizhi" placeholder="请输仓库详细地址" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx" style="display: none;" >
													<view class="icon"><text class="iconfont icon-dingwei" style="color:#F7B52C"></text></view>
													<view class="tit"><text class="redst"></text>标注地图</view>
													<view class="inpcon hs" @click="bzmap">
													  <block v-if="ckdata.lng">
														 {{ckdata.lng}} , {{ckdata.lat}}
													  </block>
													  <block v-else>
														点击标注地图
													  </block>	   
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx" @click="selyg">
													<view class="icon"><text class="iconfont icon-jiantou"></text></view>
													<view class="tit"><text class="redst"></text>管理人员</view>
													<view class="inpcon">
														<view class="inp">
															{{thygname ? thygname : '请选择仓库管理员'}}
														</view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx">
													<view class="tit"><text class="redst"></text>仓库电话</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="cktel" v-model="ckdata.cktel"  placeholder="请输入仓库电话" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
										</view>	
											
										<view class="maxcon">
											<view class="rowxmttp">
												  <view class="tit" style="font-weight:600"><text class="redst"></text>仓库照片</view>
												  <view class="imgconmt" style="margin:20upx 10upx 0upx 0upx" > 
														   <view class='item' @click="uplogo" >
															  <block v-if="dwlogo" >
																   <image :src='staticsfile + dwlogo'></image>
															  </block>
															  <block v-else>
																   <image src='/static/images/uppicaddx.png'></image>
															  </block>	  
														   </view>
														   <view class="clear"></view>
												 </view>
											</view>		 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  			<view class="tn-flex tn-footerfixed">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>确认提交</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
				
				
				
		
			

	
	</view>
  
	<view style="height:120upx"></view>
    
	
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 ckid:0, 
			 staticsfile: this.$staticsfile,
			 html:'',
			 ckdata:'',
			 status:0,
			 areatxt:'请选择 所在 地区',
			 areaid:'0,0,0',
			 multiArray: [],
			 objectDiqudata: [],
			 multiIndex: [0, 0, 0],
			 lng:'',
			 lat:'',
			 img_url: [],
			 img_url_ok:[],
			 dwlogo:'',
			 formats: {},
			 timer: null,
			 editorDetail: "",
			 xtitle:'',
			 thygid:'',
			 thygname:'',
		  }
	},

	onLoad(options) {
		let that=this;
		let ckid=options.ckid ? options.ckid : 0;
		this.ckid=ckid;
		this.getdiqudata();
		if(ckid > 0){
			this.loadData();
			this.xtitle='编辑仓库';
		}else{
			this.xtitle='创建仓库';
		}
	},
	
	onShow() {
		let thygid=uni.getStorageSync('thygid');
		let thygname=uni.getStorageSync('thygname');
		console.log(thygid)
		if(thygid){
			this.thygid=thygid;
			this.thygname=thygname;
			uni.setStorageSync('thygid','');
			uni.setStorageSync('thygname','');
		}
	},

    methods: {
			 // 选择地址
			 
			 bzmap(){
				uni.navigateTo({
					url:'/pages/user/mydanwei/bzmap'
				}) 
			 },
			 selyg(){
				 uni.navigateTo({
				 	url:'/pages/selyg/index?stype=3'
				 })
			 },
        
		loadData(){
				  let that=this;
				  let ckid=that.ckid; 
				  let da={  
				 	ckid:ckid,
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/ckgl/ckedit', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 		        that.ckdata=res.data.ckdata;
								  if(res.data.ckdata){
									that.areaid = res.data.ckdata.areaid ? res.data.ckdata.areaid : '0,0,0';
									that.areatxt = res.data.ckdata.areatxt ? res.data.ckdata.areatxt : '请选择 所在 地区';
								  	that.lng = res.data.ckdata.lng;
								  	that.lat = res.data.ckdata.lat;
								  	that.dwlogo = res.data.ckdata.logo ? res.data.ckdata.logo : '' ;
								  	that.thygid = res.data.ckdata.kguid ? res.data.ckdata.kguid : '' ;
								  	that.thygname = res.data.ckdata.kgname ? res.data.ckdata.kgname : '' ;
								  }
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			
		getdiqudata(){
			let that=this;
			let da = { };
			this.$req.post('/v1/com/getdiqu', da).then(res => {
						uni.hideLoading();
					
						if(res.errcode==0){
							var array = [];   //array用来给multiArray赋值
							var first = res.data.diqudata.crdata;
							var second = first[0].sub;
							var third = second[0].sub ? second[0].sub : [] ;
							array.push(first);
							array.push(second);
							array.push(third);
							
								that.objectDiqudata=res.data.diqudata.crdata;
								that.multiArray= array;
						
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
						
					})
		},
		
		bindMultiPickerColumnChange(e) {
		  let that=this;
		  let tcode='';
			let areaid='';
			let areatxt='';
		  let objectDiqudata=that.objectDiqudata;
		
			var multiArray = this.multiArray;
			var multiIndex = this.multiIndex;
			multiIndex[e.detail.column] = e.detail.value;
			switch (e.detail.column) {
				case 0:
					multiIndex[1] = 0;
					multiIndex[2] = 0;
					multiArray[1] = objectDiqudata[multiIndex[0]].sub;
					multiArray[2] = objectDiqudata[multiIndex[0]].sub[0].sub ? objectDiqudata[multiIndex[0]].sub[0].sub : [] ;
		 
					break;
				case 1:
					multiIndex[2] = 0;
					multiArray[2] = objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub ? objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub : [];
			 
					break;
			}
			
			if(objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub){
				tcode=objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub[multiIndex[2]].code;
				areaid=objectDiqudata[multiIndex[0]].code+','+objectDiqudata[multiIndex[0]].sub[multiIndex[1]].code+','+objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub[multiIndex[2]].code;
				areatxt=objectDiqudata[multiIndex[0]].name+' '+objectDiqudata[multiIndex[0]].sub[multiIndex[1]].name+' '+objectDiqudata[multiIndex[0]].sub[multiIndex[1]].sub[multiIndex[2]].name;
			}else{
				tcode=objectDiqudata[multiIndex[0]].sub[multiIndex[1]].code;
				areaid=objectDiqudata[multiIndex[0]].code+','+objectDiqudata[multiIndex[0]].sub[multiIndex[1]].code;
				areatxt=objectDiqudata[multiIndex[0]].name+' '+objectDiqudata[multiIndex[0]].sub[multiIndex[1]].name;
			}
			
			
				that.multiArray=multiArray;
				that.multiIndex= multiIndex;
				that.code= tcode;
				that.areaid=areaid;
				that.areatxt=areatxt;
		 
			
		
		},
		
		
		uplogo(){
				   let that = this;
				   uni.chooseImage({
					   count: 1, //默认9
					   sizeType: ['original', 'compressed'],
					   success: (resx) => {
						  const tempFilePaths = resx.tempFilePaths;
						  
										  let da={
											  filePath:tempFilePaths[0],
											  name: 'file'
										  } 
										  uni.showLoading({title: '上传中...'})
										  this.$req.upload('/v1/upload/upfile', da)
												  .then(res => {
													uni.hideLoading();
													// res = JSON.parse(res);
													console.log(res);
													if(res.errcode==0){
														that.dwlogo=res.data.fname;
														uni.showToast({
															icon: 'none',
															title: res.msg
														});
													}else{
														uni.showModal({
															content: res.msg,
															showCancel: false
														})
													}
										  
										  })
								
						   
					   }
				   });
			   
			},
		
		deldwlogo(e){
			this.dwlogo='';
		},
		
		
			formSubmit: function(e) {
						let that=this;
							
									 let pvalue = e.detail.value;
									 let lng = that.lng ;
									 let lat = that.lat;
									 let areaid = that.areaid;
									 let areatxt = that.areatxt;
									 
									 
										if(!pvalue.title){
										  uni.showToast({
											icon: 'none',
											title: '请输入仓库名称',
										  });
										  return false;
										}
										
										if(areaid=='0,0,0') {
										  uni.showModal({
											  title: '错误提示',
											  content: '请选择所在地区！',
											  showCancel: false
										  });
										  return false;
										}
										
										if(!pvalue.dizhi){
											uni.showToast({
												icon: 'none',
												title: '请输入详细地址',
											});
											return false;
										}
									
										// if(!that.dwlogo){
										// 	uni.showToast({
										// 		icon: 'none',
										// 		title: '请上传门头图片',
										// 	});
										// 	return false;
										// }
										
							  uni.showLoading({title: '处理中...'})
							  let da={
								'ckid': that.ckid,
								'title': pvalue.title,
								'cktel': pvalue.cktel ? pvalue.cktel : '',
								'dizhi': pvalue.dizhi ? pvalue.dizhi : '',
								'logo': that.dwlogo ? that.dwlogo : '',
								'areaid': areaid,
								'areatxt': areatxt,
								'lng': lng ? lng : '',
								'lat': lat ? lat : '',
								'kguid': that.thygid ? that.thygid : 0,
								'kgname': that.thygname ? that.thygname : '',
							  }
							  this.$req.post('/v1/ckgl/ckeditsave', da)
									  .then(res => {
										uni.hideLoading();
										console.log(res);
										if(res.errcode==0){
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													uni.setStorageSync('thupckgllist',1);
													setTimeout(function(){
														uni.navigateBack()
													},1000)
												}
											});
										}else{
											uni.showModal({
												content: res.msg,
												showCancel: false
											})
										}
										
									  })
									  .catch(err => {
									
									  })
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.sqbdcon{margin:32upx;}
.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
}
.rowx .icon{position: absolute;right:2upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{}
.rowx .inp .input{height:90upx;line-height:90upx;}


.rowxmttp{margn-top:30upx}
.imgconmt{}
.imgconmt .item{float:left}
.imgconmt image{width: 160upx;height:160upx;border-radius:20upx;}

.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .dptit{text-align: center;}

 
</style>

