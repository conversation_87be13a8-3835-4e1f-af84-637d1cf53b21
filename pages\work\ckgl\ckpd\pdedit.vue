<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>
					{{thckdata.title}}
				</text>
			</view>
			<view slot="right">
			</view>

		</tn-nav-bar>
		<view class="tabs-fixed-blank"></view>


		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 100;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333" :bold="true" :fontSize="28" :gutter="15" :badgeOffset="[20, 50]"
						@change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;">

					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text>
					</view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>
		</view>



		<view class="tabs-fixed " style="z-index: 99;" :style="{top: vuex_custom_bar_height + 45 + 'px'}">
			<view class="flckselcon">
				<view class="sf1">
					<view class="wpflsel">
						<view class="da0"><text class="iconfont icon-fenlei"></text></view>
						<view class="da1">
							<view @click="selwpfl">{{sortid ? sortnamep : '按物品分类筛选'}}</view>

						</view>
						<view class="da2">
							<block v-if="sortid > 0">
								<text class="iconfont icon-cuohao02" style="margin-left:10upx"
									@click="clssortid"></text>
							</block>
							<block v-else>
								<text class="iconfont icon-jiantou2" style="margin-left:10upx"></text>
							</block>
						</view>
						<view class="clear"></view>
					</view>
				</view>
				<view class="sf2">
					<view class="wpcksel">
						<text class="iconfont icon-cangkupeizhi" style="margin-right:10upx"></text>
						仓库盘点
					</view>
				</view>
				<view class="clear"></view>
			</view>
		</view>


		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 45 + 'px'}"></view>

		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						<text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text>
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						<text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text>
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>

		<view class="tjtiao">

			<view class="fless">

				<view class="ite">
					<view class="sz">{{tjdata.tjnum1}}</view>
					<view class="tx">物品数量</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum2}}</view>
					<view class="tx">已盘点</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum3}}</view>
					<view class="tx">未盘点</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum4}}</view>
					<view class="tx">盘盈</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum5}}</view>
					<view class="tx">盘亏</view>
				</view>
			</view>

		</view>



		<block v-if="listdata.length>0">



			<view class="splist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="item ">

						<view class="inf1">
							<view class="spbh">编号：{{item.spid}}</view>

							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + item.smallpic"></image>
								</view>
								<view class="xx">
									<view class="n0"><text class="ddtype"
											:class="'ys'+item.ddtype">{{item.ddtypename}}</text> {{item.sortname}}
									</view>
									<view class="n1">{{item.title}}</view>
									<view class="n2">
										盘点状态：<text class="status" :class="'sta'+item.status"
											style="margin-right:30upx">{{item.statusname}}</text>
										盈亏：<text class="status" :class="'sta'+item.pdyk">{{item.pdykname}}</text>
									</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
						<block v-if="ckpdsta==2">
							<view class="inf2">
								<!-- <view class="bzbtn">备注</view> -->
								<view class="s1">库存数量：<text style="color:#ff0000">{{item.kucun}}</text></view>
								<view class="s1">单价：<text style="color:#ff0000">{{item.danjia}}</text></view>
								<view class="scpdtime">上次盘点时间：{{item.lastpdtime ? item.lastpdtime : '--'}}</view>

								<view class="pdczcon">
									<view class="tx1">盘点数量：</view>
									<view class="tx2">
										<input class="input" type="digit" v-model="item.pdkucun" placeholder="请输入盘点数量"
											placeholder-class="placeholder" />
									</view>
									<view class="tx3">
										<view class="setbtn" @click="pdqrcz" :data-index='index' :data-id='item.id'
											:data-spid='item.spid'>确认</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</block>
						<block v-else>
							<view class="inf2">
								<view class="s1">
									库存：<text style="color:#ff0000;margin-right:20upx">{{item.kucun}}</text>
									盘点数量：<text style="color:#ff0000">{{item.pdkucun}}</text>
								</view>
								<view class="scpdtime">时间：{{item.pdtime ? item.pdtime : '--'}}</view> 
								<view class="s1">单价：<text style="color:#ff0000">{{item.danjia}}</text></view> 
							</view>
						</block>



					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关物品</view>
			</view>
		</block>


		<block v-if="ckpdsta==2">
			<view class="tn-flex tn-footerfixed" style="z-index:100;">
				<view class="tn-flex-1 justify-content-item tn-text-center">
					<button class="bomsubbtn" style="width:50%;margin:0 auto;border-radius:100px;font-size:28upx"
						@click="ckwcpd">
						<text>完成盘点</text>
					</button>
				</view>
			</view>
		</block>


		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">物品搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入物品名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>


		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择盘点日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>
		<tn-wpfl v-model="wpflshow" @qrxz="wpflqrxz" @close="wpflclose"></tn-wpfl>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				statxt: '状态',
				ddtype: 0,
				ddtypetxt: '类型',
				index: 1,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						sta: 0,
						name: '全部'
					},
					{
						sta: 2,
						name: '未盘点'
					},
					{
						sta: 1,
						name: '已盘点'
					},
					{
						sta: 21,
						name: '盘盈'
					},
					{
						sta: 22,
						name: '盘亏'
					},
				],

				show3: false,
				checked: true,
				tbdhdownshow: false,
				dwtype: 0,

				tjdata: [],
				spsortdata: [],

				rqshow: false,
				rqkstime: 0,
				rqjstime: 0,

				wpflshow: false,
				sortid: 0,
				sortnamep: '',

				tjfs: 0,
				thckdata: '',
				ckid: 0,
				ckpdid: 0,
				ckpdsta: 2,
			}
		},
		onLoad(options) {
			let that = this;
			let ckpdid = options.ckpdid ? options.ckpdid : 0;
			this.ckpdid = ckpdid;
			this.loadData();

			that.page = 0;
			that.listdata = [];
			that.loaditems();
		},
		onShow() {

		},
		methods: {


			clssortid() {
				let that = this;
				this.sortid = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			loadData() {
				let that = this;
				let da = {
					tjtype: 5,
					tjfs: that.tjfs,
					ckpdid: that.ckpdid ? that.ckpdid : 0,
					sortid: that.sortid ? that.sortid : 0,
					ckid: that.ckid ? that.ckid : 0,
					ddtype: that.ddtype ? that.ddtype : 0,
					kw: that.thkw ? that.thkw : '',
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/getcktj', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							that.tjdata = res.data.tjdata;
							if (that.tjfs == 0) {
								that.spsortdata = res.data.spsortdata;
								that.thckdata = res.data.thckdata;
								that.ckpdsta = res.data.ckpdsta;
							}
							that.tjfs = 0;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					// this.thkw='';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					ckpdid: that.ckpdid ? that.ckpdid : 0,
					sortid: that.sortid ? that.sortid : 0,
					sta: that.sta ? that.sta : 0,
					kw: that.thkw ? that.thkw : '',
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/ckgl/ckpdedit', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},


			onchange(e) {},
			selwpfl() {
				this.wpflshow = true;
			},
			wpflqrxz(node) {
				console.log(node);
				let sortid = node.sortid;
				let that = this;
				that.sortid = sortid;
				that.sortnamep = node.sortnamep;
				this.loadData();
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				this.wpflshow = false;
			},
			wpflclose() {
				this.wpflshow = false;
			},



			pdqrcz(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let spid = e.currentTarget.dataset.spid;
				let id = e.currentTarget.dataset.id;

				let listdata = that.listdata;
				let thwpdata = listdata[index];
				let pdkucun = thwpdata.pdkucun;

				let da = {
					pdkucun: pdkucun,
					id: id,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/pdkucunsave', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							let thspdata = res.data.thspdata;
							console.log(thspdata);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.tjfs = 1;
										that.loadData();
										listdata[index].status = thspdata.status;
										listdata[index].statusname = thspdata.statusname;
										listdata[index].pdyk = thspdata.pdyk;
										listdata[index].pdykname = thspdata.pdykname;
										that.listdata = listdata;
									}, 1000);
								}
							});

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})


			},

			ckwcpd() {

				let that = this;
				let ckpdid = that.ckpdid;
				let wpdnum = that.tjdata.tjnum3;


				let tipstr = "提交后不可撤销！确认完成盘点吗？";
				if (wpdnum > 0) {
					tipstr = "还有 " + wpdnum + " 件物品未盘点！确认继续提交吗？";
				}

				uni.showModal({
					title: '提交确认',
					content: tipstr,
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'ckpdid': ckpdid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/ckgl/ckpdsubmit', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})



						}




					}
				})



			},



		}
	}
</script>





<style lang='scss'>
	.splist {
		padding: 10upx 32upx 32upx 32upx
	}

	.splist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx;
		position: relative;
	}

	.splist .item .spbh {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 20upx;
		color: #1677FF
	}

	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .status {}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 130upx;
		height: 130upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: left;
		margin-left: 20upx;
		width: calc(100% - 155upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 45upx;
		line-height: 45upx;
		overflow: hidden;
		font-size: 28upx
	}

	.splist .item .inf2 {
		background: #fff;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		font-size: 24upx;
		padding: 15upx 28upx;
		color: #333;
		position: relative;
		line-height: 40upx;
		border-top: 1px solid #efefef;
		padding: 28upx
	}

	.splist .item .inf2 .scpdtime {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 24upx;
	}

	.splist .item .inf2 .bzbtn {
		position: absolute;
		right: 28upx;
		bottom: 28upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fafafa;
		border-radius: 10rpx;
		text-align: center;
		color: #333;
		border: 1px solid #eee
	}


	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}

	.splist .item .status.sta1 {
		color: #43b058;
	}

	.splist .item .status.sta2 {
		color: #FF2828;
	}

	.pdczcon {
		margin-top: 15upx;
		height: 50upx;
		line-height: 50upx;
		position: relative;
	}

	.pdczcon .tx1 {
		float: left
	}

	.pdczcon .tx2 {
		float: left;
	}

	.pdczcon .tx3 {
		float: left;
		margin-left: 20upx
	}

	.pdczcon .tx2 .input {
		border: 1px solid #FFD427;
		text-align: center;
		height: 50upx;
		line-height: 50upx;
		width: 200upx;
		font-size: 24upx;
		border-radius: 10upx;
	}

	.pdczcon .tx3 .setbtn {
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 10rpx;
		text-align: center;
		color: #333
	}
</style>