<template>
	<view class="main-Location">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
							 <view slot="back" class='tn-custom-nav-bar__back'  >
								  <text class='icon tn-icon-left'></text>
							 </view>
							<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
							  <text  >{{xtitle}}</text>
							</view>
							<view slot="right" >
								<view class="searchicon"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
							</view>
			</tn-nav-bar>
		
	
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 5 + 'px'}" ></view>
		
		<view class="tjtiaox" >
			<block v-if="thkw">
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> </view>
					</view>
				</block> 
			</block> 
			<block v-else>
				<block v-if="stype==7"> 
					<view class="dpctipbtn" @click="seldpcz"><text class="iconfont icon-renminbi"></text> {{dpcztit}} <text style="color:#ff0000;margin-left:5upx">￥{{dpczzhyue}}</text></view>
				</block>
				
				<block v-if="stype==4 || stype==9">
					<view class="dpctipbtn" @click="seldpyw"><text class="iconfont icon-renyuan"></text> {{dpywtit}}</view>
				</block>
				
				<view class="khznumcon">员工数量：<text>{{ygznum}}</text>人</view>
					
				<view class="khseldp">
					<view class="wpcksel" v-if="dpname">
						<text class="iconfont icon-mendian" style="margin-right:10upx;"></text>
					 	 {{dpname}}				 
					</view>
				</view>
			</block>	
		</view>
	
	
		
		<!-- 字母区域 -->
		<view class="Location-Letter">
			<view v-for="(l,i) in LatterName" :key="i" hover-class="Click-Latter" @tap="getLetter(l)"  :style="{'color': LetterId === l ? '#FFD427' : '#000' }">{{l}}</view>
		</view>
 
	
		
		
		
		
		
		
		<scroll-view scroll-y="true" class="ynq-ScrollView" :scroll-into-view="LetterId">
			<view class="ynq-yglist">
				<block v-for="(item,index) in yglist" :key="index">
					<view class="zmtxt" :id="item.initial">{{item.initial}}</view>
					<view class="yglist">
						<block v-for="(item_yg,name_index) in item.list"	:key="name_index">
							<view class="item"    @tap="selthcity(item_yg)" >
								<view class="status"  >
									<view class="aa" :class="'ygsta'+item_yg.status">{{item_yg.statusname}} </view> 
								</view>
										
										<view class="inf1"  >
										  <view class="xzsbtn">选择</view>
											<view class="tf1">
												<view class="tx"><image :src="staticsfile + item_yg.avatar"></image></view>
												<view class="xx">
													<view class="name">{{item_yg.realname}} <text class="ygidaa">（{{item_yg.zzhuid}})</text></view>
													<view class="tel">店铺：{{item_yg.ormdname}}</view>
													<view class="tel" v-if="item_yg.jiaosetxt">角色：{{item_yg.jiaosetxt}} {{item_yg.jiaosetxt2}}</view>
													<view class="tel">电话：{{item_yg.lxtel}}</view>
													<block v-if="stype==7">
														<view class="tel">可用出资额：<text style="color:#4aac09">￥{{item_yg.czkyjine}}</text></view>
													</block>
												</view>
												<view class="clear"></view>
											</view>	
										</view>
										
																
									  
							</view>  
						</block>
					</view>
				</block>
			</view>
		</scroll-view>
		
		
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">员工搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入员工名称/电话/ID'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	</view>
</template>
 
<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				stype: 0,
				isdx: 0,
				CityName: '',
				LatterName: '',
				yglist:'', 
				LetterId: '',
				xtitle:'',
				show3:false,
				thkw:'',
				thkwtt:'',
				ygznum:0,
				dpselshow:false,
				dpdata:'',
				dpid:0,
				dpname:'',
				dpcztit:'',
				dpywtit:'',
				thkhnofzr:0,//默认客户有负责人
				khyyfrztip:'',
				dpczzhyue:0,
				
			}
		},
		onLoad(options) {
			
			let stype=options.stype ? options.stype : 0;
			let dpid=options.dpid ? options.dpid : 0;
			let thkhnofzr=options.thkhnofzr ? options.thkhnofzr : 0;
			let dpname=options.dpname ? options.dpname : '';
	
			let isdx=options.isdx ? options.isdx : 0;
			let xtitle='选择员工';
			if(stype==2){xtitle='请选择店长';}
			if(stype==3){xtitle='请选择仓库管理员';}
			if(stype==4){xtitle='请选择业务员';}
			if(stype==5){xtitle='请选择验货员';}
			if(stype==6){xtitle='请选择所属员工';}
			if(stype==7){xtitle=options.xtitle}
			if(stype==8){xtitle='请选择调用人员';}
			if(stype==9){xtitle='请选择销售人员';}
			if(stype==11){xtitle='请选择查看人员';}
			
			
			
			this.xtitle=xtitle;
			this.thkhnofzr=thkhnofzr;
			this.stype=stype;
			this.dpid=dpid;
			this.dpname=dpname;
			this.isdx=isdx;
		
			this.loadData();
		},
		methods: {
			
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				this.show3=false;
				that.loadData();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
			    that.loadData();
			},
			
			loadData() {
				let that=this;
				let da={
					kw:that.thkw ? that.thkw : '',
					stype:that.stype,
					dpid:that.dpid,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/selyg/yglist', da)
				      .then(res => {
					    uni.hideNavigationBarLoading();
						console.log(res); 
						if(res.errcode==0){
						   that.yglist = res.data.yglist;
						   that.ygznum = res.data.ygznum;
						   that.LatterName = res.data.lattername;
						   that.dpcztit = res.data.dpcztit;
						   that.dpczzhyue = res.data.dpczzhyue;
						   that.dpywtit = res.data.dpywtit;
						   that.khyyfrztip = res.data.khyyfrztip;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
				      })
			},

			//获取定位点
			getLetter(name) {
				this.LetterId = name
				// uni.pageScrollTo({
				// 	selector:'#'+name,
				// 	duration:300
				// })
			},
			selthcity(item) { 
				console.log(item); 
				uni.setStorageSync('thygid',item.id)
				uni.setStorageSync('thygname',item.realname)
				uni.setStorageSync('thygczkyjine',item.czkyjine)
				uni.setStorageSync('thygidisup',1)
				setTimeout(function(){
					uni.navigateBack();
				},100)
			},
		    seldpcz(){
				uni.setStorageSync('isdpczzh',1)
				setTimeout(function(){
					uni.navigateBack();
				},100)
			},
			seldpyw(){
				// 如果已有负责人
				if(this.thkhnofzr==0){
					uni.showModal({
						content: this.khyyfrztip,
						showCancel: false
					})
				}else{
					uni.setStorageSync('isdpywzh',1)
					setTimeout(function(){
						uni.navigateBack();
					},100)
				}
			
			},
			
			
		},
	}
</script>

<style lang="scss" scoped>

    .dpctipbtn{font-size:32upx;}

	.main-Location {
		height: 100vh;
	}
 
	.Location-Letter {
		position: fixed;
		right: 0rpx;
		top: 280rpx;
		width: 85rpx;
		z-index: 100;
		view {
			display: block;
			width: 85rpx;
			text-align: center;
			height: 35rpx;
			line-height: 35rpx;
			font-size: 28rpx;
			font-weight: 600;
			transition: ease .3s;
			-webkit-transition: ease .3s;
			margin-bottom:30upx;
		}
	}
 
	.ynq-yglist {
		padding: 0upx 85rpx 20upx 32rpx;
	}
	.ynq-ScrollView {
		height: calc(100vh - 265rpx);
	}
 
	.Click-Latter {
		font-size: 30rpx !important;
	}
	
	
	.zmtxt{margin-top:0upx;margin-bottom:20upx;font-weight: 600;font-size:28upx;}
	.ygc{margin-bottom:20upx}
	
	.searchicon{margin-right:30upx}
	.searchicon .iconfont{font-size:41upx;}
	
	
	
	
	.yglist{}
	.yglist .item{margin-bottom:22upx;padding:28upx;border-radius:20upx;background:#fff;position: relative;} 
	.yglist .item .xzsbtn{position: absolute;right:32upx;top:92upx;width:90upx;height:50upx;line-height:50upx;background:#FFD427;text-align: center;border-radius:5upx;font-size:20upx; }
	.yglist .item .inf1{}
	.yglist .item .inf1 .tx{float:left}
	.yglist .item .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
	.yglist .item .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;}
	.yglist .item .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
	.yglist .item .inf1 .tel{color: #7F7F7F;font-size: 24rpx;margin-top:5upx}
	.yglist .item .inf1 .tel text{margin-right:30upx} 
	.yglist .item .inf1 .ygidaa{color:#1677FF;font-size:20upx;margin-left:10upx;font-weight:400}
	.yglist .item .status{position: absolute;right:32upx;top:32upx;font-size:24upx}
	.yglist .item .status .aa{float:left;margin-left:10upx;line-height:50upx;}
	.yglist .item .status .ygsta1{color:#4aac09}
	.yglist .item .status .ygsta2{color:#ff0000}
	
	
	.yglist .item .inf2{border-top:1px solid #F0F0F0;border-bottom:1px solid #F0F0F0;margin:20upx 0 0 0;padding:20upx 0;}
	.yglist .item .inf2 .vtm{float:left;width:33.33%;font-size:28upx}
	.yglist .item .inf2 .vtm .tt{color: #7F7F7F;font-size:24upx}
	.yglist .item .inf2 .vtm .vv{color: #333333;margin-top:15upx}
	
	
	.yglist .item .inf3{padding:20upx 0}
	.yglist .item .inf3 .f1{float:left;height: 40upx;line-height:40upx;font-size:24upx}
	.yglist .item .inf3 .f1 .x1{}
	.yglist .item .inf3 .f1 .x2{margin-top:7upx}
	.yglist .item .inf3 .f2{float:right;}
	.yglist .item .inf3 .setbtn{width: 140rpx;height: 60upx;line-height:60upx;border-radius: 8rpx;border: 2rpx solid #B2B2B2;font-size: 24rpx;text-align: center;}
	
	.yglist .item.lzstyle{background: #F0F0F0;}
	.yglist .item.lzstyle .inf3 .f1 .x1{color: #7F7F7F}
	.yglist .item.lzstyle .inf3 .f1 .x2{}
	.yglist .item.lzstyle .inf2{border-top:1px solid #fff;}
	
	.msgtypecon{padding:20upx 0 40upx 0}
	.msgtypecon .item{float:left;width:33.33%;text-align: center;}
	.msgtypecon .item .icon{width:76upx;margin:0 auto;position: relative;}
	.msgtypecon .item .icon image{width:76upx;height:80upx;}
	.msgtypecon .item .txt{font-size:24upx;margin-top:10upx;}
	.msgtypecon .item .icon .msgnum{position:absolute;right:-8upx;top:0;min-width: 22upx;height:28upx;line-height:26upx;font-size:16upx;background: #FF2828;border-radius: 100upx;border:1px solid #fff;color:#fff;padding:0 9upx;}
	
	.khznumcon{font-size: 24rpx; color: #7F7F7F;float:left;padding-bottom:20upx}
	.khznumcon text{font-weight:700;color:#ff0000;margin:0 10upx}
	.khseldp{float:right;}
	.khseldp .icon-jiantou2{font-size:22upx;}  
	.dpctipbtn{position: absolute;right:80upx;font-size:24upx;height:50upx;line-height:50upx;background: #FFD427;padding:0 15upx;border-radius:100upx;z-index: 2000;} 
	.dpctipbtn .iconfont{margin-right:10upx;font-size:24upx}  
</style>