<template>
	<view>
		
		
		<view class="topfl">
			<view class="con">
				<scroll-view scroll-x="true" style=" width: 100%;height:100%;white-space: nowrap;">
					<view class="item" :class="sortid==0 ? 'active' : ''" @tap="swtab" :data-pid="pid"
						data-sortid="0"
						:style="sortid == 0 ? 'background: '+mbjson.ztcolor : ''"
						><span>全部</span></view>
					<block v-for="(item,index) in sortdata" :key="index">
						<view class="item" :class="sortid==item.id ? 'active' : ''" @tap="swtab" 
							:data-sortid="item.id"  :style="sortid == item.id ? 'background: '+mbjson.ztcolor : ''"><span>{{item.sortname}}</span></view>
					</block>
					<view class="clear"></view>
				</scroll-view>
			</view>
		</view>
		<view class="scbox">
			
			<block v-if="listdata.length>0">
			
					<scroll-view scroll-y="true" @scrolltolower="loaditems" class="sv" :style="{height:SrollHeight+'px'}"  :scroll-top="ScrollTop"  >
							<block v-for="(item,index) in listdata" :key="index">
										<view class="notice-item" @tap="gotoview" :data-id="item.id">
											<view class="content">
												<text class="title">{{item.title}}</text>
												<view class="time">
													<text>{{item.addtime}}</text>
												</view>
												<text class="introduce">
													{{item.summary}}
												</text>
											
											</view>
										</view>
							</block>
							<view class="clear"></view>
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
					</scroll-view>
					
			</block>		
			
		</view>
		 
		 
	</view>
</template>

<script>
	

	export default {
	
		data() {
			return {
				staticsfile: this.$staticsfile,
				sortdata: '',
				sortid:0,
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				lbtustazx:1,
				swiperCurrent: 0,
				swiperLength: 0,
				lunbotu: [],
				lbtuHeight:100,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
			
			}
		},
		onLoad() {
			
		},
		onReady() {
			let that=this;
			uni.getSystemInfo({ 
				success(res) {
					that.SrollHeight=res.windowHeight-59;
					that.SrollHeightaa=res.windowHeight-250;
				}			
			})
		},
		onShow() {
			let that=this;
			that.loadData();
			
			that.page = 0;
			that.listdata = [];
			that.isloading = false;
			that.loadingType = -1;
			that.loaditems();

			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				uni.setNavigationBarColor({
				  frontColor: mbjson.tbtxtcolor,
				  backgroundColor: mbjson.tbbgcolor
				});
				uni.setTabBarStyle({
				  selectedColor: mbjson.tabbarhovcolor,
				})
				let tabbarhovcs=mbjson.tabbarhovcs;
				uni.setTabBarItem({
				  index: 0,
				  selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-home-current.png',
				})
				uni.setTabBarItem({
				  index: 1,
				  selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-cate-current.png',
				})
				uni.setTabBarItem({
				  index: 2,
				  selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-cart-current.png',
				})
				uni.setTabBarItem({
				  index: 3,
				  selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-tz-current.png',
				})
				uni.setTabBarItem({
				  index: 4,
				  selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-my-current.png',
				})
			}
		},
		methods: {
	
			loadData() {
				let that=this;
				let da={};
				uni.showNavigationBarLoading();
				this.$req.post('/v2/dinghuo/notice/index', da)
				      .then(res => {
					    uni.hideNavigationBarLoading();
						console.log(res);
						if(res.errcode==0){
							that.sortdata = res.data.sortdata;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
				      })
			},
			swtab(e) {
				let that = this;
				let sortid = e.currentTarget.dataset.sortid;
				that.kw = '';
				that.sortid = sortid ? sortid : 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
	
          // 上拉加载 
          loaditems: function() {
          	let that=this;
          	let page = ++that.page;
          	let da={
          		page:page,
          		sortid:that.sortid,
          	}
          	if(page!=1){
          		that.isloading=true;
          	}
          	if(that.loadingType==2){
          		return false;
          	}
          	uni.showNavigationBarLoading();
          	this.$req.get('/v2/dinghuo/notice/list', da)
          	        .then(res => {
          			
          				if (res.data.listdata.length > 0) {
          					that.listdata = that.listdata.concat(res.data.listdata); 
          					that.loadingType=0
          				}else{
          					that.loadingType=2
          				}
          			 uni.hideNavigationBarLoading();
          	})
          },
		   gotoview:function(e){
		   	let that=this;
		   	let id = e.currentTarget.dataset.id;  
		   	uni.navigateTo({
		   		url:'./view?id='+id
		   	})
		   }
		   
		   
		}
	}
</script>

<style lang='scss'>
	page {
		background-color: #fff;
		padding-bottom: 30upx;
	}
	/* .slide-image{width: 100%;} */
	.carousel image {
	    width: 100%;
	    height: 100%;
	    border-radius: 0rpx;
	}
    .lbtucon{padding:30upx;background:#fff;}


	

	.notice-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		border-bottom:1px solid #eee;
	}

	.content {
		padding: 0 24upx;
		background-color: #fff;
		border-radius: 4upx;
	}

	.title {
		display: flex;
		align-items: center;
		height: 90upx;
		font-size: 32upx;
		color: #303133;
	}
	.time {
		color: #888;
	}
	.introduce {
		display: inline-block;
		padding: 16upx 0;
		font-size: 22upx;
		color: #888;
		line-height: 38upx;
	}

</style>
