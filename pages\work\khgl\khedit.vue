<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>添加客户</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon">

						<view class="rowx">
							<view class="tit"><text class="redst">*</text>客户名称</view>
							<view class="inpcon">
								<view class="inp"><input class="input" type="text" name="realname" placeholder="请输入客户名"
										placeholder-class="placeholder" /></view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst">*</text>客户电话</view>
							<view class="inpcon">
								<view class="inp"><input class="input" type="text" name="lxtel" maxlength="11"
										placeholder="请输客户手机号" placeholder-class="placeholder" /></view>
							</view>
							<view class="clear"></view>
						</view>



					</view>


					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit"><text class="redst"></text>身份信息 <text
									style="font-size:24upx;color:#888;margin-left:10upx">(选填)</text></view>
							<!-- <view class="zjzkicon" @click="setvzz" style="display: none;">展开 <text class="iconfont icon-jiantou2"></text></view> -->




							<view class="uprzimgcon">
								<view class="zzx sf1">
									<view class="con">
										<view class="vt1" @click="uprzimg1">
											<block v-if="rzimg1">
												<image :src='staticsfile + rzimg1'></image>
											</block>
											<block v-else>
												<image src="/static/images/rzupimg1.png"></image>
											</block>
										</view>
										<view class="vt2">上传身份证头像面</view>
										<view class="clear"></view>
									</view>
								</view>
								<view class="zzx sf2">
									<view class="con">
										<view class="sf2" @click="uprzimg2">
											<block v-if="rzimg2">
												<image :src='staticsfile + rzimg2'></image>
											</block>
											<block v-else>
												<image src="/static/images/rzupimg2.png"></image>
											</block>
										</view>
										<view class="vt2">上传身份证国徽面</view>
									</view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx" style="margin-top:20upx;">
								<view class="tit"><text class="redst"></text>身份证号</view>
								<view class="inpcon">
									<view class="inp"><input class="input" type="text" name="sfzhao" maxlength="18"
											placeholder="请输入身份证号" placeholder-class="placeholder"
											style="border-bottom:1px solid #eee" /></view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>现住址</view>
								<view class="inpcon">
									<view class="inp"><input class="input" type="text" name="dizhi"
											placeholder="请输入客户现住址" placeholder-class="placeholder" /></view>
								</view>
								<view class="clear"></view>
							</view>


						</view>
					</view>

					<view class="maxcon">
						<view class="vstit">备注信息</view>
						<view class="bzcon">
							<textarea name="remark" class="beizhu"></textarea>
						</view>
					</view>

				</view>


				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" form-type="submit">
							<text>确认提交</text>
						</button>
					</view>
				</view>
			</form>








		</view>

		<view style="height:70px"></view>





	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				rzimg1: '',
				rzimg2: '',
			}
		},

		onLoad(options) {
			let that = this;
		},

		methods: {

			formSubmit: function(e) {
				let that = this;
				var formdata = e.detail.value
				let pvalue = e.detail.value;
				if (!pvalue.realname) {
					uni.showToast({
						icon: 'none',
						title: '请输入客户姓名',
					});
					return false;
				}

				if (!pvalue.lxtel) {
					uni.showToast({
						icon: 'none',
						title: '请输入客户手机号',
					});
					return false;
				}

				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'realname': pvalue.realname ? pvalue.realname : '',
					'lxtel': pvalue.lxtel ? pvalue.lxtel : '',
					'sfzhao': pvalue.sfzhao ? pvalue.sfzhao : '',
					'dizhi': pvalue.dizhi ? pvalue.dizhi : '',
					'remark': pvalue.remark ? pvalue.remark : '',
					'rzimg1': that.rzimg1 ? that.rzimg1 : '',
					'rzimg2': that.rzimg2 ? that.rzimg2 : '',
				}
				this.$req.post('/v1/khgl/kheditsave', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							// uni.showToast({
							// 	icon: 'none',
							// 	title: res.msg,
							// 	success() {
							// 		uni.setStorageSync('thupkhlist', 1);
							// 		setTimeout(function() {
							// 			uni.navigateBack()
							// 		}, 1000)
							// 	}
							// });
							uni.setStorageSync('thupkhlist', 1);
							uni.showModal({
								title: '',
								content: res.msg,
								showCancel:false,
								success: function(e) {
									//点击确定
									if (e.confirm) {
									  uni.navigateBack()
									}
								}
							})
							
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},

			uprzimg1() {
				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg1 = res.data.fname;
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},

			uprzimg2() {
				let that = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg2 = res.data.fname;
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},






		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
	}

	.rowx .icon {
		position: absolute;
		right: 32upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 20upx
	}

	.maxcon .dptit {
		text-align: center;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}


	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}
</style>