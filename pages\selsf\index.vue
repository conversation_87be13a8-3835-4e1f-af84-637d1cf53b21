<template>
  <view class="page-e tn-safe-area-inset-bottom">
	

	<block v-if="mydpdata" >
	    <tn-nav-bar fixed   :bottomShadow="false" :zIndex="100" :isBack="istopback">
			<view slot="back" class='tn-custom-nav-bar__back' >
				  <text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
			  <text  >选择身份</text>
			</view>
		</tn-nav-bar>
	
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
	
	    <view class="xzsm">选择身份后进入</view>
		 
		<view class="sjlist">
				<block v-for="(item,index) in mydpdata" :key="index">
					<view class="sjitem" @click="selthsjitem" :data-sjid="item.sjid" >
						<view class="logo"><image :src="staticsfile + item.logo"></image></view>
						<view class="tit">{{item.sjname}}</view>
						<view class="icon">
							<text class="iconfont " :class="item.sjid==sjid ? 'icon-jtup2' : 'icon-jtdown2' " ></text>
						</view>
					</view>
					<view class="sflist" :class="item.sjid==sjid ? 'hoverss' : '' " >
							<block v-for="(itemx,indexx) in item.glsfdata" :key="indexx">
								 <view class="sfitem">
									 <view class="tit" >{{itemx.realname}}
										 <text class="iconfont icon-chaojiguanliyuan cjglicon" v-if="itemx.isadmin==1 "></text>
									 </view>
									 <view class="zss" style="display: none;">员工编号：{{itemx.id}}</view>
									 <view class="zss" v-if="itemx.mdname">店铺名称：{{itemx.mdname}}</view>
									 <view class="zss"  v-if="itemx.jiaosetxt">店铺角色：<text class="jstxt">{{itemx.jiaosetxt}}</text></view>
									 <view class="zss cjboss"  v-if="itemx.jiaosetxt2">超级角色：<text class="jstxt">{{itemx.jiaosetxt2}}</text></view>
									
									 <view class="jrbtn" @click="jrdp"  :data-ygid="itemx.id" :data-mdid="itemx.mdid">进入</view>
								 </view>
							</block>
						<!-- 	<block v-for="(items,indexs) in item.otmddata" :key="'a'+indexs">
								 <view class="sfitem">
									 <view class="tit" style="color:#1677FF">{{items.title}}</view>
									 <view class="zss">所在地区：{{items.areatxt}}</view>
									 <view class="jrbtn" @click="jrdp"  :data-mdid="items.id"   :data-ygid="0">进入</view>
								 </view>
							</block>	 -->
					</view>
					
					
				</block>
		</view>
		
		
		<view style="height:180upx"></view>
		<view class="bomfixedcz whitex" style="height:120upx">
			  <view class="dpgnss">
					<view class="item" @click="toLogout()">
						退出登录
					</view>
					<view class="item" style="color:#1677FF"  @click="gotozcqy" >
						企业注册
					</view>
					<view class="item"   @click="saomac" >
						扫码加入
					</view>
			  </view>	  
		</view>
		
	</block>
	<block v-else>
		
		
		<view class="login">
					<view class="login__bg login__bg--top"><image class="bg" src="/static/images/loginbg.jpg" mode="widthFix"></image></view>
			        <view class="topk"></view>
					<view class="titwz">欢迎来到漏鱼寄卖行管家</view>
					<view class="main">
					
							<view class="zxbtn" @click="saomac()">
								<text class="iconfont icon-saoyisaosaoma"></text> 加入企业
							</view>
							<view class="zxsm">*请扫一扫店铺二维码加入已有企业</view>
							
							<view class="zxbtn" style="margin-top:60upx" @click="gotozcqy">
								<text class="iconfont icon-bianji1"></text> 注册企业
							</view>
							<view class="zxsm">*未注册的企业主注册新的企业</view>
						
					</view>
		</view>
		
		<view class="bomfixedcz " style="height:120upx">
			  <view class="dpgnss">
					<view class="item" @click="toLogout()" style="width:100%;">
						退出登录
					</view>
			  </view>	  
		</view>
		
	</block>	





    <view class='tn-tabbar-height'></view>
	
	<yomol-upgrade  :type="upgradeType" :url="upgradeUrl" title="发现新版本" :content="upgradeContent" ref="yomolUpgrade"></yomol-upgrade>
	
	
  </view>
</template>

<script>
	import yomolPrompt from '@/components/yomol-prompt/yomol-prompt.vue'
	import yomolUpgrade from '@/components/yomol-upgrade/yomol-upgrade.vue'
  export default {
	  
	  components: {
	  		yomolPrompt,
	  		yomolUpgrade,
	  },
	  
    data() {
      return {
		  staticsfile: this.$staticsfile,
		  istopback:false,
		  mydpdata:'',
		  sjid:0,
		  
		  version:'',
		  upgradeType: 'pkg',  
		  upgradeContent: '', 
		  upgradeUrl: '', 
		  
		  sqxctip: '', 
		  
      }
    },
	
	onLoad(options){
		let that=this;
		let isback=options.isback ? options.isback : 0;
		if(isback==1){
			this.istopback=true;
		}
		

		//#ifdef APP-PLUS
			that.checkupone()
		//#endif
		
		
	},
	onShow(){
		let that=this;
		that.loadData();
	},
	
	mounted() {
	    
	 },
	
	computed: {
	
	},
	
    methods: {
		goBack(){
			uni.navigateBack()
		},
		selthsjitem(e){
			let sjid = e.currentTarget.dataset.sjid;
			if(sjid==this.sjid){
				this.sjid=0;
			}else{
				this.sjid=sjid;   
			}
		},
		
		jrdp(e){
			let that=this;
			let ygid = e.currentTarget.dataset.ygid;
			let mdid = e.currentTarget.dataset.mdid;
			let da={
				ygid:ygid,
				mdid:mdid,
			};
			uni.showNavigationBarLoading();
			this.$req.post('/v1/selsf/jrdp', da)
			      .then(res => {
			         console.log(222,res);
				    uni.hideNavigationBarLoading();
					if(res.errcode==0){
							that.$tool.setTokenStorage(res.data.tokendata);
							setTimeout(function(){
								uni.switchTab({
								   url: '/pages/index/index',
								})
							},500)
					}else{
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
					}
			      })
		},
		
		loadData() {
			let that=this;
			let da={};
			uni.showNavigationBarLoading();
			this.$req.post('/v1/selsf/index', da)
			      .then(res => {
			         console.log(222,res);
				    uni.hideNavigationBarLoading();
					if(res.errcode==0){
						
							that.mydpdata = res.data.mydpdata;
							that.sqxctip = res.data.sqxctip;
							
					}else{
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
					}
			      })
		},
		
		
		//退出登录
		toLogout(){
				uni.showModal({
					content: '确定要退出登录吗？',
					success: (e)=>{
						if(e.confirm){
							// uni.clearStorage();
							this.$tool.setTokenStorage('');
							this.$tool.setMudataStorage('');
							setTimeout(()=>{
								// uni.navigateBack();
								uni.redirectTo({
								   url: '/pages/public/login',
								})
							}, 200)
						}
					}
				});
		},	
		
      // 跳转
      tn(e) {
        uni.navigateTo({
          url: e,
        });
      },
	  
	 checkupone(){
	 
	 	uni.getStorage({
	 	      key: "checkup-time",
	 	      success: (res) => {
	 				   if ( !res.data || new Date().getTime() - res.data > 1000 * 60 * 60 * 6 ) {
	 						this.checkversion();
	 				   } 
	 		   },
	 		   fail: (err) => {
	 			  this.checkversion();
	 		   },
	 	  });
	 			
	 },
	 
	 
	 checkversion() {
	 	let that=this;
	 				// 检测升级
	 				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
	 				 var platform = uni.getSystemInfoSync().platform
	 							
	 					
	 								let da={
	 									platform: platform,
	 									version: widgetInfo.version,
	 									name: widgetInfo.name
	 								}
	 								// console.log(da);
	 								that.$req.post('/v1/checkver/index', da)
	 										.then(res => {
	 											console.log(res);
	 											
	 											
	 											if(res.errcode==0){
	 												
	 												 
	 												
	 												let isnew=res.data.isnew;
	                                                 if(isnew==1){
	 													uni.setStorage({
	 													      key: "checkup-time",
	 													      data: new Date().getTime(),
	 													});
	 													if (res.data.appupdata.pkgurl != '' && res.data.appupdata.wgturl == '') {
	 														that.upgradeType = 'pkg'
	 														that.upgradeContent = res.data.appupdata.content
	 														that.upgradeUrl = res.data.appupdata.pkgurl
	 														that.$refs.yomolUpgrade.show()
	 													} else {
	 														that.upgradeType = 'wgt'
	 														that.upgradeContent = res.data.appupdata.content
	 														that.upgradeUrl = res.data.appupdata.wgturl
	 														that.$refs.yomolUpgrade.show()
	 													}
	 												} 														 
	 											}else{
	 												uni.showToast({
	 													icon: 'none',
	 													title: res.msg
	 												});
	 											}
	 								
	 								})
	 				
	 				
	 				});
	 			},
	 
	 
	      gotozcqy(){
			  uni.navigateTo({
			  	url:'/pages/selsf/sjreg'
			  })
		  },
		  
		  
		  saomac(){
		  	      let that=this;
		  		  uni.scanCode({
		  		  	success: function (res) {
						console.log(res);
		  				let sbma=res.result;
						uni.navigateTo({
							url:'./addyginfo?ygyqma='+sbma
						})
		  		  	}
		  		  });
		  },
		  
		 saomac1() {
		 	
		 	
		 uni.scanCode({
		 	success: function(resv) {
		 		let sbma = resv.result;
		 		let da = {
		 			ygyqma: sbma ? sbma : ''
		 		};
		 		uni.showNavigationBarLoading();
		 		that.$req.post('/v1/yggl/addygcheck', da)
		 			.then(res => {
		 				console.log(222, res);
		 				uni.hideNavigationBarLoading();
		 				if (res.errcode == 0) {
		 					//如果注册过，则跳转到选择提示界面
		 					if (res.data.yyzh == 1) {
		 						let xsjid = res.data.xsjid;
		 						uni.navigateTo({
		 							url: '/pages/selsf/seldlzh?xsjid=' + xsjid + '&sbma=' +
		 								sbma
		 						})
		 					} else {
		 						uni.navigateTo({
		 							url: '/pages/selsf/addyginfo?ygyqma=' + sbma
		 						})
		 					}
		 				} else {
		 					uni.showToast({
		 						icon: 'none',
		 						title: res.msg
		 					});
		 				}
		 			})
		 	}
		 });
		 	
		 },
		  
	 
	
	
	
	
    
    }
  }
</script>

<style lang="scss" scoped>
  .page-e{
    max-height: 100vh;
  }
.dpgnss{}
.dpgnss .item{float:left;width:33.33%;text-align: center;line-height:120upx;}
.toplinex{border-top:1px solid #fff}
.xzsm{margin:32upx;font-size: 28upx;font-weight: 500;}

.sjlist{margin:32upx;}
.sjitem{height: 124upx;line-height: 124upx;background: #FFFFFF;border-radius: 8upx;padding:0 32upx;position: relative;margin-top:20upx}
.sjitem .logo{float:left;margin-top:20upx}
.sjitem .logo image{width:60upx;height:60upx;border-radius:100px;}
.sjitem .tit{font-size: 28upx;font-weight: 600;float:left;width: calc(100% - 100upx);margin-left:12px}
.sjitem .icon{position: absolute;top:0;right:22upx;}
.sjitem .icon text{font-size:35px}

.sflist{background:#fff;padding:0 32upx;display: none;}
.sflist.hoverss{display: block;}
.sfitem{border-top:1px solid #F0F0F0;padding:32upx 0;position: relative;}
.sfitem .tit{font-size:28upx;color:#333}
.sfitem .zss{font-size:24upx;color:#7F7F7F;margin-top:6upx}
.sfitem .zss.cjboss .jstxt{color:#ff0000}
.sfitem .jrbtn{width: 120rpx;height: 66rpx;line-height: 66rpx;background: #FFD427;border-radius: 8rpx;text-align: center;font-size: 28rpx;position: absolute;right:0;top:40upx}
.sfitem .js{font-size:24upx;}
.sfitem .cjglicon{color:#FF6600}



.login {
		position: relative;
		height: 100%;
		z-index: 1;
		
		/* 背景图片 start */
		&__bg {
		  z-index: -1;position: fixed;
		  &--top {
		    top: 0;left: 0;right: 0;width: 100%;
		    .bg {
		      width: 750upx;
		      will-change: transform;
		    }
		  }
		}
		/* 背景图片 end */
		.topk{height:280upx}
	    .titwz{
			height: 60upx;font-size: 44upx;font-weight: 600;color: #333333;line-height: 60upx;text-align: center;
		}
		.main{margin:56upx;margin-top:270upx}
		.zxbtn{height: 112upx;line-height: 112upx;background: #FFD427;border-radius: 24upx;text-align: center;font-size: 32upx;margin-bottom:20upx}
        .zxbtn text{margin-right:10upx;}
		.zxsm{text-align: center;}
	
	
	}
	


  
</style>
