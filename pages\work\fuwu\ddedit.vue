<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{djdata.ddtypetxt}}详情</text>
			</view>
			<view slot="right">
				<view style="margin-right:40upx;" @click="fxdd"><text class="iconfont icon-fenxiang1"
						style="font-size:35upx"></text></view>
			</view>
		</tn-nav-bar>

		<view class="toplinex" style="background:#fff;" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="ddtopcon">
				<view class="ddbh">
					{{djdata.ordernum}}
				</view>
				<view class="ddstacon" :class="'fwyssta'+djdata.ddsta ">{{statusname}}</view>
				<view class="ddtticon">
					<image src="/static/images/ddtoicon1.png" style="width:105px;height:90px;"></image>
				</view>
			</view>


			<view class="sqbdcon">





				<view class="maxcon" style="padding-bottom:10upx">
					<view class="vstit">{{djdata.ddtypetxt}}信息</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>业务类型</view>
						<view class="inpcon">
							<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" style="display: none;">
						<view class="tit"><text class="redst"></text>单据编号</view>
						<view class="inpcon">
							<view class="inp">{{djdata.ordernum}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>客户姓名</view>
						<view class="inpcon">
							<view class="inp">{{djdata.khname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>客户电话</view>
						<view class="inpcon">
							<view class="inp">{{djdata.khtel}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<block v-if="djdata.ddtype==1 && djdata.ddsta!=13">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>寄卖周期</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>寄卖时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<block v-if="djdata.isedit==1">
								<view class="xqbtnbbs" @click="gotoxq"><text class="iconfont icon-shuhui1"></text> 续期
								</view>
							</block>
							<view class="tit"><text class="redst"></text>到期时间</view>
							<view class="inpcon">
								<view class="inp">
									{{djdata.ywdqtime}}
									<block v-if='xqrcddata.length > 0'>
										<text style="color:#1677FF;z-index:200;margin-left:20upx"
											@click="xqrcdv">续期记录</text>
									</block>
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</block>
					<block v-if="djdata.ddtype==2 && djdata.ywtime>0">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>回收时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
					</block>

					<block v-if="djdata.ddtype==3 && djdata.ddsta!=9">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>暂存周期</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>暂存时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<block v-if="djdata.isedit==1">
								<view class="xqbtnbbs" @click="gotoxq"><text class="iconfont icon-shuhui1"></text> 续期
								</view>
							</block>
							<view class="tit"><text class="redst"></text>到期时间</view>
							<view class="inpcon">
								<view class="inp">
									{{djdata.ywdqtime}}
									<block v-if='xqrcddata.length > 0'>
										<text style="color:#1677FF;z-index:200;margin-left:20upx"
											@click="xqrcdv">续期记录</text>
									</block>
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</block>


					<block v-if="djdata.ddtype==4">

						<view class="rowx">
							<view class="icon" v-if="vcp==0">
								<view class="czjlbtn" @click="czjlmx">出资分润</view>
							</view>

							<view class="tit"><text class="redst"></text>预付金额</view>
							<view class="inpcon">
								<view class="inp">￥{{djdata.yfjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>预付时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywtime}}</view>
							</view>
							<view class="clear"></view>
						</view>


						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="icon" style="color:#1677FF;z-index:200;">{{djdata.ywzqdwtxt}}</view>
							<view class="tit"><text class="redst"></text>还款周期</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywzqnum}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>利率</view>
							<view class="inpcon">
								<view class="inp">{{djdata.lilv}}%</view>
							</view>
							<view class="clear"></view>
						</view>

						<!-- 
							<view class="rowx" @click="ckhkjh">
								<view class="icon"><text class="iconfont icon-jiantou"></text></view>
								<view class="tit"><text class="redst"></text>还款计划</view>
								<view class="inpcon">
									<view class="inp">
										{{hkjhtip}}
									</view>
								</view>
								<view class="clear"></view>
							</view> -->

						<view class="rowx">
							<view class="tit"><text class="redst"></text>总利息</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>总还款金额</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<block v-if='hkrcddata.length > 0'>
								<view class="icon" style="color:#1677FF;z-index:200;" @click="hkrcdv">还款记录</view>
							</block>
							<view class="tit"><text class="redst"></text>已还款金额</view>
							<view class="inpcon">
								<view class="inp colorls">￥{{djdata.sjhkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>待还款金额</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.whkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<!-- 		<block v-if="djdata.ksjine > 0">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>亏损金额</view>
									<view class="inpcon">
										<view class="inp colorhs">￥{{djdata.ksjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block> -->

						<block v-if="djdata.hkfs==2">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">
										{{djdata.dqtimetxt}}

										<text style="color:#1677FF;z-index:200;margin-left:20upx"
											@click="xqrcdv">续期记录</text>

									</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.hkjqtime !=0 ">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>结清时间</view>
								<view class="inpcon">
									<view class="inp">
										{{djdata.hkjqtime}}
									</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

					</block>

				</view>



				<block v-if="djdata.ddtype!=4">
					<view class="maxcon">

						<view class="vstit" style="margin-bottom:30upx">物品列表</view>

						<block v-if="djshop">
							<view class="splist">
								<block v-for="(item,index) in djshop" :key="index">
									<view class="spitem">
										<view class="spname">
											<view class="tline"></view>
											{{item.title}}
											<text
												style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.id}})</text>
										</view>
										<!-- 	<view class="spbjbtn" @click="spedit" :data-spid="item.id"
												v-if="item.isedit==1"><text class="iconfont icon-bianji"></text> 编辑
											</view> -->

										<!-- 	<view class="rowa">
												<view class="tip">物品编号</view>
												<view class="nr colorls">{{item.id}}</view>
												<view class="clear"></view>
											</view> -->

										<block v-if="djdata.ddtype ==1 ">
											<view class="rowa">
												<view class="tip" @click="jmdjmx" :data-spid="item.id"
													:data-spname="item.title">{{dtptxt}}{{dtpjgtxt}}
													<text class="iconfont icon-yewucaozuo"
														style="margin-left:10upx;color:#1677FF"></text>
												</view>
												<view class="nr" @click="setjmdj" :data-spid="item.id">
													¥{{item.danjia}} x {{item.kucun}} = ¥<text
														style="color:#ff0000">{{item.zongjia}}</text>
													<!-- 		<text class="iconfont icon-bianji"
															style="margin-left:10upx;color:#1677FF"
															v-if="djdata.ddsta==2"></text> -->
												</view>
												<view class="clear"></view>
											</view>
										</block>
										<block v-else>
											<view class="rowa">
												<view class="tip">{{dtptxt}}{{dtpjgtxt}}</view>
												<view class="nr">
													¥{{item.danjia}} x {{item.kucun}} = ¥<text>{{item.zongjia}}</text>
												</view>
												<view class="clear"></view>
											</view>
										</block>


										<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
											<view class="rowa">
												<view class="tip" @click="qtfymx" :data-spid="item.id"
													:data-spname="item.title">其他收费 <text
														class="iconfont icon-yewucaozuo"
														style="margin-left:10upx;color:#1677FF"></text> </view>
												<view class="nr">
													<text @click="qtfymx" :data-spid="item.id"
														:data-spname="item.title">¥{{item.fjfjine}}</text>
													<!-- 		<text class="addotfybtn" @click="qtfyedit" :data-spid="item.id"
														:data-spname="item.title" data-fyid="0"
														v-if="item.isedit==1">+新增</text> -->
												</view>
												<view class="clear"></view>
											</view>
										</block>

										<view class="rowa">
											<view class="tip" @click="qtcbmx" :data-spid="item.id"
												:data-spname="item.title">其他成本 <text class="iconfont icon-yewucaozuo"
													style="margin-left:10upx;color:#1677FF"></text> </view>
											<view class="nr">
												<text @click="qtcbmx" :data-spid="item.id"
													:data-spname="item.title">¥{{item.cbfjine}}</text>
												<!-- 		<text class="addotfybtn" @click="qtcbedit" :data-spid="item.id"
															:data-spname="item.title" data-fyid="0"
															v-if="item.isedit==1">+新增</text> -->
											</view>
											<view class="clear"></view>
										</view>




										<block v-if="djdata.ddtype==1">
											<view class="rowa">
												<view class="tip">服务费比例</view>
												<view class="nr">{{item.fwfbl}} %</view>
												<view class="clear"></view>
											</view>
										</block>
										<block v-if="djdata.ddtype==1 ">
											<view class="rowa">
												<view class="tip">寄卖服务费</view>
												<view class="nr" style="color:#ff0000">¥{{item.fwfjine}}</view>
												<view class="clear"></view>
											</view>
										</block>
										<block v-if="djdata.ddtype==3">
											<view class="rowa">
												<view class="tip">服务费金额</view>
												<view class="nr" style="color:#ff0000">¥{{item.fwfjinenow}}</view>
												<view class="clear"></view>
											</view>
										</block>



										<view class="rowa">
											<view class="tip">物品状态</view>
											<view class="nr">
												<text class="status"
													:class="'sta'+item.status">{{item.statusname}}</text>
											</view>
											<view class="clear"></view>
										</view>
										<block v-if="djdata.ddtype !=3 || item.zssta==1">
											<view class="rowa">
												<view class="tip" @click="sjjgmx" :data-spid="item.id"
													:data-spname="item.title">上架价格
													<text class="iconfont icon-yewucaozuo"
														style="margin-left:10upx;color:#1677FF"></text>
												</view>

												<!-- 	<view class="nr" @click="setsjjg" :data-spid="item.id">
														¥{{item.sjjiage}} 
														<text class="iconfont icon-bianji"	style="margin-left:10upx;color:#1677FF"	v-if="djdata.ddsta==2"></text> 
													</view> -->

												<view class="nr">
													¥{{item.sjjiage}}
												</view>

												<view class="clear"></view>
											</view>
											<view class="rowa">
												<view class="tip">上架状态</view>
												<view class="nr">
													{{item.sjstatusname}}
													<!-- 			<block v-if="item.isedit==1">
															<view @click="setspsta" :data-spid='item.id'
																style="padding-top:15upx;">
																<tn-switch v-model="item.sjsta==1 ? true : false "
																	activeColor="#FFD427" inactiveColor="#dfdfdf"
																	leftIcon="success" right-icon="close"
																	change="ss"></tn-switch>
															</view>
														</block>
														<block v-else>
															{{item.sjstatusname}}
														</block> -->
												</view>
												<view class="clear"></view>
											</view>
										</block>


										<block v-if="item.vxq==1">

											<view class="rowa">
												<view class="tip">验货员</view>
												<view class="nr">{{item.yhrystr ? item.yhrystr : '--' }}</view>
												<view class="clear"></view>
											</view>
											<view class="rowa">
												<view class="tip">仓库</view>
												<view class="nr">{{item.ckname ? item.ckname  : '--' }}</view>
												<view class="clear"></view>
											</view>

											<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
												<view class="rowa">
													<view class="tip">{{itemx.title}}</view>
													<view class="nr">{{itemx.value}}</view>
													<view class="clear"></view>
												</view>
											</block>




											<block v-if="item.picturesarr">
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
														<block v-for="(itemxx,indexx) in item.picturesarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemxx"
																	:data-src='staticsfile + itemxx '
																	@tap='previewImagev'
																	:data-picarr="item.picturesarr"></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</view>
											</block>

											<block v-if="item.beizhu">
												<view class="bzcon">
													<view class="bztit">物品备注：</view>
													<view class="bznr">{{item.beizhu}}</view>
												</view>
											</block>

										</block>

										<view class="czvvcon">

											<view class="czv2">
												<view class="" @click="setvxq" :data-spid="item.id">
													<block v-if="item.vxq==0">
														展开详情<text class="iconfont icon-jtdown2"></text>
													</block>
													<block v-else>
														收起详情<text class="iconfont icon-jtup2"></text>
													</block>
												</view>
											</view>


											<view class="czv1">

												<block v-if="item.isvczjl && vcp==0">
													<view class="zmbtn" @click="czjlmx" :data-spid="item.id"
														:data-spname="item.title">
														<text class="iconfont icon-shuhui1"></text> 出资分润
													</view>
												</block>

												<block v-if="item.isedit==1">

													<block v-if="item.ksh==1">
														<view class="zmbtn" @click="gotosh" :data-spid="item.id">
															<text class="iconfont icon-shuhui1"></text> 赎回
														</view>
													</block>

													<block v-if="djdata.ddtype==3">
														<block v-if="item.zssta!=1 ">
															<view class="zmbtn" @click="gotozj" :data-spid="item.id">
																<text class="iconfont icon-zhuanmaijilu"></text> 资金
															</view>
														</block>
														<block v-if="item.zssta==2 ">
															<view class="zmbtn bt2"> {{item.zsstatxt}} <text
																	class="iconfont icon-shijian"></text></view>
														</block>
														<block v-if="item.zssta==1">
															<block v-if="item.zschsta==0">
																<view class="zmbtn bt1" @click="gotozm"
																	:data-spid="item.id" :data-isch="1"> <text
																		class="iconfont icon-zuo"></text>
																	{{zschbtntxt}}
																</view>
															</block>
															<block v-else>
																<block v-if="item.zschsta==5">
																	<view class="zmbtn bt2"> {{item.zschstatxt}}
																		<text class="iconfont icon-shijian"></text>
																	</view>
																</block>
																<block v-if="item.zschsta==6">
																	<view class="zmbtn bt1" @click="gotozm"
																		:data-spid="item.id" :data-isch="1"> <text
																			class="iconfont icon-zuo"></text>
																		{{item.zschstatxt}}
																	</view>
																</block>
															</block>
														</block>

														<block v-if="item.zssta==0 || item.zssta==3">
															<view class="zmbtn" @click="gotozm" :data-spid="item.id"
																:data-isch="0"> {{item.zsstatxt}} <text
																	class="iconfont icon-arrow-rightxs"></text>
															</view>
														</block>
													</block>

													<block v-if="djdata.ddtype==1">
														<block v-if="item.zmsta==2 ">
															<view class="zmbtn bt2"> {{item.zmstatxt}} <text
																	class="iconfont icon-shijian"></text></view>
														</block>

														<block v-if="item.zmsta==1">
															<block v-if="item.zmchsta==0">
																<view class="zmbtn bt1" @click="gotozhs"
																	:data-spid="item.id" :data-isch="1"> <text
																		class="iconfont icon-zuo"></text>
																	{{zmchbtntxt}}
																</view>
															</block>
															<block v-else>
																<block v-if="item.zmchsta==5">
																	<view class="zmbtn bt2"> {{item.zmchstatxt}}
																		<text class="iconfont icon-shijian"></text>
																	</view>
																</block>
																<block v-if="item.zmchsta==6">
																	<view class="zmbtn bt1" @click="gotozhs"
																		:data-spid="item.id" :data-isch="1"> <text
																			class="iconfont icon-zuo"></text>
																		{{item.zmchstatxt}}
																	</view>
																</block>
															</block>
														</block>

														<block v-else>
															<block v-if="item.zmsta==0 || item.zmsta==3">
																<view class="zmbtn" @click="gotozhs"
																	:data-spid="item.id" :data-isch="0">
																	{{item.zmstatxt}} <text
																		class="iconfont icon-arrow-rightxs"></text>
																</view>
															</block>
														</block>
													</block>
												</block>

											</view>

											<view class="clear"></view>
										</view>




									</view>
								</block>
							</view>
						</block>

					</view>



					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">费用信息</view>

						<block v-if="djdata.ddtype==3  && vcp==0 ">
							<view class="more">
								<view class="czjlbtn"
									style="background:#FFD427 ;height:50upx;line-height:50upx;border-radius:100upx;padding:0 15upx;font-size:24upx;color:#000"
									@click="czjlmx">其他分润</view>
							</view>
						</block>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品总价</view>
							<view class="inpcon">
								<view class="inp" style="color:#ff0000">{{djdata.spzongjia}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他收费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fjfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>其他成本</view>
							<view class="inpcon">
								<view class="inp">{{djdata.cbfzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="djdata.ddtype==1 ">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>总服务费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fwfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>服务费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fwfzjinenow}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==2">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>单据总价</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{djdata.zongjia}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-else>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>总费用</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{djdata.zongfy}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
					</view>

				</block>


				<block v-if="djdata.ddtype==4 && djdata.hkfs==1">
					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">还款计划</view>

						<view class="khjhcon" style="padding-left:70upx">
							<view class="hkjh-timeline">
								<view class="hkjh-timeline-content">

									<checkbox-group @change="checkboxGroupChange">
										<block v-for="(item,index) in hkjhdata" :key="index">
											<view class="item hkjh-timeline-item" :class="'hksta'+item.status">
												<block v-if="item.yhkzjine > 0">

													<view class="dxbox" v-if="item.status==2 || item.status==3">
														<checkbox :value="item.id" color="#FFD427"></checkbox>
													</view>

													<view class="hkbtn" v-if="item.status==2 || item.status==3"
														@click="gotohk" :data-hkid="item.id">还款</view>
												</block>
												<em class="yd-timeline-icon"></em>
												<view class="hkjine">

													¥{{item.yhkzjine}}

													<block v-if="item.bqsyhkjine > 0">
														<text class="yhtip">已还 {{item.bqyhkjine}}</text>
													</block>
													<text class="yhqtxt" v-if="item.status==1">已还清</text>
													<text class="yyqtxt" v-if="item.status==3">已逾期</text>
												</view>
												<!-- <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view> -->
												<view class="qbcon">
													<view class="t">{{item.xhtit}}</view>
													<view class="r">{{item.rq}}</view>
												</view>
											</view>
										</block>
									</checkbox-group>

								</view>
							</view>
						</view>


					</view>
				</block>

				<view class="maxcon" style="padding-bottom:10upx">

					<view class="rowx">
						<view class="tit"><text class="redst"></text>业务人员</view>
						<view class="inpcon">
							<view class="inp">{{djdata.ywrytxtstr}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>录单人员</view>
						<view class="inpcon">
							<view class="inp">{{djdata.opname}}</view>
						</view>
						<view class="clear"></view>
					</view>

				</view>

				<view class="maxcon">
					<view class="rowxmttp">
						<view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
						<view class="zjzkicon" @click="setvzz">{{vsfnr==0 ? '展开' : '收起'}} <text
								class="iconfont icon-jiantou_liebiaozhankai"></text></view>

						<block v-if="vsfnr==1">
							<view class="uprzimgcon">
								<view class="zzx sf1">
									<view class="con">
										<view class="vt1">
											<block v-if="djdata.rzimg1">
												<image :src='staticsfile + djdata.rzimg1'></image>
											</block>
											<block v-else>
												<image src="/static/images/rzupimg1.png"></image>
											</block>
										</view>
										<view class="vt2">身份证头像面</view>
										<view class="clear"></view>
									</view>
								</view>
								<view class="zzx sf2">
									<view class="con">
										<view class="sf2">
											<block v-if="djdata.rzimg2">
												<image :src='staticsfile + djdata.rzimg2'></image>
											</block>
											<block v-else>
												<image src="/static/images/rzupimg2.png"></image>
											</block>
										</view>
										<view class="vt2">身份证国徽面</view>
									</view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx" style="margin-top:20upx;">
								<view class="tit"><text class="redst"></text>身份证号</view>
								<view class="inpcon">
									<view class="inp">{{djdata.khsfzhao}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx" style="margin-top:15upx;position: relative;" v-if="djdata.isrlsb==1">

								<view class="tit"><text class="redst"></text>人脸识别</view>
								<view class="inpcon">
									<view class="inp">
										{{djdata.isrlsb==1 ? '需要' : '不需要'}}

									</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


					</view>
				</view>




				<block v-if="djdata.ddtype!=2">
					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit" style="font-weight:600"><text class="redst"></text>历史记录</view>
							<view class="zjzkicon" @click="setvzz2">{{vsfnr2==0 ? '展开' : '收起'}} <text
									class="iconfont icon-jiantou_liebiaozhankai"></text></view>

							<block v-if="vsfnr2==1">


								<block v-if="lsjrdata.length > 0">

									<view class="fwshlist">

										<block v-for="(item,index) in lsjrdata" :key="index">

											<view class="item ">
												<view class="con">

													<view class="inf0">
														<view class="tf1">
															<text class="ddtype"
																style="background:#333;color:#FFF;border-left:6upx solid #FFD427">{{item.typetxt}}</text>
															{{item.shtime}}
														</view>
														<view class="clear"></view>
													</view>

													<view class="inf1">
														<!-- 1赎回2续期3质押转售4寄卖转回收5增资6减资7结清8亏损9等额本息还款10先息后本还款11押转卖撤回13寄转回收撤回, -->

														<view v-if="item.spname"><text
																class="tit">物品名称：</text>{{item.spname}}</view>
														<block v-if="item.type==1">
															<view><text
																	class="tit">物品价格：</text><text>￥{{item.xzongjia}}</text>
															</view>
															<view><text class="tit">赎回费用：</text><text
																	class="yfjine">￥{{item.shfeiyong}}</text></view>
														</block>
														<block v-if="item.type==2">
															<block v-if="item.ddtype==4">
																<view><text
																		class="tit">续期周期：</text><text>{{item.ywzqnum}}</text><text>{{item.ywzqdwtxt}}</text>
																</view>
																<view><text class="tit">续期利息：</text><text
																		class="yfjine">￥{{item.xqlixi}}</text></view>
															</block>
															<block v-if="item.ddtype==1">
																<view><text
																		class="tit">续期周期：</text><text>{{item.ywzqnum}}</text><text>{{item.ywzqdwtxt}}</text>
																</view>
															</block>
															<block v-if="item.ddtype==3">
																<view><text
																		class="tit">续期周期：</text><text>{{item.ywzqnum}}</text><text>{{item.ywzqdwtxt}}</text>
																</view>
																<view><text class="tit">续期费用：</text><text
																		class="yfjine">￥{{item.xqfwf}}</text></view>
															</block>
														</block>

														<block v-if="item.type==5">
															<view><text class="tit">增资金额：</text><text
																	class="yfjine">￥{{item.zjzjine}}</text></view>
															<view><text class="tit">增资服务费：</text><text
																	class="yfjine">￥{{item.zzfwf}}</text></view>
														</block>
														<block v-if="item.type==6">
															<view><text class="tit">减资金额：</text><text
																	class="yfjine">￥{{item.zjzjine}}</text></view>
														</block>
														<block v-if="item.type==7">
															<view><text class="tit">结清金额：</text><text
																	class="yfjine">￥{{item.hkjine}}</text></view>
														</block>
														<block v-if="item.type==9">
															<view><text class="tit">还款金额：</text><text
																	class="yfjine">￥{{item.hkjine}}</text></view>
														</block>
														<block v-if="item.type==8">
															<view><text class="tit">亏损金额：</text><text
																	class="yfjine">￥{{item.ksjine}}</text></view>
														</block>
														<block v-if="item.type==10">
															<view><text class="tit">还款金额：</text><text
																	class="yfjine">￥{{item.hkjine}}</text></view>
														</block>
														<block v-if="item.type==15">
															<view><text class="tit">增加成本：</text><text
																	class="yfjine">￥{{item.shfeiyong}}</text></view>
														</block>

														<block v-if="item.type==16">
															<view><text class="tit">还款笔数：</text><text
																	class="yfjine">{{item.dbhkqs}}</text>笔</view>
															<view><text class="tit">还款金额：</text><text
																	class="yfjine">￥{{item.hkjine}}</text></view>
														</block>

														<block v-if="item.type==3 || item.type==11">
															<view><text class="tit">物品价格：</text><text
																	class="yfjine">￥{{item.xzongjia}}</text></view>
														</block>
														<block v-if="item.type==4 || item.type==13">
															<view><text class="tit">物品价格：</text><text
																	class="yfjine">￥{{item.xzongjia}}</text></view>
														</block>
														<view class="inf2">
															<view class="setbtn3" @click="gotoddfwshview"
																:data-djid="item.id">查看</view>
														</view>

													</view>

													<view class="clear"></view>
												</view>
											</view>

										</block>



									</view>

								</block>
								<view class="fwshlist">
									<view class="item  ">
										<view class="con">
											<view class="inf0">
												<view class="tf1">
													<text class="ddtype"
														style="background:#333;color:#FFF;border-left:6upx solid #FFD427">开单信息</text>
													{{djdata.addtime}}
												</view>
												<view class="clear"></view>
											</view>
											<view class="inf2">
												<view class="setbtn3" @click="gotoyd" :data-djtype="djdata.djtype" :data-djid="djdata.id" >查看</view>
											</view>
											<view class="nrcon">
												<view class="info">
													<view v-if="djdata.ddtype==4"><text>开单金额：</text> <text
															class="jine">￥{{djdata.yfjine}}</text></view>

													<view
														v-if="djdata.ddtype==3 || djdata.ddtype==2 || djdata.ddtype==1">
														<text>开单金额：</text> <text class="jine">￥{{djdata.zongjia}}</text>
													</view>

													<view><text>周期：</text>{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
													<view v-if="djdata.orfwfzjine > 0"><text>服务费：</text> <text
															class="jine"
															style="color:#ff0000">￥{{djdata.orfwfzjine}}</text></view>
													<view v-if="djdata.orlxzjine > 0"><text>利息：</text> <text
															class="jine"
															style="color:#ff0000">￥{{djdata.orlxzjine}}</text></view>
													<view><text>到期时间：</text>{{djdata.orywdqtime}}</view>
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</view>
								</view>

							</block>


						</view>
					</view>
				</block>






				<block v-if="djdata.remark">
					<view class="maxcon">
						<view class="vstit" style="font-weight:400;margin-bottom:25upx">单据备注说明 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户可见）</text></view>

						<view class="bzcon">
							{{djdata.remark}}
						</view>

					</view>
				</block>

			</view>


			<block v-if="shremark">
				<view class="maxcon">
					<view class="vstit" style="font-weight:400;margin-bottom:25upx">审核备注 <text
							style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

					<view class="bzcon">
						{{shremark}}
					</view>
				</view>
			</block>







			<block v-if="djdata.ddsta==12">
				<view class="maxcon">
					<view class="vstit" style="margin-bottom:25upx">亏损说明</view>

					<view class="rowx">
						<view class="tit"><text class="redst"></text>亏损金额</view>
						<view class="inpcon">
							<view class="inp" style="color:#ff0000">{{djdata.ksjine}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="bzcon" v-if="djdata.ksremark" style="margin-top:20upx">
						{{djdata.ksremark}}
					</view>

					<block v-if="djdata.kspicturesarr">
						<view class="rowxmttp">
							<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
								<block v-for="(itemxx,indexx) in djdata.kspicturesarr" :key="indexx">
									<view class='item'>
										<image :src="staticsfile+itemxx" :data-src='staticsfile + itemxx '
											@click='previewImagex'></image>
									</view>
								</block>
								<view class="clear"></view>
							</view>
						</view>
					</block>

				</view>
			</block>


			<block v-if="djdata.isedit==1">


				<block v-if="djdata.ddtype==4 ">
					<view class="tn-flex tn-footerfixed">



						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="dbczbtn db2" @click="gotoks">
								<text class="iconfont icon-kezhuanmai"></text> <text>亏损</text>
							</button>
						</view>


						<block v-if="djdata.hkfs==1 ">

							<view class="tn-flex-1 justify-content-item tn-text-center">
								<button class="dbczbtn db2" @click="gotojq">
									<text class="iconfont icon-shuhui1"></text> <text>提前结清</text>
								</button>
							</view>
							<view class="tn-flex-1 justify-content-item tn-text-center">
								<button class="dbczbtn db2" @click="gotobfhk">
									<text class="iconfont icon-xuqiguanli"></text> <text>多笔还款</text>
								</button>
							</view>

							<view class="hkhjcon" v-if="hkidstr.length > 0">已选：<text
									style="margin-right:10upx">{{dbhkqs}}</text> 笔 ，合计：<text>￥{{dbhkjine}}</text></view>

						</block>

						<block v-if="djdata.hkfs==2 ">

							<view class="tn-flex-1 justify-content-item tn-text-center">
								<button class="dbczbtn db2" @click="gotoxq">
									<text class="iconfont icon-shuhui1"></text> <text>续期</text>
								</button>
							</view>
							<view class="tn-flex-1 justify-content-item tn-text-center">
								<button class="dbczbtn db2" @click="gotojq">
									<text class="iconfont icon-shuhui1"></text> <text>结清</text>
								</button>
							</view>
							<view class="tn-flex-1 justify-content-item tn-text-center">
								<button class="dbczbtn db2" @click="gotohk2">
									<text class="iconfont icon-xuqi1"></text> <text>还款</text>
								</button>
							</view>
						</block>
					</view>
				</block>

			</block>


			<!-- 如果已完成 或已赎回-->
			<block v-if="djdata.ddsta==1 || djdata.ddsta==11">
				<block v-if="djdata.ddtype==1">

					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="dbczbtn db2" @click="gotoeckd"
								style="width:400upx;margin:0 auto;border-radius:100px;">
								<text style="font-size:28upx;">二次寄卖</text>
							</button>
						</view>
					</view>

				</block>
				<block v-if="djdata.ddtype==3">

					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="dbczbtn db2" @click="gotoeckd"
								style="width:400upx;margin:0 auto;border-radius:100px;">
								<text style="font-size:28upx;">二次质押</text>
							</button>
						</view>
					</view>

				</block>
			</block>


		</view>

		<view style="height:180upx"></view>



		<tn-popup v-model="czjlmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">出资分润记录</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtfymxsptit" v-if="czjlmxspname"><text class="iconfont icon-zanwushuju"
						style="margin-right:10upx"></text>
					{{czjlmxspname}}
				</view>
				<view class="czjllist">

					<block v-if="czjldata.length>0">

						<block v-for="(item,index) in czjldata" :key="index">
							<view class="czrli">
								<view class="cname">{{item.ygname}}
									<text class="czjs">({{item.rytypetxt}})</text>
								</view>
								<view class="xx">
									<view class="czxm" v-if="item.rytype==8">累计出资金额：<text
											:class="item.czjine > 0 ? '' : ''">{{item.czjine}}</text>
									</view>
									<view class="czxm">累计分润：<text
											:class="item.frjine > 0 ? 'ysz' : ''">{{item.frjine}}</text>
									</view>

									<view class="clear"></view>
								</view>
								<view class="xx">
									<view class="czxm" v-if="item.cbfyczjine > 0">其他成本出资：<text
											:class="item.cbfyczjine > 0 ? '' : ''">{{item.cbfyczjine}}</text>
									</view>
									<view class="czxm" v-if="item.ksjine > 0">累计亏损：<text
											style="color:#4aac09">{{item.ksjine}}</text>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</block>
					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无出资分润记录</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="qtfymxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他收费明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtfymxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtfymxspname}}
				</view>
				<view class="qtfylist">

					<block v-if="qtfydata.length>0">

						<block v-for="(item,index) in qtfydata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<!-- 		<view class="bjbtn" v-if="item.isedit==1">
											<text @click="qtfydel" :data-fyid="item.id">删除</text>
											<text @click="qtfyedit" :data-fyid="item.id" :data-spid="item.spid"
												style="color:#1677FF">编辑</text>
										</view> -->


										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他收费</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="qtcbmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他成本明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtcbmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtcbmxspname}}
				</view>
				<view class="qtcblist">

					<block v-if="qtcbdata.length>0">

						<block v-for="(item,index) in qtcbdata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<!-- 	<view class="bjbtn" v-if="djdata.isedit==1">
											<text @click="qtcbdel" :data-fyid="item.id">删除</text>
											<text @click="qtcbedit" :data-fyid="item.id" :data-spid="item.spid"
												style="color:#1677FF">编辑</text>
										</view> -->


										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtcbvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他成本</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>


		<tn-popup v-model="hkrcdshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit" style="border-bottom:0">还款记录</view>

			<scroll-view scroll-y="true" style="height: 700rpx;">


				<view class="hkrcdlist">

					<block v-if="hkrcddata.length>0">

						<block v-for="(item,index) in hkrcddata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view><text>还款：</text> <text class="jine">￥{{item.hkjine}}</text></view>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view v-if="item.remark"><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无还款记录</view>
						</view>
					</block>


				</view>

			</scroll-view>
		</tn-popup>


		<tn-popup v-model="xqrcdshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit" style="border-bottom:0">历史记录</view>

			<scroll-view scroll-y="true" style="height: 700rpx;">
				<view v-if="djdata.ddtype==3"
					style="padding:2upx 32upx 0 32upx;border-bottom:1px solid #eee;padding-bottom:20upx">服务费总额：<text
						style="color:#ff0000">￥{{djdata.fwfzjine}}</text>
				</view>
				<view v-if="djdata.ddtype==4"
					style="padding:2upx 32upx 0 32upx;border-bottom:1px solid #eee;padding-bottom:20upx">利息总金额：<text
						style="color:#ff0000">￥{{djdata.lxzjine}}</text>
				</view>
				<view class="xqrcdlist">

					<block v-if="xqrcddata.length > 0">

						<block v-for="(item,index) in xqrcddata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="rq"><text></text>{{item.addtime}}</view>
										<view v-if="item.xqjine > 0"><text>续期金额：</text>￥{{item.xqjine}}</view>
										<view v-if="item.xqlixi > 0"><text>续期利息：</text><text
												style="color:#ff0000">￥{{item.xqlixi}}</text></view>
										<view><text>续期周期：</text>{{item.ywzqnum}} {{item.ywzqdwtxt}}</view>
										<view><text>到期时间：</text>{{item.ywdqtime}}</view>
										<view v-if="item.xqfwf > 0"><text>续期服务费：</text> <text
												class="jine">￥{{item.xqfwf}}</text></view>
										<view v-if="item.remark"><text>备注说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>
					</block>

					<view class="lis-item  ">
						<view class="nrcon">
							<view class="info">
								<view class="rq"><text></text>{{djdata.addtime}}</view>

								<view v-if="djdata.ddtype==4"><text>开单金额：</text> <text
										class="jine">￥{{djdata.yfjine}}</text></view>

								<view><text>周期：</text>{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								<view v-if="djdata.orfwfzjine > 0"><text>服务费：</text> <text
										class="jine">￥{{djdata.orfwfzjine}}</text></view>
								<view v-if="djdata.orlxzjine > 0"><text>利息：</text> <text
										class="jine">￥{{djdata.orlxzjine}}</text></view>
								<view><text>到期时间：</text>{{djdata.orywdqtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>




				</view>

			</scroll-view>
		</tn-popup>



		<tn-popup v-model="sjjgmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">上架价格调整记录</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="sjjgmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{sjjgmxspname}}
				</view>
				<view class="sjjglist">

					<block v-if="sjjgdata.length>0">

						<block v-for="(item,index) in sjjgdata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="cztype">{{item.typetxt}}</view>
										<view><text class="tit">操作时间：</text>{{item.addtime}}</view>
										<view v-if="item.opname"><text class="tit">操作人员：</text>{{item.opname}}</view>
										<block v-if="item.type==10">
											<view><text class="tit">原上架价格：</text>￥{{item.orsjjiage}}</view>
											<view><text class="tit">新上架价格：</text><text
													class="colorls">￥{{item.sjjiage}}</text></view>
										</block>
										<block v-if="item.type==11">
											<view><text class="tit">上架价格：</text><text
													class="colorls">￥{{item.sjjiage}}</text></view>
										</block>

										<view v-if="item.remark"><text>调整说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>
						</block>

					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无调整价格记录</view>
						</view>
					</block>

				</view>
			</scroll-view>
		</tn-popup>




		<tn-popup v-model="jmdjmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">寄卖底价调整记录</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="jmdjmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{jmdjmxspname}}
				</view>
				<view class="jmdjlist">

					<block v-if="jmdjdata.length>0">

						<block v-for="(item,index) in jmdjdata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="cztype">{{item.typetxt}}</view>
										<view><text class="tit">操作时间：</text>{{item.addtime}}</view>
										<view v-if="item.opname"><text class="tit">操作人员：</text>{{item.opname}}</view>
										<block v-if="item.type==10">
											<view><text class="tit">原寄卖底价：</text>￥{{item.orsjjiage}}</view>
											<view><text class="tit">新寄卖底价：</text><text
													class="colorls">￥{{item.sjjiage}}</text></view>
										</block>
										<block v-if="item.type==11">
											<view><text class="tit">寄卖底价：</text><text
													class="colorls">￥{{item.sjjiage}}</text></view>
										</block>
										<block v-if="item.type==15">
											<view><text class="tit">原寄卖底价：</text>￥{{item.ordanjia}}</view>
											<view><text class="tit">新寄卖底价：</text><text
													class="colorls">￥{{item.danjia}}</text></view>
										</block>
										<view v-if="item.remark"><text>调整说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="jmdjsetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thjmdjvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>
						</block>

					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无寄卖底价记录</view>
						</view>
					</block>

				</view>
			</scroll-view>
		</tn-popup>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				spzongjia: 0,
				clicksta: false,
				remark: '',
				statusname: '',
				vsfnr: 0,
				vsfnr2: 0,
				hkjhshow: false,
				hkjhtip: '',
				hkjhdata: '',
				jjtgshow: false,
				bhsm: '',
				thspid: 0,

				thczjine: 0,
				thfrbl: 0,
				czfrspid: 0,
				czfrisfr: 0,
				thczygid: 0,
				thczygname: '',

				czryeditshow: false,

				czjlmxshow: false,
				czjlmxspname: '',
				czjldata: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid: 0,

				shremark: '',
				shuhuidata: '',

				hkrcddata: '',
				hkrcdshow: false,

				xqrcddata: '',
				xqrcdshow: false,


				sjjgmxshow: false,
				sjjgmxspname: '',
				sjjgdata: '',
				thsjjgvid: 0,

				jmdjmxshow: false,
				jmdjmxspname: '',
				jmdjdata: '',
				thjmdjvid: 0,



				qtcbmxshow: false,
				qtcbmxspname: '',
				qtcbdata: '',
				thqtcbvid: 0,

				dtptxt: '',
				dtpjgtxt: '',

				zmchbtntxt: '',
				zschbtntxt: '',

				vcp: 0,

				lsjrdata: '',

				hkidstr: '',
				dbhkqs: 0,
				dbhkjine: 0,
				isgtjq: 0,


			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			let vcp = options.vcp ? options.vcp : 0;
			this.vcp = vcp; //查看点2来自我的订单 业务员管理进来的
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			this.clicksta = false;
			let thczygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			// if (thczygid) {
			// 	console.log(thczygid);
			// 	this.thczygid = thczygid;
			// 	this.thczygname = thygname;
			// 	uni.setStorageSync('thygid', '');
			// 	uni.setStorageSync('thygname', '');
			// 	this.czryeditshow = true;
			// }


			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}

			if (uni.getStorageSync('thupspotfyspid') && uni.getStorageSync('thupspotfyspid') > 0) {
				this.getqyfylist(uni.getStorageSync('thupspotfyspid'));
				uni.setStorageSync('thupspotfyspid', 0)
			}

			if (uni.getStorageSync('thupspotcbspid') && uni.getStorageSync('thupspotcbspid') > 0) {
				this.getqycblist(uni.getStorageSync('thupspotcbspid'));
				uni.setStorageSync('thupspotcbspid', 0)
			}

		},

		methods: {
			checkboxGroupChange(e) {
				let that = this;
				let value = e.detail.value;
				this.hkidstr = value;

				if (!value || value.length <= 0) {
					this.dbhkjine = 0;
					return false;
				}
				let da = {
					'djid': that.djid,
					'hkidstr': value,
				}
				that.$req.post('/v1/fuwu/jsplhkjine', da)
					.then(res => {
						console.log(res);
						if (res.errcode == 0) {
							that.dbhkqs = res.data.dbhkqs;
							that.dbhkjine = res.data.dbhkjine;
							that.isgtjq = res.data.isgtjq;
						}
					})

			},
			// 等额本息多笔还款
			gotobfhk(e) {
				let that = this;
				let hkidstr = this.hkidstr;
				if (!hkidstr || hkidstr.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择要还款的期数！'
					})
					return false;
				}

				if (that.isgtjq == 1) {
					uni.navigateTo({
						url: './huankuan?type=3&djid=' + this.djid
					})
				} else {
					uni.navigateTo({
						url: './huankuan?type=5&djid=' + this.djid + '&hkidstr=' + hkidstr
					})
				}

			},

			// 分享
			fxdd() {
				uni.navigateTo({
					url: "./fxddewm?djid=" + this.djid
				})
			}, // 赎回
			gotosh(e) {
				let spid = e.currentTarget.dataset.spid;
				uni.navigateTo({
					url: "./shsh?type=1&spid=" + spid + "&djid=" + this.djid
				})
			},
			// 续期
			gotoxq() {
				uni.navigateTo({
					url: "./shxq?type=2&djid=" + this.djid
				})
			},

			// 还款
			gotohk(e) {
				let hkid = e.currentTarget.dataset.hkid;
				uni.navigateTo({
					url: './huankuan?type=1&hkid=' + hkid + '&djid=' + this.djid
				})
			},
			// 还款
			gotohk2(e) {
				uni.navigateTo({
					url: './huankuan?type=2&djid=' + this.djid
				})
			},

			// 结清
			gotojq(e) {
				uni.navigateTo({
					url: './huankuan?type=3&djid=' + this.djid
				})
			},
			// 亏损
			gotoks(e) {
				uni.navigateTo({
					url: './huankuan?type=4&djid=' + this.djid
				})
			},
			// 资金
			gotozj(e) {
				let spid = e.currentTarget.dataset.spid;
				uni.navigateTo({
					url: "./zijin?spid=" + spid + "&djid=" + this.djid
				})
			},
			// 转卖
			gotozm(e) {
				let spid = e.currentTarget.dataset.spid;
				let isch = e.currentTarget.dataset.isch;
				uni.navigateTo({
					url: "./zyzm?type=1&spid=" + spid + "&djid=" + this.djid + "&isch=" + isch
				})
			},
			//转回收
			gotozhs(e) {
				let spid = e.currentTarget.dataset.spid;
				let isch = e.currentTarget.dataset.isch;
				uni.navigateTo({
					url: "./jmhs?type=1&spid=" + spid + "&djid=" + this.djid + "&isch=" + isch
				})
			},



			addczry(e) {
				let isfr = this.czfrisfr;
				let spid = e.currentTarget.dataset.spid;
				this.czfrspid = spid;
				this.thczjine = 0;
				this.thfrbl = 0;
				let yytitle = "选择出资人";
				if (isfr == 1) {
					let yytitle = "选择出资/分润人";
				}
				uni.navigateTo({
					url: '/pages/selyg/index?stype=7&xtitle=' + yytitle
				})
			},
			hkrcdv() {
				this.hkrcdshow = true;
			},
			xqrcdv() {
				this.xqrcdshow = true;
			},
			ckhkjh() {
				this.hkjhshow = true;
			},
			toback() {
				uni.navigateBack();
			},
			setvzz() {
				if (this.vsfnr == 0) {
					this.vsfnr = 1;
				} else {
					this.vsfnr = 0;
				}
			},
			setvzz2() {
				if (this.vsfnr2 == 0) {
					this.vsfnr2 = 1;
				} else {
					this.vsfnr2 = 0;
				}
			},
			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					et: 2,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/ddedit', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.djshop = res.data.djshop;
							that.statusname = res.data.statusname;
							that.remark = res.data.djdata.remark;
							that.shremark = res.data.djdata.shremark;
							that.spzongjia = res.data.spzongjia;
							that.hkjhdata = res.data.hkjhdata;
							that.hkjhtip = res.data.hkjhtip;
							that.hkrcddata = res.data.hkrcddata;
							that.xqrcddata = res.data.xqrcddata;
							that.dtptxt = res.data.dtptxt;
							that.dtpjgtxt = res.data.dtpjgtxt;
							that.zmchbtntxt = res.data.zmchbtntxt;
							that.zschbtntxt = res.data.zschbtntxt;

							that.lsjrdata = res.data.lsjrdata;

							if (res.data.djdata.ddtype == 3 || res.data.djdata.ddtype == 4) {
								that.czfrisfr = 1;
							}

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			previewImagev: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;

				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},
			setspsta(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let da = {
					'spid': spid
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/danju/rkdshsetspsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})

			},



			jjtgsh() {
				this.jjtgshow = true;
			},


			spedit(e) {
				let spid = e.currentTarget.dataset.spid
				let djid = this.djid;
				uni.navigateTo({
					url: '/pages/work/ckgl/ckshop/spedit?spid=' + spid
				})
			},

			czryedit(e) {

				let that = this;
				let czfrspid = e.currentTarget.dataset.spid;
				let czryid = e.currentTarget.dataset.czryid;
				let czygid = e.currentTarget.dataset.czygid;
				let czygname = e.currentTarget.dataset.czygname;
				let czjine = e.currentTarget.dataset.czjine;
				let frbl = e.currentTarget.dataset.frbl;
				this.czryeditshow = true;
				this.thczjine = czjine;
				this.thfrbl = frbl;
				this.czfrspid = czfrspid;
				this.thczygid = czygid;
				this.thczygname = czygname;

			},


			gotoeckd() {
				let that = this;
				let tiptxt = '确认二次寄卖吗？'
				if (that.djdata.ddtype == 3) {
					tiptxt = '确认二次质押吗？'
				}

				uni.showModal({
					title: '',
					content: tiptxt,
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'djid': that.djid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/eckd', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let newdjid = res.data.newdjid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.redirectTo({
														url: '/pages/work/rukudan/edit?djid=' +
															newdjid
													})
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			delczry(e) {
				let that = this;
				let czryid = e.currentTarget.dataset.czryid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'czryid': czryid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdshdelczry', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},

			czrysettj() {
				let that = this;
				let djid = that.djid;
				let thczygid = that.thczygid;
				let czfrspid = that.czfrspid;
				let thczjine = that.thczjine;
				let thfrbl = that.thfrbl;
				let da = {
					djid: djid,
					spid: czfrspid,
					czryid: thczygid,
					czjine: thczjine ? thczjine : 0,
					frbl: thfrbl ? thfrbl : 0,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdshsetczry', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {

							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.czryeditshow = false;
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},






			qtfyedit(e) {

				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if (spname) {
					this.qtfymxspname = spname;
				}
				let djid = this.djid;

				uni.navigateTo({
					url: '/pages/work/rukudan/qtfyedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtfydel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtfydel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqyfylist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			czjlmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;

				this.czjlmxspname = spname ? spname : '';

				let djid = this.djid;
				if (!spid) {
					spid = 0;
				}
				this.getczjllist(djid, spid);
			},
			getczjllist(djid, spid) {
				let that = this;
				let da = {
					djid: djid,
					spid: spid,
					issh: 1,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/getczjllist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.czjldata = res.data.czjldata;
							that.czjlmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},


			qtfymx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtfymxspname = spname;
				this.getqyfylist(spid);
			},
			qtfysetvxq(e) {
				let thqtfyvid = e.currentTarget.dataset.id;
				if (thqtfyvid == this.thqtfyvid) {
					this.thqtfyvid = 0;
				} else {
					this.thqtfyvid = thqtfyvid;
				}

			},
			getqyfylist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtfylist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtfydata = res.data.qtfydata;
							that.qtfymxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},

			setsjjg(e) {
				let spid = e.currentTarget.dataset.spid;
				if (this.djdata.ddsta != 2) {
					return false;
				}
				uni.navigateTo({
					url: '/pages/work/ckgl/ckshop/sjjgset?spid=' + spid
				})
			},
			sjjgmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.sjjgmxspname = spname;
				this.getsjjglist(spid);
			},
			sjjgsetvxq(e) {
				let thsjjgvid = e.currentTarget.dataset.id;
				if (thsjjgvid == this.thsjjgvid) {
					this.thsjjgvid = 0;
				} else {
					this.thsjjgvid = thsjjgvid;
				}

			},
			getsjjglist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/spsjjglist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.sjjgdata = res.data.sjjgdata;
							that.sjjgmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},

			setjmdj(e) {
				let spid = e.currentTarget.dataset.spid;
				if (this.djdata.ddsta != 2) {
					return false;
				}
				uni.navigateTo({
					url: '/pages/work/ckgl/ckshop/jmdjset?spid=' + spid
				})
			},
			jmdjmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.jmdjmxspname = spname;
				this.getjmdjlist(spid);
			},
			jmdjsetvxq(e) {
				let thjmdjvid = e.currentTarget.dataset.id;
				if (thjmdjvid == this.thjmdjvid) {
					this.thjmdjvid = 0;
				} else {
					this.thjmdjvid = thjmdjvid;
				}

			},
			getjmdjlist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/spjmdjlist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.jmdjdata = res.data.jmdjdata;
							that.jmdjmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},



			qtcbedit(e) {

				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if (spname) {
					this.qtcbmxspname = spname;
				}
				let djid = this.djid;

				uni.navigateTo({
					url: '/pages/work/rukudan/qtcbedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtcbdel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtcbdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqycblist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			qtcbmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtcbmxspname = spname;
				this.getqycblist(spid);
			},
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}

			},
			getqycblist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtcblist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtcbdata = res.data.qtcbdata;
							that.qtcbmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			gotoddfwshview(e) {
				let djid = e.currentTarget.dataset.djid;
				uni.navigateTo({
					url: '/pages/user/caiwu/ddfwshview?shid=' + djid
				})
			},



			gotoyd(e) {
				let djtype = e.currentTarget.dataset.djtype;
				let djid = e.currentTarget.dataset.djid;
				if (djtype == 1) {
					uni.navigateTo({
						url: '/pages/work/rukudan/view?vsour=2&djid=' + djid
					})
				}
				if (djtype == 2) {
					uni.navigateTo({
						url: '/pages/work/chukudan/view?vsour=2&djid=' + djid
					})
				}
				if (djtype == 3) {
					uni.navigateTo({
						url: '/pages/work/rukudan/rzdview?vsour=2&djid=' + djid
					})
				}
			},





		}
	}
</script>

<style lang="scss">
	.sqbdcon {}

	.xqbtnbbs {
		height: 60upx;
		line-height: 60upx;
		border-radius: 100upx;
		background: #FFD427;
		font-size: 24upx;
		padding: 0 20upx;
		position: absolute;
		right: 0;
		top: 20upx;
	}


	.dbczbtn {
		height: 85upx;
		line-height: 85upx;
		background: #FFD427;
		border-radius: 10rpx;
		margin: 0 22upx;
		color: #333;
		text-align: center;
		font-size: 24upx
	}

	.dbczbtn.db2 {
		margin: 0 12upx;
	}

	.dbczbtn .iconfont {
		margin-right: 10upx
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inpcon .colorhs {
		color: #ff0000
	}

	.rowx .inpcon .colorls {
		color: #1677FF
	}

	.rowx .inp {}

	.rowx .czjlbtn {
		background: #FFD427;
		height: 50upx;
		line-height: 50upx;
		border-radius: 100upx;
		padding: 0 15upx;
		margin-top: 18upx
	}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		position: relative;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
	}

	.spitem .spbjbtn {
		position: absolute;
		right: 20upx;
		top: 20upx;
		color: #1677FF
	}

	.spitem .spbjbtn .iconfont {
		margin-right: 10upx
	}








	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		padding-right: 100upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #F0F0F0;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .czvvcon {
		margin-top: 20upx
	}

	.spitem .czvvcon .czv1 {
		float: right;
	}

	.spitem .czvvcon .czv2 {
		float: left;
		color: #ff6600;
		text-align: left;
		font-size: 24upx;
		height: 50upx;
		line-height: 50upx;
	}

	.spitem .czvvcon .czv1 .zmbtn {
		height: 50upx;
		line-height: 50upx;
		border-radius: 100upx;
		background: #FFD427;
		font-size: 24upx;
		padding: 0 20upx;
		float: left;
		margin-left: 15upx
	}

	.spitem .czvvcon .czv1 .zmbtn .iconfont {
		font-size: 24upx;
		display: none;
	}

	.spitem .czvvcon .czv1 .zmbtn.bt1 {
		background: #63b8ff;
		color: #fff
	}

	.spitem .czvvcon .czv1 .zmbtn.bt2 {
		background: #ddd;
	}

	.spitem .viewxqcon {}

	.spitem .czfrcon {
		border-top: 1px dashed #FFD427;
		margin-top: 25upx;
		padding: 25upx 0
	}

	.spitem .czfrcon .tit {
		float: left;
		font-size: 28upx;
		font-weight: 600
	}

	.spitem .czfrcon .tit .iconfont {
		margin-right: 10upx;
		color: #ff0000
	}

	.spitem .czfrcon .tadd {
		float: right;
	}

	.spitem .czfrcon .tadd .iconfont {
		margin-right: 10upx;
		color: #1677FF
	}


	.spitem .status {
		border-radius: 30upx;
		padding: 5upx 15upx;
		color: #fff;
		background: #ccc;
		font-size: 24upx
	}

	.spitem .status.sta1 {
		background: #54D216;
	}

	.spitem .status.sta12 {
		background: #54D216;
	}

	.spitem .status.sta2 {
		background: #FF6600;
		color: #fff
	}

	.spitem .status.sta3 {
		background: #FF2828;
		color: #fff
	}

	.spitem .status.sta8 {
		background: #888;
	}

	.spitem .status.sta9 {
		background: #c76096;
		color: #fff
	}

	.spitem .sjstatus.sta1 {
		color: #43b058;
	}

	.spitem .sjstatus.sta2 {
		color: #888;
	}

	.czrli {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		position: relative;
		border-bottom: 1px solid #eee;
	}

	.czrli .del {
		position: absolute;
		right: 10upx;
		top: 0upx;
	}

	.czrli .del .iconfont {}

	.czrli .cname {
		font-weight: 600;
		margin-bottom: 10upx;
	}

	.czrli .cname .iconfont {
		margin-right: 10upx;
		color: #1677FF
	}

	.czrli .cname .czjs {
		margin-left: 20upx;
		color: #ff6600;
		font-size: 24upx;
	}

	.czrli .czxm {
		float: left;
		width: 44%;
		font-size: 24upx;
	}

	.czrli .czxm text {
		color: #1677FF
	}

	.czrli .czxm .hs {
		color: #ff0000
	}

	.czrli .czxm2 {
		float: right;
		color: #1677FF;
		width: 10%;
		;
		font-size: 24upx;
		text-align: right;
	}

	.czrli:last-child {
		margin-bottom: 0;
		border-bottom: 0
	}




	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 0upx;
		position: relative;
	}

	.zjzkicon {
		position: absolute;
		right: 0;
		top: 0;
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}


	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}



	.cklist {}

	.cklist .item {
		padding: 28upx;
		position: relative;
		border-bottom: 1px solid #eee;
	}

	.cklist .item .xzbtn {
		position: absolute;
		right: 30upx;
		top: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		font-size: 24upx;
	}

	.cklist .item .tx {
		float: left
	}

	.cklist .item .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.cklist .item .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 10upx;
	}

	.cklist .item .name {
		font-size: 28rpx;
		height: 40upx;
		line-height: 40upx;
	}

	.cklist .item .dizhi {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.cklist .item .dizhi text {
		color: #F7B52C;
		font-size: 24rpx;
		margin-top: 10upx;
		margin-right: 5upx
	}

	.cklist .item:last-child {
		border-bottom: 0
	}


	.cznrcon {
		padding: 40upx;
	}

	.czry4info {
		margin-top: 20upx
	}

	.addotfybtn {
		color: #1677FF;
		border-radius: 0upx;
		font-size: 24upx;
		margin-left: 25upx
	}

	.czjllist {
		padding: 30upx
	}

	// .czjllist .item{border-bottom:1px dashed #ddd;padding:30upx}
	// .czjllist .item .t1{margin-bottom:10upx}
	// .czjllist .item .t2 .jine{color:#ff0000}
	// .czjllist .item:last-child{border-bottom:0}




	.fwshlist {
		padding: 10upx 0
	}

	.fwshlist .item {
		background: #FFFFFF;
		margin-bottom: 20upx;
		position: relative;
		font-size: 24upx;
		padding-bottom: 20upx;
		border-bottom: 1px solid #eee;
	}

	.fwshlist .item .yfjine {
		color: #ff0000;
	}

	.fwshlist .item .ddtype {
		font-size: 20upx;
		border-radius: 0 40upx 40upx 0;
		height: 42upx;
		line-height: 42upx;
		padding: 5upx 15upx 5upx 10upx;
		color: #0EC45C;
		margin-right: 10upx;
		margin-left: -8upx;
		border-left: 8upx solid #FFD427
	}

	.fwshlist .item .inf0 {
		padding: 0 20upx 0 0;
		height: 70upx;
		line-height: 70upx;
	}

	.fwshlist .item .inf0 .tf1 {
		float: left;
	}

	.fwshlist .item .inf0 .tf2 {
		float: right
	}

	.fwshlist .item .inf1 {
		line-height: 40upx;
		font-size: 24upx
	}

	.fwshlist .item .inf1 .khname {
		font-weight: 700;
		margin-right: 10upx;
		color: #333
	}

	.fwshlist .item .inf1 .khid {
		color: #888
	}

	.fwshlist .item .setbtn3 {
		position: absolute;
		right: 15upx;
		bottom: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fafafa;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		border: 1px solid #eee
	}

	.fwshlist .item:last-child {
		border-bottom: 0;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.hkhjcon {
		position: absolute;
		right: 20upx;
		top: -70upx;
		background: #fff;
		border-radius: 100px;
		padding: 0 30upx;
		height: 55upx;
		line-height: 55upx;
		border: 1px solid #FFD427;
		font-size: 24upx;
	}

	.hkhjcon text {
		color: #ff0000
	}
</style>