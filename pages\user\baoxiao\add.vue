<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >费用报销单</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
		
		<block v-if="systixian.istx!=1" >
		     <view class="matxcont">
			  <view  class="notixiancon" >
				  <view class='tipcon'>
					  <text class="iconfont icon-tishi3"></text>
				  </view>
				  <view class='tipsmzcon'>
					  <view class='tipwz'>
						  <view class='t1'>{{tipwztxt}}</view>
					  </view>
				  </view>
				  <view class="smlink" @tap="toback" >
					  【查看报销记录】
				  </view>
				  <view class='wsarcon' style="padding:50px">
					  <view class='wanshanbtn'>
						  <button @tap="toback" class="bomsubbtn" >
							 返回
						  </button>
					  </view>
				  </view>
			  </view>
			</view>
		</block>
     	<block v-else>	
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
								
										<view class="maxcon" style="padding:10upx 28upx">
											<view class="rowx" @click="seldianpu"> 
												<view class="icon"><text class="iconfont  icon-jiantou"></text></view>
												<view class="tit">报销门店</view>
												<view class="inpcon"><view class="inp">{{mdname ? mdname : '请选择门店' }}</view></view>
												<view class="clear"></view>
											</view>
										</view>
										
										<view class="maxcon">
												<view class="vstit">申请报销金额</view>
												<view class="jinecon">
													<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
													<input  type="digit"  name='jine'  placeholder='在此输入报销金额' maxlength='60' class="inputss" />
												</view>
										</view>
										<view class="maxcon">
												<view class="vstit">申请说明</view>
												
												<view class="bzcon">
													  <textarea name="remark" class="beizhu"></textarea>
												</view>
									
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx"> 
																<block v-for="(item,index) in img_url_ok" :key="index">
																   <view class='item'  >
																	   <view class="del" @tap="deleteImg"   :data-index="index" ><i class="iconfont icon-shanchu2"></i></view>
																	   <image  :src="staticsfile+item" :data-src='staticsfile + item ' @tap='previewImage' ></image>
																   </view>
																</block>
															   <view class='item' v-if="img_url_ok.length <= 8" >
																   <image @tap="chooseimage"  src='/static/images/uppicaddx.png'></image>
															   </view>
															   <view class="clear"></view>
													 </view>
												</view>		
												 
												 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  			<view class="tn-flex tn-footerfixed">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>提交</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
				
				
		</block>		
		
			

	
	</view>
  
	<view style="height:120upx"></view>
    
	
	<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
		<view class="popuptit">选择店铺</view>
	    <scroll-view scroll-y="true" style="height: 800rpx;">
	          <view class="seldplist">
	              <block v-for="(item,index) in dpdata" :key="index">
					  
					  <view class="item" @click="selthdp" :data-dpid="item.id"  :data-dpname="item.title"  >
						    <view class="xzbtn"  >选择</view>
					  		<view class="tx"><image :src="staticsfile + item.logo"></image></view>
					  		<view class="xx">
					  			<view class="name">{{item.title}}</view>
					  			<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
					  		</view>
					  		<view class="clear"></view>
					  </view>
					  
			      </block>		  
	          </view>
	        </scroll-view>
	</tn-popup>
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 ygdata:'',
			 status:0,
			 jine:0,
			 img_url: [],
			 img_url_ok:[],
			 systixian:'',
			 tipwztxt:'',
			  dpdata:'',
			  dpselshow:false,
			  mdid:'',
			  mdname:'',
		  }
	},

	onLoad(options) {
		let that=this;
		this.loadData();
		
	},
	onShow() {
		
	},

    methods: {
		
		seldianpu(){
		 	this.dpselshow=true;
		 },
		selthdp(e){
		 let dpid=e.currentTarget.dataset.dpid;
		 let dpname=e.currentTarget.dataset.dpname;
		 this.mdid=dpid;
		 this.mdname=dpname;
		 this.dpselshow=false;
		},
					
		
		toback:function(){
		   uni.navigateBack();
		},
					   
		loadData(){
				  let that=this;
				  let da={
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/muser/ygbaoxiao', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 		        let systixian=res.data.systixian;
				 		         //如果暂不允许报销则提示
				 		         if(systixian.istx!=1){ 
									  that.isnotxtip=false;
									  that.tipwztxt='系统维护中，暂停报销!';
				 		         }
								 that.systixian=systixian;
								 that.dpdata=res.data.dpdata;
								 that.mdid=res.data.mdid;
								 that.mdname=res.data.mdname;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			chooseimage(){
								   let that = this;
								   let img_url_ok=that.img_url_ok;
								   uni.chooseImage({
									   count: 9, //默认9
									   sizeType: ['original', 'compressed'],
									   success: (resx) => {
										  const tempFilePaths = resx.tempFilePaths;
										  
										
												  for(let i = 0;i < tempFilePaths.length; i++) {
														  let da={
															  filePath:tempFilePaths[i],
															  name: 'file'
														  } 
														  uni.showLoading({title: '上传中...'})
														  this.$req.upload('/v1/upload/upfile', da)
																  .then(res => {
																	uni.hideLoading();
																	// res = JSON.parse(res);
																	if(res.errcode==0){
																		img_url_ok.push(res.data.fname);
																		that.img_url_ok=img_url_ok;
																		
																		uni.showToast({
																			icon: 'none',
																			title: res.msg
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
														  
														  })
												}
										   
									   }
								   });
							   
							},
							previewImage: function (e) {
									  let that = this;
									  let src = e.currentTarget.dataset.src;
									  let img_url_ok=that.img_url_ok;
									  let imgarr=[];
									  img_url_ok.forEach(function(item,index,arr){
										  imgarr[index] = that.staticsfile+item;
									  });
									  wx.previewImage({
										  current: src,
										  urls: imgarr
									  });
							},
							deleteImg: function (e) {
								let that = this;
								let index = e.currentTarget.dataset.index;
								let img_url_ok = that.img_url_ok;
								img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
								that.img_url_ok=img_url_ok;
							},
		
		
		
			formSubmit: function(e) {
						let that=this;
					   var formdata = e.detail.value
						
							
									 let pvalue = e.detail.value;
									 let lng = that.lng ;
									 let lat = that.lat;
									 
										 if(!that.mdid || that.mdid==0){
										   uni.showToast({
											icon: 'none',
											title: '请选择门店',
										   });
										   return false;
										 }
									 
										if(!pvalue.jine || pvalue.jine < 0){
										  uni.showToast({
											icon: 'none',
											title: '请输入报销金额',
										  });
										  return false;
										}
										
										if(!pvalue.remark){
										  uni.showToast({
											icon: 'none',
											title: '请输入申请说明',
										  });
										  return false;
										}
									  
								       uni.showModal({
													title: that.mdname,
													content: '报销的费用通过审核后将直接增加到余额中，可以申请提现取出，以上信息已确认无误并提交吗？',
													success: function(e) {
														//点击确定
														if (e.confirm) {
												                   uni.showLoading({title: '处理中...'})
																   let da={
																		'jine': pvalue.jine,
																		'remark': pvalue.remark ? pvalue.remark : '',
																		'pictures': that.img_url_ok ? that.img_url_ok  : '' ,
																		'mdid': that.mdid ? that.mdid  : 0 ,
																		'mdname': that.mdname ? that.mdname  : '' ,
																   }
																   that.$req.post('/v1/muser/ygbxsave', da)
																  .then(res => {
																	uni.hideLoading();
																	console.log(res);
																	if(res.errcode==0){
																		uni.showToast({
																			icon: 'none',
																			title: res.msg,
																			success() {
																				setTimeout(function(){
																					uni.navigateBack()
																				},1000)
																			}
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
																	
																  })
																  
															
														}
													}
								})	  
									  
									  
									  
									  
									  
									  
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.toplinex{border-top:1px solid #fff}

.matxcont{margin:32upx;padding:32upx;border-radius:20upx;background: #fff;min-height: 500upx;}
.notixiancon{text-align: center;}
.tipcon{padding:50px 0}
.tipcon text{font-size:55px;color:#FFD427}
.smlink{margin-top:40px}

.ttinfo{position: relative;}
.ttinfo .icon{position: absolute;right:0upx;top:30upx}
.ttinfo .icon .iconfont{color:#ddd}
.ttinfo .inf1{text-align: center;border-bottom:1px solid #F0F0F0;padding-bottom:32upx;margin-bottom:32upx}
.ttinfo .inf1 .tx{}
.ttinfo .inf1 .xx{padding-top:10upx}
.ttinfo .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;margin:0 auto;}
.ttinfo .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.ttinfo .inf1 .tel{color: #7F7F7F;font-size: 24rpx;margin-top:10upx}
.ttinfo .inf1 .tel text{margin-right:30upx}
.ttinfo .inf2{text-align: center;padding-bottom:30upx}
.ttinfo .inf2 .kyjine{font-size: 48rpx;font-weight: 600;}
.ttinfo .inf2 .tmsm{color: #7F7F7F;margin-top:20upx;font-size: 24rpx;}


.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{margin-bottom:20upx}


.sqbdcon{margin:32upx;}


.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.rowxmttp{margin-top:30upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:200}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}

.jinecon{position: relative;margin:40upx 0}
.jinecon .icon{position: absolute;left:0;top:10upx;}
.jinecon .icon .iconfont{font-size:40upx}
.jinecon .inputss{font-size:40upx;color:#000;padding-left:40upx}



.rowx{
	position: relative;
	border-bottom:1px solid #F0F0F0;
}
.rowx .icon{position: absolute;right:0;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{color: #7F7F7F;}
.rowx:last-child{border-bottom:0;}

 
</style>

