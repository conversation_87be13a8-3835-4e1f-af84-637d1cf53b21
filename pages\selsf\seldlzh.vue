<template>
  <view class="page-e tn-safe-area-inset-bottom">
		
		
		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100" >
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				{{sjdata.title}}
			</view>
			<view slot="right">
		 
			</view>
		</tn-nav-bar>
		 <view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 20 + 'px'}" ></view>
		
		<view class="tmdname">{{smdpname}}</view>
		
		<view class="tstip">
			{{selyghtip}} 
		</view>
		
		<view class="maxcon" >
			<view class="mydpzhlis" >
					<block v-for="(itemx,indexx) in mydpdata.glsfdata" :key="indexx">
						 <view class="sfitem">
							 <view class="tit" >{{itemx.realname}}
							     <text class="bh">({{itemx.zzhuid}})</text>
								 <text class="iconfont icon-chaojiguanliyuan cjglicon" v-if="itemx.isadmin==1 "></text>
							 </view>
							 <view class="zss" v-if="itemx.mdname">店铺名称：{{itemx.mdname}}</view>
							 <view class="zss"  v-if="itemx.jiaosetxt">店铺角色：<text class="jstxt">{{itemx.jiaosetxt}}</text></view>
							 <view class="zss cjboss"  v-if="itemx.jiaosetxt2">超级角色：<text class="jstxt">{{itemx.jiaosetxt2}}</text></view>
							 									
							 
							 
							 <view class="jrbtn" @click="jrdp"  :data-ygid="itemx.id" :data-mdid="itemx.mdid">登录</view>
						 </view>
					</block>
			</view>
		</view>
		
		
		
		<view class="tn-flex tn-footerfixed">
		  <view class="tn-flex-1 justify-content-item tn-text-center">
			<button class="bomsubbtn"  style="width:50%;margin:0 auto;border-radius:100px;font-size:28upx" @click="gotoaddygh">
			   <text class="iconfont icon-jia" style="margin-right:10upx;font-size:28upx"></text> <text>继续注册新的员工号</text>
			</button>
		  </view>
		</view>
	

    <view style="height:120upx"></view>
	
	
	
  </view>
</template>

<script>

  export default {
    data() {
      return {
		  staticsfile: this.$staticsfile,
		  istopback:false,
		  mydpdata:'',
		  xsjid:0,
		  sjdata:'',
		  sbma:'',
		  selyghtip:'',
		  smdpname:'',
      }
    },
	
	onLoad(options){
		let that=this;
		let xsjid=options.xsjid ? options.xsjid : 0;
		let sbma=options.sbma ? options.sbma : '';
		this.xsjid=xsjid;
		this.sbma=sbma;
	},
	onShow(){
		let that=this;
		that.loadData();
	},

    methods: {
		goBack(){
			uni.navigateBack()
		},
		
		
		jrdp(e){
			let that=this;
			let ygid = e.currentTarget.dataset.ygid;
			let mdid = e.currentTarget.dataset.mdid;
			let da={
				ygid:ygid,
				mdid:mdid,
			};
			uni.showNavigationBarLoading();
			this.$req.post('/v1/selsf/jrdp', da)
			      .then(res => {
			         console.log(222,res);
				    uni.hideNavigationBarLoading();
					if(res.errcode==0){
							that.$tool.setTokenStorage(res.data.tokendata);
							setTimeout(function(){
								uni.switchTab({
								   url: '/pages/index/index',
								})
							},500)
					}else{
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
					}
			      })
		},
		
		loadData() {
			let that=this;
			let da={
				xsjid:that.xsjid ,
				ygyqma:that.sbma 
			};
			uni.showNavigationBarLoading();
			this.$req.post('/v1/yggl/seldlzh', da)
			      .then(res => {
			         console.log(222,res);
				    uni.hideNavigationBarLoading();
					if(res.errcode==0){
							that.mydpdata = res.data.mydpdata;
							that.sjdata = res.data.sjdata;
							that.selyghtip = res.data.selyghtip;
							that.smdpname = res.data.smdpname;
							
					}else{
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
					}
			      })
		},
			
		gotoaddygh(e){
			uni.navigateTo({
				url:'/pages/selsf/addyginfo?ygyqma='+this.sbma
			})
		},
    
    }
  }
</script>

<style lang="scss" scoped>
  .page-e{
    max-height: 100vh;
  } 
  
  .maxcon{margin:20upx 32upx 0 32upx ;border-radius: 10px;background: #fff;}
  
  .mydpzhlis{padding:0 32upx;}
  .mydpzhlis .sfitem{border-top:1px solid #F0F0F0;padding:32upx 0;position: relative;}
  .mydpzhlis .sfitem .tit{font-size:28upx;color:#333}
  .mydpzhlis .sfitem .zss{font-size:24upx;color:#7F7F7F;margin-top:5upx}
  .mydpzhlis .sfitem .jrbtn{width: 120rpx;height: 66rpx;line-height: 66rpx;background: #FFD427;border-radius: 8rpx;text-align: center;font-size: 28rpx;position: absolute;right:0;top:40upx}
  .mydpzhlis .sfitem .js{font-size:24upx;}
  .mydpzhlis .sfitem .bh{font-size:24upx;color:#888;margin-left:10upx}
  .mydpzhlis .sfitem .cjglicon{color:#ff6600}
  .mydpzhlis .sfitem .zss.cjboss .jstxt{color:#ff0000}

.tstip{text-align:center;color:#888}
.tmdname{text-align: center;font-size:32upx;margin-bottom:30upx;font-weight:700}

</style>
