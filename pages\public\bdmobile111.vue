<template>
	<view class="login">
		
		<tn-nav-bar fixed alpha customBack>
		  <view slot="back" class='tn-custom-nav-bar__back2' @click="goBack">
			<text class='icon iconfont icon-fanhui'></text>
		  </view>
		</tn-nav-bar>
		 
		<view class="login">
		
				  <view class="login__bg login__bg--top"><image class="bg" src="/static/images/loginbg.jpg" mode="widthFix"></image></view>
		          <view class="topk"></view>
		
					<view class="login-type-content">
						
						
						<view class="dltype">
							<view class="item " >
								<view class="text">微信登录绑定手机号</view>
								<view class="line"></view>
							</view>
						</view>
						
						
						<view class="main">
								<view class="login-type-form">
									<view class="input-item">
										<input class="login-type-input"	type="number"  name="mobile"	v-model="mobile"  placeholder="请输入您的手机号码"	maxlength="50"	/>
									</view>
									<view class="input-item input-item-sms-code">
										<input class="login-type-input"	type="number"  name="mscode"	v-model="mscode"  placeholder="请输入手机验证码"	maxlength="6"	/>
										<button :disabled='disabled'	class="sms-code-btn" @tap.stop="getVerificationCode()">
											<text>{{codename}}</text>
										</button>
									</view>
								</view>
								<view>
									<button	class="confirm-btn bg-active tn-main-gradient-indigo" 	:disabled="btnLoading"	:loading="btnLoading"	@tap="toRegister" >确认提交</button>
								</view>
						

						</view>
						
						
						
					</view>
				
		 </view>		
			
	</view>
</template>
<script>
	
	
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				clicksta:false,
				logininfo:'',
				mobile: '',
				password: '',
				repassword: '',
				disabled:false,
				btnLoading: false,
				userInfo: null,
				appName: '',
				codename:'获取验证码',
				mscode:'',
				reqBody: {},
				codeSeconds: 0, // 验证码发送时间间隔
				smsCodeBtnDisabled: true,
				// 是否显示密码
				showPassword: false,
				// 倒计时提示文字
				tips: '获取验证码',
				ydxysta:false,
				
				clientid:'',
				appver:'',
				apptype:0,
				
			};
		},
		onShareAppMessage(res) {
					return {
					  title: '',
					  path: ''
					}
		},
		
		
		onLoad(options) {
			// this.loadData();
		},
		
		watch: {
		
		},
		onShow() {
			let that=this;
			// #ifdef APP-PLUS
			  let clientid=plus.push.getClientInfo().clientid;
			   that.clientid=clientid ? clientid : ''; 
			  
			   plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
			   that.appver=widgetInfo.version;
			   let platform = uni.getSystemInfoSync().platform
			     if(platform=='android'){
					 that.apptype=1;
				 }else{
					 that.apptype=2;
				 }
			  });
			  
			// #endif
			 
		},
		
		methods: {
			goHome(){
				uni.navigateTo({
					url:'/pages/index/index'
				});
			},
			goBack(){
				 uni.navigateBack()
			},
			
		
			// 统一跳转路由
			navTo(url) {
		      uni.navigateTo({ url });
			},
			
			toRegister() {
			   let that=this;
			   this.ydxysta=true;
			   let tgsbma=uni.getStorageSync('tgsbma');
			   if(!tgsbma){tgsbma='';}
			   let qdsbma=uni.getStorageSync('qdsbma');
			   if(!qdsbma){qdsbma='';}
			   
			   
			    let wxlogininfo=uni.getStorageSync('wxlogininfo');
				if(!wxlogininfo){
					uni.showToast({
						icon: 'none',
						title: '信息有误，请重新授权',
						success() {
							setTimeout(function(){
								wx.navigateBack()
							},1000)
						}
					});
					return false;
				}
				let openid=wxlogininfo.openid;
				let unionid=wxlogininfo.unionid;
				let avatar=wxlogininfo.avatar;
				let nickname=wxlogininfo.nickname;
				
				
			
				   if (this.mobile.length != 11) {
				   	uni.showToast({ 
				   		icon: 'none',
				   		title: '请输入手机号'
				   	});
				   	return false;
				   }
					if (this.mscode.length <= 0) {
						uni.showToast({
							icon: 'none',
							title: '请输入验证码'
						});
						return false;
					}
			  
			if (this.clicksta) return;this.clicksta = true;
			   uni.showLoading({title: ''})
			  
			  let da={
				  mobile: this.mobile,
				  mscode: this.mscode,
				  tgsbma: tgsbma || '',
				  qdsbma: qdsbma || '',
				  openid: openid,
				  unionid: unionid,
				  avatar: avatar,
				  nickname: nickname,
				  clientid: that.clientid || '' ,
				  apptype: that.apptype ||  '' ,
				  appver: that.appver ||  '' ,
			  }
			   this.$req.post('/v1/login/wxbdmobile',da)
			   		.then(res => {
			   			uni.hideLoading();
			   					
			   			if(res.errcode!=0){
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
							that.clicksta=false;
			   			}else{
			   				this.$tool.setTokenStorage(res.data.tokendata);
							uni.setStorageSync('wxlogininfo','');
			   				uni.showToast({
			   					icon: 'none',
			   					title: res.msg,
			   					success() {
			   						setTimeout(function(){
										
										if(res.data.gotowszl==1){
											uni.redirectTo({
											   url: '/pages/user/regwszl',
											})
										}else{
											uni.switchTab({
											   url: '/pages/index/index',
											})
										}
										
										
			   						},1000)
			   					}
			   				});
			   			}
			   			
			   		})
			   		.catch(err => {
			   	     	that.clicksta=false;
			   		})
			   
			},
			
			  getCode(){
			      let mobile = this.mobile;
			      let that = this;
			      // var myreg = /^(14[0-9]|13[0-9]|15[0-9]|17[0-9]|18[0-9])\d{8}$$/;
			      if (mobile == "") {
			        uni.showToast({
			          title: '手机号不能为空',
			          icon: 'none',
			          duration: 1000
			        });
			        return false;
			      }
			      if (mobile.length!=11) {
			        uni.showToast({
			          title: '请输入正确的手机号',
			          icon: 'none',
			          duration: 1000
			        });
			        return false;
			      }
			  
			        // 发起网络请求
			        let da = {
			          'mobile': mobile,
			          'fstype': 6
			        };
			  	  uni.showLoading({ 'title': '' });
			  	  this.$req.post('/v1/login/getmscode', da)
			  	  	.then(res => {
			  	  
			  			uni.hideLoading();
			  	  		if (res.errcode == 0) {
			  				
			  	  			 that.disabled= true;
			  	  			 uni.showToast({
			  	  			   title: res.msg,
			  	  			   icon: 'none', 
			  	  			   duration: 1000,
			  	  			   success: function () {
			  	  			     var num = 61;
			  	  			     var timer = setInterval(function () {
			  	  			       num--;
			  	  			       if (num <= 0) {
			  	  			         clearInterval(timer);
			  	  			           that.codename='重新发送';
			  	  			           that.disabled= false;
			  	  			       } else {
			  						    that.codename= num + "s"
			  	  			       }
			  	  			     }, 1000)
			  	  			 			
			  	  			 			
			  	  			   }
			  	  			 })
			  	  				
			  	  		} else {
			  	  			uni.showToast({
			  	  			  title: res.msg,
			  	  			  icon: 'none'
			  	  			})
			  	  		}
			  	  
			  	  	})
			   
			    },
			    //获取验证码
			    getVerificationCode() {
			      this.getCode();
			    },
			    
			
		
			
			
			
		}
	};
</script>
<style lang="scss">
	page {
		background: #fff;
	}

	
	.login {
		
		position: relative;
		height: 100%;
		z-index: 1;
		
		
		/* 背景图片 start */
		&__bg {
		  z-index: -1;
		  position: fixed;
		  
		  &--top {
		    top: 0;
		    left: 0;
		    right: 0;
		    width: 100%;
		    
		    .bg {
		      width: 750upx;
		      will-change: transform;
		    }
		  }
		
		}
		/* 背景图片 end */
		
		.topk{height:380upx}
		
		.dltype{}
		.dltype .item{float:left}
		.dltype .item .text{
	
			height: 74upx;
			font-size: 52upx;
			font-weight: 600;
			color: #333333;
			line-height: 74upx;
		}
		.dltype .item .line{
			width: 60upx;
			height: 8upx;
			background: #3366FF;
			border-radius: 4upx;
			margin:10upx auto;
		}
		.dltype .item.nohover  .text{
			font-size: 44rpx;font-weight: 600;color: #7F7F7F;line-height: 80rpx;
		}
		
	.yszc{margin-top:20upx;text-align: center;}
	.yszc .nr{font-size:24upx;color:#B4B3B3}
	.yszc .nr span{color:#3366FF}
	.yszc .nr text{margin-right:10upx;font-size:24upx}
	.yszc .nr .hoverss{color:#3366FF}
		
		.wxdlcon{text-align: center;margin-top:100upx}
		.wxdlcon image{width: 96upx;height:96upx}
		.wxdlcon .txt{height:28upx;line-height:28upx;font-size:20upx;margin-top:10upx}
		
		.login-type-content {
			margin:0 100upx;
			position: relative;
			.main {
				position: absolute;
				width: 100%;
				top: 160upx;
				.login-type-form {
					.input-item {
						position: relative;
						height: 90upx;
						line-height: 90upx;
						margin-bottom: 30upx;
						font-size:28upx;
						color:#7F7F7F;
						.login-type-input {
							height: 90upx;
							border-bottom: 1upx solid #F2F2F2;
						}
					}
					.tricon{position: absolute;right:0;top:0;z-index:100;}
					.tricon .iconfont{font-size:44upx}
				}
				.confirm-btn {
				  margin-top: 60upx;
				  height: 80upx;
				  line-height: 80upx;
				  border-radius:100upx;
				  background: #3366FF;
				}
			}
		}
	
	    .dlmmcon{height: 28upx;font-size: 20upx;line-height:28upx;color: #3366FF;} 
	    .dlmmcon .df1{float:left} 
	    .dlmmcon .df2{float:right} 

    
	}
	
	
	// 发送验证码样式
	.input-item-sms-code {
	
	  .sms-code-btn {
	      position: absolute;right:0;top:10upx;z-index: 200;background:#fff;color: #3366FF;font-size:28upx;padding:0
	  }
	
	  .sms-code-resend {
	    color: #666;background:#fff;
	  }
	}
	
	
 /* 胶囊*/
  .tn-custom-nav-bar__back2 {

    height: 100%;
    position: relative;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-sizing: border-box;
    font-size: 18px;
    
    .icon {
      display: block;
      flex: 1;
      margin: auto;
      text-align: center;
	  font-size:45upx
    }
    
   
  }
	
	
	
	
	
</style>
