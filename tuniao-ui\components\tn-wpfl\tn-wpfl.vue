<template>
	<view v-if="value" class="tn-modal-class tn-modal">
		<tn-popup v-model="value" mode="bottom" :popup="false" :borderRadius="radius" :width="width" :zoom="zoom"
			:height="'80%'" :zIndex="zIndex" :closeBtn="showCloseBtn" @close="close">
			<!-- 内容 -->
			<view>
				<view class="wpfl_topss">
					<view class="stt1">物品分类</view>
					<view class="stt2">
						<view class="sscon90">
							<text class="iconfont icon-sousuo2 icon1"></text>
							<block v-if="issearch==true">
								<text class="iconfont icon-cuohao02 icon2" @click="clskw"></text>
							</block>
							<input :placeholder="'物品分类名称'" class="inpsaa" v-model="thkwtt" name="input"
								confirm-type="search" @confirm="confirmsearch" placeholder-style="color:#AAAAAA"
								maxlength="50"></input>
						</view>
					</view>
					<view class="clear"></view>
				</view>


				<block v-if="issearch">
					<block v-if="!searchresdata">
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无相关分类</view>
						</view>

					</block>
					<block v-else>

						<scroll-view class='scroll_search' scroll-y="true">
                         	<view class="zlcon22"> 
                         		<block v-for="(item,index) in searchresdata" :key="index">
                         			<view class="flitem" @click="xzsearchxm" :data-sortid="item.id"
                         				:data-sortname="item.sortname" :class="sortid == item.id ? 'hoverss' : ''">
                         				<view class="con">
                         					{{item.psortname}}
                         				</view>
                         			</view>
                         		</block>
                         		<view class="clear"></view>
                         	</view>
                        </scroll-view>

					</block>
				</block>
				<block v-else>

					<!--左侧栏-->
					<scroll-view class='scroll_left' scroll-y="true">
						<view class="nav_left">
							<block v-for="(item,index) in wpflsort1" :key="index">
								<view class="nav_left_items" :class="sortid1 == item.id ? 'active' : ''"
									@click="setsort2" :data-sortid1="item.id" :data-sortname1="item.sortname">
									<view class="con"
										:style="sortid1 == item.id ? 'color:#ff6600;border-left-color:#ff6600 ' : ''">
										{{item.sortname}}
									</view>
								</view>
							</block>
						</view>
					</scroll-view>

					<scroll-view scroll-y="true" class="scroll_right sv">

                       <view class="thpasortcon" v-if="thpasort" @click="backsort">
					     <text class="iconfont icon-fanhui" style="margin-right:10upx;"></text>
					     {{thpasort.sortname}}
					   </view>
					   <view class="clear"></view>
 

						<block v-if="wpflsort2 ">
							<view class="zlcon" >
								<block v-for="(item,index) in wpflsort2" :key="index">
									<view class="flitem" @click="setsort3" :data-sortid2="item.id"
										:data-sortname2="item.sortname" :class="sortid2 == item.id ? 'hoverss' : ''">
										<view class="con">
											{{item.sortname}}
										</view>
									</view>
								</block>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="wpflsort3">
							<view class="zlcon">
								<block v-for="(item,index) in wpflsort3" :key="index">
									<view class="flitem" @click="setsort4" :data-sortid3="item.id"
										:data-sortname3="item.sortname" :class="sortid3 == item.id ? 'hoverss' : ''">
										<view class="con">
											{{item.sortname}}
										</view>
									</view>
								</block>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="wpflsort4">
							<view class="zlcon">
								<block v-for="(item,index) in wpflsort4" :key="index">
									<view class="flitem" @click="setsort5" :data-sortid4="item.id"
										:data-sortname4="item.sortname" :class="sortid4 == item.id ? 'hoverss' : ''">
										<view class="con">
											{{item.sortname}}
										</view>
									</view>
								</block>
								<view class="clear"></view>
							</view>
						</block>
                        <view style="height:130upx"></view>

						<view class="clear"></view>
					</scroll-view>

				</block>


			</view>
            
				<view class="tn-flex tn-footerfixed" v-if="sortid > 0">
					<view style="position: absolute;right:230upx;bottom:0">
						<button @click="closeth"
							style="font-size:14px;background:#fff;border-radius:100px;color:#333;border:1px solid #ddd;">
							<text>取消</text>
						</button>
					</view>
					<block v-if="issearch==true">
						<view style="position: absolute;right:30upx;bottom:0">
							<button @click="qrxz22" style="font-size:14px;background:#ff6600;border-radius:100px;color:#fff">
								<text>确认选择</text>
							</button>
						</view>
					</block>
					<block v-else>
						<view style="position: absolute;right:30upx;bottom:0">
							<button @click="qrxz" style="font-size:14px;background:#ff6600;border-radius:100px;color:#fff">
								<text>确认选择</text>
							</button>
						</view>
					</block>
				</view>
		

		</tn-popup>
	</view>
</template>

<script>
	export default {

		name: 'tn-wpfl',
		props: {
			// 显示控制
			value: {
				type: Boolean,
				default: false
			},
			// 弹框宽度
			width: {
				type: String,
				default: '84%'
			},
			// 内边距
			padding: {
				type: String,
				default: ''
			},
			// 圆角
			radius: {
				type: Number,
				default: 20
			},
			// 标题
			title: {
				type: String,
				default: ''
			},
			// 内容
			content: {
				type: String,
				default: ''
			},
			// 按钮内容 设置参数与button组件的参数一致
			// {
			//   text: '确定',
			//   backgroundColor: 'red',
			//   fontColor: 'white',
			//   plain: true,
			//   shape: ''
			// }
			button: {
				type: Array,
				default: () => {
					return []
				}
			},
			safeAreaInsetBottom: {
				type: Boolean,
				default: false
			},
			// 点击遮罩是否可以关闭
			maskCloseable: {
				type: Boolean,
				default: true
			},
			// 是否显示右上角关闭按钮
			showCloseBtn: {
				type: Boolean,
				default: false
			},
			// 放大动画
			zoom: {
				type: Boolean,
				default: true
			},
			// 自定义弹框内容
			custom: {
				type: Boolean,
				default: false
			},
			// 弹框的z-index
			zIndex: {
				type: Number,
				default: 0
			}
		},
		computed: {

		},

		data() {
			return {
				
				thpasort:'',
				
				wpflsort1: '',
				wpflsort2: '',
				wpflsort3: '',
				wpflsort4: '',
				
				orwpflsort2: '',
				orwpflsort3: '',
				orwpflsort4: '',
				
				sortid1: 0,
				sortid2: 0,
				sortid3: 0,
				sortid4: 0,
				sortname1: '',
				sortname2: '',
				sortname3: '',
				sortname4: '',
				
				sortid:0,
				sortname: '',

				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,

				retdata: {},

				searchresdata: '',
				issearch: false,
			}
		},


		mounted() {
			this.$nextTick(() => {
				this.loadData();
			})
		},
		methods: {
			closeth() {
				this.$emit('close')
			},
			loadData() {
				let that = this;
				let da = {}
				this.$req.post('/v1/com/getwpfl1', da)
					.then(res => {
						if (res.errcode == 0) {
							that.wpflsort1 = res.data.wpflsort1;
							
						}
					})

			},
			
			backsort(){
				let that=this;
				let thpasort=this.thpasort;
				if(thpasort){
					if(thpasort.dj==2){
						that.wpflsort2=that.orwpflsort2;
						that.wpflsort3='';
						that.thpasort='';
					}
					if(thpasort.dj==3){
						that.wpflsort3=that.orwpflsort3;
						that.wpflsort4='';
						that.thpasort='';
					}
				}
			},

			loadData2() {
				let that = this;
				let da = {
					sortid1: that.sortid1
				}
				this.$req.post('/v1/com/getwpfl2', da)
					.then(res => {
						if (res.errcode == 0) {
							that.thpasort='';
							that.orwpflsort2 = res.data.wpflsort2;
							that.wpflsort2 = res.data.wpflsort2;
							
						}
					})
			},
			loadData3() {
				let that = this;
				let da = {
					sortid2: that.sortid2
				}
				this.$req.post('/v1/com/getwpfl3', da)
					.then(res => {
						if (res.errcode == 0) {
					       that.thpasort='';
							that.orwpflsort3 = res.data.wpflsort3;
							that.wpflsort3 = res.data.wpflsort3;
							
							if(res.data.wpflsort3.length > 0){
								that.wpflsort2='';
								that.thpasort={
									dj:2,
									sortid:that.sortid2,
									sortname:that.sortname2,
								}
							}
						}
					})
			},
			loadData4() {
				let that = this;
				let da = {
					sortid3: that.sortid3
				}
				this.$req.post('/v1/com/getwpfl4', da)
					.then(res => {
						if (res.errcode == 0) {
							that.wpflsort4 = res.data.wpflsort4;
							that.wpflsort4 = res.data.wpflsort4;
							if(res.data.wpflsort4.length > 0){
								that.wpflsort3='';
								that.thpasort={
									dj:3,
									sortid:that.sortid3,
									sortname:that.sortname3,
								}
							}
						}
					})
			},

			setsort2(e) {
				let that = this;
				let sortid1 = e.currentTarget.dataset.sortid1;
				let sortname1 = e.currentTarget.dataset.sortname1;
				this.sortid1 = sortid1;
				this.sortname1 = sortname1;
				this.sortid2 = 0;
				this.sortid3 = 0;
				this.sortid4 = 0;

				this.sortname2 = '';
				this.sortname3 = '';
				this.sortname4 = '';

				that.wpflsort2 = '';
				that.wpflsort3 = '';
				that.wpflsort4 = '';
			

				this.loadData2();
				this.setRetdata();
			},

			setsort3(e) {
				let that = this;
				let sortid2 = e.currentTarget.dataset.sortid2;
				let sortname2 = e.currentTarget.dataset.sortname2;
				this.sortid2 = sortid2;
				this.sortname2 = sortname2;
				this.sortid3 = 0;
				this.sortid4 = 0;
				this.sortname3 = '';
				this.sortname4 = '';

				that.wpflsort3 = '';
				that.wpflsort4 = '';
					

				this.loadData3();
				this.setRetdata();
			},
			setsort4(e) {
				let that = this;
				let sortid3 = e.currentTarget.dataset.sortid3;
				let sortname3 = e.currentTarget.dataset.sortname3;
				this.sortid3 = sortid3;
				this.sortname3 = sortname3;
				this.sortid4 = 0;
				this.sortname4 = '';
				that.wpflsort4 = '';
				this.loadData4();
				this.setRetdata();
			},

			setsort5(e) {
				let that = this;
				let sortid4 = e.currentTarget.dataset.sortid4;
				let sortname4 = e.currentTarget.dataset.sortname4;
				this.sortid4 = sortid4;
				this.sortname4 = sortname4;
				this.setRetdata();
			},
			setRetdata() {
				let retdata = {};
				let sortid1 = this.sortid1;
				let sortid2 = this.sortid2;
				let sortid3 = this.sortid3;
				let sortid4 = this.sortid4;
				let sortname1 = this.sortname1;
				let sortname2 = this.sortname2;
				let sortname3 = this.sortname3;
				let sortname4 = this.sortname4;

				let sortid = 0;
				let sortname = '';
				let sortnamep = '';

				if (sortid1 > 0) {
					sortid = sortid1;
					sortname = sortname1;
					sortnamep = sortname1;
				}

				if (sortid2 > 0) {
					sortid = sortid2;
					sortname = sortname2;
					sortnamep = sortname1 + '/' + sortname2;
				}

				if (sortid3 > 0) {
					sortid = sortid3;
					sortname = sortname3;
					sortnamep = sortname1 + '/' + sortname2 + '/' + sortname3;
				}

				if (sortid4 > 0) {
					sortid = sortid4;
					sortname = sortname4;
					sortnamep = sortname1 + '/' + sortname2 + '/' + sortname3 + '/' + sortname4;
				}

				retdata = {
					sortid: sortid,
					sortname: sortname,
					sortnamep: sortname,
				}
				
				this.sortid = sortid;
				this.sortname = sortname;

				this.retdata = retdata;
			},
			qrxz() {
				let retdata = this.retdata;
				this.$emit('qrxz', retdata)
			},
			
			qrxz22() {
				let retdata = {
					sortid: this.sortid,
					sortname: this.sortname,
					sortnamep: this.sortname,
				}
				this.$emit('qrxz', retdata)
			},
			xzsearchxm(e){
				let sortid = e.currentTarget.dataset.sortid;
				let sortname = e.currentTarget.dataset.sortname;
				this.sortid = sortid;
				this.sortname = sortname;
				
			},

			confirmsearch: function(e) {
				let that = this;
				let thkwtt = e.detail.value;
				that.thkw = thkwtt;
				that.issearch = true;

				this.sortid1 = 0;
				this.sortname1 = '';
				this.sortid2 = 0;
				this.sortid3 = 0;
				this.sortid4 = 0;

				this.sortname2 = '';
				this.sortname3 = '';
				this.sortname4 = '';

				that.wpflsort2 = '';
				that.wpflsort3 = '';
				that.wpflsort4 = '';

				that.loadSearch();
			},

			loadSearch() {
				let that = this;
				let da = {
					kw: that.thkw
				}
				this.$req.post('/v1/com/getwpflsearch', da)
					.then(res => {
						console.log(res);
						if (res.errcode == 0) {
							that.searchresdata = res.data.searchresdata;
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})

			},
			clskw() {
                this.thkw='';
                this.thkwtt='';
                this.searchresdata='';
				this.issearch = false;
			},

			// 处理按钮点击事件
			handleClick(index) {
				if (!this.value) return
				this.$emit("click", {
					index: Number(index)
				})
			},
			// 处理关闭事件
			close() {
				this.$emit("cancel")
				this.$emit('input', false)
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.thpasortcon{margin:0upx 20upx 20upx 10upx;background:#ff6600;color:#fff;overflow: hidden;float:left;padding:10upx 20upx;border-radius:10upx;} 
	
	
	.wpfl_topss {
		padding: 0 10px;
		border-bottom: 1px solid #eee;
		margin-bottom: 10px;
		height: 52px;

	}

	.wpfl_topss .stt1 {
		float: left;
		font-size: 15px;
		font-weight: 700;
		height: 52px;
		line-height: 52px;
	}

	.wpfl_topss .stt2 {
		float: right;
		padding: 10px 0;
		width: calc(100% - 120px);
	}

	.wpfl_topss .sscon90 {
		position: relative;
	}

	.wpfl_topss .sscon90 .inpsaa {
		height: 32px;
		line-height: 32px;
		background: #fff;
		border-radius: 100px;
		font-size: 12px;
		text-align: center;
		border: 1px solid #ddd;
	}

	.wpfl_topss .icon1 {
		position: absolute;
		left: 10px;
		top: 7px;
		color: #888
	}

	.wpfl_topss .icon2 {
		position: absolute;
		right: 10px;
		top: 7px;
		color: #888;
		z-index:2000;
	}

	.zlcon {
		margin-bottom: 20upx;
		border-bottom: 1px dashed #eee;
		padding-bottom: 15upx;
	}

	.zlcon .flitem {
		float: left;
		border: 1px solid #eee;
		margin: 10upx;
		border-radius: 10upx;
		padding: 0 25upx;
		line-height: 60upx;
		font-size: 28upx;
	}

	.zlcon .flitem.hoverss {
		border: 1px solid #ff6600;
		color: #ff6600
	}

	.nav_left {

		display: inline-block;
		width: 100%;
		height: 100%;
		text-align: center;
		left: 0;
		top: 0;
	}

	.nav_left .nav_left_items {
		height: 80upx;
		line-height: 80upx;
		border-bottom: 1px solid #efefef;
		font-size: 28upx;
		color: #000;
		background: #fafafa;
	}

	.nav_left .nav_left_items .con {}

	.nav_left .nav_left_items.active {
		background: #fff;
	}

	.nav_left .nav_left_items.active .con {
		background: #fff;
		border-left: 8upx solid #fa436a;
		color: #fa436a;
		font-weight: 700;
	}

	.nav_left .nav_left_items .tb {}

	.nav_left .nav_left_items .tb .txt {
		text-align: center
	}



	.scroll_left {
		width: 25%;
		height: calc(100% - 55px);
		background: #fff;
		text-align: center;
		position: fixed;
		left: 0;
		top: 52px;
		background: #fafafa;
	}

	.scroll_right {
		position: fixed;
		top: 52px;
		right: 0;
		overflow: auto;
		flex: 1;
		width: 75%;
		height: 100%;
		padding: 30upx;
		box-sizing: border-box;

	}
	
	
	.scroll_search {
		width: 100%;
		height: calc(100% - 55px);
		background: #fff;
		position: fixed;
		left: 0;
		top: 52px;
		background: #fafafa;
	}
	
	.zlcon22 {padding-left:15upx}
	.zlcon22 .flitem {
		border: 1px solid #eee;
		margin: 10upx;
		border-radius: 10upx;
		padding: 15upx 25upx;
		font-size: 28upx;
		text-align: left;
	}
	
	.zlcon22 .flitem.hoverss {
		border: 1px solid #ff6600;
		color: #ff6600
	}
</style>