<!--
  parser 主模块组件
  github：https://github.com/jin-yufeng/Parser 
  docs：https://jin-yufeng.github.io/Parser
  插件市场：https://ext.dcloud.net.cn/plugin?id=805
  author：JinYufeng
  update：2020/03/12
-->
<template>
	<view style="display:inherit;">
		<slot v-if="!nodes.length"></slot>
		<view class="top" :style="showAm+(selectable?';user-select:text;-webkit-user-select:text':'')" :animation="scaleAm"
		 @tap="_tap" @touchstart="_touchstart" @touchmove="_touchmove">
			<!--#ifdef H5-->
			<div :id="'rtf'+uid"></div>
			<!--#endif-->
			<!--#ifndef H5-->
			<trees :nodes="nodes" :lazy-load="lazyLoad" :loadVideo="loadVideo" />
			<!--#endif-->
		</view>
		<image v-for="(item, index) in imgs" v-bind:key="index" :id="index" :src="item" hidden @load="_load" />
	</view>
</template>

<script>
	// #ifndef H5
	import trees from "./libs/trees";
	var cache = {},
		CssHandler = require("./libs/CssHandler.js"),
		// #ifdef MP-WEIXIN || MP-TOUTIAO
		fs = uni.getFileSystemManager ? uni.getFileSystemManager() : null,
		// #endif
		Parser = require("./libs/MpHtmlParser.js");
	var document; // document 补丁包 https://jin-yufeng.github.io/Parser/#/instructions?id=document
	// 计算 cache 的 key
	function hash(str) {
		for (var i = str.length, val = 5381; i--;)
			val += (val << 5) + str.charCodeAt(i);
		return val;
	};
	// #endif
	const cfg = require('./libs/config.js');
	export default {
		name: 'parser',
		data() {
			return {
				// #ifdef APP-PLUS
				loadVideo: false,
				// #endif
				// #ifdef H5
				uid: this._uid,
				// #endif
				scaleAm: '',
				showAm: '',
				nodes: [],
				imgs: []
			}
		},
		// #ifndef H5
		components: {
			trees
		},
		// #endif
		props: {
			"html": null,
			// #ifndef MP-ALIPAY
			"autopause": {
				type: Boolean,
				default: true
			},
			// #endif
			"autosetTitle": {
				type: Boolean,
				default: true
			},
			"compress": Number,
			"domain": String,
			// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS
			"gestureZoom": Boolean,
			// #endif
			// #ifdef MP-WEIXIN || MP-QQ || H5 || APP-PLUS
			"lazyLoad": Boolean,
			// #endif
			"selectable": Boolean,
			"tagStyle": Object,
			"showWithAnimation": Boolean,
			"useAnchor": Boolean,
			"useCache": Boolean
		},
		watch: {
			html(html) {
				this.setContent(html);
			}
		},
		mounted() {
			// #ifdef APP-NVUE
			console.error("本组件暂不支持 NVUE");
			// #endif
			// 图片数组
			this.imgList = [];
			this.imgList.each = function(f) {
				for (var i = 0, len = this.length; i < len; i++)
					this.setItem(i, f(this[i], i, this));
			}
			this.imgList.setItem = function(i, src) {
				if (!i || !src) return;
				// #ifndef MP-ALIPAY || APP-PLUS
				// 去重
				if (src.indexOf("http") == 0 && this.includes(src)) {
					var newSrc = '';
					for (var j = 0, c; c = src[j]; j++) {
						if (c == '/' && src[j - 1] != '/' && src[j + 1] != '/') break;
						newSrc += Math.random() > 0.5 ? c.toUpperCase() : c;
					}
					newSrc += src.substring(j);
					return this[i] = newSrc;
				}
				// #endif
				this[i] = src;
				// 暂存 data src
				if (src.includes("data:image")) {
					var info = src.match(/data:image\/(\S+?);(\S+?),(.+)/);
					if (!info) return;
					// #ifdef MP-WEIXIN || MP-TOUTIAO
					var filePath = `${wx.env.USER_DATA_PATH}/${Date.now()}.${info[1]}`;
					fs && fs.writeFile({
						filePath,
						data: info[3],
						encoding: info[2],
						success: () => this[i] = filePath
					})
					// #endif
					// #ifdef APP-PLUS
					var filePath = `_doc/parser_tmp/${Date.now()}.${info[1]}`;
					var bitmap = new plus.nativeObj.Bitmap();
					bitmap.loadBase64Data(src, () => {
						bitmap.save(filePath, {}, () => {
							bitmap.clear()
							this[i] = filePath;
						})
					})
					// #endif
				}
			}
			if (!this.nodes.length) this.setContent(this.html, true);
		},
		beforeDestroy() {
			// #ifdef H5
			if (this._observer) this._observer.disconnect();
			// #endif
			this.imgList.each(src => {
				// #ifdef APP-PLUS
				if (src && src.includes("_doc")) {
					plus.io.resolveLocalFileSystemURL(src, entry => {
						entry.remove();
					});
				}
				// #endif
				// #ifdef MP-WEIXIN || MP-TOUTIAO
				if (src && src.includes(uni.env.USER_DATA_PATH))
					fs && fs.unlink({
						filePath: src
					})
				// #endif
			})
			clearInterval(this._timer);
		},
		methods: {
			// #ifdef H5
			_Dom2Str(nodes) {
				var str = "";
				for (var node of nodes) {
					if (node.type == "text")
						str += node.text;
					else {
						str += ('<' + node.name);
						for (var attr in node.attrs || {})
							str += (' ' + attr + '="' + node.attrs[attr] + '"');
						if (!node.children || !node.children.length) str += '>';
						else str += ('>' + this._Dom2Str(node.children) + "</" + node.name + '>');
					}
				}
				return str;
			},
			// #endif
			setContent(html, append) {
				// #ifdef H5
				if (!html) {
					if (this.rtf && !append) this.rtf.parentNode.removeChild(this.rtf);
					return;
				}
				if (typeof html != "string") html = this._Dom2Str(html.nodes || html);
				// 处理 rpx
				if (html.includes("rpx"))
					html = html.replace(/[0-9.]*rpx/g, $ => parseFloat($) * cfg.screenWidth / 750 + "px");
				var div = document.createElement("div");
				if (!append) {
					// 处理 tag-style 和 userAgentStyles
					var style = "<style>@keyframes show{0%{opacity:0}100%{opacity:1}}";
					for (var item in cfg.userAgentStyles)
						style += (item + '{' + cfg.userAgentStyles[item] + '}');
					for (var item in this.tagStyle)
						style += (item + '{' + this.tagStyle[item] + '}');
					style += "</style>";
					html = style + html;
					if (this.rtf) this.rtf.parentNode.removeChild(this.rtf);
					this.rtf = div;
				} else {
					if (!this.rtf) this.rtf = div;
					else this.rtf.appendChild(div);
				}
				div.innerHTML = html;
				for (var styles = this.rtf.getElementsByTagName("style"), i = 0, style; style = styles[i++];) {
					style.innerHTML = style.innerHTML.replace(/\s*body/g, "#rtf" + this._uid);
					style.setAttribute("scoped", "true");
				}
				// 懒加载
				if (!this._observer && this.lazyLoad && IntersectionObserver) {
					this._observer = new IntersectionObserver(changes => {
						for (var item, i = 0; item = changes[i++];) {
							if (item.isIntersecting) {
								item.target.src = item.target.getAttribute("data-src");
								item.target.removeAttribute("data-src");
								this._observer.unobserve(item.target);
							}
						}
					}, {
						rootMargin: "900px 0px 900px 0px"
					})
				}
				var _ts = this;
				// 获取标题
				var title = this.rtf.getElementsByTagName("title");
				if (title.length && this.autosetTitle)
					uni.setNavigationBarTitle({
						title: title[0].innerText
					})
				// 图片处理
				this.imgList.length = 0;
				var imgs = this.rtf.getElementsByTagName("img");
				for (var i = 0, j = 0, img; img = imgs[i]; i++) {
					img.style.maxWidth = "100%";
					var src = img.getAttribute("src");
					if (this.domain && src) {
						if (src[0] == "/") {
							if (src[1] == "/")
								img.src = (this.domain.includes("://") ? this.domain.split("://")[0] : '') + ':' + src;
							else img.src = this.domain + src;
						} else if (!src.includes("://")) img.src = this.domain + '/' + src;
					}
					if (!img.hasAttribute("ignore") && img.parentElement.nodeName != 'A') {
						img.i = j++;
						_ts.imgList.push(img.src || img.getAttribute("data-src"));
						img.onclick = function() {
							var preview = true;
							this.ignore = () => preview = false;
							_ts.$emit("imgtap", this);
							if (preview) {
								uni.previewImage({
									current: this.i,
									urls: _ts.imgList
								});
							}
						}
					}
					img.onerror = function() {
						_ts.$emit('error', {
							source: "img",
							target: this
						});
					}
					if (_ts.lazyLoad && this._observer && img.src && img.i != 0) {
						img.setAttribute("data-src", img.src);
						img.removeAttribute("src");
						this._observer.observe(img);
					}
				}
				// 链接处理
				var links = this.rtf.getElementsByTagName('a');
				for (var link of links) {
					link.onclick = function(e) {
						var jump = true,
							href = this.getAttribute("href");
						_ts.$emit("linkpress", {
							href,
							ignore: () => jump = false
						});
						if (jump && href) {
							if (href[0] == '#') {
								if (_ts.useAnchor) {
									_ts.navigateTo({
										id: href.substring(1)
									})
								}
							} else if (href.indexOf("http") == 0 || href.indexOf("//") == 0)
								return true;
							else {
								uni.navigateTo({
									url: href
								})
							}
						}
						return false;
					}
				}
				// 视频处理
				var videos = this.rtf.getElementsByTagName("video");
				_ts.videoContexts = videos;
				for (var video, i = 0; video = videos[i++];) {
					video.style.maxWidth = "100%";
					video.onerror = function() {
						_ts.$emit('error', {
							source: "video",
							target: this
						});
					}
					video.onplay = function() {
						if (_ts.autopause)
							for (var item, i = 0; item = ts.videoContexts[i++];)
								if (item != this) item.pause();
					}
				}
				// 音频处理
				var audios = this.rtf.getElementsByTagName("audios");
				for (var audio of audios)
					audio.onerror = function(e) {
						_ts.$emit("error", {
							source: "audio",
							target: this
						});
					}
				this.document = this.rtf;
				if (!append) document.getElementById("rtf" + this._uid).appendChild(this.rtf);
				this.$nextTick(() => {
					this.nodes = [1];
					this.$emit("load");
				})
				setTimeout(() => this.showAm = '', 500);
				// #endif
				// #ifndef H5
				var nodes;
				if (!html)
					return this.nodes = [];
				else if (typeof html == "string") {
					var parser = new Parser(html, this);
					// 缓存读取
					if (this.useCache) {
						var hashVal = hash(html);
						if (cache[hashVal])
							nodes = cache[hashVal];
						else {
							nodes = parser.parse();
							cache[hashVal] = nodes;
						}
					} else nodes = parser.parse();
					this.$emit("parse", nodes);
				} else if (Object.prototype.toString.call(html) == "[object Array]") {
					// 非本插件产生的 array 需要进行一些转换
					if (html.length && html[0].PoweredBy != "Parser") {
						var parser = new Parser(html, this);
						(function f(ns) {
							for (var i = 0, n; n = ns[i]; i++) {
								if (n.type == "text") continue;
								n.attrs = n.attrs || {};
								for (var item in n.attrs)
									if (typeof n.attrs[item] != "string") n.attrs[item] = n.attrs[item].toString();
								parser.matchAttr(n, parser);
								if (n.children && n.children.length) {
									parser.STACK.push(n);
									f(n.children);
									parser.popNode(parser.STACK.pop());
								} else n.children = void 0;
							}
						})(html);
					}
					nodes = html;
				} else if (typeof html == "object" && html.nodes) {
					nodes = html.nodes;
					console.warn("错误的 html 类型：object 类型已废弃");
				} else
					return console.warn("错误的 html 类型：" + typeof html);
				// #ifdef APP-PLUS
				this.loadVideo = false;
				// #endif
				if (document) this.document = new document(this.nodes, "nodes", this);
				if (append) this.nodes = this.nodes.concat(nodes);
				else this.nodes = nodes;
				if (nodes.length && nodes[0].title && this.autosetTitle)
					uni.setNavigationBarTitle({
						title: nodes[0].title
					})
				this.$nextTick(() => {
					this.imgList.length = 0;
					this.videoContexts = [];
					// #ifdef MP-TOUTIAO
					setTimeout(() => {
						// #endif
						var f = (cs) => {
							for (let i = 0, c; c = cs[i++];) {
								if (c.$options.name == "trees") {
									var observered = false;
									for (var j = c.nodes.length, item; item = c.nodes[--j];) {
										if (item.c) continue;
										if (item.name == "img") {
											this.imgList.setItem(item.attrs.i, item.attrs.src);
											// #ifndef MP-ALIPAY
											if (!c.observer && !c.imgLoad && item.attrs.i != "0") {
												if (this.lazyLoad && uni.createIntersectionObserver) {
													c.observer = uni.createIntersectionObserver(c);
													c.observer.relativeToViewport({
														top: 900,
														bottom: 900
													}).observe("._img", res => {
														c.imgLoad = true;
														c.observer.disconnect();
													})
												} else
													c.imgLoad = true;
											}
											// #endif
										}
										// #ifndef MP-ALIPAY
										else if (item.name == "video") {
											var ctx = uni.createVideoContext(item.attrs.id, c);
											ctx.id = item.attrs.id;
											this.videoContexts.push(ctx);
										}
										// #endif
										// #ifdef MP-WEIXIN
										else if (item.name == "audio" && item.attrs.autoplay)
											wx.createAudioContext(item.attrs.id, c).play();
										// #endif
										// #ifdef MP-BAIDU || MP-ALIPAY || APP-PLUS
										if (item.attrs && item.attrs.id) {
											this.anchors = this.anchors || [];
											this.anchors.push({
												id: item.attrs.id,
												node: c
											})
										}
										// #endif
									}
								}
								if (c.$children.length)
									f(c.$children)
							}
						}
						f(this.$children);
						// #ifdef MP-TOUTIAO
					}, 200)
					this.$emit("load");
					// #endif
					// #ifdef APP-PLUS
					setTimeout(() => {
						this.loadVideo = true;
					}, 3000);
					// #endif
				})
				// #endif
				var height;
				clearInterval(this._timer);
				this._timer = setInterval(() => {
					// #ifdef H5
					var res = [this.rtf.getBoundingClientRect()];
					// #endif
					// #ifndef APP-PLUS
					this.createSelectorQuery()
					// #endif
					// #ifdef APP-PLUS
					uni.createSelectorQuery().in(this)
						// #endif
						// #ifndef H5
						.select(".top").boundingClientRect().exec(res => {
							// #endif
							this.width = res[0].width;
							if (res[0].height == height) {
								this.$emit("ready", res[0])
								clearInterval(this._timer);
							}
							height = res[0].height;
							// #ifndef H5
						});
					// #endif
				}, 350)
				if (this.showWithAnimation && !append) this.showAm = "animation:show .5s";
			},
			getText(ns = this.html || this.nodes) {
				// #ifdef H5
				return this.rtf.innerText;
				// #endif
				// #ifndef H5
				var txt = '';
				for (var i = 0, n; n = ns[i++];) {
					if (n.type == "text") txt += n.txt.replace(/&nbsp;/g, '\u00A0').replace(/&lt;/g, '<').replace(/&gt;/g, '>')
						.replace(/&amp;/g, '&');
					else if (n.type == "br") txt += '\n';
					else {
						// 块级标签前后加换行
						var block = n.name == 'p' || n.name == "div" || n.name == "tr" || n.name == "li" || (n.name[0] == 'h' && n.name[1] >
							'0' && n.name[1] < '7');
						if (block && txt && txt[txt.length - 1] != '\n') txt += '\n';
						if (n.children) txt += this.getText(n.children);
						if (block && txt[txt.length - 1] != '\n') txt += '\n';
						else if (n.name == "td" || n.name == "th") txt += '\t';
					}
				}
				return txt;
				// #endif
			},
			navigateTo(obj) {
				if (!this.useAnchor)
					return obj.fail && obj.fail({
						errMsg: "Anchor is disabled"
					})
				// #ifdef H5
				if (!obj.id) {
					window.scrollTo(0, this.rtf.offsetTop);
					return obj.success && obj.success({
						errMsg: "pageScrollTo:ok"
					});
				}
				var target = document.getElementById(obj.id);
				if (!target) return obj.fail && obj.fail({
					errMsg: "Label not found"
				});
				obj.scrollTop = this.rtf.offsetTop + target.offsetTop;
				uni.pageScrollTo(obj);
				// #endif
				// #ifndef H5
				var Scroll = (selector, component) => {
					uni.createSelectorQuery().in(component ? component : this).select(selector).boundingClientRect().selectViewport()
						.scrollOffset()
						.exec(res => {
							if (!res || !res[0])
								return obj.fail && obj.fail({
									errMsg: "Label not found"
								});
							obj.scrollTop = res[1].scrollTop + res[0].top;
							uni.pageScrollTo(obj);
						})
				}
				if (!obj.id) Scroll(".top");
				else {
					// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS
					Scroll(".top >>> #" + obj.id + ', .top >>> .' + obj.id);
					// #endif
					// #ifdef MP-BAIDU || MP-ALIPAY || APP-PLUS
					for (var anchor of this.anchors)
						if (anchor.id == obj.id)
							Scroll('#' + obj.id + ", ." + obj.id, anchor.node);
					// #endif
				}
				// #endif
			},
			getVideoContext(id) {
				if (!id) return this.videoContexts;
				else
					for (var i = this.videoContexts.length; i--;)
						if (this.videoContexts[i].id == id) return this.videoContexts[i];
			},
			// 预加载
			preLoad(html, num) {
				// #ifdef H5
				if (html.constructor == Array)
					html = this._Dom2Str(html);
				var contain = document.createElement('div');
				contain.innerHTML = html;
				var imgs = contain.querySelectorAll("img");
				for (var i = imgs.length - 1; i >= num; i--)
					imgs[i].removeAttribute("src");
				// #endif
				// #ifndef H5
				if (typeof html == "string") {
					var id = hash(html);
					html = new Parser(html, this).parse();
					cache[id] = html;
				}
				var wait = [];
				(function f(ns) {
					for (var i = 0, n; n = ns[i++];) {
						if (n.name == "img" && n.attrs.src && !wait.includes(n.attrs.src))
							wait.push(n.attrs.src);
						f(n.children || []);
					}
				})(html);
				if (num) wait = wait.slice(0, num);
				this._wait = (this._wait || []).concat(wait);
				if (!this.imgs) this.imgs = this._wait.splice(0, 15);
				else if (this.imgs.length < 15)
					this.imgs = this.imgs.concat(this._wait.splice(0, 15 - this.imgs.length));
				// #endif
			},
			_load(e) {
				// #ifndef H5
				if (this._wait.length)
					this.$set(this.imgs, e.target.id, this._wait.shift());
				// #endif
			},
			_tap(e) {
				// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS
				if (this.gestureZoom && e.timeStamp - this._lastT < 300) {
					var initY = e.touches[0].pageY - e.currentTarget.offsetTop;
					if (this._zoom) {
						this._scaleAm.translateX(0).scale(1).step();
						uni.pageScrollTo({
							scrollTop: (initY + this._initY) / 2 - e.touches[0].clientY,
							duration: 400
						})
					} else {
						var initX = e.touches[0].pageX - e.currentTarget.offsetLeft;
						this._initY = initY;
						this._scaleAm = uni.createAnimation({
							transformOrigin: `${initX}px ${this._initY}px 0`,
							timingFunction: "ease-in-out"
						});
						// #ifdef MP-TOUTIAO
						this._scaleAm.opacity(1);
						// #endif
						this._scaleAm.scale(2).step();
						this._tMax = initX / 2;
						this._tMin = (initX - this.width) / 2;
						this._tX = 0;
					}
					this._zoom = !this._zoom;
					this.scaleAm = this._scaleAm.export();
				}
				this._lastT = e.timeStamp;
				// #endif
			},
			_touchstart(e) {
				// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS
				if (e.touches.length == 1)
					this._initX = this._lastX = e.touches[0].pageX;
				// #endif
			},
			_touchmove(e) {
				// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS
				var diff = e.touches[0].pageX - this._lastX;
				if (this._zoom && e.touches.length == 1 && Math.abs(diff) > 20) {
					this._lastX = e.touches[0].pageX;
					if ((this._tX <= this._tMin && diff < 0) || (this._tX >= this._tMax && diff > 0))
						return;
					this._tX += (diff * Math.abs(this._lastX - this._initX) * 0.05);
					if (this._tX < this._tMin) this._tX = this._tMin;
					if (this._tX > this._tMax) this._tX = this._tMax;
					this._scaleAm.translateX(this._tX).step();
					this.scaleAm = this._scaleAm.export();
				}
				// #endif
			}
		}
	}
</script>

<style>
	@keyframes show {
		0% {
			opacity: 0
		}

		100% {
			opacity: 1;
		}
	}

	/* #ifdef MP-WEIXIN || APP-PLUS */
	:host {
		display: block;
		overflow: scroll;
		-webkit-overflow-scrolling: touch;
	}

	.top {
		display: inherit;
	}

	/* #endif */
</style>
