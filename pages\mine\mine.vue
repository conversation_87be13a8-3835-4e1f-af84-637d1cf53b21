<template>
	<view class="page-e ">


		<tn-nav-bar fixed :isBack="false" customBack :bottomShadow="false" backgroundColor="#FFD427" :zIndex="3000">
			<view class="itop">
				<view class="if1">
					<view class="sname"></view>
				</view>
				<view class="" style="position: absolute;width:100%;text-align: center;left:0;">
					<text>{{mudata.mdname}}</text>
				</view>

				<view class="if2">
					<view style="padding-right:40upx;" @click="vkefu" v-if="isvkf==1">
						<text class="iconfont icon-kefu1"></text> 客服
					</view>
				</view>
				<view class="clear"></view>
			</view>
		</tn-nav-bar>

		<!-- <view class="login__bg login__bg--top">
		<image class="bg" src="/static/images/regbg.jpg" mode="widthFix"></image>
	  </view> -->


		<view class="topbg" :style="{marginTop: vuex_custom_bar_height + 'px'}">
			<!-- <view class="trbjtip" @click="gotogrzl">编辑资料 <text class="iconfont icon-jiantou"></text></view> -->



			<view class="usinfo">
				<view class="seticon" @click="qhseldianpu"><text class="iconfont icon-qiehuandianpu1"></text></view>
				<view class="tx">
					<image :src="mudata.avatar ? staticsfile + mudata.avatar : '/static/images/ava_def.png'"></image>
				</view>
				<view class="xx" style="padding-top:20upx">
					<view class="name">
						<view class="t1">{{mudata.realname ? mudata.realname : '完善资料' }}</view>
						<view class="t2 " v-if="mudata.jiaosetxt">
							<view class="jscon">{{mudata.jiaosetxt}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="nid">
						<view class="idv">员工编号：{{mudata.zzhuid}}</view>
						<!-- <view class="idv" style="margin-left:20upx">漏鱼号：{{mudata.userid}}</view> -->
						<view class="clear"></view>
					</view>
					<!-- <view class="nid"><view class="idv" >当前门店：{{mudata.mdname}}</view></view> -->
				</view>
			</view>
		</view>

		<!-- <view class="tjflus">
		<view class="txbtn" @click="gototiixan">提现</view>
		<view class="tit">余额</view>
		<view class="jine">{{mudata.yue}}</view>
	</view> -->

		<view class='uhxmv'>
			<view class='con yinying'>
				<view class='tjmul'>
					<view class='mli'>
						<navigator url='/pages/user/caiwu/index' hover-class="none">
							<view class='icon  sz'>{{mudata.yue}}</view>
							<view class='tit'>我的余额</view>
						</navigator>
					</view>
					<view class='mli'>
						<navigator url='/pages/user/caiwu/index?type=6' hover-class="none">
							<view class='icon  sz'>{{mudata.syzjine}}</view>
							<view class='tit'>本店累计收益</view>
						</navigator>
					</view>

					<view class='mli'>
						<navigator url='/pages/user/zjrcd/index?type=3' hover-class="none">
							<view class='icon  sz'>{{mudata.czzjine}}</view>
							<view class='tit'>当前出资</view>
						</navigator>
					</view>
					<view class='mli last'>
						<navigator url='/pages/user/zjrcd/index' hover-class="none">
							<view class='icon sz'>{{mudata.czkyjine}}</view>
							<view class='tit'>可用资金</view>
						</navigator>
					</view>
				</view>
			</view>
		</view>


		<view class="maxcon ">
			<view class="itemcon">
				<view class="itemli">
					<navigator url='/pages/user/zjrcd/index?type=3' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-1.png"></image>
						</view>
						<view class="wz">我的出资</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>
				<view class="itemli">
					<navigator url='/pages/user/caiwu/index?type=6' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-2.png"></image>
						</view>
						<view class="wz">收益明细</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>
				<!-- 	<view class="itemli" >
						<navigator url='/pages/user/rzdp/index' hover-class="none">
							<view class="icon"><image src="/static/images/itemic-5.png"></image></view>
							<view class="wz">入账店铺</view>
							<view class="more"><text class="iconfont icon-jiantou"></text></view>
							<view class="clear"></view>
						</navigator>
					</view> -->
				<view class="itemli">
					<navigator url='/pages/user/baoxiao/index' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-5.png"></image>
						</view>
						<view class="wz">费用报销</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>
				<view class="itemli">
					<navigator url='/pages/user/feiyong/index' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-6.png"></image>
						</view>
						<view class="wz">申请费用</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>
				<view class="itemli">
					<navigator url='/pages/user/gongzi/index' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-6.png"></image>
						</view>
						<view class="wz">工资预支</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>
				<view class="itemli">
					<navigator url='/pages/user/grinfo' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-3.png"></image>
						</view>
						<view class="wz">个人设置</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>
				<view class="itemli">
					<navigator url='/pages/user/set' hover-class="none">
						<view class="icon">
							<image src="/static/images/itemic-4.png"></image>
						</view>
						<view class="wz">账号安全</view>
						<view class="more"><text class="iconfont icon-jiantou"></text></view>
						<view class="clear"></view>
					</navigator>
				</view>



			</view>
		</view>


		<!--  <view class="grzsycon"> 
				 <text class="iconfont icon-shuhui2 tbssaa"></text>
			    
				 <view>
					 累计余额：<text class="jine">￥{{mudata.syzjine0}}</text>
					 累计收益：<text class="jine">￥{{mudata.syzjine0}}</text>
				 </view>
				
			</view> -->

		<view class="dibutj">
			<view class="titssa"><text class="iconfont icon-shuhui2 tbssaa"></text> 个人所有店铺</view>
			<view class="tjitem noline">
				<view class="ite gx">
					<view class="cons">
						<view class="txt">累计余额</view>
						<view class="sz">{{mudata.syzjine1}}</view>

					</view>
				</view>
				<view class="ite">
					<view class="cons">
						<view class="txt">累计收益</view>
						<view class="sz">{{mudata.syzjine0}}</view>
					</view>
				</view>
				<view class="clear"></view>
			</view>
		</view>



		<tn-popup v-model="qhdpselshow" mode="bottom" :borderRadius="20" :closeBtn='true' :zIndex="30000">
			<view class="popuptit">切换店铺/身份</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">

				<view class="qhsflist">
					<block v-for="(itemx,indexx) in mydpdata.glsfdata" :key="indexx">
						<view class="sfitem">
							<view class="tit">{{itemx.mdname}}
								<text class="iconfont icon-mendian cjglicon" v-if="itemx.iszzh==1 "></text>
							</view>
							<view class="zss">员工名称：{{itemx.realname}}</view>
							<!-- <view class="zss" style="display: none;">员工编号：{{itemx.id}}</view> -->
							<!-- <view class="zss" v-if="itemx.mdname">所属店铺：{{itemx.mdname}}</view> -->
							<view class="zss" v-if="itemx.jiaosetxt">店铺角色：<text class="jstxt">{{itemx.jiaosetxt}}</text>
							</view>
							<view class="zss cjboss" v-if="itemx.jiaosetxt2">超级角色：<text
									class="jstxt">{{itemx.jiaosetxt2}}</text></view>
							<view class="jrbtn" @click="jrdp" :data-ygid="itemx.id" :data-mdid="itemx.mdid">切换</view>
						</view>
					</block>
					<block v-for="(items,indexs) in mydpdata.otmddata" :key="'a'+indexs">
						<view class="sfitem">
							<view class="tit" style="color:#1677FF;font-weight:400">{{items.title}}</view>
							<view class="zss">所在地区：{{items.areatxt}}</view>
							<view class="jrbtn" @click="jrdp" :data-mdid="items.id" :data-ygid="0">切换</view>
						</view>
					</block>
				</view>


			</scroll-view>
		</tn-popup>



		<tn-popup v-model="vkefushow" mode="center" :borderRadius="20" :closeBtn='true' :zIndex="30000">
			<view style="text-align: center;padding-top:30upx">客服电话</view>
			<view style="width:400upx;text-align: center;">
				<view style="padding:40upx;">
					<view style="padding:10upx 0;font-size:40upx">{{vkftel}}</view>
					<view style="margin-top:40upx;margin-bottom:50upx;" @click="bdtel"><text
							class="iconfont icon-tianchongxing-" style="font-size:55upx;"></text></view>
				</view>
			</view>
		</tn-popup>






		<view style="height:60upx"></view>


		<view class='tn-tabbar-height'></view>
		<yomol-upgrade :type="upgradeType" :url="upgradeUrl" title="发现新版本" :content="upgradeContent"
			ref="yomolUpgrade"></yomol-upgrade>
	</view>
</template>

<script>
	import yomolPrompt from '@/components/yomol-prompt/yomol-prompt.vue'
	import yomolUpgrade from '@/components/yomol-upgrade/yomol-upgrade.vue'

	export default {

		components: {
			yomolPrompt,
			yomolUpgrade,
		},
		data() {
			return {
				staticsfile: this.$staticsfile,
				mudata: '',
				vkftel: '',
				fwodnum: 0,
				fwsyjine: 0,
				fwxccs: 0,
				fwbycs: 0,
				isvkf: 0,

				vkefushow: false,
				qhdpselshow: false,
				mydpdata: [],

				version: '',
				upgradeType: 'pkg',
				upgradeContent: '',
				upgradeUrl: '',

			}
		},

		onLoad() {
			let that = this;




		},
		onShow() {
			let that = this;
			uni.setStorageSync('thupdpkb', 1);
			that.loadData();
			//#ifdef APP-PLUS
			that.checkupone()
			//#endif
		},


		computed: {

		},

		methods: {
			vkefu() {
				this.vkefushow = true;
			},
			gototiixan() {
				uni.navigateTo({
					url: '/pages/user/tixian/tixian'
				})
			},
			loadData() {
				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/muser/index', da)
					.then(res => {
						console.log(222, res);
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.mudata = res.data.mudata;
							that.vkftel = res.data.vkftel;
							that.isvkf = res.data.isvkf;

						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})
			},

			gotogrzl() {
				uni.navigateTo({
					url: '/pages/user/grinfo'
				})
			},

			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			selqhdp() {
				uni.navigateTo({
					url: '/pages/selsf/index?isback=1'
				})
			},
			saomac() {
				let that = this;
				uni.scanCode({
					success: function(res) {
						let orhxma = res.result;
						uni.navigateTo({
							url: '/pages/user/saomahx/index?orhxma=' + orhxma
						})
					}
				});
			},


			checkupone() {

				uni.getStorage({
					key: "checkup-time",
					success: (res) => {
						if (!res.data || new Date().getTime() - res.data > 1000 * 60 * 60 * 6) {
							this.checkversion();
						}
					},
					fail: (err) => {
						this.checkversion();
					},
				});

			},


			checkversion() {
				let that = this;
				// 检测升级
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					var platform = uni.getSystemInfoSync().platform


					let da = {
						platform: platform,
						version: widgetInfo.version,
						name: widgetInfo.name
					}
					console.log(da);
					that.$req.post('/v1/checkver/index', da)
						.then(res => {
							console.log(res);


							if (res.errcode == 0) {



								let isnew = res.data.isnew;
								if (isnew == 1) {
									uni.setStorage({
										key: "checkup-time",
										data: new Date().getTime(),
									});
									if (res.data.appupdata.pkgurl != '' && res.data.appupdata.wgturl == '') {
										that.upgradeType = 'pkg'
										that.upgradeContent = res.data.appupdata.content
										that.upgradeUrl = res.data.appupdata.pkgurl
										that.$refs.yomolUpgrade.show()
									} else {
										that.upgradeType = 'wgt'
										that.upgradeContent = res.data.appupdata.content
										that.upgradeUrl = res.data.appupdata.wgturl
										that.$refs.yomolUpgrade.show()
									}
								}
							} else {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}

						})


				});
			},





			qhseldianpu() {

				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/selsf/dpnqh', da)
					.then(res => {
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.mydpdata = res.data.mydpdata;
							that.qhdpselshow = true;
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})


			},

			jrdp(e) {
				let that = this;
				let ygid = e.currentTarget.dataset.ygid;
				let mdid = e.currentTarget.dataset.mdid;
				let da = {
					ygid: ygid,
					mdid: mdid,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/selsf/jrdp', da)
					.then(res => {
						console.log(222, res);
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thupdpkb', 1);
							uni.setStorageSync('thygid', '');
							uni.setStorageSync('thygname', '');
							that.$tool.setTokenStorage(res.data.tokendata);
							setTimeout(function() {
								that.qhdpselshow = false;
								that.loadData();
							}, 500)
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})
			},


			bdtel(e) {
				let that = this;
				let tel = this.vkftel;
				uni.makePhoneCall({
					phoneNumber: tel
				});
			},


		}
	}
</script>

<style lang="scss">
	.itop {
		padding: 0 32upx
	}

	.itop .if1 {
		position: absolute;
		left: 30upx;
		z-index: 200;
	}

	.itop .if1 .sname {
		font-size: 32upx;
	}

	.itop .if2 {
		position: absolute;
		right: 0;
		z-index: 200;
	}

	.itop .if2 .smicon {
		float: right;
		margin-right: 30upx
	}

	.itop .if2 .smicon text {
		font-size: 40upx;
		color: #333333
	}

	.grzsycon {
		text-align: left;
		border-radius: 10upx;
		padding: 35upx;
		margin: 30upx 30upx 0 30upx;
		background: #fff;
		position: relative;
		padding-left: 90upx
	}

	.grzsycon .jine {
		color: #ff0000;
	}

	.grzsycon .tbssaa {
		position: absolute;
		left: 30upx;
		top: 32upx;
		font-size: 45upx;
		color: #ff0000;
	}

	.uhxmv {
		margin: 0upx 30upx 0 30upx;
		position: relative;
		z-index: 200;
		margin-top: -70upx
	}

	.uhxmv .con {
		border-radius: 15upx;
		padding: 30upx 0;
		background: #fff;
	}

	.tjmul {
		display: flex;
	}

	.tjmul .mli {
		flex: 1;
		border-right: 1px solid #f3f3f3;
		text-align: center;
	}

	.tjmul .mli.last {
		border-right: 0;
	}

	.tjmul .mli .sz {
		height: 30px;
		line-height: 30px;
		color: #ff0000;
		font-size: 28upx
	}

	.tjmul .tit {
		margin-top: 0px;
		font-size: 24upx
	}


	.login {
		position: relative;
		height: 100%;
		z-index: 1;

		/* 背景图片 start */
		&__bg {
			z-index: -1;
			position: fixed;

			&--top {
				top: 0;
				left: 0;
				right: 0;
				width: 100%;

				.bg {
					width: 750upx;
					will-change: transform;
				}
			}

		}

		/* 背景图片 end */
	}

	.topbg {
		position: relative;
		background: #FFD427;
		border-radius: 0 0 10% 10%;
	}

	.topbg .trbjtip {
		position: absolute;
		right: 40upx;
		top: 50upx;
		font-size: 28upx;
	}

	.topbg .trbjtip text {
		font-size: 28upx;
	}

	.usinfo {
		margin: 0 40upx 0 40upx;
		height: 250upx;
		padding-top: 20upx
	}

	.usinfo .tx {
		float: left
	}

	.usinfo .tx image {
		width: 130upx;
		height: 130upx;
		border: 8upx solid #fff;
		display: block;
		border-radius: 100px;
		background: #fff;
	}

	.usinfo .xx {
		float: left;
		margin-left: 30upx;
		padding-top: 10upx
	}

	.usinfo .xx .name {
		height: 50upx;
		font-size: 36upx;
		font-weight: 500;
		color: #333;
		line-height: 50upx;
	}

	.usinfo .xx .name .t1 {
		float: left
	}

	.usinfo .xx .name .t2 {
		float: left;
		margin-left: 20upx;
		padding-top: 5upx
	}

	.usinfo .xx .name .jscon {
		background: #222;
		font-size: 18upx;
		color: #fff;
		border-radius: 100px;
		padding: 0 10upx;
		height: 30upx;
		line-height: 30upx;
		margin-top: 4upx
	}

	.usinfo .xx .nid {
		margin-top: 5upx
	}

	.usinfo .xx .nid .idv {
		float: left;
		height: 34upx;
		font-size: 24upx;
		font-weight: 500;
		color: #333;
		line-height: 34upx;
	}

	.usinfo .seticon {
		position: absolute;
		right: 40upx;
		top: 60upx;
	}

	.usinfo .seticon .iconfont {
		font-size: 45upx;
		color: #333
	}


	.tjflus {
		border-radius: 20upx;
		background: #fff;
		padding: 28upx;
		margin: 20upx 32upx 20upx 32upx;
		position: relative;
	}

	.tjflus .txbtn {
		width: 116upx;
		height: 56upx;
		line-height: 56upx;
		background: #FFD427;
		border-radius: 12upx;
		text-align: center;
		position: absolute;
		right: 28upx;
		top: 50upx;
		font-size: 28upx
	}

	.tjflus .tit {
		font-size: 28upx;
		height: 40upx;
		line-height: 40upx;
		font-weight: 500;
		margin-bottom: 15upx
	}

	.tjflus .jine {
		font-size: 40upx;
		height: 48upx;
		line-height: 48upx;
		font-weight: 600;
	}

	.maxcon {
		margin: 20upx 32upx 0 32upx;
		border-radius: 10px;
		background: #ffffff;
	}

	.itemcon {}

	.itemcon .itemli {
		border-bottom: 1px solid #f3f3f3;
		padding: 0 30upx;
		height: 120upx;
		line-height: 120upx;
		overflow: hidden;
		font-size: 28upx
	}

	.itemcon .itemli .icon {
		float: left;
		padding-top: 13upx;
	}

	.itemcon .itemli .icon image {
		width: 44upx;
		height: 44upx;
	}

	.itemcon .itemli .wz {
		float: left;
		margin-left: 12px;
	}

	.itemcon .itemli .more {
		float: right;
	}

	.itemcon .itemli.last {
		bottom: 0
	}


	.dibutj {
		margin: 32upx;
		background: #fff;
		border-radius: 20upx;
		padding-top: 30upx
	}

	.dibutj .titssa {
		padding-left: 33upx
	}

	.dibutj .titssa .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}

	.dibutj .tjitem {
		border-bottom: 1px solid #F3F3F3;
		padding: 20upx 0 40upx 0;
	}

	.dibutj .tjitem .ite {
		width: 50%;
		float: left;
		position: relative
	}

	.dibutj .tjitem .ite.gx:after {
		position: absolute;
		top: 35%;
		right: 0;
		content: '';
		width: 1px;
		height: 45%;
		-webkit-transform: scaleX(0.5);
		transform: scaleX(0.5);
		border-right: 1px solid #D9D9D9;
	}

	.dibutj .tjitem .txt {
		color: #7F7F7F;
		margin-bottom: 15upx;
		font-size: 28upx
	}

	.dibutj .tjitem .sz {
		font-size: 36upx
	}

	.dibutj .tjitem.noline {
		border-bottom: 0
	}

	.dibutj .tjitem .cons {
		padding-left: 40upx;
	}
</style>