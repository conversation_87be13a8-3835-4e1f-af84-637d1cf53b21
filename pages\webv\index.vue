<template>
	<view  >
		
	     <web-view :src="weburl"></web-view>
		 
	</view>
</template>

<script>
	export default {
		data() {
			return {
				weburl: ''
			}
		},
		onLoad(option){
			this.weburl = option.weburl;
		},
		onShow(){
			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				uni.setNavigationBarColor({
				  frontColor: mbjson.tbtxtcolor,
				  backgroundColor: mbjson.tbbgcolor
				});
			}
		},
		methods: {
			
			
		}
	}
</script>

<style lang='scss'>

</style>
