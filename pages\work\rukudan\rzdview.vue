<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>入账开单详情</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">

				<block v-if="ygdata">
					<view class="maxcon" v-if="isvallfrcz==1">
						<view class="ttinfo">
							<view class="inf1">
								<view class="tf1">
									<view class="tx">
										<image :src="staticsfile + ygdata.avatar"></image>
									</view>
									<view class="xx">
										<view class="name">{{ygdata.realname}}</view>
										<view class="tel"><text>员工号:{{ygdata.zzhuid}}</text>
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>
					</view>
				</block>

				<view class="maxcon">

					<view class="vstit">基本信息</view>
					<view class="rowx">
						<view class="tit">入账金额：</view>
						<view class="inpcon">
							<view class="inp" style="color:#ff0000">¥ {{rzdata.jine}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">入账门店：</view>
						<view class="inpcon">
							<view class="inp">{{rzdata.mdname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" v-if="rzdata.khname">
						<view class="tit">入账客户：</view>
						<view class="inpcon">
							<view class="inp">{{rzdata.khname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">申请时间：</view>
						<view class="inpcon">
							<view class="inp">{{rzdata.addtime}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="bzcon">{{rzdata.remark}}</view>

					<block v-if="rzdata.picturescarr">
						<view class="pjimgcon">
							<block v-for="(itemx,indexx) in rzdata.picturescarr" :key="indexx">
								<view class='item'>
									<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
										:data-picturescarr="rzdata.picturescarr" @tap='previewImagex'>
									</image>
								</view>
							</block>
							<view class="clear"></view>
						</view>
					</block>

				</view>

				<block v-if="rzdata.txstatus==2 || rzdata.txstatus==3"> 
					
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
							
								<block v-if="rzdata.czfrry2data">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in rzdata.czfrry2data" :key="indexx">
											<view class="czrli">
												<block v-if="rzdata.txstatus==2 && itemx.rytype==9">
													<view class="del" @click="delczry" :data-czryid="itemx.id"><text
															class="iconfont icon-cuohao02"></text></view>
												</block>
						
												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm">分润金额：<text
															:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
													</view>
												
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>
							<view class="maxcon" style="padding-bottom:10upx" v-if="isvallfrcz==1">
									<view class="czrli">
										<view class="xx">
											<view class="czxm hj" style="width:100%;">已分润金额：<text
													class="ysz">{{rzdata.frjinehj}}</text> <text class="gx">,</text>
												未分润金额：<text class="hs">{{rzdata.frjinehj2}}</text></view>
											<view class="clear"></view>
										</view>
									</view>
							</view>
					 
				</block>
				
				

			


			</view>





		</view>

		<view style="height:120upx"></view>



	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				djid: '',
				rzdata: '',
				ygdata: '',
				bhsm: '',
				jjtgshow: false,
				
				gzselshow: false,
				thfrjine: 0,
				frgzdata: '',
				addrytype: 0,
				addrytypetxt: '人员姓名',
				
				thczjine: 0,
				thfrbl: 0,
				czfrspid: 0,
				czfrisfr: 1,
				isofr: 0,
				thczygid: 0,
				thczygname: '',
				thygczkyjine: '',
				
				czryeditshow: false,
				isdpczzh:0,
				isvallfrcz:0,
				vsour:0,
				
			}
		},

		onLoad(options) {
			let that = this;
			let vsour = options.vsour ? options.vsour : 0;
			this.vsour = vsour;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},
		onShow() {
			
		},

		methods: {
            

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					vsour: that.vsour,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ydview/ygrzdpview', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.rzdata = res.data.rzdata;
							that.bhsm = res.data.rzdata.bhsm;
							that.ygdata = res.data.ygdata;
							that.isvallfrcz = res.data.isvallfrcz;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},




		}
	}
</script>

<style lang="scss">
	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}
	.bzcon {
		margin-top:20upx;
	}
	.bhsmcon {
		margin: 10upx 0 30upx 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}
	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}
	
	.maxcon .vstit .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}
	
	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}
	
	.maxcon .more .t1 {
		color: #7F7F7F;
	}
	
	.maxcon .more.vls {
		color: #1677FF;
	}
	
	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.pjimgcon {
		margin-top: 20upx;
	}

	.pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
	
	
	.stats.stacolor1 {
		color: #0DD400;
	}
	
	.stats.stacolor2 {
		color: #FA6400;
	}
	
	.stats.stacolor3 {
		color: #3366ff;
	}
	
</style>