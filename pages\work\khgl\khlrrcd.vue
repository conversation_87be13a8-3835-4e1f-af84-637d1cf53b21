<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					   <text > 客户录入记录</text>
					</view>
					<view slot="right" >
						<view class="gnssrr">
							<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
							<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
						</view>
					</view>
		</tn-nav-bar>
		
	
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}" ></view>
		
		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>
		
		
			<block v-if="listdata.length>0">
			
			     
						   <view class="ygc">
						   	<block v-for="(item,index) in listdata"	:key="index">
						   		<view class="item"  >
						   			
						   			<view class="inf1">
						   				  <!-- <view class="djcon">{{item.dengjitxt}}</view> -->
						   				  <view class="tx"> 
						   					  <view class="avatar"><image :src="staticsfile + item.avatar"></image></view>
						   					  <view class="sexicon" v-if="item.sex > 0"><image :src="'/static/images/sexicon'+item.sex+'.png'"></image></view>
						   				  </view>
						   				  <view class="xx">
						   					  <view class="tit">
						   						  <text class="name">{{item.realname}}</text>
						   						  <!-- <text class="uid" >（编号:{{item.id}})</text> -->
						   					  </view>
						   					  <view class="ssyg" v-if="ismy!=1  &&  item.lrygname">录入员工：{{item.lrygname}}</view>
						   					  <view class="ssyg">录入时间：{{item.addtime}}</view>
						   				  </view>
						   				  <view class="clear"></view>
						   			</view>
						   		</view>  
						   	</block>
						   </view>  
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关数据</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">客户名称</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入客户名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
			
		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[],
				show3:false,
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				tjdata:[],
				
				ygid:0,
				ygname:'',
				ygdata:[],
				
				ismy:0,
			}
		},
		onLoad(options) {
			let that=this;
			let ismy=options.ismy ? options.ismy : 0
			this.ismy=ismy;
		
			setTimeout(function() {
				  that.page = 0;
				  that.listdata = [];
				  that.isloading = false;
				  that.loadingType = -1;
				  that.loaditems(); 
			}, 100);
			
		},
		onShow() {
			let that=this;
	
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			
			rqmodal(){
				this.rqshow=true
			},
			rqmodalchange(e){
				let that=this;
				if(e.startDate && e.endDate){
					// this.thkw='';
					this.rqkstime=e.startDate;
					this.rqjstime=e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch(){
				let that=this;
				this.rqkstime='';
				this.rqjstime='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
		
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;
				
			 //    this.rqkstime='';
				// this.rqjstime='';				
			
				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							ismy:that.ismy ? that.ismy : 0 ,
							kw:that.thkw ? that.thkw : '' ,
							rqkstime:that.rqkstime ? that.rqkstime : 0 ,
							rqjstime:that.rqjstime ? that.rqjstime : 0 ,
						}
					if(page!=1){
						that.isloading=true;
					} 
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/khgl/khlrrcd', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
		   
		}
	}
</script>

<style lang='scss'>
.ygc{margin:0 32upx}
	.ygc .item{background: #FFFFFF;border-radius: 20rpx;margin-bottom:20upx;padding:28upx 28upx;position: relative;}
	.ygc .item .wgluid{position: absolute;right:30upx;top:80upx;font-size:20upx;background:#FFD427;border-radius: 100upx;padding:5upx 15upx;}
	.ygc .item .tx{float:left;position: relative;}
	.ygc .item .tx .avatar image{width: 100upx;height:100upx;border-radius:100upx;display: block;}
	.ygc .item .tx .sexicon{position: absolute;bottom:-20upx;left:35upx}
	.ygc .item .tx .sexicon image{width: 32upx;height:32upx;}
	.ygc .item .xx{float:left;margin-left:30upx}
	.ygc .item .xx .tit{height:40upx;line-height:40upx;font-size:28upx;padding-top:10upx;margin-bottom:20upx}
	.ygc .item .xx .tit .name{font-size:28upx;font-weight:600}
	.ygc .item .xx .tit .uid{font-size: 24rpx;margin-left:10upx;color:#888}
	.ygc .item .xx .ssyg{height:30upx;line-height:30upx;font-size:20upx;margin-top:0upx;color: #7F7F7F;overflow: hidden;}
	.ygc .djcon{position: absolute;right:0;top:0;font-size:24upx;background:#E7FFE2;border-radius: 0rpx 20rpx 0rpx 20rpx;height:42upx;line-height:42upx;padding:0 15upx;color:#0EC45C}
	.ygc .inf3{text-align: center;margin-top:30upx}
	.ygc .inf3 .ite{float:left;width:33%;}
	.ygc .inf3 .ite .ic{display: inline-block;}
	.ygc .inf3 .ite image{width:36upx;height:36upx;vertical-align: middle;}
	.ygc .inf3 .ite .wb{display: inline-block;font-size: 24rpx;margin-left:10upx}
	

</style>
