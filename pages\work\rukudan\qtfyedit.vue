<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >其他收费</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
		
		
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
										
										<view class="maxcon">
										    <view>{{spdata.title}}</view>
										</view>
										
										<view class="maxcon" style="padding:5upx 30upx;display: none;">
											<view class="rowx">
												<view class="tit"><text class="redst"></text>操作类型</view>
												<view class="inpcon">
												   <view class="inp">
													   <radio-group  @change="radio_type">
															<label  ><radio :value="1" :checked="type == 1" color="#FFD427" /> 增加</label>
															<label  style="margin-left:10px"><radio :value="2" :checked="type == 2" color="#FFD427" /> 减少</label>
													   </radio-group>
												   </view>
												</view>
												<view class="clear"></view>
											</view>
										</view>
										
										<view class="maxcon">
												<view class="vstit">费用金额</view>
												<view class="jinecon">
													<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
													<input  type="digit"  name='jine' v-model="jine"  placeholder='在此输入费用金额' maxlength='60' class="inputss" />
												</view>
										</view>
										<view class="maxcon">
												<view class="vstit">费用说明</view>
												
												<view class="bzcon">
													  <textarea name="remark" class="beizhu" v-model="remark"></textarea>
												</view>
									
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx"> 
																<block v-for="(item,index) in img_url_ok" :key="index">
																   <view class='item'  >
																	   <view class="del" @tap="deleteImg"   :data-index="index" ><i class="iconfont icon-shanchu2"></i></view>
																	   <image  :src="staticsfile+item" :data-src='staticsfile + item ' @tap='previewImage' ></image>
																   </view>
																</block>
															   <view class='item' v-if="img_url_ok.length <= 8" >
																   <image @tap="chooseimage"  src='/static/images/uppicaddx.png'></image>
															   </view>
															   <view class="clear"></view>
													 </view>
												</view>		
												 
												 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  			<view class="tn-flex tn-footerfixed">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>提交</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
				

	
	</view>
  
	<view style="height:120upx"></view>
    
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 status:0,
			 jine:0,
			 img_url: [],
			 img_url_ok:[],
			 spid:0,
			 djid:0,
			 fyid:0,
			 spname:'',
			 spdata:[],
			 fydata:[],
			 jine:'',
			 remark:'',
			 type:1,
		  }
	},

	onLoad(options) {
		let that=this;
		let spid=options.spid ? options.spid : 0 ;
		let djid=options.djid ? options.djid : 0 ;
		let fyid=options.fyid ? options.fyid : 0 ;
		this.spid=spid;
		this.djid=djid;
		this.fyid=fyid;
		this.loadData();
		
	},
	onShow() {
		
	},

    methods: {
		toback:function(){
		   uni.navigateBack();
		},
		
		radio_type(e) {
			this.type=e.detail.value;
		},	
					   
		loadData(){
				  let that=this;
				  let da={
					  fyid:that.fyid,
					  spid:that.spid,
				  }
				  uni.showLoading({title: ''})
				  this.$req.post('/v1/danju/rkdqtfyedit', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 	            that.spdata=res.data.spdata;
				 	            that.fydata=res.data.fydata;
								that.remark=res.data.fydata.remark;
								that.jine=res.data.fydata.jine;
								that.img_url_ok=res.data.fydata.picturesarr;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			chooseimage(){
								   let that = this;
								   let img_url_ok=that.img_url_ok;
								   uni.chooseImage({
									   count: 9, //默认9
									   sizeType: ['original', 'compressed'],
									   success: (resx) => {
										  const tempFilePaths = resx.tempFilePaths;
										  
										
												  for(let i = 0;i < tempFilePaths.length; i++) {
														  let da={
															  filePath:tempFilePaths[i],
															  name: 'file'
														  } 
														  uni.showLoading({title: '上传中...'})
														  this.$req.upload('/v1/upload/upfile', da)
																  .then(res => {
																	uni.hideLoading();
																	// res = JSON.parse(res);
																	if(res.errcode==0){
																		img_url_ok.push(res.data.fname);
																		that.img_url_ok=img_url_ok;
																		
																		uni.showToast({
																			icon: 'none',
																			title: res.msg
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
														  
														  })
												}
										   
									   }
								   });
							   
							},
							previewImage: function (e) {
									  let that = this;
									  let src = e.currentTarget.dataset.src;
									  let img_url_ok=that.img_url_ok;
									  let imgarr=[];
									  img_url_ok.forEach(function(item,index,arr){
										  imgarr[index] = that.staticsfile+item;
									  });
									  wx.previewImage({
										  current: src,
										  urls: imgarr
									  });
							},
							deleteImg: function (e) {
								let that = this;
								let index = e.currentTarget.dataset.index;
								let img_url_ok = that.img_url_ok;
								img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
								that.img_url_ok=img_url_ok;
							},
		
		
		
			formSubmit: function(e) {
						let that=this;
					   var formdata = e.detail.value
						
							
									 let pvalue = e.detail.value;
									 
										if(!that.jine || that.jine < 0){
										  uni.showToast({
											icon: 'none',
											title: '请输入费用金额',
										  });
										  return false;
										}
										
										if(!that.remark){
										  uni.showToast({
											icon: 'none',
											title: '请输入费用说明',
										  });
										  return false;
										}
									  
								       uni.showModal({
													title: '',
													content: '以上信息已确认无误并提交吗？',
													success: function(e) {
														//点击确定
														if (e.confirm) {
												                   uni.showLoading({title: '处理中...'})
																   let da={
																		'fyid': that.fyid,
																		'djid': that.djid,
																		'spid': that.spid,
																		'jine': that.jine,
																		'type': that.type,
																		'remark': that.remark ? that.remark : '',
																		'pictures': that.img_url_ok ? that.img_url_ok  : '' ,
																   }
																   that.$req.post('/v1/danju/rkdqtfyeditsave', da)
																  .then(res => {
																	uni.hideLoading();
																	console.log(res);
																	if(res.errcode==0){
																		let xspid=res.data.spid;
																		uni.setStorageSync('thupsplist',1);
																		uni.setStorageSync('thupspotfyspid',xspid);
																		uni.showToast({
																			icon: 'none',
																			title: res.msg,
																			success() {
																				setTimeout(function(){
																					uni.navigateBack()
																				},1000)
																			}
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
																	
																  })
																  
															
														}
													}
								})	  
									  
									  
									  
									  
									  
									  
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.toplinex{border-top:1px solid #fff}

.matxcont{margin:32upx;padding:32upx;border-radius:20upx;background: #fff;min-height: 500upx;}
.notixiancon{text-align: center;}
.tipcon{padding:50px 0}
.tipcon text{font-size:55px;color:#FFD427}
.smlink{margin-top:40px}

.ttinfo{position: relative;}
.ttinfo .icon{position: absolute;right:0upx;top:30upx}
.ttinfo .icon .iconfont{color:#ddd}
.ttinfo .inf1{text-align: center;border-bottom:1px solid #F0F0F0;padding-bottom:32upx;margin-bottom:32upx}
.ttinfo .inf1 .tx{}
.ttinfo .inf1 .xx{padding-top:10upx}
.ttinfo .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;margin:0 auto;}
.ttinfo .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.ttinfo .inf1 .tel{color: #7F7F7F;font-size: 24rpx;margin-top:10upx}
.ttinfo .inf1 .tel text{margin-right:30upx}
.ttinfo .inf2{text-align: center;padding-bottom:30upx}
.ttinfo .inf2 .kyjine{font-size: 48rpx;font-weight: 600;}
.ttinfo .inf2 .tmsm{color: #7F7F7F;margin-top:20upx;font-size: 24rpx;}


.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{margin-bottom:20upx}


.sqbdcon{margin:32upx;}


.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.rowxmttp{margin-top:30upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:200}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}

.jinecon{position: relative;margin:40upx 0}
.jinecon .icon{position: absolute;left:0;top:10upx;}
.jinecon .icon .iconfont{font-size:40upx}
.jinecon .inputss{font-size:40upx;color:#000;padding-left:40upx}

 
 .rowx{position: relative;	border-bottom:1px solid #efefef; }
 .rowx .tit{float:left;font-size:28upx;height:90upx;line-height:90upx;}
 .rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 110px);font-size:28upx;height:90upx;line-height:90upx;}
 .rowx .inp .input{height:90upx;line-height:90upx;}
 .rowx:last-child{border-bottom:0;}
 
 
</style>

