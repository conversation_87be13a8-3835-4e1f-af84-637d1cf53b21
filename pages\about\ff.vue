<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex=100>
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >入库单</text>
					</view>
					<view slot="right" >
					</view>
					
		</tn-nav-bar>
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px;">
			
                <view class="tbdhdown">
					<view class="xite" :class="ddtype==0 && sta==0  ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="0"><view class="con">全部</view></view>
					<view class="xite" :class="dwtype==1 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="1"><view class="con">{{ddtype!=0 ? ddtypetxt : '类型'}} <text class="iconfont icon-jiantou2"></text></view></view>
					<view class="xite" :class="dwtype==2 ? 'hoverss' : ''"  @click="tbdhdownclk" :data-dwtype="2"><view class="con">{{sta!=0 ? statxt : '状态'}} <text class="iconfont icon-jiantou2"></text></view></view>
				</view>
			</view>
			<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;" >
				<view class="item btnimg" @click="adddanju" >
					<image src="/static/images/addbtn.png"></image>
				</view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		  
		  
		  
		</view>
	
	

		
	
	
		
	  
	
		
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				statxt:'状态',
				ddtype:0,
				ddtypetxt:'类型',
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{value: 0,name: '不限状态'},
					{value: 1,name: '已审核'},
					{value: 2,name: '待审核'},
					{value: 3,name: '未通过'},
					{value: 9,name: '录入中'},
				],
				ddtypedata:[
					{value: 0,name: '不限类型'},
					{value: 1,name: '寄卖服务'},
					{value: 2,name: '实物回收'},
					{value: 3,name: '质押暂存'},
					{value: 4,name: '信用预付'},
				],
				show3:false,
			    checked: true,
				tbdhdownshow:false,
				dwtype:0,
				
				
			}
		},
		onLoad(options) {
			let that=this;
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
		},
		onShow() {
		
			let that=this;
			if(uni.getStorageSync('thupdpgllist')==1){
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems(); 
				uni.setStorageSync('thupdpgllist',0)
			}
		
		},
		methods: {
			 // switch打开或者关闭时触发，值为true或者false
			change(status) {
				console.log(status);
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

		
			adddanju:function(){
				  let that=this;
				  uni.navigateTo({
				  	url:'./edit'
				  })
				  
			  },
		
				
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							sta:that.sta ? that.sta : 0 ,
							ddtype:that.ddtype ? that.ddtype : 0 ,
							kw:that.thkw ? that.thkw : '' ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/ckgl/rkdlist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
				
						
			    tbdhdownclk(e){
					let that=this;
					let dwtype = e.currentTarget.dataset.dwtype;
				
					that.dwtype=dwtype;
					if(dwtype==0){
						that.tbdhdownshow=false;
						this.thkw='';
						this.thkwtt='';
						that.ddtype = 0;
						
						that.thkw = '';
						that.sta = 0;
						that.page = 0;
						that.listdata = [];
						that.isloading = false;
						that.loadingType = -1;
						that.loaditems();
					}else{
						that.tbdhdownshow=true;
					}
					
				},
				tbdhdownitemsel(e){
					let that=this;
					let dwtype = e.currentTarget.dataset.dwtype;
					let value = e.currentTarget.dataset.value;
					let name = e.currentTarget.dataset.name;
				
				    if(dwtype==1){
				    	that.ddtype=value;
				    	that.ddtypetxt=name;
				    }
				
				    if(dwtype==2){
						that.sta=value;
						that.statxt=name;
					}
					
					that.dwtype=dwtype;
					that.tbdhdownshow=false;
					
					// that.thkw = '';
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
					
				},
				caozuo:function(e){
					  let that=this;
					  let djid = e.currentTarget.dataset.djid;
					  let cztype = e.currentTarget.dataset.cztype;
					  if(cztype==1){
						 uni.navigateTo({
							url:'./view?djid='+djid
						 })
					  }
					  if(cztype==2){
						 uni.navigateTo({
							url:'./edit?djid='+djid
						 })
					  }
					  if(cztype==3){
						  uni.showModal({
						  	title: '删除确认',
						  	content: '删除后不可恢复！确认删除吗？',
						  	success: function(e) {
						  		//点击确定
						  		if (e.confirm) {
						  			let da = {
						  				'djid': djid
						  			}
						  			uni.showLoading({title: ''})
						  			that.$req.post('/v1/ckgl/rkddel', da)
						  				.then(res => {
						  					uni.hideLoading();
						  					if (res.errcode == 0) {
						  						uni.showToast({
						  							icon: 'none',
						  							title: res.msg,
						  							success() {
						  								setTimeout(function() {
						  										that.page = 0;
						  										that.listdata = [];
						  										that.isloading = false;
						  										that.loadingType = -1;
						  										that.loaditems();
						  								}, 1000);
						  							}
						  						});
						  					} else {
						  						uni.showModal({
						  							content: res.msg,
						  							showCancel: false,
						  						})
						  					}
						  				})
						  		}
						  	}
						  })
								
								
								
					  }
				  },	
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}


.ssnrtip{height:124upx;line-height:124upx;background:#f6f6f6;position: relative;padding:0 32upx}
.ssnrtip .con{}
.ssnrtip .con text{color:#3366ff}
.ssnrtip .cls{position: absolute;right:32upx;}
.ssnrtip .cls text{font-size:45upx}



.djlist{padding:10upx 32upx 32upx 32upx}
.djlist .item{background: #FFFFFF;border-radius: 20rpx;margin-bottom:20upx;padding:28upx 28upx;position: relative;font-size:24upx;;}
.djlist .item .status{position: absolute;right:32upx;top:82upx}
.djlist .item .status.ys9{color:#3366ff}
.djlist .item .status.ys1{color:#43b058}
.djlist .item .status.ys2{color:#f86c02}
.djlist .item .status.ys3{color:#333}



.djlist .item .ddtype{position: absolute;left:0;top:0;font-size:24upx;border-radius: 20rpx 0rpx 20rpx 0rpx;height:42upx;line-height:42upx;padding:0 15upx;color:#0EC45C}
.djlist .item .ddtype.ys1{background:#f86c02;color:#fff}
.djlist .item .ddtype.ys2{background:#63b8ff;color:#fff}
.djlist .item .ddtype.ys3{background:#c76096;color:#fff}
.djlist .item .ddtype.ys4{background:#43b058;color:#fff}
.djlist .item .inf1{line-height:50upx;padding-top:30upx;font-size:28upx}
.djlist .item .inf1 .khname{font-weight:700;margin-right:10upx;color:#333}
.djlist .item .inf1 .khid{color:#888}
.djlist .item .inf2{border-top:1px solid #eee;padding-top:20upx;margin-top:20upx;line-height:40upx;position: relative;color:#888}
.djlist .item .inf2 .setbtn{position: absolute;right:0;top:35upx;width: 100rpx;height: 50upx;line-height:50upx;background: #FFD427;border-radius: 8rpx;text-align: center;color:#333}
.djlist .item .inf2 .setbtn2{position: absolute;right:120upx;top:35upx;width: 100rpx;height: 50upx;line-height:50upx;background: #efefef;border-radius: 8rpx;text-align: center;color:#333}
.djlist .item .inf2 .setbtn3{position: absolute;right:0;top:35upx;width: 100rpx;height: 50upx;line-height:50upx;background: #fafafa;border-radius: 8rpx;text-align: center;color:#333;border:1px solid #eee}

</style>
