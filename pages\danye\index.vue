<template>
	<view class="template-news tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >{{viewdata.title}}</text>
					</view>
					<view slot="right" >
					</view>
					
		</tn-nav-bar>
	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		

		<view class="viewcon" >
			  <view class="nrcon">
				   <mp-html :content="viewdata.content" />
			  </view>
		</view>
		  
	
		
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				html:'',
				id:0,
		      	viewdata:[]
			}
		},
		onLoad(options) {
			let id=options.id;
		
			this.id=id;

			
			this.loadData();
		},
		onShow(){
		
		},
		methods: {
			 loadData(){
				  let that=this;
				  let id=that.id; 
				  let da={  
				 	id:id
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/danye/index', da)
				         .then(res => {
				 		    uni.hideLoading();
						
				 			if(res.errcode==0){
				 			   that.viewdata=res.data.viewdata;
							     uni.setNavigationBarTitle({
									 title: res.data.viewdata.title
								 });
				 			}else{
				 				uni.showToast({
				 					icon: 'none',
				 					title: res.msg,
									success() {
										setTimeout(function(){
											uni.navigateBack();
										},1000)
									}
				 				});
				 			}
				         })
			 }	
		},
	
	
	}
</script>

<style>
	page{background:#fff;}
     .viewcon{margin:30upx 40upx 30upx 40upx;}
     .viewcon .nrcon{
		 color: #333333;
		 line-height: 40upx;
		 font-size: 28upx;
	 }

	
</style>
