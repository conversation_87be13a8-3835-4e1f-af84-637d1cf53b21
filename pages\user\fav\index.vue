<template>
  <view class="tn-safe-area-inset-bottom">
	
     <tn-nav-bar fixed  customBack :bottomShadow="false" >
			 <view slot="back" class='tn-custom-nav-bar__back'
				  >
				  <text class='icon tn-icon-left'></text>
			 </view>
	  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
	  		  <text class="tn-text-bold tn-text-xl "  >我的收藏</text>
	  		</view>
	  </tn-nav-bar>
		
	
	<!-- 顶部自定义导航 -->
	<view class="tabs-fixed tn-bg-white tn-shadow">
	  <view class="tn-flex tn-flex-col-between tn-flex-col-center " :style="{marginTop: vuex_custom_bar_height + 'px'}">
	    <view class="justify-content-item" style="width: 87vw;overflow: hidden;">
	      <tn-tabs :list="hdstadata" :current="current" :isScroll="true" :activeColor="mbjson.ztcolor" :bold="true" :fontSize="28" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
	    </view>
	    <view class="justify-content-item" style="width: 13vw;overflow: hidden;" >
	   
	        <view class="tn-color-black tn-round" style="font-size: 45rpx;margin-top: -5rpx;margin-left: 15rpx;"  @click="swtab()">
	          <text class="tn-icon-level tn-padding-xs" ></text>
	        </view>
	    
	    </view>
	  </view>  
	</view>
	
	
	    
	<view class="" :style="{paddingTop: vuex_custom_bar_height + 40 + 'px'}" >
	
	
	    <view class="tn-margin-bottom-lg">
	      
		  <block v-for="(item,index) in listdata" :key="index">
	          <view class="article-shadow tn-margin-sm hditems" >
				  
				<view class="stacon" @click="delth" :data-id="item.id" >
					<text class="iconfont icon-delete"></text>
				</view>
	          	<view class="tn-flex" @click="tn" :data-mpskurl="item.mpskurl"   >
					
	          	  <view class="image-pic tn-margin-sm" >
					  <image :src="item.favimg" mode="widthFix"></image>
	          	  </view>
	          	  <view class="tn-margin-sm tn-padding-top-xs" style="width: 100%;margin-left:0">
	          		<view class="tn-text-lg tn-text-bold clamp-text-1 tn-text-justify">
	          		  {{ item.title }}
	          		</view>
	          		<view  style="margin-top:5px;margin-bottom:5px;line-height:23px;font-size:12px;overflow: hidden;color:#333">
						<view>类型：<span>{{ item.typename }}</span></view> 
						<view>时间：<span>{{ item.addtime }}</span></view> 
	          		</view>
	          	  </view>
				  
				  
	          	</view>
			
				
				
	        </view>
	      </block>
	      
	    </view>
	  
		<view class="clear"></view>
		<text class="loading-text" v-if="isloading" >
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
		</text>
	  
	</view>
	<view class='tn-tabbar-height' ></view>
	
	

	<!-- <view class="tn-flex tn-footerfixed">
	  <view class="tn-flex-1 justify-content-item tn-margin-sm tn-text-center">
		<tn-button backgroundColor="#00FFC6" padding="40rpx 0" width="50%"  shadow fontBold @click="gotorh" >
		  <text class="tn-color-black">清空收藏夹</text>
		</tn-button>
	  </view>
	</view> -->
		
	</view>
</template>

<script>
	import template_page_mixin from '@/libs/mixin/template_page_mixin.js'
	
	export default {
		name: 'TemplateSet',
		mixins: [template_page_mixin],
		data() {
			return {
				mbjson:'',
				hdstadata:[],
				type:0, 
				// 筛选
				index: 1,
				current: 0,
				
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				
				
			}
		},
		onLoad(options) {
			let that=this;
			
			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				that.mbjson = mbjson;
			}
			
			that.loadData();
			that.page = 0;
			that.type=0;
			that.listdata = [];
			that.isloading = false;
			that.loadingType = -1;
			that.loaditems();
			
		},
		onShow() {
			let that=this;
		
		},
	
		methods: {
	
			// 跳转
			tn(e) {
				let that=this;
				let dataset=e.currentTarget.dataset;
				let skurl=dataset.mpskurl;
				uni.navigateTo({
					url: skurl,
				});
			},
			// tab选项卡切换
			tabChange(index) {
				let that=this;
				let type=that.hdstadata[index].type;
				that.current = index;
				that.type = type;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
		 swtab:function(e){
		 	let that=this;
			that.current = 0
		 	that.type = 0;
		 	that.page = 0;
		 	that.listdata = [];
		 	that.isloading = false;
		 	that.loadingType = -1;
		 	that.loaditems();
		 },
		 
		 loadData(){
		 	  let that=this;
		 	  let sta=that.sta; 
		 	  let da={  
		 		
		 	  }
		 	 uni.showLoading({title: '读取中...'})
		 	 this.$req.post('/v2/muser/fav', da)
		 	 .then(res => {
		 		uni.hideLoading();
		 		console.log(res);
		 		if(res.errcode==0){
					that.hdstadata = res.data.hdstadata;
		 		}else{
		 			uni.showModal({
		 				content: res.msg,
		 				showCancel: false
		 			})
		 		}
		 	 })
		 },	
		 			//加载更多
		 			onReachBottom(){
		 				this.loaditems();
		 			},
		 		loaditems() {
		 			let that=this;
		 			let page = ++that.page;
		 			let da={
		 				page:page,
		 				type:that.type,
		 			}
		 			if(page!=1){
		 				that.isloading=true;
		 			}
		 			if(that.loadingType==2){
		 				return false;
		 			}
		 			uni.showNavigationBarLoading();
		 			this.$req.get('/v2/muser/favlist', da)
		 			        .then(res => {
		 						console.log(res);
		 						if (res.data.listdata.length > 0) {
		 							that.listdata = that.listdata.concat(res.data.listdata); 
		 							that.loadingType=0
		 						}else{
		 							that.loadingType=2
		 						}
		 					 uni.hideNavigationBarLoading();
		 			})
		 		},
		 			
		   
		   delth(e){
			   let that=this;
			   let id=e.currentTarget.dataset.id;
			   uni.showModal({
			   	title: '删除确认',
			   	content: '删除后不可恢复！确认删除吗？',
			   	success: function(e) {
			   		//点击确定
			   		if (e.confirm) {
			   
			   			let da = {
			   				'favid': id
			   			}
			   			uni.showLoading({
			   				title: ''
			   			})
			   			that.$req.post('/v2/muser/favdel', da)
			   				.then(res => {
			   					uni.hideLoading();
			   					if (res.errcode == 0) {
			   						uni.showToast({
			   							icon: 'none',
			   							title: res.msg,
			   							success() {
			   								setTimeout(function() {
			   										that.page = 0;
			   										that.listdata = [];
			   										that.isloading = false;
			   										that.loadingType = -1;
			   										that.loaditems();
			   								}, 1000);
			   							}
			   						});
			   					} else {
			   						uni.showModal({
			   							content: res.msg,
			   							showCancel: false,
			   						})
			   					}
			   				})
			   
			   
			   
			   		}
			   
			   
			   
			   
			   	}
			   })
			   						
			   
		   },
		   
		   
		}
	}
</script>

<style lang='scss'>
	
	.pages-a { 
		  max-height: 100vh;
	  // background-image: linear-gradient(to top, #4C3FAE 20%, #6E26BA 80%);
	}
	
	
	
	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 150upx;
		height: calc(140upx + env(safe-area-inset-bottom) / 2);
	}
	
	
	/* 自定义导航栏内容 start */
	.custom-nav {
	  height: 100%;
	  
	  &__back {
	    margin: auto 5rpx;
	    font-size: 40rpx;
	    margin-right: 10rpx;
	    margin-left: 30rpx;
	    flex-basis: 5%;
	  }
	  
	  &__search {
	    flex-basis: 58%;
	    width: 100%;
	    height: 100%;
	    
	    &__box {
	      width: 100%;
	      height: 70%;
	      padding: 10rpx 0;
	      margin: 0 30rpx;
	      border-radius: 60rpx 60rpx 0 60rpx;
	      font-size: 24rpx;
	    }
	    
	    &__icon {
	      padding-right: 10rpx;
	      margin-left: 20rpx;
	      font-size: 30rpx;
	    }
	    
	    &__text {
	      // color: #AAAAAA;
	    }
	  }
	}
	 
	.tabs-fixed{
	  position: fixed;
	  top: 0;
	  width: 100%;
	  transition: all 0.25s ease-out;
	  z-index: 1;
	}
	
	
	/* 标签内容 start*/
	.tn-tag-content {
	  &__item {
	    display: inline-block;
	    line-height: 35rpx;
	    padding: 5rpx 25rpx;
	
	    &--prefix {
	      padding-right: 10rpx;
	    }
	  }
	}
	
	
	
		 .image-pic{padding:5px;display:flex;align-items: center;border:1px solid #eee;}
		 .image-pic image{max-width:80px;max-height:80px;display: block;}
	
	
	.article-shadow {
	  border-radius: 15rpx;
	  box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}
	
	/* 文字截取*/
	.clamp-text-1 {
	  -webkit-line-clamp: 1;
	  display: -webkit-box;
	  -webkit-box-orient: vertical;
	  text-overflow: ellipsis;
	  overflow: hidden;
	}
	
	
	
	.hditems{position: relative;}
	.hditems .more{position: absolute;right:20rpx;bottom:20rpx;color:#888}
	.hditems .stacon{position: absolute;right:20upx;bottom:20upx}
	.hditems .stacon text{font-size:18px;color:#888}
	  
	  /* 图标容器9 start */
	  .icon9 {
	    &__item {
	      width: 30%;
	      background-color: #FFFFFF;
	      border-radius: 10rpx;
	      padding: 30rpx;
	      margin: 20rpx 10rpx;
	      transform: scale(1);
	      transition: transform 0.3s linear;
	      transform-origin: center center;
	      
	      &--icon {
	        width: 90rpx;
	        height: 90rpx;
	        font-size: 65rpx;
	        border-radius: 50%;
	        margin: 20rpx 40rpx;
	        position: relative;
	        z-index: 1;
	        
	        &::after {
	          content: " ";
	          position: absolute;
	          z-index: -1;
	          width: 100%;
	          height: 100%;
	          left: 0;
	          bottom: 0;
	          border-radius: inherit;
	          opacity: 1;
	  
	        }
	      }
	    }
	  }
	  

	
	.gwcno{text-align: center;padding-top:50px}
	.gwcno .icon{padding-top:30px}
	.gwcno .icon i{font-size:55px;color:#888;}
	.gwcno .tit{height:45px;line-height:45px;margin-bottom:12px;}
	.gwcno .gotobtn{background:#d9a26c;color:#fff;font-size:14px;padding:0 10px;border-radius:100px;width:80px;height:30px;line-height:30px;margin:0 auto;}
	
	.status1{color:#4CAF50}
	.status2{color:#7293FF}
	.status3{color:#ff6600}
	.status9{color:#888}


.czbtn{border-top:1px solid #eee;display: flex;text-align: center;border-radius:10px 10px 0 0;}
.czbtn .itm{flex: 1;border-right:1px solid #eee;height:90upx;line-height:90upx;}
.czbtn .itm.last{border-right:0;}
.czbtn .itm .iconfont{margin-right:4px}




</style>
