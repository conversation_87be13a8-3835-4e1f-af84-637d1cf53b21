<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>企业客户信息</text>
			</view>
			<view slot="right">
				
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">


			<view class="sqbdcon">

				<view class="maxcon">
					<view class="ttinfo">
					
					    <block v-if="khdata.isedit==1">
					    	<block v-if="khdata.userid">
					    		<view class="jcbdbtn" @click="jclyhbd">解除账号关联</view>
					    	</block>
					    	<block v-else>
					    	  <text class="wgluid"  @click="bdlyudi" :data-khid="khdata.id" >关联账号</text>
					    	</block> 
					    </block> 
					
						<view class="inf1">
							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + khdata.avatar"></image>
								</view>
								<view class="xx">
									<view class="name">{{khdata.realname}}</view>
									<view class="tel">客户号:{{khdata.id}} <text v-if="khdata.userid"
											style="margin-left:40upx">漏鱼UID:{{khdata.userid}}</text> </view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
					</view>
				</view>


				<view class="maxcon">

					<view class="vstit">基本信息</view>

					<view class="rowx" @click="showModal3" data-zdtype="2">
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">客户姓名</view>
						<view class="inpcon">
							<view class="inp">{{khdata.realname}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx" @click="showModal3" data-zdtype="3">
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">手机号码</view>
						<view class="inpcon">
							<view class="inp">{{khdata.lxtel}}</view>
						</view>
						<view class="clear"></view>
					</view>


					<view class="rowx" @click="showModal3" data-zdtype="4">
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">身份证号</view>
						<view class="inpcon">
							<view class="inp">{{khdata.sfzhao ? khdata.sfzhao : '点击编辑身份证号'}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx" @click="selkhdj">
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">客户等级</view>
						<view class="inpcon">
							<view class="inp">{{khdata.dengjitxt ? khdata.dengjitxt : '点击选择客户等级'}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">客户生日</view>
						<view class="inpcon">
							<view class="inp">
								<picker mode="date" :value="shengri" @change="bindDateChange">
									{{khdata.shengri ? khdata.shengri : '点击设置客户生日'}}
								</picker>
							</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" @click="showModal3" data-zdtype="6">
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">现住址</view>
						<view class="inpcon">
							<view class="inp">{{khdata.dizhi ? khdata.dizhi : '点击编辑现住址'}}</view>
						</view>
						<view class="clear"></view>
					</view>
					
					<view class="rowx" @click="gotosetfzr" >
						<view class="icon"><text class="iconfont  icon-bianji" v-if="khdata.isedit==1"></text></view>
						<view class="tit">所属员工</view>
						<view class="inpcon">
							<view class="inp">{{khdata.ygname ? khdata.ygname : '设置负责人'}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" >
						<view class="tit">所属店铺</view>
						<view class="inpcon">
							<view class="inp">{{khdata.mdname ? khdata.mdname : '无'}}</view>
						</view>
						<view class="clear"></view>
					</view>
				</view>
				
				
			
				

				<view class="maxcon">
					<view class="rowxmttp">
						
							<view class="tit" style="font-weight:700"><text class="redst"></text>身份证信息</view>
							<view class="zjzkicon" @click="setvzz">{{vsfnr==0 ? '展开' : '收起'}} 
								<text class="iconfont icon-jiantou_liebiaozhankai"></text>
							</view>
						
							<block v-if="vsfnr==1">
									<view class="uprzimgcon">
										<view class="zzx sf1">
											<view class="con">
												<view class="vt1" @click="uprzimg1">
													<block v-if="rzimg1">
														<image :src='staticsfile + rzimg1'></image>
													</block>
													<block v-else>
														<image src="/static/images/rzupimg1.png"></image>
													</block>
												</view>
												<view class="vt2">身份证头像面</view>
												<view class="clear"></view>
											</view>
										</view>
										<view class="zzx sf2">
											<view class="con">
												<view class="sf2" @click="uprzimg2">
													<block v-if="rzimg2">
														<image :src='staticsfile + rzimg2'></image>
													</block>
													<block v-else>
														<image src="/static/images/rzupimg2.png"></image>
													</block>
												</view>
												<view class="vt2">身份证国徽面</view>
											</view>
										</view>
										<view class="clear"></view>
									</view>
						</block>	

					</view>
				</view>



				<view class="maxcon"> 
						<view class="tit" style="font-weight:700"><text class="redst"></text>其他信息</view>
						<view class="zjzkicon" @click="setvzz2">{{vsfnr2==0 ? '展开' : '收起'}} 
							<text class="iconfont icon-jiantou_liebiaozhankai"></text>
						</view>
						<block v-if="vsfnr2==1">
							<view class="rowx" >
								<view class="tit">录入店铺</view>
								<view class="inpcon">
									<view class="inp">{{khdata.lrmdname ? khdata.lrmdname : '--'}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit">录入员工</view>
								<view class="inpcon">
									<view class="inp">{{khdata.lrygname ? khdata.lrygname : '--'}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit">录入时间</view>
								<view class="inpcon">
									<view class="inp">{{khdata.addtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>	
				</view>


				<view class="maxcon" >
						<view class="tit" style="font-weight:700"><text class="redst"></text>负责人变更记录</view>
						<view class="zjzkicon" @click="setvzz3">{{vsfnr3==0 ? '展开' : '收起'}} 
							<text class="iconfont icon-jiantou_liebiaozhankai"></text>
						</view>
						<block v-if="vsfnr3==1">
							
							
							<view class="khfzrbgrcd">
							
								<block v-if="khfrzrcd">
									<block v-for="(item,index) in khfrzrcd" :key="index">
										<view class="lis-item  ">
											<view class="nrcon">
												<view class="info">
												
													
													<view><text>变更：</text>
													  {{item.orygname}} <text class="iconfont icon-xiangyou" style="margin:0 20upx"></text> {{item.ygname}}
													</view>
													<view><text>时间：</text>{{item.addtime}}</view>
													<view><text>说明：</text>{{item.remark}}</view>
													<block v-if="item.picturescarr">
														<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
															详情 <text class="iconfont icon-jtdown2"></text>
														</view>
													</block>
													<block v-if="item.id==thqtcbvid">
														<view class="des">
															<block v-if="item.picturescarr">
																<view class="pjimgcon">
																	<block v-for="(itemx,indexx) in item.picturescarr"
																		:key="indexx">
																		<view class='item'>
																			<image :src="staticsfile+itemx"
																				:data-src='staticsfile + itemx '
																				:data-picturescarr="item.picturescarr"
																				@tap='previewImagex'></image>
																		</view>
																	</block>
																	<view class="clear"></view>
																</view>
															</block>
														</view>
													</block>
							
												</view>
												<view class="clear"></view>
											</view>
										</view>
							
									</block>
							
							
								</block> 
								<block v-else>
									<view class="gwcno">
										<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
										<view class="tit">暂无变更记录</view>
									</view>
								</block>
							
						    </view>	
							
						</block>	
				</view>




			</view>







		</view>

		<view style="height:20upx"></view>

		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">请输入{{zdtxt}}</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;text-align: center;">
						<input :placeholder="'请输入'+zdtxt" v-model="zdvalue" name="input"
							placeholder-style="color:#AAAAAA" maxlength="50"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="upzdvalue">
						<text style="color:#333">保 存</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>

		<tn-popup v-model="khdjselshow" mode="bottom" :borderRadius="20" :closeBtn=true>
			<view class="popuptit">选择客户等级</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="khdjlist">
					<block v-if="khdjdata">
						<block v-for="(item,index) in khdjdata" :key="index">

							<view class="item" @click="selthkhdj" :data-dengji="item.dengji" :data-djtxt="item.title">
								<view class="yxicon" v-if="thkhdj==item.dengji"><text
										class="iconfont icon-duigou1"></text></view>
								<view class="tit">{{item.title}}</view>
							</view>

						</block>
					</block>
				</view>
			</scroll-view>
		</tn-popup>

		

	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				khdata: '',
				status: 0,
				multiArray: [],
				objectDiqudata: [],
				multiIndex: [0, 0, 0],
				img_url: [],
				img_url_ok: [],
				show3: false,
				zdvalue: '',
				zdtxt: '',
				zdtype: '',
				shengri: '',
				rzimg1: '',
				rzimg2: '',
				khdjselshow: false,
				khdjdata: '',
				khfrzrcd: '',
		
				thkhdj: 0,
				isxgssdp:0,
				isxgssdp:0,
				vsfnr:0,
				vsfnr2:0,
				vsfnr3:0,
				
				thqtcbvid:0,
			}
		},

		onLoad(options) {
			let that = this;
			let khid = options.khid ? options.khid : 0;
			this.khid = khid;
		},
		onShow() {
			this.loadData();

			let thygid = uni.getStorageSync('thygid');
			if (thygid) {
				this.zdtype = 12;
				this.zdvalue = thygid;
				this.upzdvalue();
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
			}

		},

		methods: {
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}
			
			},
			gotosetfzr(){
				let khid=this.khid;
				uni.navigateTo({
					url:'/pages/work/qykhgl/setfzr?khid='+khid
				})
			},
			bdlyudi(e){
				let khid=e.currentTarget.dataset.khid;
				uni.navigateTo({
					url:'/pages/work/khgl/bdlyuidewm?khid='+khid
				})
			},
			selyg() {
				
				if(this.isxgssyg!=1){
					return false;
				}
				uni.navigateTo({
					url: '/pages/selyg/index?stype=6&isdx=0'
				})
			},
			selkhdj() {
				if(this.khdata.isedit!=1){
					return false;
				}
				this.khdjselshow = true;
			},
			selthkhdj(e) {
				let dengji = e.currentTarget.dataset.dengji;
				this.zdtype = 13;
				this.zdvalue = dengji;
				this.thkhdj = dengji;
				this.upzdvalue();
				this.khdjselshow = false;
			},
			
			loadData() {
				let that = this;
				let khid = that.khid;
				let da = {
					khid: khid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/qykhgl/khinfo', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.khdata = res.data.khdata;
							that.dpdata = res.data.dpdata;
							that.khfrzrcd = res.data.khfrzrcd;
							that.khdjdata = res.data.khdjdata;
							that.rzimg1 = res.data.khdata.rzimg1 ? res.data.khdata.rzimg1 : '';
							that.rzimg2 = res.data.khdata.rzimg2 ? res.data.khdata.rzimg2 : '';
							
							that.isxgssdp = res.data.isxgssdp;
							that.isxgssyg = res.data.isxgssyg;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			gotoygglurl(url) {
				uni.navigateTo({
					url: url + '?khid=' + this.khid
				})
			},



			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				console.log(src);
				let imgarr = [src];
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},
			showModal3(e) {
				console.log(e);
				
				if(this.khdata.isedit!=1){
					return false;
				}
				
				let that = this;
				let dataset = e.currentTarget.dataset;
				let zdtype = dataset.zdtype;
				if (zdtype == 2) {
					that.zdtype = 2;
					that.zdtxt = '姓名';
					that.zdvalue = that.khdata.realname;
				}
				if (zdtype == 3) {
					that.zdtype = 3;
					that.zdtxt = '手机号码';
					that.zdvalue = that.khdata.lxtel;
				}
				if (zdtype == 4) {
					that.zdtype = 4;
					that.zdtxt = '身份证号';
					that.zdvalue = that.khdata.sfzhao;
				}
				if (zdtype == 6) {
					that.zdtype = 6;
					that.zdtxt = '现住址';
					that.zdvalue = that.khdata.dizhi;
				}
				console.log(zdtype, that.zdvalue);
				this.openModal3()
			},
			// 打开模态框
			openModal3() {
				this.show3 = true
			},


			upzdvalue() {
				let that = this;
				let zdtype = that.zdtype;
				let zdvalue = that.zdvalue;
				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'zdtype': zdtype,
					'zdvalue': zdvalue,
					'khid': that.khid,
				}
				this.$req.post('/v1/qykhgl/upgrinfo', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thupkhlist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
										that.show3 = false;
									}, 1000)
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch(err => {

					})

			},
			bindDateChange(e) {
				let that = this;
				let shengri = e.detail.value;
				
				if(this.khdata.isedit!=1){
					return false;
				}
				
				that.zdtype = 7;
				that.zdvalue = shengri;
				that.upzdvalue();
			},

			uprzimg1() {
				let that = this;
				if(this.khdata.isedit!=1){
					return false;
				}
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg1 = res.data.fname;

									that.zdtype = 8;
									that.zdvalue = res.data.fname;
									that.upzdvalue();

									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},

			uprzimg2() {
				let that = this;
				if(this.khdata.isedit!=1){
					return false;
				}
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;
						let da = {
							filePath: tempFilePaths[0],
							name: 'file'
						}
						uni.showLoading({
							title: '上传中...'
						})
						this.$req.upload('/v1/upload/upfile', da)
							.then(res => {
								uni.hideLoading();
								// res = JSON.parse(res);
								console.log(res);
								if (res.errcode == 0) {
									that.rzimg2 = res.data.fname;

									that.zdtype = 9;
									that.zdvalue = res.data.fname;
									that.upzdvalue();

									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false
									})
								}

							})
					}
				});
			},

			jclyhbd(){
				let that=this;
				uni.showModal({
					title: '',
					content: '确认解除账号关联吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								khid: that.khid,
							}
							uni.showLoading({title: ''})
							that.$req.post('/v1/qykhgl/jclyhbd', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
														that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
						}
					}
				})
				
			},
			setvzz() {
				if (this.vsfnr == 0) {
					this.vsfnr = 1;
				} else {
					this.vsfnr = 0;
				}
			},
			setvzz2() {
				if (this.vsfnr2 == 0) {
					this.vsfnr2 = 1;
				} else {
					this.vsfnr2 = 0;
				}
			},
			setvzz3() {
				if (this.vsfnr3 == 0) {
					this.vsfnr3 = 1;
				} else {
					this.vsfnr3 = 0;
				}
			},


		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .dptit {
		text-align: center;
	}



	.ttinfo {
		position: relative;
	}
    .ttinfo .wgluid{position: absolute;right:10upx;top:10upx;font-size:20upx;background:#FFD427;border-radius: 100upx;padding:5upx 15upx;}

	.ttinfo .jcbdbtn {
		position: absolute;
		right: 0;
		top: 0;
		font-size: 20upx;
		color: #1677FF
	}

	.ttinfo .status {
		position: absolute;
		right: 0;
		top: 0
	}

	.ttinfo .status .aa {
		float: left;
		margin-left: 10upx;
		line-height: 50upx;
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}


	.inf2 {
		padding-top: 20upx;
	}

	.inf2 .vtm {
		float: left;
		width: 33.33%;
		font-size: 28upx
	}

	.inf2 .vtm .tt {
		color: #7F7F7F;
		font-size: 24upx
	}

	.inf2 .vtm .vv {
		color: #333333;
		margin-top: 15upx
	}


	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #333
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}

	.rowx:last-child {
		border-bottom: 0;
	}



	.rowxmttp {
		margn-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx
	}

	.imgconmt image {
		width: 240upx;
		height: 160upx;
		border-radius: 10upx;
	}

	.ifwdh {}

	.ifwdh .tjmul {}

	.ifwdh .tjmul .mli {
		float: left;
		width: 25%;
		text-align: center;
	}

	.ifwdh .tjmul .mli .icon {
		position: relative;
		width: 60upx;
		height: 60upx;
		margin: 0 auto;
	}

	.ifwdh .tjmul .mli .icon image {
		width: 60upx;
		height: 60upx;
		display: block;
		margin: 0 auto;
	}

	.ifwdh .tjmul .tit {
		margin-top: 20upx;
		font-size: 24upx;
		color: #333
	}

	.ifwdh .tjmul.smx .mli {
		margin-bottom: 28upx;
	}



	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}


	.khdjlist {}

	.khdjlist .item {
		padding: 0 30upx;
		height: 100upx;
		line-height: 100upx;
		border-bottom: 1px solid #efefef;
		position: relative;
	}
	
	.khdjlist .item .yxicon {
		position: absolute;
		right: 30upx;
		top: 0;
		color: #FFD427
	}
	.zjzkicon {
		position: absolute;
		right: 25upx;
		top: 25upx;
	}
	
	
	.khfzrbgrcd .lis-item{position: relative;border-bottom: 1px solid #efefef;padding:20upx 0upx;background: #fff;}
	.khfzrbgrcd .lis-item .qtcbzkbtn{position: absolute;right:0upx;top:60upx;font-size:24upx;color:#888}
	.khfzrbgrcd .lis-item .bjbtn{position: absolute;right:30upx;top:20upx;font-size:24upx;}
	.khfzrbgrcd .lis-item .bjbtn text{margin-left:30upx}
	.khfzrbgrcd .lis-item .info {font-size: 28upx;line-height: 45upx;	}
	.khfzrbgrcd .lis-item .jine {color: #ff0000;	}
	.khfzrbgrcd .lis-item .time {font-size: 12px;color: #888;line-height: 15px;margin-top: 10upx;	}
	.khfzrbgrcd .lis-item .pjimgcon {margin-top: 10upx;}
	.khfzrbgrcd .lis-item .pjimgcon .item {float: left;margin-right: 15upx;margin-bottom: 15upx;position: relative	}
	.khfzrbgrcd .lis-item .pjimgcon image {width: 120upx;height: 120upx;display: block;border-radius: 8upx	}
	.khfzrbgrcd .lis-item .des {}
	.khfzrbgrcd .lis-item .inf2{border-top:1px solid #eee;padding-top:20upx;margin-top:20upx;line-height:40upx;position: relative;color:#888}
	
	
</style>