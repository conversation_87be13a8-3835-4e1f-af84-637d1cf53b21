<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<block v-if="thdpid">
					<text>{{thdpname}}仓库</text>
				</block>
				<block v-else>
					<text>企业仓库</text>
				</block>
			</view>
			<view slot="right">
			</view>

		</tn-nav-bar>
		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333"  :bold="true" :fontSize="28"
						:badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;">
					<view class="item btnimg" @click="dpedit" :data-ckid='0'>
						<image src="/static/images/addbtn.png"></image>
					</view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>
		</view>
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 20 + 'px'}"></view>



		<block v-if="thkw">
			<view class="ssvtiao" style="margin-bottom:20upx">
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
			</view>
		</block>


		<block v-if="listdata.length>0">



			<view class="dplist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class="item " :class="item.status!=1 ? 'tystyle' : '' ">
						<view class="inf1">
							<view class="status" @click="setsta" :data-ckid='item.id'>
								<view class="aa">{{item.statusname}}</view>
								<view class="aa"><tn-switch v-model="item.status==1 ? true : false "
										activeColor="#FFD427" inactiveColor="#7F7F7F" leftIcon="success"
										right-icon="close" change="ss"></tn-switch></view>
							</view>
							<view class="tf1">
								<view class="tx">
									<image :src="staticsfile + item.logo"></image>
								</view>
								<view class="xx">
									<!-- <view class="n1">仓库ID：{{item.id}}</view> -->
									<view class="n1">{{item.title}}</view>
									<view class="n2">{{item.dizhi}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
						 
						<view class="infx">
						   <view class="vtm">
							   <view class="tt">当前价值</view>
							   <view class="vv">￥{{item.zjiazhi}}</view>
						   </view>
						   <view class="vtm">
							   <view class="tt">物品总量</view>
							   <view class="vv">{{item.wpznum}}</view>
						   </view>
						   <view class="vtm">
							   <view class="tt">库存总量</view>
							   <view class="vv">{{item.kcznum}}</view>
						   </view>
						   <view class="clear"></view>
						</view>
						
																		
						<view class="scpdsjcon" v-if="item.lastpdtime">上次盘点：<text class="sj">{{item.lastpdtime}}</text></view>
						
						
						<view class="inf2">
						
							<view class="setbtn" @click="dpedit" :data-ckid='item.id'>仓库设置</view>
					     	<view class="setbtn2" @click="gotowpgl" :data-ckid='item.id'>物品列表</view>
							<view class="f1">
								库管人员：
								<block v-if="item.kguid > 0">
									<text @click="setdz" :data-ckid='item.id' :data-ckname='item.title' :data-dpid='item.mdid' :data-dpname='item.mdname'>
										<text style="color:#1677FF">{{item.kgname}}</text>
										<text class="iconfont icon-bianji bjbtn"></text>
									</text>
									<text class="iconfont icon-shanchu bjbtn2" @click="setdzjc" :data-ckid='item.id' :data-ckname='item.title' :data-kgname='item.kgname'  ></text>
									
								</block>
								<block v-else>
									<text @click="setdz" :data-ckid='item.id' :data-ckname='item.title'   :data-dpid='item.mdid' :data-dpname='item.mdname'   >
										<text style="color:#ff0000">设置库管</text>
										<text class="iconfont icon-bianji bjbtn"></text>
									</text>
								</block>
							</view>
							<view class="f2">所属门店：{{item.mdname}}</view>
							<!-- <view class="f2">创建日期：{{item.addtime}}</view> -->
						</view>




					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无仓库</view>
			</view>
		</block>





		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">仓库搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入仓库名称/门店名称'" v-model="thkwtt" name="input"
							placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				index: 1,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						sta: 0,
						name: '全部'
					},
					{
						sta: 1,
						name: '启用'
					},
					{
						sta: 2,
						name: '停用'
					},
				],
				show3: false,
				checked: true,
				thckid: 0,
				thckname: '',
				
				thdpid: 0,
				thdpname: '',

			}
		},
		onLoad(options) {
			let that = this;
			let thdpid=options.dpid ? options.dpid : 0;
			let thdpname=options.dpname ? options.dpname : '';
			that.thdpid=thdpid;
			that.thdpname=thdpname;
			
			that.page = 0;
			that.listdata = [];
			that.loaditems();
		},
		onShow() {

			let that = this;
			if (uni.getStorageSync('thupckgllist') == 1) {
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
				uni.setStorageSync('thupckgllist', 0)
			}

			let thygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			if (thygid) {
				this.thygid = thygid;
				this.thygname = thygname;
				this.setdpdz();
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
			}


		},
		methods: {
			 gotowpgl(e){
				let that=this;
				let ckid = e.currentTarget.dataset.ckid;
				uni.navigateTo({
					url:'/pages/work/qyshop/index?isck=1&ckid='+ckid
				})
			 },
			setdz(e) {
				let thckid = e.currentTarget.dataset.ckid;
				let thckname = e.currentTarget.dataset.ckname;
				let thdpid = e.currentTarget.dataset.dpid;
				let thdpname = e.currentTarget.dataset.dpname;
				console.log(thdpid,thdpname);
				this.thckid = thckid;
				this.thckname = thckname;
				uni.navigateTo({
					url: '/pages/selyg/index?stype=3&ckid=' + thckid + '&ckname=' + thckname + '&dpid=' + thdpid + '&dpname=' + thdpname
				})
			},

			// switch打开或者关闭时触发，值为true或者false
			change(status) {
				console.log(status);
			},
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				// that.sta = 0;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			dpedit(e) {
				let that = this;
				let ckid = e.currentTarget.dataset.ckid;
				let dpid = that.thdpid;
				let dpname = that.thdpname;
				uni.navigateTo({
					url: './edit?ckid=' + ckid + '&dpid='+ dpid + '&dpname=' + dpname
				})
			},


			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					sta: that.sta,
					dpid: that.thdpid ? that.thdpid : 0 ,
					kw: that.thkw ? that.thkw : '',
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/qyck/cklist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},





			setsta(e) {
				let that = this;
				let ckid = e.currentTarget.dataset.ckid;
				let da = {
					'ckid': ckid
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/qyck/setsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.page = 0;
										that.sta = 0;
										that.listdata = [];
										that.isloading = false;
										that.loadingType = -1;
										that.loaditems();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})


			},

			setdpdz() {
				let that = this;
				
				let thckid= this.thckid;
				let thckname= this.thckname;
				let thygid= this.thygid;
				let thygname= this.thygname;
				let xtip='确认将【'+thygname+'】设置为【'+thckname+'】的库管吗？';
				
				uni.showModal({
					title: '',
					content: xtip,
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'ckid': thckid,
								'kguid': thygid,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/qyck/setckkg', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})



						}




					}
				})


			},
			setdzjc(e) {
				let that=this;
				let thckid = e.currentTarget.dataset.ckid;
				let kgname = e.currentTarget.dataset.kgname;
				let xtip='确认解除【'+kgname+'】的库管职务吗？';
				uni.showModal({
					title: '',
					content: xtip,
					success: function(e) {
						//点击确定
						if (e.confirm) {
				
							let da = {
								'ckid': thckid,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/qyck/setckkgjc', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
				
				
				
						}
				
				
				
				
					}
				})
			},







		}
	}
</script>

<style lang='scss'>
	page {}


	.ssnrtip {
		height: 124upx;
		line-height: 124upx;
		background: #f6f6f6;
		position: relative;
		padding: 0 32upx
	}

	.ssnrtip .con {}

	.ssnrtip .con text {
		color: #3366ff
	}

	.ssnrtip .cls {
		position: absolute;
		right: 32upx;
	}

	.ssnrtip .cls text {
		font-size: 45upx
	}



	.dplist {
		padding: 0 32upx 32upx 32upx
	}

	.dplist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx
	}

	.dplist .item .ewms {
		position: absolute;
		right: 40upx;
		top: 152upx
	}

	.dplist .item .ewms .iconfont {
		font-size: 55upx;
		color: #666
	}



	.dplist .item .status {
		position: absolute;
		right: 32upx;
		top: 32upx
	}

	.dplist .item .status .aa {
		float: left;
		margin-left: 10upx;
		line-height: 50upx;
	}

	.dplist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.dplist .item .inf1 .tx {
		float: left
	}

	.dplist .item .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 10upx;
	}

	.dplist .item .inf1 .xx {
		float: left;
		margin-left: 20upx
	}
	.dplist .item .inf1 .n1 {
		margin-top:8upx;
		font-weight: 600;
	}

	.dplist .item .inf1  .n2 {
		margin-top: 10upx;
		color: #7F7F7F;
		font-size: 24upx
	}

	.dplist .item .inf2 {
		background: #fff;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 8upx 28upx 28upx 28upx;
		font-size: 24rpx;
		color: #333;
		position: relative;
	}

	.dplist .item .inf2 .tel {
		position: absolute;
		right: 28upx;
		top: 28upx
	}

	.dplist .item .inf2 .f1 {
		margin-bottom: 10upx
	}

	.dplist .item .inf2 .f2 {}

	.dplist .item .inf2 .setbtn {
		position: absolute;
		right: 28upx;
		bottom: 30upx;
		width: 120rpx;
		height: 60upx;
		line-height: 60upx;
		background: #FFD427;
		border-radius: 80rpx;
		font-size: 22rpx;
		text-align: center;
	}

	.dplist .item .inf2 .setbtn2 {
		position: absolute;
		right: 168upx;
		bottom: 30upx;
		width: 120rpx;
		height: 60upx;
		line-height: 60upx;
		background: #efefef;
		border-radius: 80rpx;
		font-size: 22rpx;
		text-align: center;
	}

	.dplist .item .inf2 .bjbtn {
		font-size: 24upx;
		margin-left: 10upx;
	}
	.dplist .item .inf2 .bjbtn2 {
		font-size: 24upx;
		margin-left: 30upx;
		color:#ff6600
	}
	.dplist .item.tystyle .inf1 {
		background: #F0F0F0;
	}

	.dplist .item.tystyle .inf2 {
		background: #F0F0F0;
	}
	 
	 
	.dplist .item .infx{background: #F6F6F6;margin:0upx 25upx 20upx 25upx;padding:20upx 0;border-radius:10upx;}
	.dplist .item .infx .vtm{float:left;width:33.33%;font-size:24upx;text-align: center;}
	.dplist .item .infx .vtm .tt{color: #7F7F7F;font-size:24upx} 
	.dplist .item .infx .vtm .vv{color: #ff0000;height:40upx;line-height:40upx;}
	.dplist .scpdsjcon{margin-top:15upx;font-size: 24rpx;padding-left:25upx}
	.dplist .scpdsjcon .sj{color: #333;}
	
</style>