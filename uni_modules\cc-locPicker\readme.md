#### 使用方法 
```使用方法
<!-- leftTitle:左边标题 name：输入框名字 value：输入框选择值  placeholder：占位符 @click：点击事件-->
<cc-locPicker leftTitle="收获地点" name="location" :value="mapSelData.poiname" placeholder="请选择位置"
				@click="chooseAddress"></cc-locPicker>	
				
<!-- 跳转腾讯云地图Api 页面实现 -->	
			<template>
				<view class="map">
			
					<!-- 腾讯地图Api  key:腾讯地图key -->
					<web-view
						src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI&referer=myapp"></web-view>
				
				</view>
			</template>
			
			<script>
				// 引入设置地址存储工具
				import {
					setlocation
				} from './utils.js'
			
			
				// #ifdef H5
				window.addEventListener('message', event => {
					// 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
					var loc = event.data;
					if (loc && loc.module == 'locationPicker') {
						//防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
						let location = {
							poiaddress: loc.poiaddress,
							poiname: loc.poiname,
							latlng: loc.latlng
						}
						// 设置存储地址信息
						setlocation(location)
						
						uni.navigateBack();
			
					}
				}, false);
				// #endif
			</script>
			
			<style></style>
					
```

#### HTML代码实现部分
```html
<template>
	<view class="content">

		<form @submit="formSubmit" @reset="formReset">

			<!-- leftTitle:左边标题 name：输入框名字 value：输入框选择值  placeholder：占位符 @click：点击事件-->
			<cc-locPicker leftTitle="收获地点" name="location" :value="mapSelData.poiname" placeholder="请选择位置"
				@click="chooseAddress"></cc-locPicker>

			<ccInputView leftTitle="详细地址" name="address" :value="mapSelData.poiaddress" placeholder="请输入详细地址">
			</ccInputView>


			<ccInputView leftTitle="经度信息" name="lng" :value="mapSelData.latlng.lng" placeholder="请输入精度信息">
			</ccInputView>

			<ccInputView leftTitle="纬度信息" name="lat" :value="mapSelData.latlng.lat" placeholder="请输入纬度信息">
			</ccInputView>


			<view class="uni-btn-v">
				<button class="botBtn" type="primary" form-type="submit">下一步</button>
				<view class="tipText"> 注意事项: 请确保您填写的收获位置准确 </view>

			</view>
		</form>

	</view>
</template>

<script>
	import ccInputView from '../../components/ccInputView.vue'

	// 获取地址工具
	import {
		getlocation
	} from './utils.js'

	export default {
		components: {

			ccInputView
		},
		data() {
			return {

				mapSelData: {
					"latlng": {}
				},
			}
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {

			// #ifdef H5
			let locations = getlocation() //获取位置信息
			if (locations) {
				this.mapSelData = locations

				uni.clearStorageSync();
			}
			// #endif
		},

		methods: {

			formSubmit: function(e) {
				console.log('form发生了submit事件，携带数据为：' + JSON.stringify(e));
				var formdata = e.detail.value;
				uni.showModal({
					title: '温馨提示',
					content: 'formsubmit事件携带数据为：' + JSON.stringify(e.detail.value)
				})

			},
			// 选择地址
			chooseAddress(e) {
				let myThis = this;

				uni.navigateTo({
					url: './h5map'
				})


			},

		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;

	}

	.uni-btn-v {
		width: 100%;
		height: auto;
	}

	.botBtn {
		width: 90%;
		margin-top: 36px;
	}

	.tipText {
		width: 100%;
		margin-left: 0px;
		text-align: center;
		color: #666666;
		margin-top: 36px;
		margin-bottom: 36px;
		font-size: 28rpx;
	}
</style>



```