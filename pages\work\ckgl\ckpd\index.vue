<template>
	<view class="tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{thckdata.title}} 盘点记录</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="tabs-fixed-blank"></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-col-between tn-flex-col-center ">
				<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
					<tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true" activeColor="#000"
						inactiveColor="#333"  :bold="true" :fontSize="28"
						:gutter="20" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF"
						:height="70"></tn-tabs>
				</view>
				<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;">

					<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text>
					</view>
					<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text>
					</view>
				</view>
			</view>
		</view>

		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>

        <block v-if="thkw || rqkstime">
        	<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
        		<block v-if="thkw">
        			<view class="ssgjc">
        				 <text class="gjc">" {{thkw}} "</text>
        				<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
        				</view>
        			</view>
        		</block>
				<view class="clear"></view>
        	</view>
        </block>

		<view class="tjtiao">
		
				<view class="fless">
					<view class="ite">
						<view class="sz">{{tjdata.tjnum1}}</view>
						<view class="tx">累计盘点</view>
					</view>
					<view class="ite">
						<view class="sz">{{tjdata.tjnum3}}</view>
						<view class="tx">盘点中</view>
					</view>
					<view class="ite">
						<view class="sz">{{tjdata.tjnum2}}</view>
						<view class="tx">已完成</view>
					</view>
				</view>
			
		</view>

		<block v-if="listdata.length>0">


			<view class="splist">

				<block v-for="(item,index) in listdata" :key="index">

					<view class=" item ">
						<view class="nrcon">
							<view class="info">
								<view class="stats " :class="'stacolor'+item.status">{{item.statusname}}</view>

								<view><text class="tit">盘点编号：</text>{{item.ordernum}}</view>

								<view><text class="tit">盘点时间：</text>{{item.addtime}}</view>
								 
								<view class="pdtjcc">
								   <view class="vtm2">
									   <view class="vv">{{item.wpnum}}</view>
									   <view class="tt">物品总数</view>
								   </view>
									<view class="vtm2">
									   <view class="vv">{{item.ypdnum}}</view>
									   <view class="tt">已盘点</view> 
									</view>
									<view class="vtm2">
									   <view class="vv">{{item.wpdnum}}</view>
									   <view class="tt">未盘点</view>
									</view>
									<view class="vtm2">
									   <view class="vv">{{item.pynum}}</view>
									   <view class="tt">盘盈</view>
									</view>
									<view class="vtm2">
									   <view class="vv">{{item.pknum}}</view>
									   <view class="tt">盘亏</view>
									</view>
								   <view class="clear"></view>
								</view>
								
								<block v-if="item.remark || item.picturescarr">
									<view class="zkbtn" @click="setvxq" :data-id="item.id">
										展开详情 <text class="iconfont icon-jtdown2"></text>
									</view>
									<block v-if="item.id==thvid">
										<view class="des">
											<view v-if="item.remark">备注说明：{{item.remark}}</view>
											<block v-if="item.picturescarr">
												<view class="pjimgcon">
													<block v-for="(itemx,indexx) in item.picturescarr" :key="indexx">
														<view class='item'>
															<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
																:data-picturescarr="item.picturescarr" @tap='previewImagex'>
															</image>
														</view>
													</block>
													<view class="clear"></view>
												</view>
											</block>
										</view>
									</block>
								</block>

							</view>



							<view class="inf2">
								<view class="pdtime"><text class="iconfont icon-tianjia3"></text> {{item.opname}}
								</view>
								<block v-if="item.status==1">
								    <view class="setbtn3" @click="gopdedit" :data-ckpdid='item.id'>查看</view>
								</block>
								<block v-if="item.status==2">
									<view class="setbtn" @click="gopdedit" :data-ckpdid='item.id'>继续盘点</view>
								    <view class="setbtn2" @click="pddel" :data-ckpdid='item.id'>删除</view>
								</block>
							</view>

						</view> 
					</view>

				</block>
			</view>

			<text class="loading-text" v-if="isloading">
				{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
			</text>


		</block>
		<block v-else>
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
				<view class="tit">暂无相关数据</view>
			</view>
		</block>





		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">盘点单搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入盘点编号'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>


		<view class="tn-flex tn-footerfixed">
			<view class="tn-flex-1 justify-content-item tn-text-center">
				<button class="bomsubbtn" style="width:50%;margin:0 auto;border-radius:100px;font-size:28upx"
					@click="addpd">
					<text class="iconfont icon-jia" style="margin-right:10upx;font-size:28upx"></text>
					<text>新增盘点</text>
				</button>
			</view>
		</view>

		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择盘点日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta: 0,
				index: 1,
				thvid: 0,
				current: 0,
				thkw: '',
				thkwtt: '',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				stadata: [{
						sta: 0,
						name: '全部'
					},
					{
						sta: 2,
						name: '盘点中'
					},
					{
						sta: 1,
						name: '已完成'
					},
				],
				show3: false,
				rqshow: false,
				rqkstime: 0,
				rqjstime: 0,
				tjdata: [],

      
				ckid: 0,
				ckname: '',
				thckdata: '',

			}
		},
		onLoad(options) {
			let ckid = options.ckid ? options.ckid : 0;
			this.ckid = ckid;
			this.loadData();

		},
		onShow() {
			let that = this;
			
			if(uni.getStorageSync('thuppdlist')==1){
				this.loadData();
				uni.setStorageSync('thuppdlist',0)
			}
			
			that.page = 0;
			that.listdata = [];
			that.isloading = false;
			that.loadingType = -1;
			that.loaditems();
		},
		onReady() {

		},
		onPullDownRefresh() {

		},
		methods: {


			loadData() {
				let that = this;
				let da = {
					tjtype: 4, 
					sortid:that.sortid ? that.sortid : 0 ,
					ckid:that.ckid ? that.ckid : 0 ,
					ddtype:that.ddtype ? that.ddtype : 0 ,
					kw:that.thkw ? that.thkw : '' ,
					rqkstime:that.rqkstime ? that.rqkstime : 0 ,
					rqjstime:that.rqjstime ? that.rqjstime : 0 ,
				}
				uni.showLoading({ 
					title: ''
				})
				this.$req.post('/v1/ckgl/getcktj', da)
					.then(res => {
						uni.hideLoading();
						console.log(22111, res);
						if (res.errcode == 0) {
							that.tjdata = res.data.tjdata;
							that.thckdata = res.data.thckdata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			addpd() {
				let that = this;
				let da = {
					ckid: that.ckid ? that.ckid : 0
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/ckpdadd', da)
					.then(res => {
						uni.hideLoading();
						console.log(111, res);
						if (res.errcode == 0) {
							uni.setStorageSync('thuppdlist',1);
							let ckpdid = res.data.ckpdid;
							uni.navigateTo({
								url: './pdedit?ckpdid=' + ckpdid
							})
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})

			},



			gopdedit(e) {
				let ckpdid=e.currentTarget.dataset.ckpdid;
				uni.navigateTo({
					url: './pdedit?ckpdid=' + ckpdid
				})
			},

			rqmodal() {
				this.rqshow = true
			},
			rqmodalchange(e) {
				let that = this;
				if (e.startDate && e.endDate) {
					// this.thkw = '';
					this.rqkstime = e.startDate;
					this.rqjstime = e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch() {
				let that = this;
				this.rqkstime = '';
				this.rqjstime = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			caozuo(e) {
				let djid = e.currentTarget.dataset.djid;
				console.log(djid);
				uni.navigateTo({
					url: './view?djid=' + djid
				})
			},
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;

				// this.rqkstime = '';
				// this.rqjstime = '';

				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			setvxq(e) {
				let thvid = e.currentTarget.dataset.id;
				if (thvid == this.thvid) {
					this.thvid = 0;
				} else {
					this.thvid = thvid;
				}

			},
			// tab选项卡切换
			tabChange(index) {
				let that = this;
				let sta = that.stadata[index].sta;
				console.log(sta);
				that.thkw = '';
				that.current = index;
				that.sta = sta;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},

			onReachBottom() {
				this.loaditems();
			},
			// 上拉加载
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					sta: that.sta,
					ckid: that.ckid,
					kw: that.thkw ? that.thkw : '',
					rqkstime: that.rqkstime ? that.rqkstime : 0,
					rqjstime: that.rqjstime ? that.rqjstime : 0,
				}
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/ckgl/ckpdlist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},

			pddel(e) {
				let that = this;
				let ckpdid = e.currentTarget.dataset.ckpdid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'ckpdid': ckpdid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/ckgl/ckpddel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
													that.page = 0;
													that.listdata = [];
													that.isloading = false;
													that.loadingType = -1;
													that.loaditems();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})



						}




					}
				})


			},







		}
	}
</script>

<style lang='scss'>
	.splist {
		padding: 10upx 30upx;
	}

	.splist .item {
		position: relative;
		border-bottom: 1px solid #efefef;
		padding: 30upx;
		background: #fff; 
		margin-bottom: 20upx;
		border-radius: 20upx;
	}

	.splist .item .info {
		font-size: 24upx;
		line-height: 45upx;
	}

	.splist .item .inf2 {
		border-top: 1px solid #efefef;
		position: relative;
		padding: 20upx 0 0 0; 
		font-size: 24upx;
	}

	.splist .item .inf2 .setbtn {
		position: absolute;
		right: 0upx;
		top: 20upx;
		width: 130rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		font-size: 20upx
	}

	.splist .item .inf2 .setbtn2 {
		position: absolute;
		right: 145upx;
		top: 20upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #efefef;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		font-size: 20upx
	}
	.splist .item .inf2 .setbtn3 {
		position: absolute;
		right: 0;
		top: 20upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fafafa;
		border-radius: 8rpx;
		text-align: center;
		color: #333;
		font-size: 20upx;
		border:1px solid #eee;
	}

	.splist .item .stats {
		position: absolute;
		right: 30upx;
		top: 30upx;
		font-size: 24upx;
	}

	.splist .item .pdtime {}

	.splist .item .pdtime .iconfont {
		margin-right: 10upx
	}

	.splist .stats.stacolor1 {
		color: #0DD400;
	}

	.splist .stats.stacolor2 {
		color: #FA6400;
	}

	.splist .pjimgcon {
		margin-top: 10upx;
	}

	.splist .pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.splist .pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}

	.splist .zkbtn {
		color: #888;
		position: absolute;
		right: 20upx;
		top: 120upx;
		font-size: 24upx
	}

	.splist .des {
		border-top: 1px solid #eee;
		margin-top: 10upx;
		padding-top: 10upx;
		color: #888
	}
	
	
	.splist .pdtjcc{border-top:1px solid #F0F0F0;padding:10upx 0;margin-top:10upx;text-align: center;}
	.splist .pdtjcc .vtm2{float:left;width:20%;font-size:24upx}
	.splist .pdtjcc .vtm2 .tt{font-size:20upx;} 
	.splist .pdtjcc .vtm2 .vv{ color:#f5980a;font-size:28upx} 
	
	
</style>