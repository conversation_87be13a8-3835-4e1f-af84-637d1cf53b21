<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >角色权限</text>
					</view>
					<view slot="right" >
					</view>
					
		</tn-nav-bar>
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="jstypedata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;" >
				<view class="item btnimg" @click="addjstype" :data-jsid='0'><image src="../../../static/images/addbtn.png"></image></view>
				<!-- <view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view> -->
			</view>
		  </view>  
		</view>
	<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 20 + 'px'}" ></view>
	

		
			<block v-if="listdata.length>0">
			
			
			    
						    <view class="frlist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
										
											<view class="item " :class="item.status!=1 ? 'tystyle' : '' "  >
											
													<view class="inf1"  >
														<view class="tf2">
															<view class="tline"></view>
															<view class="n1">{{item.title}}</view>
														</view>
													</view>
													<view class="inf2">
														<view class="setbtn1" @click="fredit" :data-jsid='item.id' :data-jstype='item.jstype'>编辑</view>
														<view class="setbtn0" >
															
															<view  @click="setsta" :data-jsid='item.id' v-if="item.issys==2" >
															    <view class="aa"><tn-switch v-model="item.status==1 ? true : false " activeColor="#FFD427"  inactiveColor="#dfdfdf" leftIcon="success" right-icon="close" change="ss"></tn-switch></view>
															</view>
															
														</view>
													</view>
												
													
											</view>
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无角色权限</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">角色搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入角色名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				jstype:1,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				jstypedata:[
					{jstype: 1,name: '员工角色'},
					{jstype: 2,name: '超级角色'},
				],
				show3:false,
			    checked: true,
				
			}
		},
		onLoad(options) {
			let that=this;
		
		},
		onShow() {
		
			let that=this;
		
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems(); 
			
		
		},
		methods: {
			 // switch打开或者关闭时触发，值为true或者false
			change(status) {
				console.log(status);
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.jstype = 1;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			fredit(e){
				let that=this;
				let jsid = e.currentTarget.dataset.jsid;
				let jstype = e.currentTarget.dataset.jstype;
				if(jstype==1){
					uni.navigateTo({
						url:'./edit?jsid='+jsid
					})
				}else{
					uni.navigateTo({
						url:'./edit2?jsid='+jsid
					})
				}
				
			},
			
			
			// tab选项卡切换
				tabChange(index) {
					let that=this;
					let jstype=that.jstypedata[index].jstype;
					console.log(jstype);
					that.thkw = '';
					that.current = index;
					that.jstype = jstype;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				},
				
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							jstype:that.jstype,
							kw:that.thkw ? that.thkw : '' ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/sjgl/jiaoselist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
		         setsta(e){
		         	let that=this; 
		         	let jsid = e.currentTarget.dataset.jsid;
		         	let da = {
		         		'jsid': jsid
		         	}
		         	uni.showLoading({title: ''})
		         	that.$req.post('/v1/sjgl/jiaosesetsta', da)
		         		.then(res => {
		         			uni.hideLoading();
		         			if (res.errcode == 0) {
		         				uni.showToast({
		         					icon: 'none',
		         					title: res.msg,
		         					success() {
		         						setTimeout(function() {
		         								that.page = 0;
		         								that.sta = 0;
		         								that.listdata = [];
		         								that.isloading = false;
		         								that.loadingType = -1;
		         								that.loaditems();
		         						}, 1000);
		         					}
		         				});
		         			} else {
		         				uni.showModal({
		         					content: res.msg,
		         					showCancel: false,
		         				})
		         			}
		         	})
		         	
		         	
		         },
				 
				 addjstype(){
					 let that=this;
					 uni.showActionSheet({
						  itemList: ['添加员工角色','添加超级角色'],
						  success: function (res)
						  {
							var index = res.tapIndex;
							if(index==0){
								uni.navigateTo({
									url:'./edit?jsid='+0
								})
							}
							if(index==1){
								uni.navigateTo({
									url:'./edit2?jsid='+0
								})
							}
						 
						  },
					 });
				 }
		
		
				
				
						
			
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}




.frlist{padding:0 32upx 32upx 32upx}
.frlist .item{margin-bottom:32upx;background:#fff;border-radius:20upx;padding:30upx 30upx;position: relative;}
/* .frlist .item .tline{position: absolute;left:0;top:37upx;width:10upx;height:30upx;background:#FFD427 ;} */
.frlist .n1{font-size: 28rpx;font-weight:600;}
.frlist .setbtn1{width: 120rpx;height: 60rpx;line-height:60rpx;background: #FFD427;border-radius: 8rpx;font-size:24upx;text-align: center;position: absolute;right:20upx;top:20upx;}
.frlist .setbtn0{text-align: center;position: absolute;right:180upx;top:26upx;}



</style>
