// 设置储存tokendata
const setTokenStorage = (tokendata) => {
  uni.setStorageSync('tokendata', tokendata)
}

// 获取tokendata
const getTokenStorage = () => {
  let tokendata = ''
  try {
    tokendata = uni.getStorageSync('tokendata')
  } catch (e) {
  }
  return tokendata
}

// 重新整理一下config
const configHandle = (config) => {
  config.header = {
    ...config.header,
    token: getTokenStorage() // token 特殊处理，主要是header有可能使用的是局部配置
  }
  return config
}


// 设置储存mudata
const setMudataStorage = (mudata) => {
  uni.setStorageSync('mudata', mudata)
}

// 获取mudat
const getMudataStorage = () => {
  let mudata = ''
  try {
    mudata = uni.getStorageSync('mudata')
  } catch (e) {
  }
  return mudata
}



const checkVersion = () => {
	uni.getSystemInfo({
		success:(res) => {
			//检测当前平台，如果是安卓则启动安卓更新  
			if(res.platform=="android"){  
				console.log(res)
				// this.AndroidCheckUpdate();  
			}
		}  
	})
	uni.showToast({
		icon: 'none',
		title: '已是最新版本'
	});
}

export {
  setTokenStorage,
  getTokenStorage,
  configHandle,
  setMudataStorage,
  getMudataStorage,
  checkVersion
}
