/**
 * 页面展示列表数据
 */
export default {
  data: [{
      title: '图鸟首页',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'code',
          title: '关于我们',
          url: '/homePages/about',
          author: '图鸟北北'
        },{
          icon: 'code',
          title: '全局搜索',
          url: '/homePages/search',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '今日热榜',
          url: '/homePages/hot',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '前端业务',
          url: '/homePages/profession',
          author: '图鸟北北'
        }
      ]
    },
    {
      title: '酷炫圈子',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'code',
          title: '博主_Me',
          url: '/circlePages/blogger',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '博主_Ta',
          url: '/circlePages/blogger_other',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '编辑发布',
          url: '/circlePages/edit',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '广告页',
          url: '/circlePages/advertise',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '资讯详情',
          url: '/circlePages/news',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '名片王者',
          url: '/circlePages/king',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '智能名片',
          url: '/circlePages/business',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '精选圈子',
          url: '/circlePages/group',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '积分排行',
          url: '/circlePages/ranking',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '圈子详情',
          url: '/circlePages/details',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '预约接龙',
          url: '/circlePages/reserve',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '活动创建',
          url: '/circlePages/create',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '打造圈子',
          url: '/circlePages/build',
          author: '图鸟北北'
        }
      ]
    },
    {
      title: '活动广场',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'code',
          title: '地图打卡',
          url: '/activityPages/map',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '快速答题',
          url: '/activityPages/topic',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '课程学习',
          url: '/activityPages/study',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '开源项目',
          url: '/activityPages/project',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '活动星球',
          url: '/activityPages/planet',
          author: '图鸟北北'
        }
      ]
    },
    {
      title: '商品优选',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'code',
          title: '优质商家',
          url: '/preferredPages/shop',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '商品详情',
          url: '/preferredPages/product',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '历史订单',
          url: '/preferredPages/order',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '商品分类',
          url: '/preferredPages/classify',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '商家相册',
          url: '/preferredPages/photo',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '品牌官网',
          url: '/preferredPages/website',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '积分兑换',
          url: '/preferredPages/redeem',
          author: '图鸟北北'
        }
      ]
    },
    {
      title: '关于我的',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'code',
          title: '使用协议',
          url: '/minePages/protocol',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '授权登录',
          url: '/minePages/login',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '消息通知',
          url: '/minePages/message',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '全局设置',
          url: '/minePages/set',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '立即体验',
          url: '/minePages/start',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '感谢名单',
          url: '/minePages/thanks',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '版本更新',
          url: '/minePages/version',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '帮助中心',
          url: '/minePages/help',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '头像上传',
          url: '/minePages/avatar',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '积分明细',
          url: '/minePages/integral',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '积分签到',
          url: '/minePages/signed',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '好物收藏',
          url: '/minePages/collect',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '账号安全',
          url: '/minePages/safety',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '缺省页',
          url: '/minePages/default',
          author: '图鸟北北'
        },
        {
          icon: 'code',
          title: '富文本',
          url: '/minePages/content',
          author: '图鸟北北'
        }
      ]
    }
  ]
}
