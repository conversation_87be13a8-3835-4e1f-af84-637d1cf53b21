<template>
	<view class="page-e tn-safe-area-inset-bottom">


		<tn-nav-bar fixed :isBack="false" customBack :bottomShadow="false" backgroundColor="#FFD427" :zIndex="3000">

			<view class="itop">
				<view class="if1" @click="selqhdp">
					<text class="iconfont icon-qiehuan1"></text>
					<text class="sname">{{vtitle}}</text>
				</view>
				<view class="if2">

					<view class="msgicon" @click="gotomsg">
						<text class="iconfont icon-xiaoxi1"></text>
						<view class="jbx" v-if="wdmsgnum>0">{{wdmsgnum}}</view>
					</view>
					<view class="smicon" @click="gotolink" data-linkurl="/pages/sousuo/index">
						<text class="iconfont icon-sousuo2"></text>
					</view>
					<view class="smicon" @click="saomac">
						<text class="iconfont icon-saoyisaox"></text>
					</view>

				</view>
				<view class="clear"></view>
			</view>
		</tn-nav-bar>

		<view :style="{height: vuex_custom_bar_height + 'px'}"></view>

		<block v-if="lunbotu.length > 0">
			<view class="lbtucon">
				<view class="carousel-section">
					<swiper class="card-swiper" circular indicator-dots indicator-active-color="#FFFFFF" autoplay
						:style="'height:'+lbtuHeight">
						<block v-for="(item, index) in lunbotu" :key="index">
							<block v-if="item.target=='self' || item.target==''">
								<swiper-item class="carousel-item">
									<navigator :url="item.linkurl2" :open-type="item.opentype">
										<image :src="staticsfile+item.smallpic" class="slide-image img" mode="widthFix"
											@load="imgHeight" />
									</navigator>
								</swiper-item>
							</block>
							<block v-if="item.target=='weburl'">
								<swiper-item class="carousel-item">
									<navigator :url="'/pages/webv/index?weburl='+item.linkurl2">
										<image :src="staticsfile+item.smallpic" class="slide-image img" mode="widthFix"
											@load="imgHeight" />
									</navigator>
								</swiper-item>
							</block>
							<block v-if="item.target=='webedit'">
								<swiper-item class="carousel-item">
									<navigator :url="'/pages/adv/index?id='+item.id+'&type=21'">
										<image :src="staticsfile+item.smallpic" class="slide-image img" mode="widthFix"
											@load="imgHeight" />
									</navigator>
								</swiper-item>
							</block>
						</block>
					</swiper>



				</view>
			</view>
		</block>


		<view class="imyinfo">

			<view class="usinfo">
				<view class="seticon" @click="qhseldianpu"><text class="iconfont icon-qiehuandianpu1"></text></view>
				<view class="tx">
					<image :src="staticsfile + thdpdata.logo"></image>
				</view>
				<view class="xx">
					<view class="name">
						<view class="t1">{{thdpdata.title}}</view>
					</view>
					<view class="name2">
						<view class="t1">{{musfdata.realname}}</view>
						<view class="t2 ">
							<view class="jscon" v-if="musfdata.jiaosetxt">{{musfdata.jiaosetxt}}</view>
						</view>
					</view>

					<view class="nid">
						<view class="idv">员工号：{{musfdata.zzhuid}}</view>
						<view class="clear"></view>
					</view>

				</view>
				<view class="clear"></view>
			</view>

			<view class="mygjcon">
				<view class="item">
					<view class="con" @click="gotolink" data-linkurl="/pages/work/rukudan/index">
						<view class="icon">
							<image src="/static/images/tticon1.png" mode="widthFix"></image>
							<view class="jbx" v-if="kdtipnum > 0">{{kdtipnum}}</view>
						</view>
						<view class="tit">开单</view>
					</view>
				</view>
				<view class="item">
					<view class="con" @click="gotolink" data-linkurl="/pages/work/chukudan/index">
						<view class="icon">
							<image src="/static/images/tticon2.png" mode="widthFix"></image>
							<view class="jbx" v-if="csdtipnum > 0">{{csdtipnum}}</view>
						</view>
						<view class="tit">售卖</view>
					</view>
				</view>

				<view class="item">
					<view class="con" @click="gotolink" data-linkurl="/pages/myod/ddlist">
						<view class="icon">
							<image src="/static/images/tticon4.png" mode="widthFix"></image>
							<view class="jbx" v-if="wddjyqtx > 0">{{wddjyqtx}}</view>
						</view>
						<view class="tit">我的订单</view> 
					</view>
				</view>
 
				<view class="item">
					<view class="con" @click="gotolink" data-linkurl="/pages/work/khgl/index?ismy=1">
						<view class="icon">
							<image src="/static/images/tticon3.png" mode="widthFix"></image>
						</view>
						<view class="tit">我的客户</view>
					</view>
				</view>

				<view class="clear"></view>
			</view>

		</view>



		<block v-if="gnmkdata.length > 0">
			<view class="gnmkmain">
				<block v-for="(item, index) in gnmkdata" :key="index">
					<block v-if="item.gnitem.length > 0">
						<view class="sortname">
							{{item.sortname}}
							<text v-if="item.sortid==14 && musfdata.cjjiaosetxt" class="qxcjjsscon">{{musfdata.cjjiaosetxt}}</text>
							
						<!-- 	<view v-if="item.sortid==12 && isrkqrqx==1" class="ykrkqrtip" @click="gotoykrkqr">
								 移库入库确认<text v-if="ykdrknum > 0">({{ykdrknum}})</text>
							</view> -->
							
						</view>
						<view class="gnmkcon">

							<block v-for="(itemx,indexx) in item.gnitem" :key="indexx">
								<view class="item">
									<view class="con" @click="gotolink" :data-linkurl="itemx.linkurl">
										<view class="icon">
											<image :src="staticsfile + itemx.smallpic"></image>
											<view class="jbx" v-if="itemx.tipnum > 0">{{itemx.tipnum}}</view>
										</view>
										<view class="tit">{{itemx.title}}</view>
									</view>
								</view>
							</block>
							<view class="clear"></view>

						</view>
					</block>
				</block>
			</view>
		</block>


		<tn-popup v-model="qhdpselshow" mode="bottom" :borderRadius="20" :closeBtn='true' :zIndex="30000">
			<view class="popuptit">切换店铺/身份</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">

				<view class="qhsflist">
					<block v-for="(itemx,indexx) in mydpdata.glsfdata" :key="indexx">
						<view class="sfitem">
							<view class="tit">{{itemx.mdname}}
								<text class="iconfont icon-mendian cjglicon" v-if="itemx.iszzh==1 "></text>
							</view>
							<view class="zss">员工名称：{{itemx.realname}}</view>
							<!-- <view class="zss" style="display: none;">员工编号：{{itemx.id}}</view> -->
							<!-- <view class="zss" v-if="itemx.mdname">所属店铺：{{itemx.mdname}}</view> -->
							<view class="zss" v-if="itemx.jiaosetxt">店铺角色：<text class="jstxt">{{itemx.jiaosetxt}}</text>
							</view>
							<view class="zss cjboss" v-if="itemx.jiaosetxt2">超级角色：<text
									class="jstxt">{{itemx.jiaosetxt2}}</text></view>
							<view class="jrbtn" @click="jrdp" :data-ygid="itemx.id" :data-mdid="itemx.mdid">切换</view>
						</view>
					</block>
					<block v-for="(items,indexs) in mydpdata.otmddata" :key="'a'+indexs">
						<view class="sfitem">
							<view class="tit" style="color:#1677FF;font-weight:400">{{items.title}}</view>
							<view class="zss">所在地区：{{items.areatxt}}</view>
							<view class="jrbtn" @click="jrdp" :data-mdid="items.id" :data-ygid="0">切换</view>
						</view>
					</block>
				</view>


			</scroll-view>
		</tn-popup>



		<view style="height:30upx"></view>
		<view class='tn-tabbar-height'></view>
		<yomol-upgrade :type="upgradeType" :url="upgradeUrl" title="发现新版本" :content="upgradeContent"
			ref="yomolUpgrade"></yomol-upgrade>
	</view>
</template>

<script>
	import yomolPrompt from '@/components/yomol-prompt/yomol-prompt.vue'
	import yomolUpgrade from '@/components/yomol-upgrade/yomol-upgrade.vue'

	export default {

		components: {
			yomolPrompt,
			yomolUpgrade,
		},
		data() {
			return {
				staticsfile: this.$staticsfile,
				wdmsgnum: 0,
				sjdata: '',
				gnmkdata: '',
				vtitle: '',

				version: '',
				upgradeType: 'pkg',
				upgradeContent: '',
				upgradeUrl: '',


				lunbotu: '',
				swiperCurrent: 0,
				swiperHeight: 0,
				current: 0,

				lbtuHeight: 0,

				musfdata: '',
				thdpdata: '',

				qhdpselshow: false,
				mydpdata: [],

				kdtipnum: 0,
				csdtipnum: 0,
				
				ykdrknum:0,
				isrkqrqx:0,
				
				wddjyqtx:0,
				
				sqxctip: '', 

			}
		},

		onLoad() {
			let that = this;




		},
		onShow() {
			let that = this;
			uni.setStorageSync('thupdpkb', 1);
			that.loadData();
			//#ifdef APP-PLUS
			that.checkupone()
			//#endif
		},


		methods: {


			imgHeight: function(e) {
				var that = this;
				var winWid = uni.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
				var imgh = e.detail.height - 16; //图片高度
				var imgw = e.detail.width; //图片宽度
				var swiperH = winWid * imgh / imgw + "px"
				that.lbtuHeight = swiperH //设置高度
			},

			gotomsg() {
				uni.navigateTo({
					url: '/pages/user/msg/index'
				})
			},
			gotolink(e) {
				let that = this;
				let target = e.currentTarget.dataset.target;
				let linkurl = e.currentTarget.dataset.linkurl;
				uni.navigateTo({
					url: linkurl
				})
			},
			selqhdp() {
				uni.navigateTo({
					url: '/pages/selsf/index?isback=1'
				})
			},

			loadImg() {
				this.getCurrentSwiperHeight('.img');
			},
			// 轮播切换
			viewChange(e) {
				this.current = e.target.current;
				this.getCurrentSwiperHeight('.img');
			},
			// 动态获取内容高度
			getCurrentSwiperHeight(element) {
				let query = uni.createSelectorQuery().in(this);
				query.selectAll(element).boundingClientRect();
				query.exec((res) => {
					console.log(res[0][this.current]);
					// 切换到其他页面swiper的change事件仍会触发，这时获取的高度会是0，会导致回到使用swiper组件的页面不显示了
					if (this.lunbotu.length) {
						this.swiperHeight = res[0][this.current].height;
					}
				});
			},

			loadData() {
				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/index/index', da)
					.then(res => {
						console.log(222, res);
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.musfdata = res.data.musfdata;
							that.thdpdata = res.data.thdpdata;
							that.sjdata = res.data.sjdata;
							that.wdmsgnum = res.data.wdmsgnum;
							that.vtitle = res.data.vtitle;
							that.lunbotu = res.data.lunbotu;
							that.gnmkdata = res.data.gnmkdata;
							that.kdtipnum = res.data.kdtipnum;
							that.csdtipnum = res.data.csdtipnum;
							that.ykdrknum = res.data.ykdrknum;
							that.isrkqrqx = res.data.isrkqrqx;
							that.wddjyqtx = res.data.wddjyqtx;
							that.sqxctip = res.data.sqxctip;
						} else {
							if (res.errcode == 102) {
								
								
								uni.showModal({ 
									content: res.msg,
									showCancel: false,
									success() {
										uni.redirectTo({
											url: '/pages/selsf/index'
										})
									}
								})
								
							} else {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}

						}
					})
			},

			gotogrzl() {
				uni.switchTab({
					url: '/pages/mine/mine'
				})
			},
			gotoykrkqr(){
				uni.navigateTo({
					url: '/pages/work/ckgl/ykrkqr/index'
				})
			},

			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},


			checkupone() {

				uni.getStorage({
					key: "checkup-time",
					success: (res) => {
						if (!res.data || new Date().getTime() - res.data > 1000 * 60 * 60 * 6) {
							this.checkversion();
						}
					},
					fail: (err) => {
						this.checkversion();
					},
				});

			},


			checkversion() {
				let that = this;
				// 检测升级
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					var platform = uni.getSystemInfoSync().platform


					let da = {
						platform: platform,
						version: widgetInfo.version,
						name: widgetInfo.name
					}
					// console.log(da);
					that.$req.post('/v1/checkver/index', da)
						.then(res => {
							console.log(res);


							if (res.errcode == 0) {



								let isnew = res.data.isnew;
								if (isnew == 1) {
									uni.setStorage({
										key: "checkup-time",
										data: new Date().getTime(),
									});
									if (res.data.appupdata.pkgurl != '' && res.data.appupdata.wgturl == '') {
										that.upgradeType = 'pkg'
										that.upgradeContent = res.data.appupdata.content
										that.upgradeUrl = res.data.appupdata.pkgurl
										that.$refs.yomolUpgrade.show()
									} else {
										that.upgradeType = 'wgt'
										that.upgradeContent = res.data.appupdata.content
										that.upgradeUrl = res.data.appupdata.wgturl
										that.$refs.yomolUpgrade.show()
									}
								}
							} else {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}

						})


				});
			},



			qhseldianpu() {

				let that = this;
				let da = {};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/selsf/dpnqh', da)
					.then(res => {
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.mydpdata = res.data.mydpdata;
							that.qhdpselshow = true;
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})


			},

			jrdp(e) {
				let that = this;
				let ygid = e.currentTarget.dataset.ygid;
				let mdid = e.currentTarget.dataset.mdid;
				let da = {
					ygid: ygid,
					mdid: mdid,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/selsf/jrdp', da)
					.then(res => {
						console.log(222, res);
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thupdpkb', 1);
							uni.setStorageSync('thygid', '');
							uni.setStorageSync('thygname', '');
							that.$tool.setTokenStorage(res.data.tokendata);
							setTimeout(function() {
								that.qhdpselshow = false;
								that.loadData();
							}, 500)
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					})
			},
			saomac(){
				  let that=this;
				  uni.scanCode({
					success: function (res) {
						console.log(res);
						let sbma=res.result; 
						uni.navigateTo({
							url:'/pages/selsf/addyginfo?ygyqma='+sbma
						})
					}
				  });
			},
			saomac1() {
				
				
				uni.scanCode({
					success: function(resv) {
						let sbma = resv.result;
						let da = {
							ygyqma: sbma ? sbma : ''
						};
						uni.showNavigationBarLoading();
						that.$req.post('/v1/yggl/addygcheck', da)
							.then(res => {
								console.log(222, res);
								uni.hideNavigationBarLoading();
								if (res.errcode == 0) {
									//如果注册过，则跳转到选择提示界面
									if (res.data.yyzh == 1) {
										let xsjid = res.data.xsjid;
										uni.navigateTo({
											url: '/pages/selsf/seldlzh?xsjid=' + xsjid + '&sbma=' +
												sbma
										})
									} else {
										uni.navigateTo({
											url: '/pages/selsf/addyginfo?ygyqma=' + sbma
										})
									}
								} else {
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								}
							})
					}
				});
				
			},




		}
	}
</script>

<style lang="scss" scoped>
	.itop {
		padding: 0 32upx
	}

	.itop .if1 {
		float: left
	}

	.itop .if1 .iconfont {
		font-size: 36upx;
		margin-right: 10upx;
		font-weight: 600;
	}

	.itop .if1 .sname {
		font-size: 36upx;
		font-weight: 600;
	}

	.itop .if2 {
		float: right
	}

	.itop .if2 .smicon {
		float: right;
		margin-right: 30upx
	}

	.itop .if2 .smicon text {
		font-size: 44upx;
		color: #333333
	}

	.itop .if2 .msgicon {
		position: relative;
		float: right;
	}

	.itop .if2 .msgicon text {
		font-size: 44upx;
		color: #333333
	}

	.itop .if2 .msgicon .jbx {
		position: absolute;
		padding: 0 2px;
		top: 20upx;
		right: -5upx;
		border-radius: 30upx;
		min-width: 28upx;
		height: 28upx;
		background: #FF2828;
		font-size: 16upx;
		color: #FFFFFF;
		line-height: 28upx;
		text-align: center;
		border: 1px solid #fff
	}

	.lbtucon {
		margin: 32upx 32upx 32upx 32upx
	}

	.carousel-item {}

	.carousel-item image {
		width: 100%;
		border-radius: 20upx;
	}

	.gnmkmain {
		margin: 32upx
	}

	.gnmkmain .sortname {
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 20upx;
		position: relative;
	}

	.gnmkmain .gnmkcon {
		background: #fff;
		border-radius: 20upx;
		margin-bottom: 40upx;
		padding: 40upx 0 0upx 0
	}

	.gnmkmain .gnmkcon .item {
		width: 25%;
		float: left;
		text-align: center;
		margin-bottom: 50upx
	}

	.gnmkmain .gnmkcon .item .con {}

	.gnmkmain .gnmkcon .item .icon {
		margin: 0 auto;
		margin-bottom: 20upx;
		position: relative;
		width: 80upx;
		height: 80upx;
	}

	.gnmkmain .gnmkcon .item .icon .jbx {
		position: absolute;
		padding: 0 2px;
		top: -5upx;
		right: -5upx;
		border-radius: 30upx;
		min-width: 28upx;
		height: 28upx;
		background: #FF2828;
		font-size: 16upx;
		color: #FFFFFF;
		line-height: 28upx;
		text-align: center;
		border: 1px solid #fff;
		z-index: 10;
	}

	.gnmkmain .gnmkcon .item .icon image {
		width: 80upx;
		height: 80upx;
		display: block;
		margin: 0 auto;
	}

	.gnmkmain .gnmkcon .item .tit {
		font-size: 28rpx;
	}

	.imyinfo {
		margin: 32upx;
		margin-top: 55upx;
		padding: 32upx 32upx 32upx 22upx;
		background: #fff;
		border-radius: 20upx;
	}

	.usinfo {
		margin-top: -65upx;
		position: relative;
	}

	.usinfo .tx {
		float: left
	}

	.usinfo .tx image {
		width: 150upx;
		height: 150upx;
		border: 12upx solid #fff;
		display: block;
		border-radius: 100px;
		background: #fff;
	}

	.usinfo .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 15upx
	}

	.usinfo .xx .name {
		height: 50upx;
		font-size: 36upx;
		font-weight: 600;
		line-height: 50upx;
	}

	.usinfo .xx .name .t1 {
		float: left;
	}

	.usinfo .xx .name2 {
		height: 40upx;
		font-size: 24upx;
		font-weight: 500;
		color: #333;
		line-height: 40upx;
	}

	.usinfo .xx .name2 .t1 {
		float: left;
	}

	.usinfo .xx .name2 .t2 {
		float: left;
		margin-left: 20upx;
		padding-top: 5upx
	}

	.usinfo .xx .name2 .jscon {
		background: #FFD427;
		font-size: 18upx;
		color: #333;
		border-radius: 100px;
		padding: 0 10upx;
		height: 30upx;
		line-height: 30upx;
	}


	.usinfo .xx .nid {
		margin-top: 5upx
	}

	.usinfo .xx .nid .idv {
		float: left;
		height: 34upx;
		font-size: 24upx;
		font-weight: 500;
		color: #333;
		line-height: 34upx;
		color: #888
	}

	.usinfo .xx .nid .idv .iconfont {
		font-size: 24upx;
		color: #FFD427
	}

	.usinfo .seticon {
		position: absolute;
		right: 0upx;
		top: 60upx;
	}

	.usinfo .seticon .iconfont {
		font-size: 45upx;
		color: #333
	}


	.mygjcon {}

	.mygjcon .item {
		width: 25%;
		float: left;
		text-align: center;
		margin-top: 20upx
	}

	.mygjcon .item .con {}

	.mygjcon .item .icon {
		margin: 0 auto;
		margin-bottom: 15upx;
		position: relative;
		width: 80upx;
	}

	.mygjcon .item .icon image {
		width: 50upx;
		display: block;
		margin: 0 auto;
	}

	.mygjcon .item .tit {
		font-size: 28rpx;
	}

	.mygjcon .item .icon .jbx {
		position: absolute;
		padding: 0 2px;
		top: -5upx;
		right: -5upx;
		border-radius: 30upx;
		min-width: 28upx;
		height: 28upx;
		background: #FF2828;
		font-size: 16upx;
		color: #FFFFFF;
		line-height: 28upx;
		text-align: center;
		border: 1px solid #fff;
		z-index: 10;
	}

	.qxcjjsscon {
		background: #FFD427;
		font-size: 20upx;
		color: #333;
		border-radius: 100upx;
		padding: 2upx 10upx;
		margin-left: 20upx;
		position: absolute;
		top:7upx;
	}
	
	
	.ykrkqrtip {
		position: absolute;
	    right:0;top:0;
		font-size:13px;font-weight:400;
	}
	.ykrkqrtip text{
		color:#ff0000;font-weight:700
	}
	
</style>