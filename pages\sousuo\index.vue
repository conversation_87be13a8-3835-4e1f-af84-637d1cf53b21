<template>
	<view class="page-e tn-safe-area-inset-bottom">


		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" backgroundColor="#FFD427" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>搜索</text>
			</view>
			<view slot="right">
			</view>

		</tn-nav-bar>
		<view class="tabs-fixed-blank" :style="{paddingTop: vuex_custom_bar_height + 10 + 'px'}"></view>


		<view class="mascon">
			<view class="tabs">
				<view class="item" :class="sstype==1 ? 'hoverss' : ''">
					<view class="con" @click="setsstype" :data-sstype='1'><text
							class="iconfont icon-cangkupeizhi"></text> 物品搜索</view>
				</view>
				<view class="item fr" :class="sstype==2 ? 'hoverss' : ''">
					<view class="con" @click="setsstype" :data-sstype='2'><text class="iconfont icon-kehuC"></text> 客户搜索
					</view>
				</view>
				<view class="clear"></view>
			</view>
			<view class="sskcon" :class="sstype==1 ? 'hoverss1' : 'hoverss2'">
				<view class="ss1">
					<input class="inp" :placeholder="'请输'+sstypetxt+'名称'" v-model="thkwtt" name="input"
						placeholder-style="color:#AAAAAA" maxlength="50"></input>
						
						<block v-if="thkw">
							<text class="iconfont icon-cuohao02" style="margin-left:10upx;" @click="clsthkw"></text> 
						</block>
				</view>
				<view class="ss2">
					<view class="ssbtn" @click="searchsubmit">
						<text class="iconfont icon-sousuo1"></text><text>搜索</text>
					</view>
				</view>
				<view class="clear"></view>
			</view>
		</view>

		<block v-if="listdata.length > 0">
			<view class="maxcon">
				<view class="ssjgcon">
					<scroll-view scroll-y="true" @scrolltolower="loaditems" class="scroll_right sv"
						:style="{height:SrollHeight+'px'}" :scroll-top="ScrollTop">
					

								<block v-if="sstype==1">
									
									<view class="splist">
									
										<block v-for="(item,index) in listdata" :key="index">
									
												<view class="item ">
													<view class="sjstatus" :class="'sta'+item.sjsta">{{item.sjstatusname}}</view>
													<view class="inf1">
														<view class="spbh">编号：{{item.id}}</view>
														<view class="status" :class="'sta'+item.status" v-if="item.statusname">
															{{item.statusname}}</view>
														<view class="otbstxt" v-if="item.otbstxt">{{item.otbstxt}}</view>

														<view class="tf1">
															<view class="tx">
																<image :src="staticsfile + item.smallpic"></image>
															</view>
															<view class="xx">
																<view class="n0"><text class="ddtype"
																		:class="'ys'+item.ddtype">{{item.ddtypename}}</text>
																	{{item.sortname}}</view>
																<view class="n1">{{item.title}}</view>
																<view class="n2">库存数量：{{item.kucun}}</view>
																<view class="n2">{{item.dtptxt}}单价：￥{{item.danjia}}</view>
																<view class="n2">{{item.dtptxt}}总价：<text
																		class="zj">￥{{item.zongjia}}</text></view>
																<view class="n2">上架单价：<text>￥{{item.sjjiage}}</text></view>

															</view>
															<view class="clear"></view>
														</view>
													</view>
													<view class="inf2">
														<view class="f1">所在店铺：{{item.mdname}} <text style="margin-left:30upx">{{item.ckname}}</text></view>
														<view class="f1" v-if="item.rktime">入库时间：{{item.rktime}}</view>
													</view>
												</view>
										
										
										</block>
										
									</view>
										
								</block>
								<block v-if="sstype==2">
									    <view class="ygc">
											<block v-for="(item,index) in listdata"	:key="index">
												<view class="item"  >
													
													<view class="inf1">
														  <view class="djcon">{{item.dengjitxt}}</view>
														  <view class="tx">
															  <view class="avatar"><image :src="staticsfile + item.avatar"></image></view>
															  <view class="sexicon" v-if="item.sex > 0"><image :src="'/static/images/sexicon'+item.sex+'.png'"></image></view>
														  </view>
														  <view class="xx">
															  <view class="tit">
																  <text class="name">{{item.realname}}</text>
																  <text class="uid" >（编号:{{item.id}})</text>
															  </view>
															  <view class="ssyg" v-if="item.mdname">所属门店：{{item.mdname}}</view>
															  <view class="ssyg" v-if="item.ygname">所属员工：{{item.ygname}}</view>
															  <view class="ssyg">添加时间：{{item.addtime}}</view>
														  </view>
														  <view class="clear"></view>
													</view>
												</view>  
											</block>
									    </view>  
								</block>


						

						<text class="loading-text" v-if="isloading">
							{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
						</text>
					</scroll-view>

				</view>

			</view>
		</block>
		<block v-else>
	        <block v-if="thkw">
				<view class="sscno">
					<view class="icon"><text class="iconfont icon-wushuju"></text></view>
					<view class="tit" >暂未找到相关{{sstypetxt}}</view>
				</view>
			</block>
			<block v-else>
				<view class="sscno">
					<view class="icon"><image src="/static/images/ssicon.png" mode="widthFix"></image></view>
					<view class="tit" >输入关键字搜索</view>
				</view>
			</block>	
		</block>

		<view style="height:60upx"></view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				thkw: '',
				thkwtt: '',
				sstype: 1,
				sstypetxt: '物品',
				ScrollTop: 0,
				SrollHeight: 200,
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
			}
		},
		onReady: function() {
			let that = this; 
			let SrollHeight = uni.getSystemInfoSync().windowHeight - 290;
			that.SrollHeight = SrollHeight;

		},
		onLoad() {
			let that = this;

		},
		onShow() {
			let that = this;

		},
		methods: {
			
			clsthkw(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			searchsubmit() {
				let that = this;
			

				// let tipx = '请输入要搜索的物品';
				// if (that.sstype == 2) {
				// 	tipx = '请输入要搜索的客户';
				// }

				// if (!that.thkwtt) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: tipx
				// 	});
				// 	return;
				// }
                that.thkw=that.thkwtt;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();


			},
			setsstype(e) {
				let that=this;
				let sstype = e.currentTarget.dataset.sstype;
				this.sstype = sstype;
				if (sstype == 1) {
					this.sstypetxt = '物品';
				} else {
					this.sstypetxt = '客户';
				}
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
			},

			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					page: page,
					sstype: that.sstype ? that.sstype : 0,
					kw: that.thkw ? that.thkw : '',
				}
				console.log(da);
				if (page != 1) {
					that.isloading = true;
				}

				if (that.loadingType == 2) {
					return false;
				}
				uni.showNavigationBarLoading();
				this.$req.get('/v1/sousuo/sslist', da)
					.then(res => {
						console.log(res);
						if (res.data.listdata && res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},




		}
	}
</script>
<style>
	page {
		background: linear-gradient(to bottom, #FFD427, #FFD427, #f6f6f6, #f6f6f6);
	}
</style>
<style lang="scss" scoped>
	.mascon {
		margin: 15upx 32upx 32upx 32upx;
		position: relative;
	}

	.tabs {}

	.tabs .item {
		width: 48%;
		float: left;
	}

	.tabs .item.fr {
		float: right;
	}

	.tabs .item .con {
		text-align: center;
		font-size: 28upx;
		height: 70upx;
		line-height: 70upx;
		border-radius: 20upx 20upx 0 0;
	}

	.tabs .item .con .iconfont {
		margin-right: 10upx
	}

	.tabs .item.hoverss .con {
		background: #fff;
		font-size: 32upx
	}

	.tabs .item.hoverss .con .iconfont {
		font-size: 32upx;
	}


	.sskcon {
		padding: 32upx 32upx 32upx 32upx;
		background: #fff;
		border-radius: 20rpx;
	}

	.sskcon.hoverss1 {
		border-radius: 0 20upx 20upx 20upx;
	}

	.sskcon.hoverss2 {
		border-radius: 20upx 0 20upx 20upx;
	}

	.sskcon .ss1 {
		float: left;
		width: 70%;
		position: relative;
	}
	.sskcon .ss1 .icon-cuohao02{position: absolute;right:20upx;top:27upx;}

	.sskcon .ss1 .inp {
		height: 80upx;
		line-height: 80upx; 
		background: #fff;
		border-radius: 10upx;
	    padding-left:20upx;
		border: 1px solid #FFD427
	}

	.sskcon .ss2 {
		float: right;
		width: 25%;
	}

	.ssbtn {
		height: 80upx; 
		line-height: 80upx;
		background: #FFD427;color:#333;
		text-align: center;
		border-radius: 10upx;
		width: 100%;
	}

	.ssbtn .iconfont {
		margin-right: 5upx
	}


	.maxcon {}

	.maxcon .ssvit {
		height: 80upx;
		line-height: 80upx;
		padding: 0 32upx
	}
 

    .sscno{background:#fff;border-radius:20upx;margin:32upx}
    .sscno{text-align: center;padding:50px 0 70px 0}
	.sscno .icon{padding-top:30px}
	.sscno .icon .iconfont{font-size:55px;}
	.sscno .icon image{width:120upx;}
	.sscno .tit{height:45px;line-height:45px;margin-bottom:12px;}
	.sscno .gotobtn{background:#FFD427;border-radius:100upx;height:80upx;line-height:80upx;margin:0 auto;width: 250upx;}
	
   
	.splist {
		padding: 0 32upx 32upx 32upx
	}

	.splist .item {
		margin-bottom: 32upx;
		background: #fff;
		border-radius: 20upx;
		position: relative;
	}

	.splist .item .spbh {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 20upx;
		color: #1677FF
	}

	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .status {
		position: absolute;
		right: 20upx;
		bottom: 100upx;
		border-radius: 30upx;
		padding: 5upx 15upx;
		color: #fff;
		background: #ccc;
		font-size: 24upx
	}

	.splist .item .inf1 .otbstxt {
		position: absolute;
		right: 20upx;
		bottom: 45upx;
		font-size: 24upx;
		color: #1677FF
	}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 240upx;
		height: 240upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: left;
		margin-left: 20upx;
		width: calc(100% - 265upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 45upx;
		line-height: 45upx;
		overflow: hidden;
		margin-bottom: 5upx;
		font-size: 28upx
	}

	.splist .item .inf1 .xx .n2 {
		margin-top: 2upx;
	}

	.splist .item .inf1 .xx .n2 text {}

	.splist .item .inf1 .xx .n2 .zj {
		color: #ff0000
	}

	.splist .item .inf2 {
		background: #F0F0F0;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 15upx 28upx;
		font-size: 20rpx;
		color: #333;
		position: relative;
		line-height: 40upx;
	}

	.splist .item .inf2 .rktime {}

	.splist .item .inf2 .f1 {}

	.splist .item .inf2 .setbtn {
		position: absolute;
		right: 25upx;
		top: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .inf2 .setbtn2 {
		position: absolute;
		right: 145upx;
		top: 50upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fff;
		border-radius: 8rpx;
		text-align: center;
		color: #333
	}

	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}


	.splist .item .status.sta1 {
		background: #54D216;
	}

	.splist .item .status.sta12 {
		background: #54D216;
	}

	.splist .item .status.sta2 {
		background: #FF6600;
		color: #fff
	}

	.splist .item .status.sta3 {
		background: #FF2828;
		color: #fff
	}

	.splist .item .status.sta8 {
		background: #888;
	}

	.splist .item .status.sta9 {
		background: #c76096;
		color: #fff
	}

	.splist .item .sjstatus {
		font-size: 20upx;
		border-radius: 0upx 20upx 20upx 0upx;
		height: 40upx;
		line-height: 40upx;
		padding: 0 25upx;
		color: #0EC45C;
		margin-right: 10upx;
		position: absolute;
		left: 18upx;
		top: 40upx;
		z-index: 1;
		border-left: 5px solid #FFD427;
	}

	.splist .item .sjstatus.sta1 {
		background: #43b058;
		color: #fff
	}

	.splist .item .sjstatus.sta2 {
		background: #888;
		color: #fff
	}
	
	.ygc{margin:0 32upx}
	.ygc .item{background: #FFFFFF;border-radius: 20rpx;margin-bottom:20upx;padding:28upx 28upx;position: relative;}
	.ygc .item .wgluid{position: absolute;right:30upx;top:80upx;font-size:20upx;background:#FFD427;border-radius: 100upx;padding:5upx 15upx;}
	.ygc .item .tx{float:left;position: relative;}
	.ygc .item .tx .avatar image{width: 100upx;height:100upx;border-radius:100upx;display: block;}
	.ygc .item .tx .sexicon{position: absolute;bottom:-20upx;left:35upx}
	.ygc .item .tx .sexicon image{width: 32upx;height:32upx;}
	.ygc .item .xx{float:left;margin-left:30upx}
	.ygc .item .xx .tit{height:40upx;line-height:40upx;font-size:28upx;padding-top:10upx;margin-bottom:20upx}
	.ygc .item .xx .tit .name{font-size:28upx;font-weight:600}
	.ygc .item .xx .tit .uid{font-size: 24rpx;margin-left:10upx;color:#888}
	.ygc .item .xx .ssyg{height:30upx;line-height:30upx;font-size:20upx;margin-top:0upx;color: #7F7F7F;overflow: hidden;}
	.ygc .djcon{position: absolute;right:0;top:0;font-size:24upx;background:#E7FFE2;border-radius: 0rpx 20rpx 0rpx 20rpx;height:42upx;line-height:42upx;padding:0 15upx;color:#0EC45C}
	.ygc .inf3{text-align: center;margin-top:30upx}
	.ygc .inf3 .ite{float:left;width:33%;}
	.ygc .inf3 .ite .ic{display: inline-block;}
	.ygc .inf3 .ite image{width:36upx;height:36upx;vertical-align: middle;}
	.ygc .inf3 .ite .wb{display: inline-block;font-size: 24rpx;margin-left:10upx}
	
</style>