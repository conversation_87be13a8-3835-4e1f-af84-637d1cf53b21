<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>售卖单信息</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" style="background:#fff;" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="ddtopcon">
				<view class="ddbh">
					{{djdata.ordernum}}
				</view>
				<view class="ddstacon" :class="'ys'+djdata.status">{{djdata.statusname}}</view>
				<view class="ddtticon">
					<image src="/static/images/ddtoicon1.png" style="width:105px;height:90px;"></image>
				</view>
			</view>


			<view class="sqbdcon">


				<view class="maxcon" style="padding-bottom:10upx">
					<view class="vstit">客户信息</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>客户姓名</view>
						<view class="inpcon">
							<view class="inp">{{djdata.khname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>客户电话</view>
						<view class="inpcon">
							<view class="inp">{{djdata.khtel}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx">
						<view class="tit"><text class="redst"></text>出库时间</view>
						<view class="inpcon">
							<view class="inp">{{djdata.ywtime}}</view>
						</view>
						<view class="clear"></view>
					</view>


				</view>


				<view class="maxcon">

					<view class="vstit" style="margin-bottom:30upx">物品列表</view>

					<block v-if="djshop">
						<view class="splist">
							<block v-for="(item,index) in djshop" :key="index">
								<view class="spitem">
									<view class="spname">
										<view class="tline"></view>
										{{item.title}}
										<text
											style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.spid}})</text>
									</view>
									<!-- 		<view class="rowa">
												<view class="tip">物品编号</view>
												<view class="nr colorls" >{{item.spid}}</view>
												<view class="clear"></view>
											</view> -->
									<view class="rowa">
										<view class="tip">物品类型</view>
										<view class="nr" style="color:#1677FF">{{item.ddtypename}}</view>
										<view class="clear"></view>
									</view>
									<view class="rowa">
										<view class="tip">上架价格</view>
										<view class="nr">¥{{item.sjjiage}}</view>
										<view class="clear"></view>
									</view>
									<view class="rowa">
										<view class="tip">出售价格</view>
										<view class="nr">¥{{item.ckjiage}}</view>
										<view class="clear"></view>
									</view>
									<view class="rowa">
										<view class="tip">物品数量</view>
										<view class="nr">{{item.cknum}}</view>
										<view class="clear"></view>
									</view>
									<view class="rowa">
										<view class="tip">出售总价</view>
										<view class="nr">
											¥<text style="color:#ff0000">{{item.zongjia}}</text>
										</view>
										<view class="clear"></view>
									</view>
									<!-- 	<block v-if="item.ddtype==1 || item.ddtype==3">
													<view class="rowa">
														<view class="tip">服务费</view>
														<view class="nr">¥{{item.fwfjine}}</view>
														<view class="clear"></view>
													</view>
												</block> -->

									<block v-if="item.lirun >= 0">
										<view class="rowa">
											<view class="tip">物品利润</view>
											<view class="nr">
												¥<text style="color:#ff0000">{{item.lirun}}</text>
											</view>
											<view class="clear"></view>
										</view>
									</block>
									<block v-else>
										<view class="rowa">
											<view class="tip">亏损金额</view>
											<view class="nr">
												¥<text style="color:#4aac09">{{item.lirun}}</text>
											</view>
											<view class="clear"></view>
										</view>
									</block>
									<!-- 	<block v-if="item.ddtype==1">
													<view class="rowa">
														<view class="tip">退还客户本金</view>
														<view class="nr">
															¥<text >{{item.thkhbj}}</text>
														</view>
														<view class="clear"></view>
													</view>
												</block> -->


									<block v-if="item.vxq==1">

										<view class="rowa">
											<view class="tip">所在门店</view>
											<view class="nr">{{item.mdname}}</view>
											<view class="clear"></view>
										</view>
										<view class="rowa">
											<view class="tip">所在仓库</view>
											<view class="nr">{{item.ckname}}</view>
											<view class="clear"></view>
										</view>

										<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
											<view class="rowa">
												<view class="tip">{{itemx.title}}</view>
												<view class="nr">{{itemx.value}}</view>
												<view class="clear"></view>
											</view>
										</block>

										<block v-if="item.picturesarr">
											<view class="rowxmttp">
												<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
													<block v-for="(itemxx,indexx) in item.picturesarr" :key="indexx">
														<view class='item'>
															<image :src="staticsfile+itemxx"
																:data-src='staticsfile + itemxx ' @click='previewImagev'
																:data-picarr="item.picturesarr"></image>
														</view>
													</block>
													<view class="clear"></view>
												</view>
											</view>
										</block>

										<block v-if="item.beizhu">
											<view class="bzcon">
												<view class="bztit">物品备注：</view>
												<view class="bznr">{{item.beizhu}}</view>
											</view>
										</block>

									</block>
									<view class="saacon">
										<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
											<block v-if="item.vxq==0">
												展开详情<text class="iconfont icon-jtdown2"></text>
											</block>
											<block v-else>
												收起详情<text class="iconfont icon-jtup2"></text>
											</block>
										</view>
										<view class="clear"></view>
									</view>

							
									<block v-if="item.czfrrydata.length > 0">
										<block v-if="item.lirun >= 0">

											<view class="czfrcon">
												<view class="cstit">
													<view class="tit"><text class="iconfont icon-renminbi"></text>
														分润</view>
													
													<view class="clear"></view>
												</view>
											</view>

											<block v-if="item.czfrrydata">
												<block v-for="(itemx,indexx) in item.czfrrydata" :key="indexx">
													<view class="czrli">
														<!-- <block v-if="djdata.status==2 && itemx.rytype==9">
															<view class="del" @click="delczry" :data-czryid="itemx.id">
																<text class="iconfont icon-cuohao02"></text></view>
														</block> -->
														<view class="cname">{{itemx.ygname}}
															<text class="czjs">({{itemx.rytypetxt}})</text>
														</view>
														<view class="xx">

															<view class="czxm" v-if="itemx.czjine > 0">出资金额：<text <text
																	:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
															</view>

															<view class="czxm">分润金额：<text
																	:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
															</view>
															<!-- <view class="czxm2" @click="czryedit" :data-spid="item.spid"
																:data-ckspid="item.id" :data-czryid="itemx.id"
																:data-czygid="itemx.ygid" :data-czygname="itemx.ygname"
																:data-czjine="itemx.czjine" :data-rytype="itemx.rytype"
																:data-frjine="itemx.frjine"
																:data-rytypetxt="itemx.rytypetxt"
																:data-isdpczzh="itemx.isdpczzh" :data-frbl="itemx.frbl"
																v-if="djdata.status==2" :data-isofr="1">修改</view> -->
															<view class="clear"></view>
														</view>
													</view>
												</block>
											</block>

											<view class="czrli"  v-if="isvallfrcz==1">
												<view class="xx">
													<view class="czxm hj" style="width:100%;">已分润金额：<text
															class="ysz">{{item.frjinehj}}</text> <text
															class="gx">,</text> 未分润金额：<text
															class="hs">{{item.frjinehj2}}</text></view>
													<view class="clear"></view>
												</view>
											</view>

										</block>
										<block v-else>


											<view class="czfrcon">
												<view class="cstit">
													<view class="tit"><text class="iconfont icon-renminbi"></text>
														亏损</view>
													<view class="clear"></view>
												</view>
											</view>

											<block v-if="item.czfrrydata">
												<view class="czry4info">
													<block v-for="(itemx,indexx) in item.czfrrydata" :key="indexx">
														<view class="czrli">

															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm" v-if="itemx.rytype==8">出资金额：<text
																		:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
																</view>
																<view class="czxm">亏损金额：<text
																		style="color:#4aac09">{{itemx.ksjine}}</text>
																</view>
																<!-- <block v-if="djdata.status==2">
																	<view class="zjjzbtn">
																		<view class="btna bt3" @click="ksryedit"
																			:data-rytype="itemx.rytype"
																			:data-shczid="itemx.id">-亏损</view>
																		<view class="clear"></view>
																	</view>
																</block> -->
																<view class="clear"></view>
															</view>
														</view>
													</block>
												</view>
											</block>


											<view class="czrli" v-if="isvallfrcz==1">
												<view class="xx">
													<view class="czxm hj" style="width:100%;">亏损已分配：<text
															class="ysz">{{item.ksjinehj}}</text> <text
															class="gx">,</text>
														未分配(店铺亏损)：<text class="hs"
															style="color:#4aac09">{{item.ksjinehj2}}</text>
													</view>
													<view class="clear"></view>
												</view>
											</view>

										</block>
									</block>


								</view>
							</block>
						</view>
					</block>

				</view>




				<view class="maxcon" style="padding-bottom:10upx">
					<view class="vstit">费用信息</view>

					<view class="rowx">
						<view class="tit"><text class="redst"></text>物品总价</view>
						<view class="inpcon">
							<view class="inp" style="color:#ff0000">{{djdata.spzongjia}}</view>
						</view>
						<view class="clear"></view>
					</view>


				</view>


				<view class="maxcon" style="padding-bottom:10upx">

					<view class="rowx">
						<view class="tit"><text class="redst"></text>销售人员</view>
						<view class="inpcon">
							<view class="inp">{{djdata.xsrytxtstr}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit"><text class="redst"></text>录单人员</view>
						<view class="inpcon">
							<view class="inp">{{djdata.opname}}</view>
						</view>
						<view class="clear"></view>
					</view>

				</view>


				<block v-if="img_url_ok.length>0 || djdata.remark">
					<view class="maxcon">
						<view class="vstit" style="margin-bottom:25upx">单据备注说明 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>
						<view class="bzcon">
							{{djdata.remark}}
						</view>
						<block v-if="img_url_ok.length>0">
							<view class="rowxmttp">
								<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
									<block v-for="(item,index) in img_url_ok" :key="index">
										<view class='item'>
											<image :src="staticsfile+item" :data-src='staticsfile + item '
												@tap='previewImage'></image>
										</view>
									</block>
									<view class="clear"></view>
								</view>
							</view>
						</block>
					</view>
				</block>

			</view>















		</view>

		<view style="height:10upx"></view>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				spzongjia: 0,
				clicksta: false,
				remark: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid: 0,
				isvallfrcz: 0,
				vsour: 0,
			}
		},

		onLoad(options) {
			let that = this;
			let vsour = options.vsour ? options.vsour : 0;
			this.vsour = vsour;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},

		onShow() {

		},

		methods: {

			toback() {
				uni.navigateBack();
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					vsour: that.vsour,
					et: 2,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ydview/ckdeditx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.djshop = res.data.djshop;
							that.remark = res.data.djdata.remark;
							that.spzongjia = res.data.spzongjia;
							that.isvallfrcz = res.data.isvallfrcz;
							that.img_url_ok = res.data.djdata.picturesarr;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},
			previewImagev: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;
				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},

			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},

			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},








		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 0upx 0;
	}
	.spitem .saacon {
		border-top: 1px dashed #eee;
		padding-top: 20upx
	}
	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 75px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
		position: relative;
		
	}

	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}
	.spitem .viewxqcon {
		float: right;
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		text-align: right;
		font-size: 24upx;
	}

	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 0upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}


	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}
	
	
	.spitem .czfrcon {
		border-top: 1px dashed #FFD427;
		margin-top: 25upx;
		padding: 25upx 0
	}
	
	.spitem .czfrcon .tit {
		float: left;
		font-size: 28upx;
		font-weight: 600
	}
	
	.spitem .czfrcon .tit .iconfont {
		margin-right: 10upx;
		color: #ff0000
	}
	
	.spitem .czfrcon .tadd {
		float: right;
	}
	
	.spitem .czfrcon .tadd .iconfont {
		margin-right: 10upx;
		color: #1677FF
	}
	
</style>