<template>
	<view class="mcn">
		<tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
			  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
			  		  <text  >绑定微信</text>
			  		</view>
			  </tn-nav-bar>
		
		 
		
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		
		
		<view class="czconss">
		
		    <view class="icon">
				<text class="iconfont icon-weixin"></text>
			</view>
		
		    <view class="bdsm" >{{bdwxinfo.bd_wx_xx ?  bdwxinfo.bd_wx_xx : '绑定微信登录' }}</view>
			
			<view class="fm-btncon" v-if="!bdwxinfo.bd_wx">
			  <button class="bomsubbtn" style="margin:0" @click="wxloginbd" >授权绑定</button>
			</view>
			
			<block v-if="bdwxinfo.bd_wx">
				<view class="fm-btncon">
				  <button class="bomsubbtn" style="margin:0;background:#eee;color:#333" @click="wxjcbd"  >解除绑定</button>
				</view>
			</block>
			
			
		</view> 

	</view>
</template>

<script>
	export default {
		data() {
			return {
				bdwxinfo:''
			}
		},
		
		onShow() {
				this.loadData();
		},
		
		methods: {
			loadData(){
					  let that=this;
					  let da={  
					 
					  }
					
					 this.$req.post('/v1/muser/bdwxinfo', da)
							 .then(res => {
								console.log(res)
								if(res.errcode==0){
								   that.bdwxinfo=res.data.bdwxinfo;
								}
							 })
			},
			
			wxjcbd(){
				let that=this;
				  uni.showModal({
				  					title: '解绑确认',
				  					content: '确认解除绑定该微信账号吗？',
				  					success: function(e) {
				  						//点击确定
				  						if (e.confirm) {
				  				   
				  							let da = {
				  							
				  							}
				  							uni.showLoading({
				  								title: ''
				  							})
				  							that.$req.post('/v1/muser/bdwxjc', da)
				  								.then(res => {
				  									uni.hideLoading();
				  									if (res.errcode == 0) {
				  										uni.showToast({
				  											icon: 'none',
				  											title: res.msg,
				  											success() {
				  												setTimeout(function() {
				  													  	that.loadData();
				  												}, 1000);
				  											}
				  										});
				  									} else {
				  										uni.showToast({
				  											icon: 'none',
				  											title: res.msg
				  										});
				  									}
				  								})
				  						}
				  					}
				  })
			},
			
			
			wxloginbd(){
				
				 let that = this;
				//#ifdef MP-WEIXIN
						  uni.login({
							provider: 'weixin',
							success: function (loginRes) {
							  let code=loginRes.code;
							  let da={
								  code:code,
							  };
							  uni.showLoading({ 'title': '' });
							  that.$req.post('/v1/muser/bdwxminip',da)
							  		.then(res => {
							  			uni.hideLoading();
							  			if(res.errcode!=0){
												uni.showModal({
												  title: '信息提示',
												  content: res.msg,
												  showCancel: false
												})
							  			}else{
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													setTimeout(function(){
													  uni.navigateBack()
													},1000)
												}
											});
							  				
							  			}
							  			
							  		})
							  		.catch(err => {
							  		
							  		})
							  
							  
							}
						  });
				// #endif
				
				
				
				
				
				
				// #ifdef APP-PLUS
				 
							uni.login({
									provider: 'weixin',
									success: function (loginRes) {
										
										uni.getUserInfo({
										  provider: 'weixin',
										  success: function(infoRes) {
									
											let userinfo=infoRes.userInfo;
										    let openid=userinfo.openId;
										    let unionid=userinfo.unionId;
										    let nickname=userinfo.nickName;
										    let avatar=userinfo.avatarUrl;
											
										     
											 let da={
											 	openid:openid,
											 	unionid:unionid,
											 	nickname:nickname,
											 	avatar:avatar
											 };
											 uni.showLoading({ 'title': '' });
											 that.$req.post('/v1/muser/bdwxapp',da)
											 		.then(res => {
											 			uni.hideLoading();
											 			if(res.errcode!=0){
																uni.showModal({
																  title: '信息提示',
																  content: res.msg,
																  showCancel: false
																})
											 			}else{
															uni.showToast({
																icon: 'none',
																title: res.msg,
																success() {
																	setTimeout(function(){
																	  uni.navigateBack()
																	},1000)
																}
															});
											 			}
											 		})
											 		.catch(err => {
											 		
											 		})
											 
											 
											
										  }
										})
								  
									  
									  
									}
							});
				 
				 
				// #endif
				
				
				
				
				
				
				
				
				
			}
			
			
			
			
			
			
		}
	}
</script>

<style>


.fm-btncon {margin-top: 50px;}
	
	
.czconss{margin:32upx;padding:32upx;background:#fff;border-radius:20upx;text-align: center;padding:50px}
.czconss .icon{padding-top:50px;}
.czconss .icon text{font-size:45px;color:#09bb07}
.czconss .bdsm{font-size:38upx;margin-top:45px;}

</style>
