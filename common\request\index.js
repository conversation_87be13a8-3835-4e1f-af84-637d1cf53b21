import Request from './request'
import {setTokenStorage,getTokenStorage,configHandle} from '@/common/tool.js'
import {createNonceStr, arithmetic,signatureGenerate} from '@/common/signutil.js'
import apconfig from '@/common/apconfig.js'
import { Encrypt, Decrypt } from '@/common/aes.js'

const req = new Request()
/**
 * jwt 无痛刷新token思路（如果不使用无痛刷新token,忽略此处注释）
 * 看了很多，有个问题一直得不到解决----‘多个接口请求，token失效，如何让获取token只获取一遍’、
 * 于是想到了闭包防抖......
 * 本方案并不是最佳方案，只是给你们提供一种思路。如果你有完美解决方案，可以分享一下
 */
const expireToken = [] // 储存过期的token

// 防抖闭包来一波
function getTokenDebounce () {
  let lock = false
  let success = false
  return async function () {
    if (!lock) {
      lock = true
      getNewTokenServe().then(res => {
        console.log('获取了新的autoken')
        console.log(res.data.data.autoken)
        setTokenStorage(res.data.data.autoken) // todo 储存token，可更换为自己的储存token逻辑
        success = true
        lock = false
      }).catch(() => {
        success = false
        lock = false
      })
    }
    return new Promise(resolve => {
      // XXX 我只能想到通过轮询来看获取新的token是否结束，有好的方案可以说。一直看lock,直到请求失败或者成功
      const timer = setInterval(() => {
        if (!lock) {
          clearInterval(timer)
          if (success) {
            resolve('success')
          } else {
            resolve('fail')
          }
        }
      }, 100) // 轮询时间可以自己看改成多少合适
    })
  }
}

req.setConfig((config) => { /* 设置全局配置 */

  //console.log(asingval);
  config.baseUrl = apconfig.apiurl
  config.header = {
    ...config.header
  }
  return config
})

req.interceptor.request((config, cancel) => { /* 请求之前拦截器 */

	if(!config.data){
		config.data={};
	}
		

	let timeStamp = (new Date()).valueOf();
	let randomStr = createNonceStr(8);
	let signature =signatureGenerate(timeStamp, randomStr, apconfig.signsalt,config.url,config.data);

	
	config.data['timestamp'] = timeStamp;
	config.data['nonce'] = randomStr;
	config.data['sign'] = signature.signature;
	

  config.data={data:Encrypt(JSON.stringify(config.data))};

  let tokendata=getTokenStorage();
  let actoken=tokendata.access_token;
  if(actoken){
	 actoken = 'Bearer ' + actoken;
  }else{
	  actoken='';
  }
  
  config.header = {
    ...config.header,
	'content-type': 'application/x-www-form-urlencoded',
	'Authorization': actoken
  }
  // if (!autoken) { // 如果autoken不存在，调用cancel 会取消本次请求，但是该函数的catch() 仍会执行
  //   cancel('autoken 不存在') // 接收一个参数，会传给catch((err) => {}) err.errMsg === 'autoken 不存在'
  // }
  return config
})

/**
 * 自定义验证器，如果返回true 则进入响应拦截器的响应成功函数(resolve)，否则进入响应拦截器的响应错误函数(reject)
 * @param { Number } statusCode - 请求响应体statusCode（只读）
 * @return { Boolean } 如果为true,则 resolve, 否则 reject
 */
req.validateStatus = (statusCode) => {
  return statusCode === 200
}

req.interceptor.response((response) => { /* 请求之后拦截器 */
  if (response.data.errcode == 901) { // 如果是901则需要重新登录，则reject()
    uni.reLaunch({
       url: '/pages/public/login',
    })
	return false;
	// uni.navigateTo({
	//    url: '/pages/public/login',
	// })
	 // return false;
  }
 
  response.data.data=JSON.parse(Decrypt(response.data.data));
  return response.data
}, (response) => { // 请求错误做点什么
  return response.data
})


export {
  req
}
