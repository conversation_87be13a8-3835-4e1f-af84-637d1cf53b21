<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>信用预付信息</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">

					<view class="maxcon">

						<view class="vstip">请输入信用预付金额</view>
						<view class="rowxa">
							<view class="jinecon">
								<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
								<input type="number" name='yfjine' v-model="yfjine" placeholder='在此输入金额' maxlength='60'
									class="inputss" @blur="lxjscl('yfjine')" />
							</view>
						</view>

						<view class="rowx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>预付时间</view>
							<view class="inpcon">
								<view class="inp">
									<picker mode="date" :value="ywtime" @change="bindDateChange">
										{{ywtime ? ywtime : '点击选择时间'}}
									</picker>
								</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp">

									<radio-group @change="radioGroupChange">
										<label>
											<radio :value="1" :checked="hkfs == 1" color="#FFD427" /> 等额本息
										</label>
										<label style="margin-left:10px">
											<radio :value="2" :checked="hkfs == 2" color="#FFD427" /> 先息后本
										</label>
									</radio-group>

								</view>
							</view>
							<view class="clear"></view>
						</view>




						<view class="rowx">
							<view class="icon" @click="selywzq" style="color:#1677FF;z-index:200;">
								{{ywzqdwtxt ? ywzqdwtxt : '选择周期'}}
							</view>
							<view class="tit"><text class="redst"></text>使用周期</view>
							<view class="inpcon">
								<view class="inp"><input class="input" type="number" name="ywzqnum" v-model="ywzqnum"
										placeholder="请输入周期" placeholder-class="placeholder" @blur="lxjscl('ywzqnum')" />
								</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>总利息</view>
							<view class="inpcon">
								<view class="inp">
									<input class="input" type="number" name="lxzjine" v-model="lxzjine"
										placeholder="总利息" placeholder-class="placeholder" @blur="lxjscl('lxzjine')" />
								</view>
							</view>
							<view class="clear"></view>

							<view class="rowx6">
								<view class="wz">利率</view>
								<view class="srk"><input class="input2" type="number" name="lilv" v-model="lilv"
										placeholder="请输入" placeholder-class="placeholder" @blur="lxjscl('lilv')" />
								</view>
								<view class="tb"  style="color:#1677FF;z-index:200;">%</view>
								<view class="clear"></view>
							</view>

						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总还款金额</view>
							<view class="inpcon">
								<view class="inp">
									{{hkzjine ? hkzjine : '--' }}
								</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="hkfs==2">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">
										{{dqtimetxt ? dqtimetxt : '--' }}
									</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


						<!-- 	<block v-if="hkjhdata">
														<view class="rowx" @click="ckhkjh">
															<view class="icon"><text class="iconfont icon-jiantou"></text></view>
															<view class="tit"><text class="redst"></text>还款计划</view>
															<view class="inpcon">
															   <view class="inp">
																  {{hkjhtip}}
															   </view>
															</view>
															<view class="clear"></view>
														</view>
													</block> -->

					</view>

					<block v-if="hkfs==1 && hkjhdata.length > 0">
						<view class="maxcon" style="padding:20upx 0">
							<view class="vstit" style="padding-left:30upx">还款计划</view>
							<view class="khjhcon">
								<view class="hkjh-timeline">
									<view class="hkjh-timeline-content">
										<block v-for="(item,index) in hkjhdata" :key="index">
											<view class="item hkjh-timeline-item">
												<em class="yd-timeline-icon"></em>
												<view class="hkjineinp">
													<view class="fh">¥</view>
													<block v-if="item.isxg==1">
														<input class="input" type="number" v-model="item.yhkzjine"
															placeholder="" placeholder-class="placeholder"
														@focus="hkjhFocus"	@blur="hkjhset" :data-jhid="item.id" />
														
														<view class="cls" @click="clsthjh" :data-jhid="item.id"  v-if="thjhid==item.id"><text
																class="iconfont icon-guanbixiao" ></text></view>
													
													</block> 
													<block v-else>
                                                         <view class="input">{{item.yhkzjine}}</view>
													</block>
												</view>
												<!-- <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view> -->
												<view class="qbcon">
													<view class="t">{{item.xhtit}}</view>
													<view class="r">{{item.rq}}</view>
												</view>
											</view>
										</block>
									</view>
								</view>
							</view>
						</view>
					</block>

				</view>



				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="toback" style="background:#ddd;">
								<text>上一步</text>
							</button>
						</view>
						<view class="bomsfright">
							<block v-if="isclsinput==1">
								<button class="bomsubbtn" form-type="submit">
									<text>下一步</text>
								</button>
							</block>
							<block v-else>
								<button class="bomsubbtn"  >
									<text>下一步</text>
								</button>
							</block>	
						</view>
						<view class="clear"></view>
					</view>
				</view>


			</form>








		</view>

		<view style="height:120upx"></view>



		<tn-popup v-model="gzselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">选择利率</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="lxgzlist">
					<block v-if="lxgzdata">
						<block v-for="(item,index) in lxgzdata" :key="index">
							<view class="item" @click="selthlxgz" :data-gzid="item.id" :data-lilv="item.feilv">
								<view class="yxicon" v-if="lilv==item.feilv"><text class="iconfont icon-duigou1"></text>
								</view>
								<view class="tit">{{item.feilv}} %</view>
							</view>

						</block>
					</block>
				</view>
			</scroll-view>
		</tn-popup>


		<!-- 
	<tn-popup v-model="hkjhshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
		<view class="popuptit" style="border-bottom:0">还款计划</view>
		
		<view class="hkjh-time-smtip">借款{{ywzqnum}}{{ywzqdwtxt}}，应还总额</view>
		<view class="hkjh-time-smjine">¥{{hkzjine}}</view>
		
	        <scroll-view scroll-y="true" style="height: 700rpx;">
				
			  <view class="khjhcon">
			     <view class="hkjh-timeline">
					  <view class="hkjh-timeline-content">
						  <block v-for="(item,index) in hkjhdata" :key="index">
							  <view class="item hkjh-timeline-item">
								  <em class="yd-timeline-icon"></em>
								  <view class="hkjine">¥{{item.yhkzjine}}</view>
								  <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view>
								  <view class="qbcon">
								     <view class="t">{{item.xhtit}}</view>
								     <view class="r">{{item.rq}}</view>
								  </view>
							  </view>
						  </block>
					  </view>
			     </view>
			  </view>
			  
	        </scroll-view>
	</tn-popup> -->


	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				gzselshow: false,
				ywtime: '',
				djdata: '',
				lilv: 0,
				lxgzdata: '',
				hkjhdata: '',
				hkjhtip: '',
				ywzqnum: '',
				ywzqdw: 2,
				ywzqdwtxt: '月',

				hkfs: 1,
				lxzjine: '',
				hkzjine: '',
				yfjine: '',
				hkjhshow: false,
				dqtimetxt: '',
				isclsinput:1,
				
				thjhid:0,
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			this.clicksta = false;
			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}
		},

		methods: {
			ckhkjh() {
				this.hkjhshow = true;
			},
			toback() {
				uni.navigateBack();
			},
			sellxgz() {
				this.gzselshow = true;
			},
			selthlxgz(e) {
				let lilv = e.currentTarget.dataset.lilv;
				this.lilv = lilv;
				this.gzselshow = false;
				this.lxjscl('lilv');
			},
			radioGroupChange(e) {
				this.hkfs = e.detail.value;
				this.lxjscl('hkfs');
			},
			hkjhFocus(e){
				let that = this;
				let thjhid = e.currentTarget.dataset.jhid;
				this.thjhid=thjhid;
				this.isclsinput=0;
			},
			hkjhset(e) {
				let that = this;
				let jhid = e.currentTarget.dataset.jhid;
				let jhjine = e.target.value;
				let da = {
					djid: this.djid,
					jhid: jhid,
					xgtype: 1, //修改
					jhjine: jhjine,
				}
		
				this.$req.post('/v1/danju/hkjhxg', da)
					.then(res => {
				        
						console.log('2322222', res);
						if (res.errcode == 0) {
							that.hkjhdata = res.data.hkjhdata;
						    that.isclsinput=1;
						} else {
							that.isclsinput=1;
							console.log(res.msg)
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg,
								});
							}
							return false;
						}
					})
			},
			clsthjh(e) {
				let that = this;
				let jhid = e.currentTarget.dataset.jhid;
				let jhjine = 0;
				let da = {
					djid: this.djid,
					jhid: jhid,
					xgtype: 2, //清0
					jhjine: jhjine,
				}
				// console.log(that.isclsinput);
				// if(that.isclsinput==0){
				// 	return false;
				// }
			
				this.$req.post('/v1/danju/hkjhxg', da)
					.then(res => {
					
						console.log('2322222', res);
						if (res.errcode == 0) {
							that.hkjhdata = res.data.hkjhdata;
						} else {
							console.log(res.msg)
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg,
								});
							}
							return false;
						}
					})

			},
			lxjscl(upzd) {
				let that = this;
				let ywtime = this.ywtime;
				let ywzqnum = this.ywzqnum;
				let lilv = this.lilv;
				let yfjine = this.yfjine;
				let hkfs = this.hkfs;
				let ywzqdw = this.ywzqdw;
				let ywzqdwtxt = this.ywzqdwtxt;
				let lxzjine = this.lxzjine ? this.lxzjine : 0;


				


				// if(!yfjine || !ywzqnum || !lilv || lilv==0 ){
				//  return false;
				// }

				let da = {
					djid: this.djid,
					yfjine: yfjine,
					hkfs: hkfs,
					ywtime: ywtime,
					ywzqnum: ywzqnum,
					lilv: lilv,
					ywzqdw: ywzqdw,
					ywzqdwtxt: ywzqdwtxt,
					lxzjine: lxzjine,
					upzd: upzd ? upzd : 0,
				}
			
				this.$req.post('/v1/danju/lxjscl', da)
					.then(res => {
					
						console.log('2322222', res);
						if (res.errcode == 0) {
							that.lxzjine = res.data.lxzjine;
							that.hkzjine = res.data.hkzjine;
							that.hkjhdata = res.data.hkjhdata;
							that.hkjhtip = res.data.hkjhtip;
							that.dqtimetxt = res.data.dqtimetxt;
							that.lilv = res.data.lilv;
						} else {
							console.log(res.msg)
							// uni.showToast({
							// 	icon: 'none',
							// 	title: res.msg,
							// });
							// return false;
						}
					})
			},

			selywzq: function() {
				let that = this;
				uni.showActionSheet({
					itemList: ['月', '周', '日'],
					success: function(res) {
						var index = res.tapIndex;
						if (index == 0) {
							that.ywzqdw = 2;
							that.ywzqdwtxt = '月';
						}
						if (index == 1) {
							that.ywzqdw = 3;
							that.ywzqdwtxt = '周';
						}
						if (index == 2) {
							that.ywzqdw = 4;
							that.ywzqdwtxt = '日';
						}
						that.lxjscl('ywzqdw');
					},
				});
			},


			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					et: 1,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdeditx41', da)
					.then(res => {
						uni.hideLoading();

						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.hkfs = res.data.djdata ? res.data.djdata.hkfs : 1;
							that.lxgzdata = res.data.lxgzdata;
							that.lilv = res.data.djdata.lilv ? res.data.djdata.lilv : 0;
							// that.lxgztxt=res.data.djdata.lxgztxt;
							that.yfjine = res.data.djdata.yfjine;
							that.lxzjine = res.data.djdata.lxzjine;
							that.hkzjine = res.data.djdata.hkzjine;
							that.hkjhdata = res.data.hkjhdata;
							that.hkjhtip = res.data.hkjhtip;
							that.ywzqnum = res.data.djdata.ywzqnum != 0 ? res.data.djdata.ywzqnum : '';
							that.ywzqdw = res.data.djdata.ywzqdw ? res.data.djdata.ywzqdw : 2;
							that.ywzqdwtxt = res.data.djdata.ywzqdwtxt ? res.data.djdata.ywzqdwtxt : '月';
							that.dqtimetxt = res.data.dqtimetxt;
							that.ywtime = res.data.ywtime;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			bindDateChange(e) {
				let that = this;
				let ywtime = e.detail.value;
				this.ywtime = ywtime;
				this.lxjscl('ywtime');
			},


			formSubmit: function(e) {
				let that = this;
				let pvalue = e.detail.value;
				let ywtime = that.ywtime;
				let ywzqnum = that.ywzqnum;
				let ywzqdw = that.ywzqdw;
				let ywzqdwtxt = that.ywzqdwtxt;
				let djid = that.djid;
				let ddtype = that.ddtype;


				if (!pvalue.yfjine || pvalue.yfjine == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入预付金额',
					});
					return false;
				}

				if (!that.lilv || that.lilv == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入利率',
					});
					return false;
				}

				if (!ywzqnum || ywzqnum == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入预付周期',
					});
					return false;
				}


				if (this.clicksta) return;
				this.clicksta = true;

				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'djid': djid,
					'yfjine': that.yfjine,
					'ywtime': that.ywtime,
					'ywzqnum': ywzqnum ? ywzqnum : '',
					'ywzqdw': ywzqdw ? ywzqdw : '',
					'ywzqdwtxt': ywzqdwtxt ? ywzqdwtxt : '',
				}
				this.$req.post('/v1/danju/rkdeditx41save', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							uni.setStorageSync('thupdpgllist',1);
							setTimeout(function() {
								uni.navigateTo({
									url: './editx42?djid=' + djid
								})
							}, 500)
						} else {
							that.clicksta = false;
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch(err => {

					})

			},



		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowxa {
		border-bottom: 1px solid #efefef;
		padding-bottom: 20upx;
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
		overflow: hidden;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstip {
		margin-bottom: 10upx;
		font-size: 24upx;
		color: #7F7F7F
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}

	.jinecon {
		position: relative;
		margin-top: 20upx
	}

	.jinecon .icon {
		position: absolute;
		left: 0;
		top: 10upx;
	}

	.jinecon .icon .iconfont {
		font-size: 30upx
	}

	.jinecon .inputss {
		font-size: 32upx;
		color: #000;
		padding-left: 35upx
	}

	.lxgzlist {}

	.lxgzlist .item {
		padding: 0 30upx;
		height: 100upx;
		line-height: 100upx;
		border-bottom: 1px solid #efefef;
		position: relative;
	}

	.lxgzlist .item .yxicon {
		position: absolute;
		right: 30upx;
		top: 0;
		color: #FFD427
	}

	.rowx6 {
		margin-top: 10upx;
		position: absolute;
		right: 0upx;
		top: 0
	}

	.rowx6 .tb {
		height: 70upx;
		line-height: 70upx;
		float: left
	}

	.rowx6 .wz {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 70upx;
		line-height: 70upx;
	}

	.rowx6 .srk {
		float: left;
		margin: 0 15upx
	}

	.rowx6 .srk .input2 {
		height: 70upx;
		line-height: 70upx;
		border: 1px solid #ddd;
		text-align: center;
		width: 120upx;
		border-radius: 15upx;
		background: #fafafa;
	}
	
	.clsbtnhuise{color:#ddd}
	
</style>