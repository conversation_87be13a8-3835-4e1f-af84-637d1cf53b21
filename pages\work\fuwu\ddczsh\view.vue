<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>【{{shdata.typetxt}}】-审核</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">


				<view class="maxcon">
					<!-- 	<view class="rowx">
						<view class="tit">审核类型</view>
						<view class="inpcon">
							<view class="inp">{{shdata.typetxt}}</view>
						</view>
						<view class="clear"></view> 
					</view> -->

					<view class="rowx" v-if="shdata.ordernum">
						<view class="tit"><text class="redst"></text>原单单据

						</view>
						<view class="inpcon">
							<view class="icon" @click="gotodjxq" :data-djid="shdata.djid"
								style="position: absolute;right:0;top:0;"><text class="iconfont icon-tiaobodan"
									style="color:#1677FF"></text></view>
							<view class="inp" @click="gotodjxq" :data-djid="shdata.djid" style="color:#1677FF;">
								{{shdata.ordernum}}
							</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" v-if="shdata.ordernum">
						<view class="tit"><text class="redst"></text>原单类型</view>
						<view class="inpcon">
							<view class="inp">
								<text class="ddtypeaa" :class="'ys'+djdata.ddtype">{{djdata.ddtypetxt}}</text>
							</view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx">
						<view class="tit">客户名称</view>
						<view class="inpcon">
							<view class="inp">{{shdata.khname}}</view>
						</view>
						<view class="clear"></view>
					</view>


					<view class="rowx">
						<view class="tit">申请时间</view>
						<view class="inpcon">
							<view class="inp">{{shdata.addtime}}</view>
						</view>
						<view class="clear"></view>
					</view>

					<!-- 等额本息还款 -->
					<block v-if="shdata.type==9">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp">{{djdata.hkfstxt}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>本期还款时间</view>
							<view class="inpcon">
								<view class="inp">{{hkdata.jhhktime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>本期应还金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#ff0000">{{hkdata.yhkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>本期已还金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{shdata.bcyhkjne}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>本次还款金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{shdata.hkjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
					</block>




					<!-- 先息后本还款 -->
					<block v-if="shdata.type==10">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>预付金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">￥{{djdata.yfjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总利息</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总还款金额</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp">{{djdata.hkfstxt}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>到期时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywdqtimetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>已还金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{shdata.bcyhkjne}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>本次还款金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{shdata.hkjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
					</block>

					<!-- 提前结清 -->
					<block v-if="shdata.type==7">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>预付金额</view>
							<view class="inpcon">
								<view class="inp">￥{{djdata.yfjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总利息</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总还款金额</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp">{{djdata.hkfstxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>到期时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywdqtimetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>已还金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{shdata.bcyhkjne}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>本次结清金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{shdata.hkjine}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="shdata.shfeiyong >=0 ">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>利润金额</view>
								<view class="inpcon">
									<view class="inp colorhs" style="color:#ff0000">￥{{shdata.shfeiyong}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-else>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>亏损金额</view>
								<view class="inpcon">
									<view class="inp colorhs" style="color:#4aac09">￥{{shdata.shfeiyong}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


					</block>




					<!-- 多笔还款 -->
					<block v-if="shdata.type==16">

						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp">{{djdata.hkfstxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>已还金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">￥{{shdata.sjhkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>待还款金额</view>
							<view class="inpcon">
								<view class="inp ">￥{{shdata.whkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>



					</block>




					<!-- 预付亏损 -->
					<block v-if="shdata.type==8">

						<view class="rowx">
							<view class="tit"><text class="redst"></text>预付金额</view>
							<view class="inpcon">
								<view class="inp">￥{{djdata.yfjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总利息</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>总还款金额</view>
							<view class="inpcon">
								<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>


						<view class="rowx">
							<view class="tit"><text class="redst"></text>已还金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">￥{{shdata.sjhkzjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>亏损金额</view>
							<view class="inpcon">
								<view class="inp colorhs" style="color:#4aac09">￥{{shdata.ksjine}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>还款方式</view>
							<view class="inpcon">
								<view class="inp">{{djdata.hkfstxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>到期时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywdqtimetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>

					</block>



					<!-- 续期 -->
					<block v-if="shdata.type==2">

						<block v-if="djdata.ddtype==4">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>预付金额</view>
								<view class="inpcon">
									<view class="inp">￥{{djdata.yfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>利率</view>
								<view class="inpcon">
									<view class="inp">{{djdata.lilv}}%</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">￥{{shdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp ">￥{{shdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>原到期时间</view>
							<view class="inpcon">
								<view class="inp">{{shdata.orywdqtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>续期周期</view>
							<view class="inpcon">
								<view class="inp">{{shdata.ywzqnum}} {{shdata.ywzqdwtxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>续期截止</view>
							<view class="inpcon">
								<view class="inp">{{shdata.ywdqtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<block v-if="shdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>续期费用合计</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">{{shdata.xqfwf}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="shdata.ddtype==4">

							<view class="rowx">
								<view class="tit"><text class="redst"></text>续期金额</view>
								<view class="inpcon">
									<view class="inp ">￥{{shdata.xqjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>续期利息</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">￥{{shdata.xqlixi}}</view>
								</view>
								<view class="clear"></view>
							</view>

						</block>


					</block>




					<block v-if="spdata">

						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品名称</view>
							<view class="inpcon">
								<view class="icon" @click="gotospxq" :data-spid="spdata.id"
									style="position: absolute;right:0;top:0;"><text class="iconfont icon-zanwushuju"
										style="color:#1677FF"></text></view>

								<view class="inp" @click="gotospxq" :data-spid="spdata.id" style="color:#1677FF">
									({{spdata.id}}){{spdata.title}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx" style="display: none;">
							<view class="tit"><text class="redst"></text>所属类别</view>
							<view class="inpcon">
								<view class="inp">{{spdata.sortname}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>{{spdata.wpzjtxt}}</view>
							<view class="inpcon">
								<view class="inp">
									{{shdata.ordanjia}} x {{shdata.orshuliang}} = <text style="margin-left:10upx">
										¥{{shdata.orzongjia}}</text>
								</view>
							</view>
							<view class="clear"></view>
						</view>


						<!-- 增加成本 -->
						<block v-if="shdata.type==15">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>增加成本</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">{{shdata.shfeiyong}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


						<!-- 赎回 -->
						<block v-if="shdata.type==1">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他成本</view>
								<view class="inpcon">
									<view class="inp">{{shdata.orcbfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>赎回类型</view>
								<view class="inpcon">
									<view class="inp">{{spdata.shleixing}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>赎回费用</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{shdata.shfeiyong}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<block v-if="shdata.isfrks==1 || shdata.isfrks==2 ">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>赎回利润</view>
									<view class="inpcon">
										<view class="inp" style="color:#ff0000">¥{{shdata.zfrjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
							<block v-if="shdata.isfrks==3">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>赎回亏损</view>
									<view class="inpcon">
										<view class="inp" style="color:#4aac09">¥{{shdata.zksjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
						</block>


						<!-- 寄卖转回收 -->
						<block v-if="shdata.type==4">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他成本</view>
								<view class="inpcon">
									<view class="inp">{{shdata.orcbfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" v-if="shdata.orsjjiage > 0">
								<view class="tit"><text class="redst"></text>原上架价格</view>
								<view class="inpcon">
									<view class="inp">{{shdata.orsjjiage}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>上架价格</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">{{shdata.sjjiage}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>



						<!-- 质押转卖 -->
						<block v-if="shdata.type==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他成本</view>
								<view class="inpcon">
									<view class="inp">{{shdata.orcbfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" v-if="shdata.orsjjiage > 0">
								<view class="tit"><text class="redst"></text>原上架价格</view>
								<view class="inpcon">
									<view class="inp">{{shdata.orsjjiage}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>上架价格</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">{{shdata.sjjiage}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


						<!-- 增资 -->
						<block v-if="shdata.type==5">

							<view class="rowx">
								<view class="tit"><text class="redst"></text>增资金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{shdata.zjzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>原服务费</view>
								<view class="inpcon">
									<view class="inp">¥{{shdata.orfwf}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>增资服务费</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{shdata.zzfwf}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<!-- 减资 -->
						<block v-if="shdata.type==6">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>减资金额</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{shdata.zjzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

					</block>



					<view class="bzcon" v-if="shdata.remark">{{shdata.remark}}</view>

					<block v-if="shdata.picturescarr">
						<view class="pjimgcon">
							<block v-for="(itemx,indexx) in shdata.picturescarr" :key="indexx">
								<view class='item'>
									<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
										:data-picturescarr="shdata.picturescarr" @tap='previewImagex'>
									</image>
								</view>
							</block>
							<view class="clear"></view>
						</view>
					</block>

				</view>




				<!-- 多笔还款 -->
				<block v-if="shdata.type==16">
					<view class="maxcon">
						<view class="vstit">本次还款明细</view>

						<view class="khjhcon" style="padding-left:70upx">
							<view class="hkjh-timeline">
								<view class="hkjh-timeline-content">

									<block v-for="(item,index) in shdata.dbhkdata" :key="index">
										<view class="item hkjh-timeline-item">
											<em class="yd-timeline-icon"></em>
											<view class="hkjine">
												<view>¥{{item.yhkzjine}}</view>
												<block v-if="item.bqsyhkjine > 0">
													<view class="yhdbc">
														<text class="yhtipm">已还 {{item.bqyhkjine}}</text>
														<text class="yhtipm">本次还款 <text
																class="bc">{{item.bqsyhkjine}}</text></text>
													</view>
												</block>
											</view>
											<view class="qbcon">
												<view class="t">{{item.xhtit}}</view>
												<view class="r">{{item.rq}}</view>
											</view>
										</view>
									</block>

								</view>
							</view>
						</view>

					</view>

					<view class="maxcon">
						<view class="rowx">
							<view class="inp ">本次还款：<text style="color:#ff0000">{{shdata.dbhkqs}}</text> 笔，合计：<text
									style="color:#ff0000">￥{{shdata.hkjine}}</text></view>
						</view>
					</view>
				</block>

				<!-- 寄卖转回收 -->
				<block v-if="shdata.type==4">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资</view>
							<block v-if="shdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="shdata.spid" :data-isofr="0"
									:data-rytype="8" data-rytypetxt="出资人" style="color:#333"><text
										class="iconfont icon-jia" style="margin-right:10upx;color:#1677FF"></text> 新增出资
								</view>
							</block>
							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>
											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">出资金额：<text
														:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.czjine}}</text>
												</view>
												<view class="czxm2" @click="czryedit" :data-spid="shdata.spid"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-isdpczzh="itemx.isdpczzh"
													:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
													v-if="shdata.status==2" :data-isofr="0">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="czrli">
								<view class="xx">
									<view class="czxm hj" style="width:100%;">已出资金额：<text>{{shdata.czjinehj}}</text>
										<text class="gx">,</text>
										未出资金额：<text class="hs">{{shdata.czjinehj2}}</text>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>

					</block>
				</block>




				<!-- 寄卖转回收 撤回-->
				<block v-if="shdata.type==13">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资明细</view>

							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">出资金额：<text
														:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.czjine}}</text>
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>
						<block v-if="shdata.status==2">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="czrli">
									<view class="xx" style="color:#ff0000;font-size:24upx">
										说明：审核通过后出资金额自动退回到出资人的可用出资金额中
									</view>
								</view>
							</view>
						</block>

					</block>
				</block>



				<!-- 赎回-->
				<block v-if="shdata.type==1 && shdata.czfrrydata">
					<block v-if="shdata.status==2 || shdata.status==1">
						<block v-if="shdata.isfrks==2">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资明细</view>

								<block v-if="shdata.czfrrydata">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
											<view class="czrli">
												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm">出资金额：<text
															:class="itemx.orczjine > 0 ? '' : ''">{{itemx.orczjine}}</text>
													</view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>
							<block v-if="shdata.status==2">
								<view class="maxcon" style="padding-bottom:10upx">
									<view class="czrli">
										<view class="xx" style="color:#ff0000;font-size:24upx">
											说明：审核通过后出资金额自动退回到出资人的可用出资金额中
										</view>
									</view>
								</view>
							</block>
						</block>


						<block v-if="shdata.isfrks==1">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
								<block v-if="shdata.status==2">
									<view class="more tadd" @click="addczry" :data-spid="shdata.spid" :data-isofr="1"
										:data-rytype="9" data-rytypetxt="分润人" style="color:#333"><text
											class="iconfont icon-jia" style="margin-right:10upx;color:#1677FF"></text>
										新增分润
									</view>
								</block>

								<block v-if="shdata.czfrrydata">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
											<view class="czrli">
												<block v-if="shdata.status==2 && itemx.isorzcr==2">
													<view class="del" @click="delczry" :data-czryid="itemx.id">
														删除
													</view>
												</block>
												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm" v-if="itemx.rytype==8">出资金额：<text
															:class="itemx.orczjine > 0 ? '' : ''">{{itemx.orczjine}}</text>
													</view>
													<view class="czxm">分润金额：<text
															:class="itemx.frjine > 0 ? '' : ''">{{itemx.frjine}}</text>
													</view>
													<view class="czxm2" @click="czryedit" :data-spid="shdata.id"
														:data-czryid="itemx.id" :data-czygid="itemx.ygid"
														:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
														:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
														:data-frjine="itemx.frjine" :data-isdpczzh="itemx.isdpczzh"
														:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
														v-if="shdata.status==2" :data-isofr="1">修改
													</view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>
							<view class="maxcon" style="padding-bottom:10upx">

								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">已分润金额：<text
												class="ysz">{{shdata.frjinehj}}</text> <text class="gx">,</text>
											未分润金额：<text class="hs">{{shdata.frjinehj2}}</text></view>
										<view class="clear"></view>
									</view>
								</view>

							</view>
						</block>



						<block v-if="shdata.isfrks==3">

							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 亏损</view>
								<block v-if="shdata.czfrrydata">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
											<view class="czrli">

												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm" v-if="itemx.rytype==8">出资金额：<text
															:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.orczjine}}</text>
													</view>
													<view class="czxm">亏损金额：<text
															:class="itemx.ksjine > 0 ? 'ysz3' : ''">{{itemx.ksjine}}</text>
													</view>
													<block v-if="shdata.status==2">
														<view class="zjjzbtn">
															<view class="btna bt3" @click="ksryedit"
																:data-rytype="itemx.rytype" :data-shczid="itemx.id">-亏损
															</view>
															<view class="clear"></view>
														</view>
													</block>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>


							<view class="maxcon" style="padding-bottom:10upx">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">亏损已分配：<text
												class="ysz">{{shdata.ksjinehj}}</text> <text class="gx">,</text>
											未分配(店铺亏损)：<text class="hs" style="color:#4aac09">{{shdata.ksjinehj2}}</text>
										</view>
										<view class="clear"></view>
									</view>
								</view>
							</view>

						</block>

					</block>
				</block>


				<!-- 质押增资 -->
				<block v-if="shdata.type==5">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资</view>
							<block v-if="shdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="shdata.spid" :data-isofr="0"
									:data-rytype="8" data-rytypetxt="出资人" style="color:#333"><text
										class="iconfont icon-jia" style="margin-right:10upx;color:#1677FF"></text>
									新增出资
								</view>
							</block>
							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2 && itemx.isorzcr==2">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>
											<view class="cname">{{itemx.ygname}}
												<!-- ({{itemx.rytypetxt}}) -->
												<text class="czjs" v-if="itemx.orczjine > 0"
													style="color:#333">（原出资:<text>￥{{itemx.orczjine}}</text>）</text>
											</view>
											<view class="xx">
												<view class="czxm">{{itemx.orczjine > 0 ? '本次出资' : '出资金额'}}：<text
														:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.czjine}}</text>
												</view>
												<view class="czxm">{{itemx.orczjine > 0 ? '本次分润' : '分润金额'}}：<text
														:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
												</view>
												<view class="czxm2" @click="czryedit" :data-spid="shdata.spid"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-isdpczzh="itemx.isdpczzh"
													:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
													v-if="shdata.status==2" :data-isofr="0">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
							<block v-if="shdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="0" :data-isofr="1" :data-rytype="9"
									data-rytypetxt="分润人" style="color:#333"><text class="iconfont icon-jia"
										style="margin-right:10upx;color:#1677FF"></text>
									新增分润
								</view>
							</block>
							<block v-if="shdata.czfrry2data.length > 0">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrry2data" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2 && itemx.rytype==9">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>

											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">分润金额：<text
														:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
												</view>
												<view class="czxm2" @click="czryedit" :data-spid="0"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
													:data-isdpczzh="itemx.isdpczzh" :data-isorzcr="itemx.isorzcr"
													v-if="shdata.status==2" :data-isofr="1">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>

								</view>

							</block>


						</view>


						<view class="maxcon" style="padding-bottom:10upx">
							<view class="czrli">
								<view class="xx">
									<view class="czxm hj" style="width:100%;">已出资金额：<text>{{shdata.czjinehj}}</text>
										<text class="gx">,</text>
										未出资金额：<text class="hs">{{shdata.czjinehj2}}</text>
									</view>
									<view class="clear"></view>
								</view>
							</view>
							<block v-if="czfrisfr==1">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">已分润金额：<text>{{shdata.frjinehj}}</text>
											<text class="gx">,</text>
											未分润金额：<text class="hs">{{shdata.frjinehj2}}</text>
										</view>
										<view class="clear"></view>
									</view>
								</view>
							</block>
						</view>

					</block>
				</block>

				<!-- 质押减资 -->
				<block v-if="shdata.type==6">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 减资</view>
							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2 && itemx.isorzcr==2">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>
											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">出资金额：<text
														:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.czjine}}</text>
												</view>
												<view class="czxm">减资金额：<text
														:class="itemx.jzjine > 0 ? 'ysz' : ''">{{itemx.jzjine}}</text>
												</view>
												<block v-if="shdata.status==2">
													<view class="zjjzbtn">
														<view class="btna bt2" @click="jzryedit"
															:data-shczid="itemx.id">-减资</view>
														<view class="clear"></view>
													</view>
												</block>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>




						<view class="maxcon" style="padding-bottom:10upx">
							<view class="czrli">
								<view class="xx">
									<view class="czxm hj" style="width:100%;">已减资金额：<text
											class="ysz">{{shdata.jzjinehj}}</text> <text class="gx">,</text>
										未减资金额：<text class="hs">{{shdata.jzjinehj2}}</text></view>
									<view class="clear"></view>
								</view>
							</view>
						</view>

					</block>
				</block>



				<!-- 信用预付续期 -->
				<block v-if="shdata.type==2 && shdata.ddtype==4">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资记录</view>
							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2 && itemx.isorzcr==2">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>
											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">出资金额：<text
														:class="itemx.orczjine > 0 ? 'ysz' : ''">{{itemx.orczjine}}</text>
												</view>
												<view class="czxm">分润金额：<text
														:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
												</view>
												<view class="czxm2" @click="czryedit" :data-spid="shdata.spid"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-isdpczzh="itemx.isdpczzh"
													:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
													v-if="shdata.status==2" :data-isofr="1">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
							<block v-if="shdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="0" :data-isofr="1" :data-rytype="9"
									data-rytypetxt="分润人" style="color:#333"><text class="iconfont icon-jia"
										style="margin-right:10upx;color:#1677FF"></text>
									新增分润
								</view>
							</block>
							<block v-if="shdata.czfrry2data.length > 0">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrry2data" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2 && itemx.rytype==9">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>

											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">分润金额：<text
														:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
												</view>
												<view class="czxm2" @click="czryedit" :data-spid="0"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
													:data-isdpczzh="itemx.isdpczzh" :data-isorzcr="itemx.isorzcr"
													v-if="shdata.status==2" :data-isofr="1">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>

								</view>

							</block>


						</view>


						<view class="maxcon" style="padding-bottom:10upx">
							<block v-if="czfrisfr==1">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">已分润金额：<text
												class="ysz">{{shdata.frjinehj}}</text> <text class="gx">,</text>
											未分润金额：<text class="hs">{{shdata.frjinehj2}}</text></view>
										<view class="clear"></view>
									</view>
								</view>
							</block>
						</view>

					</block>
				</block>


				<!-- 质押暂存续期 -->
				<block v-if="shdata.type==2 && shdata.ddtype==3">

					<view class="splist">

						<block v-for="(item,index) in splist" :key="index">

							<view class="item ">

								<view class="wpcon">
									<view class="inf1">
										<view class="spbh">编号：{{item.id}}</view>

										<view class="tf1">
											<view class="tx">
												<image :src="staticsfile + item.smallpic"></image>
											</view>
											<view class="xx">
												<view class="n0"><text class="ddtype"
														:class="'ys'+item.ddtype">{{item.ddtypename}}</text>
													{{item.title}}
												</view>
												<view class="n2">
													质押金额：¥{{item.danjia}} x {{item.shuliang}} = ¥<text
														style="color:#ff0000">{{item.zongjia}}</text>
												</view>
												<view class="n2">
													服务费用：¥{{item.fwfjinenow}}</text>
												</view>

											</view>
											<view class="clear"></view>
										</view>
									</view>

									<view class="inf2">
										<view class="pdczcon">
											<view class="tx1">续期服务费：</view>
											<view class="tx2">¥{{item.xqfwf}}</view>
											<view class="tx1">其他费用：</view>
											<view class="tx2">¥{{item.xqjine}}</view>
											<view class="clear"></view>
										</view>
									</view>
								</view>

								<block v-if="shdata.status==2 || shdata.status==1">

									<view class="maxcon" style="padding-bottom:10upx">
										<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润
										</view>

										<block v-if="item.czfrrydata">
											<view class="czry4info">
												<block v-for="(itemx,indexx) in item.czfrrydata" :key="indexx">
													<view class="czrli">
														<block v-if="item.status==2 && itemx.isorzcr==2">
															<view class="del" @click="delczry" :data-czryid="itemx.id">
																删除
															</view>
														</block>
														<view class="cname">{{itemx.ygname}}
															<text class="czjs">({{itemx.rytypetxt}})</text>
														</view>
														<view class="xx">
															<view class="czxm">出资金额：<text
																	:class="itemx.orczjine > 0 ? 'ysz' : ''">{{itemx.orczjine}}</text>
															</view>
															<view class="czxm">分润金额：<text
																	:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
															</view>
															<view class="czxm2" @click="czryedit" :data-spid="item.id"
																:data-czryid="itemx.id" :data-czygid="itemx.ygid"
																:data-czygname="itemx.ygname"
																:data-czjine="itemx.czjine" :data-rytype="itemx.rytype"
																:data-rytypetxt="itemx.rytypetxt"
																:data-frjine="itemx.frjine"
																:data-isdpczzh="itemx.isdpczzh"
																:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
																v-if="shdata.status==2" :data-isofr="1">修改
															</view>
															<view class="clear"></view>
														</view>
													</view>
												</block>
											</view>
										</block>
									</view>


									<view class="maxcon" style="padding-bottom:10upx">
										<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
										<block v-if="shdata.status==2">
											<view class="more tadd" @click="addczry" :data-spid="item.id"
												:data-isofr="1" :data-rytype="9" data-rytypetxt="分润人"
												style="color:#333"><text class="iconfont icon-jia"
													style="margin-right:10upx;color:#1677FF"></text> 新增分润
											</view>
										</block>
										<block v-if="item.czfrry2data">
											<view class="czry4info">
												<block v-for="(itemx,indexx) in item.czfrry2data" :key="indexx">
													<view class="czrli">
														<block v-if="shdata.status==2 && itemx.rytype==9">
															<view class="del" @click="delczry" :data-czryid="itemx.id">
																删除
															</view>
														</block>

														<view class="cname">{{itemx.ygname}}
															<text class="czjs">({{itemx.rytypetxt}})</text>
														</view>
														<view class="xx">
															<view class="czxm">分润金额：<text
																	:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
															</view>
															<view class="czxm2" @click="czryedit" :data-spid="item.id"
																:data-czryid="itemx.id" :data-czygid="itemx.ygid"
																:data-czygname="itemx.ygname"
																:data-czjine="itemx.czjine" :data-rytype="itemx.rytype"
																:data-rytypetxt="itemx.rytypetxt"
																:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
																:data-isdpczzh="itemx.isdpczzh"
																:data-isorzcr="itemx.isorzcr" v-if="shdata.status==2"
																:data-isofr="1">修改
															</view>
															<view class="clear"></view>
														</view>
													</view>
												</block>

											</view>

										</block>


									</view>




									<view class="maxcon" style="padding-bottom:10upx">

										<block v-if="item.zjz==1">
											<view class="czrli">
												<view class="xx">
													<view class="czxm hj" style="width:100%;">已增资金额：<text
															class="ysz">{{item.czjinehj}}</text> <text
															class="gx">,</text>
														未增资金额：<text class="hs">{{item.czjinehj2}}</text></view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
										<block v-if="item.zjz==2">
											<view class="czrli">
												<view class="xx">
													<view class="czxm hj" style="width:100%;">已减资金额：<text
															class="ysz">{{item.jzjinehj}}</text> <text
															class="gx">,</text>
														未减资金额：<text class="hs">{{item.jzjinehj2}}</text></view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
										<block v-if="czfrisfr==1">
											<view class="czrli">
												<view class="xx">
													<view class="czxm hj" style="width:100%;">已分润金额：<text
															class="ysz">{{item.frjinehj}}</text> <text
															class="gx">,</text>
														未分润金额：<text class="hs">{{item.frjinehj2}}</text></view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>


								</block>


							</view>






						</block>
					</view>
					
					
					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
						<block v-if="shdata.czfrry3data">
							<view class="czry4info">
								<block v-for="(itemx,indexx) in shdata.czfrry3data" :key="indexx">
									<view class="czrli">
					
										<view class="cname">{{itemx.ygname}}
											<text class="czjs">({{itemx.rytypetxt}})</text>
										</view>
										<view class="xx">
											<view class="czxm">分润金额：<text
													:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
											</view>
											<view class="czxm2" @click="czryedit" :data-spid="0"
												:data-czryid="itemx.id" :data-czygid="itemx.ygid"
												:data-czygname="itemx.ygname"
												:data-czjine="itemx.czjine" :data-rytype="itemx.rytype"
												:data-rytypetxt="itemx.rytypetxt"
												:data-frjine="itemx.frjine" :data-frbl="itemx.frbl"
												:data-isdpczzh="itemx.isdpczzh"
												:data-isorzcr="itemx.isorzcr" v-if="shdata.status==2"
												:data-isofr="1">修改
											</view>
											<view class="clear"></view>
										</view>
									</view>
								</block>
					
							</view>
					
						</block>
					
					
					</view>
					
					<view class="maxcon" style="padding-bottom:10upx">
				
							<view class="czrli">
								<view class="xx">
									<view class="czxm hj" style="width:100%;">已分润总金额：<text
											class="ysz">{{shdata.zfrjinehj}}</text> <text
											class="gx">,</text>
										未分润总金额：<text class="hs">{{shdata.zwfenrun}}</text></view>
									<view class="clear"></view>
								</view>
							</view>
					
					</view>
					

				</block>



				<!-- 亏损 -->
				<block v-if="shdata.type==8">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 亏损</view>
							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">

											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm" v-if="itemx.rytype==8">出资金额：<text
														:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.orczjine}}</text>
												</view>
												<view class="czxm">亏损金额：<text
														:class="itemx.ksjine > 0 ? 'ysz3' : ''">{{itemx.ksjine}}</text>
												</view>
												<block v-if="shdata.status==2">
													<view class="zjjzbtn">
														<view class="btna bt3" @click="ksryedit"
															:data-rytype="itemx.rytype" :data-shczid="itemx.id">-亏损
														</view>
														<view class="clear"></view>
													</view>
												</block>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>


						<view class="maxcon" style="padding-bottom:10upx">
							<view class="czrli">
								<view class="xx">
									<view class="czxm hj" style="width:100%;">亏损已分配：<text
											class="ysz">{{shdata.ksjinehj}}</text> <text class="gx">,</text>
										未分配(店铺亏损)：<text class="hs" style="color:#4aac09">{{shdata.ksjinehj2}}</text>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>

					</block>
				</block>


				<!-- 增加成本 -->
				<block v-if="shdata.type==15">
					<block v-if="shdata.status==2 || shdata.status==1">

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资</view>
							<block v-if="shdata.status==2">
								<view class="more tadd" @click="addczry" :data-spid="shdata.spid" :data-isofr="0"
									:data-rytype="8" data-rytypetxt="出资人" style="color:#333"><text
										class="iconfont icon-jia" style="margin-right:10upx;color:#1677FF"></text>
									新增出资
								</view>
							</block>
							<block v-if="shdata.czfrrydata">
								<view class="czry4info">
									<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
										<view class="czrli">
											<block v-if="shdata.status==2 && itemx.isorzcr==2">
												<view class="del" @click="delczry" :data-czryid="itemx.id">删除</view>
											</block>
											<view class="cname">{{itemx.ygname}}
												<!-- ({{itemx.rytypetxt}}) -->
												<text class="czjs" v-if="itemx.orczjine > 0"
													style="color:#333">（原出资:<text>￥{{itemx.orczjine}}</text>）</text>
											</view>
											<view class="xx">
												<view class="czxm">{{itemx.orczjine > 0 ? '本次出资' : '出资金额'}}：<text
														:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.czjine}}</text>
												</view>

												<view class="czxm2" @click="czryedit" :data-spid="shdata.spid"
													:data-czryid="itemx.id" :data-czygid="itemx.ygid"
													:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
													:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
													:data-frjine="itemx.frjine" :data-isdpczzh="itemx.isdpczzh"
													:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
													v-if="shdata.status==2" :data-isofr="0">修改
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
							</block>
						</view>

						<view class="maxcon" style="padding-bottom:10upx">
							<view class="czrli">
								<view class="xx">
									<view class="czxm hj" style="width:100%;">已出资金额：<text>{{shdata.czjinehj}}</text>
										<text class="gx">,</text>
										未出资金额：<text class="hs">{{shdata.czjinehj2}}</text>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>

					</block>
				</block>




				<!-- 等额本息结清 -->
				<block v-if="shdata.type==7 && shdata.czfrrydata">
					<block v-if="shdata.status==2 || shdata.status==1">
						<block v-if="shdata.shfeiyong==0">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资明细</view>

								<block v-if="shdata.czfrrydata">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
											<view class="czrli">
												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm">出资金额：<text
															:class="itemx.orczjine > 0 ? '' : ''">{{itemx.orczjine}}</text>
													</view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>
							<block v-if="shdata.status==2">
								<view class="maxcon" style="padding-bottom:10upx">
									<view class="czrli">
										<view class="xx" style="color:#ff0000;font-size:24upx">
											说明：审核通过后出资金额自动退回到出资人的可用出资金额中
										</view>
									</view>
								</view>
							</block>
						</block>


						<block v-if="shdata.shfeiyong > 0">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
								<block v-if="shdata.status==2">
									<view class="more tadd" @click="addczry" :data-spid="shdata.spid" :data-isofr="1"
										:data-rytype="9" data-rytypetxt="分润人" style="color:#333"><text
											class="iconfont icon-jia" style="margin-right:10upx;color:#1677FF"></text>
										新增分润
									</view>
								</block>

								<block v-if="shdata.czfrrydata">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
											<view class="czrli">
												<block v-if="shdata.status==2 && itemx.isorzcr==2">
													<view class="del" @click="delczry" :data-czryid="itemx.id">
														删除
													</view>
												</block>
												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm" v-if="itemx.rytype==8">出资金额：<text
															:class="itemx.orczjine > 0 ? '' : ''">{{itemx.orczjine}}</text>
													</view>
													<view class="czxm">分润金额：<text
															:class="itemx.frjine > 0 ? '' : ''">{{itemx.frjine}}</text>
													</view>
													<view class="czxm2" @click="czryedit" :data-spid="shdata.id"
														:data-czryid="itemx.id" :data-czygid="itemx.ygid"
														:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
														:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt"
														:data-frjine="itemx.frjine" :data-isdpczzh="itemx.isdpczzh"
														:data-isorzcr="itemx.isorzcr" :data-frbl="itemx.frbl"
														v-if="shdata.status==2" :data-isofr="1">修改
													</view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>
							<view class="maxcon" style="padding-bottom:10upx">

								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">已分润金额：<text
												class="ysz">{{shdata.frjinehj}}</text> <text class="gx">,</text>
											未分润金额：<text class="hs">{{shdata.frjinehj2}}</text></view>
										<view class="clear"></view>
									</view>
								</view>

							</view>
						</block>



						<block v-if="shdata.shfeiyong < 0">

							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 亏损</view>
								<block v-if="shdata.czfrrydata">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in shdata.czfrrydata" :key="indexx">
											<view class="czrli">

												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm" v-if="itemx.rytype==8">出资金额：<text
															:class="itemx.czjine > 0 ? 'ysz' : ''">{{itemx.orczjine}}</text>
													</view>
													<view class="czxm">亏损金额：<text
															:class="itemx.ksjine > 0 ? 'ysz3' : ''">{{itemx.ksjine}}</text>
													</view>
													<block v-if="shdata.status==2">
														<view class="zjjzbtn">
															<view class="btna bt3" @click="ksryedit"
																:data-rytype="itemx.rytype" :data-shczid="itemx.id">-亏损
															</view>
															<view class="clear"></view>
														</view>
													</block>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>


							<view class="maxcon" style="padding-bottom:10upx">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">亏损已分配：<text
												class="ysz">{{shdata.ksjinehj}}</text> <text class="gx">,</text>
											未分配(店铺亏损)：<text class="hs" style="color:#4aac09">{{shdata.ksjinehj2}}</text>
										</view>
										<view class="clear"></view>
									</view>
								</view>
							</view>

						</block>

					</block>
				</block>



				<block v-if="shdata.status!=2">
					<view class="maxcon">
						<view class="vstit">审核信息</view>
						<view class="rowx">
							<view class="tit">审核人员：</view>
							<view class="inpcon">
								<view class="inp">{{shdata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核时间：</view>
							<view class="inpcon">
								<view class="inp">{{shdata.shtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核状态：</view>
							<view class="inpcon">
								<view class="inp">
									<view class="stats " :class="'stacolor'+shdata.status">{{shdata.statusname}}
									</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="bzcon" v-if="shdata.bhsm">{{shdata.bhsm}}</view>
					</view>
				</block>


			</view>



			<block v-if="shdata.status==2">
				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="jjtgsh" style="background:#ddd;">
								<text>拒绝</text>
							</button>
						</view>
						<view class="bomsfright">
							<button class="bomsubbtn" @click="tgsh">
								<text>审核通过</text>
							</button>
						</view>
						<view class="clear"></view>
					</view>
				</view>
			</block>



		</view>

		<view style="height:120upx"></view>


		<tn-modal v-model="jjtgshow" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">拒绝理由</view>
				<view class="bhsmcon">
					<textarea name="bhsm" class="beizhu" v-model="bhsm" placeholder="请输入拒绝的理由"
						style="height:350upx"></textarea>
				</view>

				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" @click="jjsh">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>



		<tn-popup v-model="czryeditshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">
				<view class="rowx">
					<view class="tit"><text class="redst"></text>{{addrytypetxt}}</view>
					<view class="inpcon">
						<view class="inp">{{thczygname}}
							<text v-if="thczygid > 0"
								style="color:#888;margin-left:10upx;display: none">({{thczygid}})</text>
						</view>
					</view>
					<view class="clear"></view>
				</view>

				<block v-if="isofr==0">
					<view class="rowx">
						<view class="tit"><text class="redst"></text>可用出资</view>
						<view class="inpcon">
							<view class="inp" style="color:#1677FF">{{thygczkyjine}} </view>
						</view>
						<view class="clear"></view>
					</view>

					<view class="rowx">
						<view class="tit"><text class="redst"></text>出资金额</view>
						<view class="inpcon">
							<view class="inp"><input class="input" type="number" name="title" v-model="thczjine"
									placeholder="请输入出资金额" placeholder-class="placeholder" /></view>
						</view>
						<view class="clear"></view>
					</view>
				</block>

				<block v-if="czfrisfr==1">
					<view class="rowx">
						<view class="tit"><text class="redst"></text>分润金额</view>
						<view class="inpcon">
							<view class="inp">
								<input class="input" type="number" name="frjine" v-model="thfrjine" placeholder=" "
									placeholder-class="placeholder" style="width:120upx;" />
							</view>
						</view>
						<view class="clear"></view>
						<view class="rowxff">
							<view class="wz">比例</view>
							<view class="srk"><input class="input2" type="number" name="frbl" v-model="thfrbl"
									placeholder="请输入" placeholder-class="placeholder" @input="frjscl()" />
							</view>
							<view class="tb" style="color:#1677FF;z-index:200;">%</view>
							<view class="clear"></view>
						</view>
					</view>
					<view class="smtxt">说明：分润比例只用做辅助计算,不保存!</view>
				</block>

				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="czrysettj">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>

		<tn-popup v-model="jzryeditshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">
				<view class="rowx">
					<view class="tit"><text class="redst"></text>减资人员</view>
					<view class="inpcon">
						<view class="inp">{{thjzygname}} </view>
					</view>
					<view class="clear"></view>
				</view>

				<view class="rowx">
					<view class="tit"><text class="redst"></text>出资金额</view>
					<view class="inpcon">
						<view class="inp" style="color:#1677FF">{{thkjzjine}} </view>
					</view>
					<view class="clear"></view>
				</view>

				<view class="rowx">
					<view class="tit"><text class="redst"></text>减资金额</view>
					<view class="inpcon">
						<view class="inp"><input class="input" type="number" name="thjzjine" v-model="thjzjine"
								placeholder="请输入减资金额" placeholder-class="placeholder" /></view>
					</view>
					<view class="clear"></view>
				</view>

				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="jzrysettj">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>

		<tn-popup v-model="ksryeditshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">
				<view class="rowx">
					<view class="tit"><text class="redst"></text>{{thksrytypetxt}}</view>
					<view class="inpcon">
						<view class="inp">{{thksygname}} </view>
					</view>
					<view class="clear"></view>
				</view>

				<view class="rowx" v-if="thksorczjine > 0">
					<view class="tit"><text class="redst"></text>出资金额</view>
					<view class="inpcon">
						<view class="inp" style="color:#1677FF">{{thksorczjine}} </view>
					</view>
					<view class="clear"></view>
				</view>

				<view class="rowx">
					<view class="tit"><text class="redst"></text>亏损金额</view>
					<view class="inpcon">
						<view class="inp"><input class="input" type="number" name="thksjine" v-model="thksjine"
								placeholder="请输入亏损金额" placeholder-class="placeholder" /></view>
					</view>
					<view class="clear"></view>
				</view>

				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="ksrysettj">
							<text>确认提交</text>
						</button>
					</view>
				</view>

			</view>

		</tn-popup>



		<tn-popup v-model="gzselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">选择分润规则</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="lrgzlist">
					<block v-if="frgzdata">
						<block v-for="(item,index) in frgzdata" :key="index">
							<view class="item" @click="selthfrgz" :data-gzid="item.id" :data-feilv="item.feilv">
								<view class="tit">{{item.title}}</view>
							</view>

						</block>
					</block>
				</view>
			</scroll-view>
		</tn-popup>




		<tn-popup v-model="isfybdshow" mode="center" :borderRadius="20" :closeBtn="true">
			<view class="fybdcon">
				<view class="icon"><text class="iconfont icon-tishi3"></text></view>
				<view class="tiptxtv">费用有变化,请注意审核!</view>
				<view class="qrbtn">
					<view class="btn" @click="closefybdtip">确认</view>
				</view>
			</view>
		</tn-popup>



	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				shid: '',
				shdata: '',
				spdata: '',
				bhsm: '',
				jjtgshow: false,

				gzselshow: false,
				thfrjine: 0,
				frgzdata: '',
				addrytype: 0,
				addrytypetxt: '人员姓名',

				thczjine: 0,
				thfrbl: 0,
				czfrspid: 0,
				czfrisfr: 1,
				isofr: 0,
				thczygid: 0,
				thczygname: '',
				thygczkyjine: '',

				czryeditshow: false,

				isdpczzh: 0,

				djdata: '',
				hkdata: '',

				jzryeditshow: false,
				thjzygname: '',
				thygczjine: 0,
				thjzjine: '',
				thkjzjine: '',

				ksryeditshow: false,
				thksygname: '',
				thksrytypetxt: '',
				thksjine: '',
				thksorczjine: '',


				isorzcr: 0,
				splist: '',

				isfybdshowed: 0,
				isfybdshow: false,
				isfybd: 0,

			}
		},

		onLoad(options) {
			let that = this;
			let shid = options.shid ? options.shid : 0;
			this.shid = shid;
			if (shid) {
				this.loadData();
			}
		},
		onShow() {
			this.clicksta = false;
			this.thczygid = 0;
			let thczygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			// let thygczkyjine = uni.getStorageSync('thygczkyjine');
			if (thczygid) {
				console.log(thczygid);
				this.thczygid = thczygid;
				this.loadygcz();
				this.thczygname = thygname;
				// this.thygczkyjine = thygczkyjine;
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
				uni.setStorageSync('thygczkyjine', '');
				// this.czryeditshow = true;
			}

			if (uni.getStorageSync('isdpczzh') == 1) {
				this.isdpczzh = 1;
				this.loadygcz();
				uni.setStorageSync('isdpczzh', '');
			}
		},

		methods: {
			gotospxq(e) {
				let spid = e.currentTarget.dataset.spid;
				uni.navigateTo({
					url: '/pages/work/ckgl/ckshop/spview?spid=' + spid
				})
			},
			gotodjxq(e) {
				let djid = e.currentTarget.dataset.djid;
				uni.navigateTo({
					url: '/pages/work/rukudan/view?djid=' + djid
				})
			},

			jjtgsh() {
				this.jjtgshow = true;
			},
			closefybdtip() {
				this.isfybdshow = false;
			},

			loadData() {
				let that = this;
				let shid = that.shid;
				let da = {
					shid: shid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/ddczshview', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.shdata = res.data.shdata;
							that.spdata = res.data.spdata;
							that.bhsm = res.data.shdata.bhsm;
							that.frgzdata = res.data.frgzdata;
							that.czfrisfr = res.data.czfrisfr;
							that.djdata = res.data.djdata;
							that.hkdata = res.data.hkdata;
							that.splist = res.data.splist;
							that.isfybd = res.data.isfybd;

							if (res.data.isfybd == 1 && res.data.shdata.status == 2 && that.isfybdshowed == 0) {
								that.isfybdshow = true;
								that.isfybdshowed = 1;
							}

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},


			tgsh(e) {
				let that = this;
				uni.showModal({
					title: '审核确认',
					content: '通过后不可撤销！确认通过审核吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'shid': that.shid,
								'sta': 1,
								'bhsm': ''
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/fuwu/ddczshsave', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.setStorageSync('thuplist', 1);
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},


			jjsh(e) {
				let that = this;

				if (!that.bhsm) {
					uni.showToast({
						icon: 'none',
						title: '请输入拒绝理由',
					});
					return false;
				}

				let da = {
					'shid': that.shid,
					'sta': 3,
					'bhsm': that.bhsm ? that.bhsm : ''
				}
				uni.showLoading({
					title: ''
				})
				that.$req.post('/v1/fuwu/ddczshsave', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thuplist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
							that.jjtgshow = false;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})

			},




			addczry(e) {
				let isfr = this.czfrisfr;
				let spid = e.currentTarget.dataset.spid;
				let isofr = e.currentTarget.dataset.isofr;
				let addrytype = e.currentTarget.dataset.rytype;
				let addrytypetxt = e.currentTarget.dataset.rytypetxt;
				this.isdpczzh = 0;
				console.log(isofr);

				this.addrytype = addrytype;
				this.addrytypetxt = addrytypetxt;
				this.isofr = isofr;
				this.czfrspid = spid;
				this.thczjine = 0;
				this.thfrbl = 0;
				this.thfrjine = 0;
				let yytitle = "选择出资人";
				if (isfr == 1) {
					yytitle = "选择出资/分润人";
				}
				if (isofr == 1) {
					yytitle = "选择分润人";
				}
				uni.navigateTo({
					url: '/pages/selyg/index?stype=7&xtitle=' + yytitle
				})
			},

			czryedit(e) {

				let that = this;
				let czfrspid = e.currentTarget.dataset.spid;
				let czryid = e.currentTarget.dataset.czryid;
				let czygid = e.currentTarget.dataset.czygid;
				let czygname = e.currentTarget.dataset.czygname;
				let czjine = e.currentTarget.dataset.czjine;
				let frbl = e.currentTarget.dataset.frbl;
				let frjine = e.currentTarget.dataset.frjine;
				let isofr = e.currentTarget.dataset.isofr;
				let addrytype = e.currentTarget.dataset.rytype;
				let addrytypetxt = e.currentTarget.dataset.rytypetxt;
				let isdpczzh = e.currentTarget.dataset.isdpczzh;
				let isorzcr = e.currentTarget.dataset.isorzcr;
				console.log(isdpczzh);
				// this.czryeditshow = true;
				this.thczjine = czjine;
				this.thfrbl = 0;
				this.addrytype = addrytype;
				this.addrytypetxt = addrytypetxt;
				this.isdpczzh = isdpczzh;
				this.isorzcr = isorzcr;
				this.isofr = isofr;
				this.thfrjine = frjine;
				this.czfrspid = czfrspid;
				this.thczygid = czygid;
				this.thczygname = czygname;
				this.loadygcz();

			},
			delczry(e) {
				let that = this;
				let czryid = e.currentTarget.dataset.czryid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'czryid': czryid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/fuwu/shdelczry', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},

			czrysettj() {
				let that = this;
				let shid = that.shid;
				let thczygid = that.thczygid;
				let czfrspid = that.czfrspid;
				let thczjine = that.thczjine;
				let thfrbl = that.thfrbl;
				let thfrjine = that.thfrjine;
				let isofr = that.isofr;
				let addrytype = that.addrytype;
				let isdpczzh = that.isdpczzh;
				let isorzcr = that.isorzcr;
				let da = {
					shid: shid,
					spid: czfrspid,
					czryid: thczygid,
					czjine: thczjine ? thczjine : 0,
					frbl: thfrbl ? thfrbl : 0,
					frjine: thfrjine ? thfrjine : 0,
					isofr: isofr ? isofr : 0,
					addrytype: addrytype ? addrytype : 0,
					isdpczzh: isdpczzh ? isdpczzh : 0,
					isorzcr: isorzcr ? isorzcr : 0,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/shsetczry', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {

							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.czryeditshow = false;
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},

			sellxgz() {
				this.gzselshow = true;
			},
			selthfrgz(e) {
				let feilv = e.currentTarget.dataset.feilv;
				this.thfrbl = feilv;
				this.gzselshow = false;
				this.frjscl();
			},
			loadygcz() {
				let that = this;
				let csfrjine = that.thfrjine;
				that.thczygname = 0;
				that.thygczkyjine = 0;
				that.thczjine = 0;
				that.thfrjine = 0;

				let da = {
					shid: this.shid,
					spid: this.czfrspid,
					czryid: this.thczygid,
					isdpczzh: this.isdpczzh,
					rytype: this.addrytype,
					csfrjine: csfrjine ? csfrjine : 0,
				}

				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/jsygcz', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.thczygid = res.data.thczygid;
							that.thczygname = res.data.thygname;
							that.thygczkyjine = res.data.thygczkyjine;
							that.thczjine = res.data.thczjine;
							that.thfrjine = res.data.thfrjine;
							that.czryeditshow = true;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},

			frjscl() {
				let that = this;
				let da = {
					shid: this.shid,
					spid: this.czfrspid ? this.czfrspid : 0,
					frbl: this.thfrbl ? this.thfrbl : 0,
				}

				this.$req.post('/v1/fuwu/frjscl', da)
					.then(res => {

						console.log(res);
						if (res.errcode == 0) {
							that.thfrbl = res.data.frbl;
							that.thfrjine = res.data.frjine;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},

			jzryedit(e) {
				let that = this;
				let shczid = e.currentTarget.dataset.shczid;
				let czfrspid = e.currentTarget.dataset.spid;

				this.shczid = shczid;
				this.czfrspid = czfrspid;
				let da = {
					shid: this.shid,
					spid: czfrspid ? czfrspid : 0,
					shczid: shczid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/jsygjz', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.thjzygid = res.data.thjzygid;
							that.thjzygname = res.data.thjzygname;
							that.thkjzjine = res.data.thkjzjine;
							that.thjzjine = res.data.thjzjine;
							that.jzryeditshow = true;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},


			jzrysettj() {
				let that = this;
				let shid = that.shid;
				let da = {
					shid: shid,
					shczid: this.shczid,
					jzjine: that.thjzjine,
					spid: that.czfrspid ? that.czfrspid : 0,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/shsetjzry', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {

							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.jzryeditshow = false;
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},


			ksryedit(e) {
				let that = this;
				let shczid = e.currentTarget.dataset.shczid;
				let czfrspid = e.currentTarget.dataset.spid;
				let addrytype = e.currentTarget.dataset.rytype;

				this.addrytype = addrytype;
				this.shczid = shczid;
				this.czfrspid = czfrspid;
				let da = {
					shid: this.shid,
					addrytype: addrytype ? addrytype : 0,
					spid: czfrspid ? czfrspid : 0,
					shczid: shczid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/jsygks', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.thksygid = res.data.thksygid;
							that.thksygname = res.data.thksygname;
							that.thksrytypetxt = res.data.thksrytypetxt;
							that.thksorczjine = res.data.thksorczjine;
							that.thksjine = res.data.thksjine;
							that.ksryeditshow = true;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},


			ksrysettj() {
				let that = this;
				let shid = that.shid;
				let da = {
					shid: shid,
					addrytype: this.addrytype,
					shczid: this.shczid,
					ksjine: that.thksjine,
					spid: that.czfrspid ? that.czfrspid : 0,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/shsetksry', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {

							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.ksryeditshow = false;
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},



		}
	}
</script>

<style lang="scss">
	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}

	.bzcon {
		margin-top: 20upx;
	}

	.bhsmcon {
		margin: 10upx 0 30upx 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .vstit .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}

	.sqbdcon {
		margin: 0upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.pjimgcon {
		margin-top: 20upx;
	}

	.pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 115px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #333;
	}

	.rowx:last-child {
		border-bottom: 0;
	}




	.stats.stacolor1 {
		color: #43b058;
	}

	.stats.stacolor2 {
		color: #FA6400;
	}

	.stats.stacolor3 {
		color: #888;
	}

	.zjjzbtn {
		position: absolute;
		right: 0upx;
		bottom: 20upx;
	}

	.zjjzbtn .btna {
		float: left;
		margin-left: 40upx;
		height: 60upx;
		line-height: 60upx;
		width: 120upx;
		border: 1px solid #ff0000;
		color: #ff0000;
		border-radius: 100px;
		text-align: center;
	}

	.zjjzbtn .btna.bt2 {
		border: 1px solid #4aac09;
		color: #4aac09
	}

	.zjjzbtn .btna.bt3 {
		border: 1px solid #4aac09;
		color: #4aac09
	}

	.splist {}

	.splist .item {
		margin-bottom: 20upx;
		border-radius: 20upx 20upx;
		border-top: 5px solid #FFD427
	}

	.splist .item .wpcon {
		margin-bottom: 20upx;
		background: #fff;
		border-radius: 20upx;
		position: relative;
	}

	.splist .item .spbh {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 20upx;
		color: #1677FF
	}

	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .status {}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 130upx;
		height: 130upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: left;
		margin-left: 20upx;
		width: calc(100% - 155upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
		overflow: hidden;
		font-size: 24upx
	}

	.splist .item .inf2 {
		background: #fff;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		font-size: 24upx;
		padding: 15upx 28upx;
		color: #333;
		position: relative;
		line-height: 40upx;
		border-top: 1px solid #efefef;
		padding: 28upx
	}

	.splist .item .inf2 .scpdtime {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 24upx;
	}

	.splist .item .inf2 .bzbtn {
		position: absolute;
		right: 28upx;
		bottom: 28upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fafafa;
		border-radius: 10rpx;
		text-align: center;
		color: #333;
		border: 1px solid #eee
	}


	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}

	.splist .item .status.sta1 {
		color: #43b058;
	}

	.splist .item .status.sta2 {
		color: #FF2828;
	}

	.pdczcon {
		height: 40upx;
		line-height: 40upx;
		position: relative;
	}

	.pdczcon .tx1 {
		float: left
	}

	.pdczcon .tx2 {
		float: left;
		margin-right: 20upx;
		color: #1677FF;
		font-size: 28upx
	}

	.pdczcon .tx3 {
		float: right;
	}

	.pdczcon .tx2 .input {
		border: 1px solid #FFD427;
		text-align: center;
		height: 50upx;
		line-height: 50upx;
		width: 150upx;
		font-size: 24upx;
		border-radius: 10upx;
	}

	.pdczcon .tx3 .setbtn {
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 10rpx;
		text-align: center;
		color: #333
	}




	.fybdcon {
		padding: 32upx;
		width: 580upx;
		text-align: center;
	}

	.fybdcon .icon {}

	.fybdcon .icon .iconfont {
		font-size: 55upx;
		color: #ff0000;
	}

	.fybdcon .tiptxtv {
		text-align: center;
		margin-bottom: 20upx;
		font-size: 32upx;
		padding: 40upx 0
	}

	.fybdcon .qrbtn {}

	.fybdcon .btn {
		height: 80upx;
		line-height: 80upx;
		padding: 0 30upx;
		background: #FFD427;
		border-radius: 120upx;
		color: #333
	}
</style>