<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >出资账户转出</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
   
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
										
											
										<view class="maxcon" style="padding:10upx 28upx">
											<view class="rowx">
												<view class="tit"><text class="redst"></text>店铺名称</view>
												<view class="inpcon">
												   <view class="inp">{{dpdata.title}}</view>
												</view>
												<view class="clear"></view>
											</view>
										</view>
										<view class="maxcon" style="padding:10upx 28upx">
											<view class="rowx">
												<view class="tit"><text class="redst"></text>资金余额</view>
												<view class="inpcon">
												   <view class="inp">{{dpdata.czkyjine}}</view>
												</view>
												<view class="clear"></view>
											</view>
										</view>
										<view class="maxcon" style="padding:10upx 28upx">
											<view class="rowx">
												<view class="tit"><text class="redst"></text>转出类型</view>
												<view class="inpcon">
												   <view class="inp">{{zcdtxt}}</view>
												</view>
												<view class="clear"></view>
											</view>
										</view>
										
										<view class="maxcon">
												
												<view class="vstit">转出金额</view>
												
												<view class="jinecon">
													<view class="icon"><text class="iconfont icon-fl-renminbi"></text></view>
													<input  type="digit"  name='jine'  placeholder='在此输入转出金额' maxlength='60' class="inputss" />
												</view>
												
										</view>
										<view class="maxcon">
												<view class="vstit">备注说明</view>
												<view class="bzcon">
													  <textarea name="remark" class="beizhu"></textarea>
												</view>
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx"> 
																<block v-for="(item,index) in img_url_ok" :key="index">
																   <view class='item'  >
																	   <view class="del" @tap="deleteImg"   :data-index="index" ><i class="iconfont icon-shanchu2"></i></view>
																	   <image  :src="staticsfile+item" :data-src='staticsfile + item ' @tap='previewImage' ></image>
																   </view>
																</block>
															   <view class='item' v-if="img_url_ok.length <= 8" >
																   <image @tap="chooseimage"  src='/static/images/uppicaddx.png'></image>
															   </view>
															   <view class="clear"></view>
													 </view>
												</view>		
												 
												 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  			<view class="tn-flex tn-footerfixed">
				  			  <view class="tn-flex-1 justify-content-item tn-text-center">
				  				<button class="bomsubbtn"  form-type="submit">
				  				  <text>确认提交</text>
				  				</button>
				  			  </view>
				  			</view>
				  </form>
				  
				
				
				
		
			

	
	</view>
  
	<view style="height:120upx"></view>
    
	
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 dpdata:'',
			 zctype:0,
			 zctypetxt:'',
			 status:0,
			 jine:0,
			 img_url: [],
			 img_url_ok:[],
			 dpid:0,
			 zctypedata:[],
			 zctypearr:[],
			 zcdtxt:'',
		  }
	},

	onLoad(options) {
		let that=this;
		let dpid=options.dpid ? options.dpid : 0;
		 this.dpid=dpid;
		this.loadData();
	},
	onShow() {
	
	},

    methods: {
					   
		loadData(){
				  let that=this;
				  let da={
					  dpid:that.dpid
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/dpzz/zcdpzczj', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res); 
				 			if(res.errcode==0){
				 		        that.dpdata=res.data.dpdata;
				 		        that.zcdtxt=res.data.zcdtxt;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
		
			chooseimage(){
								   let that = this;
								   let img_url_ok=that.img_url_ok;
								   uni.chooseImage({
									   count: 9, //默认9
									   sizeType: ['original', 'compressed'],
									   success: (resx) => {
										  const tempFilePaths = resx.tempFilePaths;
										  
										
												  for(let i = 0;i < tempFilePaths.length; i++) {
														  let da={
															  filePath:tempFilePaths[i],
															  name: 'file'
														  } 
														  uni.showLoading({title: '上传中...'})
														  this.$req.upload('/v1/upload/upfile', da)
																  .then(res => {
																	uni.hideLoading();
																	// res = JSON.parse(res);
																	if(res.errcode==0){
																		img_url_ok.push(res.data.fname);
																		that.img_url_ok=img_url_ok;
																		
																		uni.showToast({
																			icon: 'none',
																			title: res.msg
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
														  
														  })
												}
										   
									   }
								   });
							   
							},
							previewImage: function (e) {
									  let that = this;
									  let src = e.currentTarget.dataset.src;
									  let img_url_ok=that.img_url_ok;
									  let imgarr=[];
									  img_url_ok.forEach(function(item,index,arr){
										  imgarr[index] = that.staticsfile+item;
									  });
									  wx.previewImage({
										  current: src,
										  urls: imgarr
									  });
							},
							deleteImg: function (e) {
								let that = this;
								let index = e.currentTarget.dataset.index;
								let img_url_ok = that.img_url_ok;
								img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
								that.img_url_ok=img_url_ok;
							},
		
		
		
			formSubmit: function(e) {
						let that=this;
					   var formdata = e.detail.value
						 
							
									 let pvalue = e.detail.value;
									 
										if(!pvalue.jine || pvalue.jine <= 0){
										  uni.showToast({
											icon: 'none',
											title: '请输入转出金额',
										  });
										  return false;
										}
									  
								       uni.showModal({
													title: '',
													content: '以上信息已确认无误并提交吗？',
													success: function(e) {
														//点击确定
														if (e.confirm) {
												                   uni.showLoading({title: '处理中...'})
																   let da={
																		'dpid': that.dpid,
																		'jine': pvalue.jine,
																		'remark': pvalue.remark ? pvalue.remark : '',
																		'pictures': that.img_url_ok ? that.img_url_ok  : '' ,
																   }
																   that.$req.post('/v1/dpzz/zcdpzczjsave', da)
																  .then(res => {
																	uni.hideLoading();
																	console.log(res);
																	if(res.errcode==0){
																		uni.showToast({
																			icon: 'none',
																			title: res.msg,
																			success() {
																				setTimeout(function(){
																					uni.navigateBack()
																				},1000)
																			}
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
																	
																  })
																  
															
														}
													}
								})	  
									  
									  
									  
									  
									  
									  
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.toplinex{border-top:1px solid #fff}



.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{margin-bottom:20upx}


.sqbdcon{margin:32upx;}


.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.rowxmttp{margin-top:30upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:200}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}

.jinecon{position: relative;margin:40upx 0}
.jinecon .icon{position: absolute;left:0;top:10upx;}
.jinecon .icon .iconfont{font-size:40upx}
.jinecon .inputss{font-size:40upx;color:#000;padding-left:40upx}

 .rowx{
 	position: relative;
 	border-bottom:1px solid #efefef;
 }
 .rowx .icon{position: absolute;right:2upx;top:0;height:90upx;line-height:90upx}
 .rowx .icon .iconfont{color:#ddd}
 .rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
 .rowx .tit .redst{color:#ff0000;margin-right:2px;}
 .rowx .tit .redst2{color:#fff;margin-right:2px;}
 .rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 95px);font-size:28upx;height:90upx;line-height:90upx;}
 .rowx .inpcon.hs{color:#888}
 .rowx .inp{}
 .rowx .inp .input{height:90upx;line-height:90upx;}
 .rowx:last-child{border-bottom:0;}
</style>

