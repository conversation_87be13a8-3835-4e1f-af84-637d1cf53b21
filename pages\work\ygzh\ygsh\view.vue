<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >员工申请详情</text>
					</view>
					<view slot="right" >
					</view>
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
   
				  			<view class="sqbdcon">
										
										<view class="maxcon">
											<view class="ttinfo">
												
												<view class="inf1"  >
												
													<view class="tf1">
														<view class="tx"  ><image :src="staticsfile + ygdata.avatar"></image></view>
														<view class="xx">
															<view class="name">{{ygdata.realname}}</view>
															<view class="tel"><text>员工号:{{ygdata.userid}}</text></view>
														</view>
														<view class="clear"></view>
													</view>	
												</view>
											</view>
										</view>
								
										
										
										
										
										
										<view class="maxcon">
												
												<view class="vstit">基本信息</view>
												
												<view class="rowx" @click="showModal3" data-zdtype="2">
													<view class="icon"><text class="iconfont  icon-bianji" v-if="ygdata.status==3"></text></view>
													<view class="tit">姓名</view>
													<view class="inpcon"><view class="inp">{{ygdata.realname}}</view></view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx" @click="showModal3" data-zdtype="3">
													<view class="icon"><text class="iconfont  icon-bianji" v-if="ygdata.status==3"></text></view>
													<view class="tit">电话号码</view>
													<view class="inpcon"><view class="inp">{{ygdata.lxtel}}</view></view>
													<view class="clear"></view>
												</view>
												
												
												<view class="rowx" @click="showModal3" data-zdtype="4">
													<view class="icon"><text class="iconfont  icon-bianji" v-if="ygdata.status==3"></text></view>
													<view class="tit">身份证</view>
													<view class="inpcon"><view class="inp">{{ygdata.sfzhao ? ygdata.sfzhao : '设置身份证号'}}</view></view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx" @click="seljiaose">
													<view class="icon"><text class="iconfont  icon-bianji" v-if="ygdata.status==3"></text></view>
													<view class="tit">员工角色</view>
													<view class="inpcon"><view class="inp">{{ygdata.jiaosetxt ? ygdata.jiaosetxt : '请选择角色'}}</view></view>
													<view class="clear"></view>
												</view>
												
												
												<view class="rowx" >
												
													<view class="tit">所属店铺</view>
													<view class="inpcon"><view class="inp">{{ygdata.mdname}}</view></view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx">
										
													<view class="tit">申请时间</view>
													<view class="inpcon"><view class="inp">{{ygdata.addtime}}</view></view>
													<view class="clear"></view>
												</view>
												
												<block v-if="ygdata.rztimetxt">
													<view class="rowx">
														<view class="tit">入职时间</view>
														<view class="inpcon"><view class="inp">{{ygdata.rztimetxt}}</view></view>
														<view class="clear"></view>
													</view>
												</block>
												
										</view>	
											
										<view class="maxcon">
											<view class="rowxmttp">
												 <view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
												 
												 <view class="uprzimgcon" >
															 <view class="zzx sf1" >
																 <view class="con">
																	 <view class="vt1"  @click="uprzimg1" >	  
																			<block v-if="rzimg1" >
																				<image :src='staticsfile + rzimg1'></image>
																			</block>
																			<block v-else>
																				<image src="/static/images/rzupimg1.png"></image>
																			</block>
																	 </view>
																	  <view class="vt2">身份证头像面</view>
																	 <view class="clear"></view>
																 </view>
															 </view>
															 <view class="zzx sf2"   >
																 <view class="con"  >
																	 <view class="sf2" @click="uprzimg2">	 
																			<block v-if="rzimg2" >
																				<image :src='staticsfile + rzimg2'></image>
																			</block>
																			<block v-else>
																				<image src="/static/images/rzupimg2.png"></image>
																			</block>	
																	  </view>	
																	  <view class="vt2">身份证国徽面</view>
																 </view>
															 </view>
														<view class="clear"></view>								
												 </view>
												 
												 
											</view>		 
										</view>		 
				  					
				  					
									
									
				  			</view>	
				  			
				  	
				
				  
				
				
				
		
			

	
	</view>
  
	<view style="height:20upx"></view>
    
	
	<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
	  <view class="custom-modal-content"> 
	    <view class="">
	      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">请输入{{zdtxt}}</view>
	      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;text-align: center;">
	        <input :placeholder="'请输入'+zdtxt" v-model="zdvalue" name="input" placeholder-style="color:#AAAAAA" maxlength="50" ></input>
	      </view>
	    </view>
	    <view class="tn-flex-1 justify-content-item  tn-text-center">
	      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="upzdvalue" >
	        <text style="color:#333">保 存</text>
	      </tn-button>
	    </view>
	  </view>
	</tn-modal>
	
	
	<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
		<view class="popuptit">选择店铺</view>
	    <scroll-view scroll-y="true" style="height: 800rpx;">
	          <view class="seldplist">
	              <block v-for="(item,index) in dpdata" :key="index">
					  
					  <view class="item" @click="selthdp" :data-dpid="item.id"  :data-dpname="item.title"  >
						    <view class="xzbtn"  >选择</view>
					  		<view class="tx"><image :src="staticsfile + item.logo"></image></view>
					  		<view class="xx">
					  			<view class="name">{{item.title}}</view>
					  			<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
					  		</view>
					  		<view class="clear"></view>
					  </view>
					  
			      </block>		  
	          </view>
	        </scroll-view>
	</tn-popup>
	
	
	<tn-popup v-model="jsselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
		<view class="popuptit">请选择员工角色</view>
	    <scroll-view scroll-y="true" style="height: 800rpx;">
	          <view class="jslist">
				 
						<checkbox-group @change="jssetChange">
								<block  v-for="(item, index) in jiaosedata" :key="index">
									<view class="item">
										<label >
											<view class="icon">
												<checkbox :value="item.id"  color="#FFD427" />
											</view>
											<view class="vstxt">{{item.title}}</view>
											<view class="clear"></view>
										</label>
									</view>	
								</block>
						 </checkbox-group>
						 
						 <view style="height:160upx"></view>
						 <view class="tn-flex tn-footerfixed">
						   <view class="tn-flex-1 justify-content-item tn-text-center">
							   <view class="bomsfleft">
								  <button class="bomsubbtn"  @click="closejiaose"  style="background:#ddd;" >
									  <text>关闭</text>
								  </button>
							   </view>
						 	   <view class="bomsfright">
						 		  <button class="bomsubbtn" @click="qrzdysxm">
						 		    <text>确认选择</text>
						 		  </button>
						 	   </view> 	  
						 	   <view class="clear"></view>	
						   </view>
						 </view>
				
	          </view>
	        </scroll-view>
	</tn-popup>
	
	<block v-if="ygdata.status==3">
		<view class="tn-flex tn-footerfixed">
			<view class="tn-flex-1 justify-content-item tn-text-center">
			   <view class="bt1" style="float:left;width:49%;" >
				   <button class="bomsubbtn" @click="shcz" :data-czsta="4" style="background:#ddd ;" >
					 <text >未通过审核</text>
				   </button>
			   </view>
			   <view class="bt2" style="float:right;width:49%;">
				   <button class="bomsubbtn"   @click="shcz" :data-czsta="1" >
					 <text >确认建档</text>
				   </button>
			   </view>
			   <view class="clear"></view>
			</view>
		  </view>
	  
		  <view style="height:120upx"></view>
	</block>
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 ygdata:'',
			 status:0,
			 multiArray: [],
			 objectDiqudata: [],
			 multiIndex: [0, 0, 0],
			 img_url: [],
			 img_url_ok:[],
			 show3:false,
			 zdvalue: '',
			 zdtxt: '',
			 zdtype: '',
			 rzimg1:'',
			 rzimg2:'',
			 jsselshow:false,
			 dpselshow:false,
			 dpdata:'',
			 thsjstemp:'',
			 jiaosedata:[],
		  }
	},

	onLoad(options) {
		let that=this;
		let ygid=options.ygid ? options.ygid : 0;
		this.ygid=ygid;
	},
	onShow() {
		this.loadData();
	},

    methods: {
		        seljiaose(){
					if(this.ygdata.status!=3){
					   return false;
					}
					this.jsselshow=true;
		        },
				closejiaose(){
					this.jsselshow=false;
				},
				jssetChange(e){
					let value=e.detail.value;
				    this.thsjstemp=value;
				},
				qrzdysxm(){
					let zdvalue=this.thsjstemp;
					if(!zdvalue || zdvalue.length==0){
						uni.showToast({
							icon: 'none',
							title: '请选择角色',
						});
					}
					
					zdvalue=zdvalue.join(',');
					this.zdtype=12;
					this.zdvalue=zdvalue;
					this.upzdvalue();
				    this.jsselshow=false;
				},
			    seldianpu(){
					
					if(this.ygdata.status!=3){
					   return false;
					}
			    	this.dpselshow=true;
			    },
			   selthdp(e){
				   
				    if(this.ygdata.status!=3){
					   return false;
				    }
				   
					let dpid=e.currentTarget.dataset.dpid;
					this.zdtype=11;
					this.zdvalue=dpid;
					this.upzdvalue();
					this.dpselshow=false;
			   },
			
		
					   
		loadData(){
				  let that=this;
				  let ygid=that.ygid; 
				  let da={
				 	ygid:ygid
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/ygzh/ygset', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 		        that.ygdata=res.data.ygdata;
				 		        that.dpdata=res.data.dpdata;
				 		        that.jiaosedata=res.data.jiaosedata;
								that.rzimg1=res.data.ygdata.sfzimg1 ? res.data.ygdata.sfzimg1 : '';
								that.rzimg2=res.data.ygdata.sfzimg2 ? res.data.ygdata.sfzimg2 : '';
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			
		gotoygglurl(url){
			uni.navigateTo({
				url:url+'?ygid='+this.ygid
			})
		},
		
		
		
			setsta(e){
				let that=this; 
				let da = {
					'ygid': that.ygid
				}
				uni.showLoading({title: ''})
				that.$req.post('/v1/ygzh/setsta', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thupyglist', 1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
											that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
				})
				
				
			},
			
			previewImagex: function (e) {
					let that = this;
					let src = e.currentTarget.dataset.src;
					console.log(src);
					let imgarr=[src];
					uni.previewImage({
						current: src,
						urls: imgarr
					});
			 },

		     showModal3(e) {
		     		console.log(e);  
					
					if(this.ygdata.status!=3){
					   return false;
					}
					
		     		let that=this;
		     		let dataset=e.currentTarget.dataset;
		     		let zdtype=dataset.zdtype;
		     		if(zdtype==2){
		     			that.zdtype=2;
		     			that.zdtxt='姓名';
		     			that.zdvalue=that.ygdata.realname;
		     		}
		     		if(zdtype==3){
		     			that.zdtype=3;
		     			that.zdtxt='电话号码';
		     			that.zdvalue=that.ygdata.lxtel;
		     		}
		     		if(zdtype==4){
		     			that.zdtype=4;
		     			that.zdtxt='身份证号';
		     			that.zdvalue=that.ygdata.sfzhao;
		     		}
		     		console.log(zdtype,that.zdvalue);
		       this.openModal3()
		     },
		     // 打开模态框
		     openModal3() {
		       this.show3 = true
		     },
		
		 
		      upzdvalue(){
		      		  	let that=this;
		      			let zdtype=that.zdtype;
		      			let zdvalue=that.zdvalue;
		      			uni.showLoading({title: '处理中...'})
		      			let da={
		      				  'zdtype': zdtype,
		      				  'zdvalue': zdvalue,
		      				  'uygid': that.ygid,
		      			}
		      			this.$req.post('/v1/ygzh/upgrinfo', da)
		      			        .then(res => {
		      					    uni.hideLoading();
		      						if(res.errcode==0){
									    	uni.setStorageSync('thupyglist',1);
		      								uni.showToast({
		      									icon: 'none',
		      									title: res.msg,
		      									success() {
		      										setTimeout(function(){
		      											 that.loadData();
		      											 that.show3 = false;
		      										},1000)
		      									}
		      								});
		      						}else{
		      							uni.showModal({
		      								content: res.msg,
		      								showCancel: false
		      							})
		      						}
		      						
		      			        })
		      			        .catch(err => {
		      						
		      			        })
		      			
		      },
		 
		 uprzimg1(){
		 		   let that = this;
				   
				   if(this.ygdata.status!=3){
				      return false;
				   }
				   
		 		   uni.chooseImage({
		 			   count: 1, //默认9
		 			   sizeType: ['original', 'compressed'],
		 			   success: (resx) => {
		 				  const tempFilePaths = resx.tempFilePaths;
		 								  let da={
		 									  filePath:tempFilePaths[0],
		 									  name: 'file'
		 								  } 
		 								  uni.showLoading({title: '上传中...'})
		 								  this.$req.upload('/v1/upload/upfile', da)
		 										  .then(res => {
		 											uni.hideLoading();
		 											// res = JSON.parse(res);
		 											console.log(res);
		 											if(res.errcode==0){
		 												that.rzimg1=res.data.fname;
		 												
		 												that.zdtype=8;
		 												that.zdvalue=res.data.fname;
		 												that.upzdvalue();
		 												
		 												uni.showToast({
		 													icon: 'none',
		 													title: res.msg
		 												});
		 											}else{
		 												uni.showModal({
		 													content: res.msg,
		 													showCancel: false
		 												})
		 											}
		 								  
		 								  })
		 			   }
		 		   });
		 },
		 	
		 uprzimg2(){
		 			   let that = this;
					   
					   if(this.ygdata.status!=3){
					      return false;
					   }
					   
		 			   uni.chooseImage({
		 				   count: 1, //默认9
		 				   sizeType: ['original', 'compressed'],
		 				   success: (resx) => {
		 					  const tempFilePaths = resx.tempFilePaths;
		 									  let da={
		 										  filePath:tempFilePaths[0],
		 										  name: 'file'
		 									  } 
		 									  uni.showLoading({title: '上传中...'})
		 									  this.$req.upload('/v1/upload/upfile', da)
		 											  .then(res => {
		 												uni.hideLoading();
		 												// res = JSON.parse(res);
		 												console.log(res);
		 												if(res.errcode==0){
		 													that.rzimg2=res.data.fname;
		 													
		 													that.zdtype=9;
		 													that.zdvalue=res.data.fname;
		 													that.upzdvalue();
		 													
		 													uni.showToast({
		 														icon: 'none',
		 														title: res.msg
		 													});
		 												}else{
		 													uni.showModal({
		 														content: res.msg,
		 														showCancel: false
		 													})
		 												}
		 									  
		 									  })
		 				   }
		 			   });
		 },		  
		 
			
			shcz(e){
				let that=this;
				let czsta = e.currentTarget.dataset.czsta;
				let ygid = that.ygid;
				let cztiptxt='';
					if(czsta==1){cztiptxt='确认通过审核建档吗？';}
					if(czsta==4){cztiptxt='确认未通过审核吗？';}	
						uni.showModal({
						title: '',
						content: cztiptxt,
						success: function(e) {
						
							if (e.confirm) {
								let da = {
									'ygid': ygid,
									'sta': czsta
								}
								uni.showLoading({title: ''})
								that.$req.post('/v1/ygzh/setshsta', da)
									.then(res => {
										uni.hideLoading();
										if (res.errcode == 0) {
											uni.setStorageSync('thupyglist', 1);
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
																uni.setStorageSync('thupygshlist',1);
													setTimeout(function() {
															uni.navigateBack()
													}, 1000);
												}
											});
										} else {
											uni.showModal({
												content: res.msg,
												showCancel: false,
											})
										}
									})
							}
						}
					})
				
			},	   
	
    }
  }
</script>

<style lang="scss" >



.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .dptit{text-align: center;}



.ttinfo{position: relative;}
.ttinfo .status{position: absolute;right:0;top:0}
.ttinfo .status .aa{float:left;margin-left:10upx;line-height:50upx;}
.ttinfo .inf1{}
.ttinfo .inf1 .tx{float:left}
.ttinfo .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
.ttinfo .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;}
.ttinfo .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.ttinfo .inf1 .tel{color: #7F7F7F;font-size: 24rpx;margin-top:10upx}
.ttinfo .inf1 .tel text{margin-right:30upx}


.inf2{padding-top:20upx;}
.inf2 .vtm{float:left;width:33.33%;font-size:28upx}
.inf2 .vtm .tt{color: #7F7F7F;font-size:24upx}
.inf2 .vtm .vv{color: #333333;margin-top:15upx}


.sqbdcon{margin:32upx;}
.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
	border-bottom:1px solid #F0F0F0;
}
.rowx .icon{position: absolute;right:0;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#333}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{color: #7F7F7F;}
.rowx:last-child{border-bottom:0;}



.rowxmttp{margn-top:30upx}
.imgconmt{}
.imgconmt .item{float:left;margin-right:20upx}
.imgconmt image{width: 240upx;height:160upx;border-radius:10upx;}

.ifwdh{}
.ifwdh .tjmul {}
.ifwdh .tjmul .mli {float:left;width:25%;text-align: center;}
.ifwdh .tjmul .mli .icon{position: relative;width:60upx;height:60upx;margin:0 auto;}
.ifwdh .tjmul .mli .icon image {width:60upx;height:60upx;display: block;margin:0 auto;}
.ifwdh .tjmul .tit {margin-top:20upx;font-size: 24upx;color:#333}
.ifwdh .tjmul.smx .mli{margin-bottom:28upx;}



.uprzimgcon{margin-top:25upx}
.uprzimgcon .zzx{background: #EBEBEB;border-radius:10upx;width:48%;text-align: center;}
.uprzimgcon .zzx.sf1{float:left;}
.uprzimgcon .zzx.sf2{float:right;}
.uprzimgcon .zzx .vt1{font-size: 36rpx;}
.uprzimgcon .zzx .vt2{font-size: 24rpx;color: #7F7F7F;margin-top:25upx}
.uprzimgcon .con{padding:20upx}
.uprzimgcon image{width: 100%;height:180upx;display: block;border-radius:10upx;}
 
 
.jslist{} 
.jslist .item{padding:0 30upx;border-bottom:1px solid #efefef;height:100upx;line-height:100upx} 
.jslist .item .icon{float:left} 
.jslist .item .vstxt{float:left;mragin-left:20upx} 
 
</style>

