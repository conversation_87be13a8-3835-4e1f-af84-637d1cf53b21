<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >企业认证</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
	<block v-if="rzsta==1">
		
		<view class="sqbdcon">
			
					<view class="maxcon">
						    <view class="rzicon"><text class="iconfont icon-yirenzheng"></text></view>
							<view class="vstit">认证信息</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>企业认证状态</view>
								<view class="inpcon">
								   <view class="inp" style="color: #00aa00;">{{sjrzdata.rzstaname}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>认证通过时间</view>
								<view class="inpcon">
								    <view class="inp">{{sjrzdata.rztime}}</view>
								</view>
								<view class="clear"></view>
							</view>
					</view>	
					
					<view class="maxcon">
							<view class="vstit">企业信息</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>企业认证名称</view>
								<view class="inpcon">
								   <view class="inp">{{sjrzdata.title}}</view>
								</view>
								<view class="clear"></view>
							</view>
							
							<view class="rowx">
								<view class="tit"><text class="redst"></text>企业信用代码</view>
								<view class="inpcon">
								    <view class="inp">{{sjrzdata.xydaima}}</view>
								</view>
								<view class="clear"></view>
							</view>
							
							<view class="rowx">
								<view class="tit"><text class="redst"></text>企业法人姓名</view>
								<view class="inpcon">
									<view class="inp">{{sjrzdata.frname}}</view>
								</view>
								<view class="clear"></view>
							</view>
							
							<view class="rowx">
								<view class="tit"><text class="redst"></text>法人身份证号</view>
								<view class="inpcon">
									<view class="inp">{{sjrzdata.frsfzhao}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="uprzimgcon">
													 <view class="zz1" >
														 <view class="con" style="background:#fff;padding:0">
																<image :src='staticsfile + sjrzdata.rzimg1'></image>
														 </view>
													 </view>
													 <view class="zz2"   >
														 <view class="con" style="background:#fff;padding:0">
																<image :src='staticsfile + sjrzdata.rzimg2'></image>
														 </view>
													 </view>
													 <view class="clear"></view>
							</view>
					</view>	
			
				
		</view>	
		
		
		
	</block>
	<block v-else>	
	
		<block v-if="rzdata && rzdata.status==2">
			  <view class="sqbdcon">
				
				  <view class="maxcon ddshcon">
					  <view class="icon"><image src="../../../static/images/rzshicon.png" mode="widthFix"></image></view>
					  <view class="sm">提交成功正在审核</view>
					  <view class="fhbtn">
						  <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="gotoback" >
							<text style="color:#333">返 回</text>
						  </tn-button>
					  </view>
				  </view>
			   </view>
		</block>
		<block v-else>
		
			<view class="" >
					  <form @submit="formSubmit" >
							<view class="sqbdcon">
								         <block v-if="!rzdata">
										     <view class="sqtip">*为了保障您的店铺安全，请先完成企业认证</view>
											 <view class="maxcon" style="padding:28upx 0;font-size:24upx;pointer-events: none;"   >
											 	<tn-steps mode="number" :list="steplist" activeColor="#FFD427" :current='1'    ></tn-steps>
											 </view>
										 </block>
										 
										<block v-if="rzdata && rzdata.status==3">
											<view class="maxcon">
												<view class="wtgsmcon">
													<view class="tt1"><text class="iconfont icon-tishi5"></text> 认证审核未通过</view>
													<view class="tt2" v-if="rzdata.wtgsm">{{rzdata.wtgsm}}</view>
												</view>	
											</view>
										</block>
									
										<view class="maxcon">
											 <view class="vstit">证件上传</view>
											 <view class="more">
												 <text class="t1">下载授权书</text>
												 <text class="iconfont icon-xiazai2"></text>
												 <text class="t3" @click="gotosqsm">查看示例</text>
											 </view>
											 
											 <view class="uprzimgcon">
												 <view class="zz1"  @click="uprzimg1">
													 <view class="con">
														<block v-if="rzimg1" >
															<image :src='staticsfile + rzimg1'></image>
														</block>
														<block v-else>
															<image src="/static/images/rzupimg1.jpg"></image>
														</block>	  
													 </view>
												 </view>
												 <view class="zz2"   @click="uprzimg2">
													 <view class="con"  >
														<block v-if="rzimg2" >
															<image :src='staticsfile + rzimg2'></image>
														</block>
														<block v-else>
															<image src="/static/images/rzupimg2.jpg"></image>
														</block>	  
													 </view>
												 </view>
												 <view class="clear"></view>
											 </view>
											 
										</view>
										
										<view class="maxcon">
												<view class="vstit">企业信息</view>
												<view class="rowx">
													<view class="tit"><text class="redst"></text>企业名称</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="qyname"  v-model="rzdata.qyname" placeholder="请输入企业名称" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx">
													<view class="tit"><text class="redst"></text>社会信用代码</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="xydaima" v-model="rzdata.xydaima"  placeholder="请输入统一社会信用代码" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx">
													<view class="tit"><text class="redst"></text>法人姓名</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="frname" v-model="rzdata.frname"  placeholder="请输入法人姓名" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
												<view class="rowx">
													<view class="tit"><text class="redst"></text>法人身份证号</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="frsfzhao" v-model="rzdata.frsfzhao"  placeholder="请输入法人身份证号" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
										</view>	
										<view class="maxcon">
											<view class="vstit" style="margin-bottom:20upx">备注说明</view>
											<view class="bzcon">
												  <textarea name="beizhu" class="beizhu" v-model="rzdata.beizhu"></textarea>
											</view>
										</view>		 
									
									
							</view>	
							
								<view class="tn-flex tn-footerfixed">
								  <view class="tn-flex-1 justify-content-item tn-text-center">
									<button class="bomsubbtn"  form-type="submit">
									  <text>确认提交</text>
									</button>
								  </view>
								</view>
					  </form>
		
		</view>
		
		  </block>
	</block>  
	
	
	<view style="height:120upx"></view>
    
	
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 sjrzdata:'',
			 rzdata:'',
			 rzsta:0,
			 status:0,
			
			 steplist: [{
			             name: '填写资料',
						 icon: 'edit-write',
						 selectIcon: 'edit-write'
			           },
			           {
			             name: '正在审核',
						 icon: 'warning',
						 selectIcon: 'warning'
			           },
			           {
			             name: '审核通过',
						 icon: 'warning',
						 selectIcon: 'warning'
			           }
			         ],
					 
			  rzimg1:'',
			  rzimg2:'',
			  
		  }
	},

	onLoad(options) {
		let that=this;
		this.loadData();
	},

    methods: {
		gotoback(){
			uni.navigateBack();
		},
		loadData(){
				  let that=this;
				  let da={  
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/sjgl/qyrz', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(111,res);
				 			if(res.errcode==0){
								let rzdata=res.data.rzdata;
				 		        that.rzsta=res.data.rzsta;
				 		        that.rzdata=res.data.rzdata;
				 		        that.sjrzdata=res.data.sjrzdata;
								if(rzdata){
									that.rzimg1=rzdata.rzimg1;
									that.rzimg2=rzdata.rzimg2;
								}
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
		gotosqsm(){
			uni.navigateTo({
				url:'/pages/danye/index?id=5'
			})
		},
				
		uprzimg1(){
				   let that = this;
				   uni.chooseImage({
					   count: 1, //默认9
					   sizeType: ['original', 'compressed'],
					   success: (resx) => {
						  const tempFilePaths = resx.tempFilePaths;
										  let da={
											  filePath:tempFilePaths[0],
											  name: 'file'
										  } 
										  uni.showLoading({title: '上传中...'})
										  this.$req.upload('/v1/upload/upfile', da)
												  .then(res => {
													uni.hideLoading();
													// res = JSON.parse(res);
													console.log(res);
													if(res.errcode==0){
														that.rzimg1=res.data.fname;
														uni.showToast({
															icon: 'none',
															title: res.msg
														});
													}else{
														uni.showModal({
															content: res.msg,
															showCancel: false
														})
													}
										  
										  })
					   }
				   });
		},
			
		uprzimg2(){
					   let that = this;
					   uni.chooseImage({
						   count: 1, //默认9
						   sizeType: ['original', 'compressed'],
						   success: (resx) => {
							  const tempFilePaths = resx.tempFilePaths;
											  let da={
												  filePath:tempFilePaths[0],
												  name: 'file'
											  } 
											  uni.showLoading({title: '上传中...'})
											  this.$req.upload('/v1/upload/upfile', da)
													  .then(res => {
														uni.hideLoading();
														// res = JSON.parse(res);
														console.log(res);
														if(res.errcode==0){
															that.rzimg2=res.data.fname;
															uni.showToast({
																icon: 'none',
																title: res.msg
															});
														}else{
															uni.showModal({
																content: res.msg,
																showCancel: false
															})
														}
											  
											  })
						   }
					   });
		},
		
		
		formSubmit: function(e) {
						let that=this;
									 let pvalue = e.detail.value;
									 
									 if(!that.rzimg1){
									   	uni.showToast({
									   		icon: 'none',
									   		title: '请上传营业执照',
									   	});
									   	return false;
									 }
									 if(!that.rzimg2){
									 	uni.showToast({
									 		icon: 'none',
									 		title: '请上传盖章授权书',
									 	});
									 	return false;
									 }
										if(!pvalue.qyname){
										  uni.showToast({
											icon: 'none',
											title: '请输入企业名称',
										  });
										  return false;
										}
										
										if(!pvalue.xydaima){
											uni.showToast({
												icon: 'none',
												title: '请输入信用代码',
											});
											return false;
										}
										if(!pvalue.frname){
											uni.showToast({
												icon: 'none',
												title: '请输入法人姓名',
											});
											return false;
										}
										if(!pvalue.frsfzhao){
											uni.showToast({
												icon: 'none',
												title: '请输入法人身份证号',
											});
											return false;
										}
									
										
							  uni.showLoading({title: '处理中...'})
							  let da={
								'qyname': pvalue.qyname ? pvalue.qyname : '',
								'xydaima': pvalue.xydaima ? pvalue.xydaima : '',
								'frname': pvalue.frname ? pvalue.frname : '',
								'frsfzhao': pvalue.frsfzhao ? pvalue.frsfzhao : '',
								'beizhu': pvalue.beizhu ? pvalue.beizhu : '',
								'rzimg1': that.rzimg1 ? that.rzimg1 : '',
								'rzimg2': that.rzimg2 ? that.rzimg2 : '',
							  }
							  this.$req.post('/v1/sjgl/qyrzsave', da)
									  .then(res => {
										uni.hideLoading();
										console.log(res);
										if(res.errcode==0){
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													setTimeout(function(){
													   that.loadData();
													},1000)
												}
											});
										}else{
											uni.showModal({
												content: res.msg,
												showCancel: false
											})
										}
										
									  })
									  .catch(err => {
									
									  })
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.sqbdcon{margin:32upx;}
.sqtip{font-size: 24rpx;color: #7F7F7F;height:40upx;line-height:40upx;margin-bottom:32upx}

.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
	border-bottom:1px solid #efefef;
}
.rowx .icon{position: absolute;right:32upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#888;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 105px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{}
.rowx .inp .input{height:90upx;line-height:90upx;}
.rowx:last-child{border-bottom:0;}



.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx;position: relative;}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .dptit{text-align: center;}
.maxcon .more{position: absolute;right:28upx;top:28upx;color:#888;font-size: 24rpx;}
.maxcon .more .t1{color: #7F7F7F;}
.maxcon .more .t3{color: #1677FF;margin-left:25upx}
.maxcon .rzicon{position: absolute;right:28upx;top:28upx;}
.maxcon .rzicon .iconfont{font-size:120upx;color:#00aa00}
 
.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.uprzimgcon{margin-top:25upx}
.uprzimgcon .zz1{float:left;width:47.5%;background: #EBEBEB;border-radius:10upx;}
.uprzimgcon .zz2{float:right;width:47.5%;background: #EBEBEB;border-radius:10upx;}
.uprzimgcon .con{padding:10upx}
.uprzimgcon image{width: 100%;height:240upx;display: block;border-radius:10upx;}
 
 
.ddshcon{} 
.ddshcon .icon{} 
.ddshcon .icon image{width: 216upx;display: block;margin:0 auto;} 
.ddshcon .sm{height:80upx;line-height:80upx;font-size:40upx;text-align: center;margin:40upx 0} 
 
 
.wtgsmcon{} 
.wtgsmcon .tt1{font-weight:600;} 
.wtgsmcon .tt1 .iconfont{color:#ff0000;margin-right:20upx} 
.wtgsmcon .tt2{font-size:24upx;margin-top:20upx} 
 
 
 
</style>

