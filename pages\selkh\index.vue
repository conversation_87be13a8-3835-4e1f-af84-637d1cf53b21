<template>
	<view class="tn-safe-area-inset-bottom main-Location">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >选择客户</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 65vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="dengjisort" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :gutter="20" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 35vw;overflow: hidden;" >
				<view class="item btnimg" @click="addkhfs" ><image src="/static/images/addbtn.png"></image></view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		</view>
		
	
	
	   <view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}" ></view>
	   	
	   	
	   
	   <view class="tjtiaox"  >
			<block v-if="thkw">
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> </view>
					</view>
				</block> 
			</block> 
			<block v-else>
				<view class="khznumcon" >客户数量：<text>{{khznum}}</text>人</view>
				<view class="khseldp">
					<view class="wpcksel">
						
					</view>
				</view>
			</block>	
	   </view>
	
	
		<!-- 字母区域 -->
		<view class="Location-Letter">
			<view v-for="(l,i) in LatterName" :key="i" hover-class="Click-Latter" @tap="getLetter(l)"  :style="{'color': LetterId === l ? '#FFD427' : '#000' }">{{l}}</view>
		</view>
 
	
		<scroll-view scroll-y="true" class="ynq-ScrollView" :scroll-into-view="LetterId">
			<view class="ynq-khlist">
				<block v-for="(item,index) in khlist" :key="index">
					<view class="zmtxt" :id="item.initial">{{item.initial}}</view>
					<view class="ygc">
						<block v-for="(item_yg,name_index) in item.list"	:key="name_index">
							<view class="item"  @tap="selthcity(item_yg)" >
								
								<view class="inf1">
									  <view class="djcon">{{item_yg.dengjitxt}}</view>
									  <view class="frzlzsta" v-if="item_yg.fzrlztxt">
									  		<text class="iconfont icon-tishi5"></text>{{item_yg.fzrlztxt}}
									  </view>
									  <view class="tx">
										  <view class="avatar"><image :src="staticsfile + item_yg.avatar"></image></view>
									      <view class="sexicon" v-if="item_yg.sex > 0"><image :src="'/static/images/sexicon'+item_yg.sex+'.png'"></image></view>
									  </view>
									  <view class="xx">
										  <view class="tit">
											  <text class="name">{{item_yg.realname}}</text>
											  <text class="uid" >ID:{{item_yg.id}}</text>
										  </view>
										  <view class="ssyg" v-if="item_yg.lxtel">电话号码：{{item_yg.lxtel}}</view>
										  <!-- <view class="ssyg" v-if="item_yg.sfzhao">身份证号：{{item_yg.sfzhao}}</view> -->
										  <view class="ssyg" v-if="item_yg.mdname">所属门店：{{item_yg.mdname}}</view>
									  </view>
									  <view class="clear"></view>
								</view>
									  
							</view>  
						</block>
					</view>
				</block>
			</view>
		</scroll-view>
		
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">客户搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入客户名称/电话'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
		
		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">
		
						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
		
					</block>
				</view>
			</scroll-view>
		</tn-popup>
		
		
		
	</view>
</template>
 
<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				stype: 0,
				CityName: '',
				LatterName: '',
				khlist:'', 
				LetterId: '',
				show3:false,
				dengjisort:[],
				dengji:0,
				current:0,
				khznum:0,
				thkw:'',
				thkwtt:'',
				
				dpselshow:false,
				dpdata:'',
				dpid:0,
				dpname:'',
				
				fzrlztstxt:'',
			}
		},
		onLoad(options) {
			let stype=options.stype ? options.stype : 0;
			this.stype=stype;
			
		},
		
		onShow(){
			this.loadData();	
		},
		
		methods: {
			seldianpu() {
				this.dpselshow = true;
			},
			selthdp(e) {
				let dpid = e.currentTarget.dataset.dpid;
				let dpname = e.currentTarget.dataset.dpname;
			    this.dpid=dpid;
			    this.dpname=dpname;
				this.loadData();
				this.dpselshow = false;
			},
			
			clsdpid(){
				let that=this;
				this.dpid=0;
				this.dpname='';
				that.loadData();
			},
			
			loadData() {
				let that=this;
				let da={
					dengji:that.dengji ? that.dengji : 0,
					dpid:that.dpid ? that.dpid : 0,
					kw:that.thkw ? that.thkw : '' ,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/selkh/khlist', da)
				      .then(res => {
					    uni.hideNavigationBarLoading();
						console.log(res);
						if(res.errcode==0){
						   that.dengjisort = res.data.dengjisort;
						   that.khlist = res.data.khlist;
						   that.LatterName = res.data.lattername;
						   that.khznum = res.data.khznum;
						   that.fzrlztstxt = res.data.fzrlztstxt;
						   // that.dpdata = res.data.dpdata;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
				      })
			},

			//获取定位点
			getLetter(name) {
				this.LetterId = name
				// uni.pageScrollTo({
				// 	selector:'#'+name,
				// 	duration:300
				// })
			},
		
		
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
				that.loadData();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
			    that.loadData();
			},
			  bdtel(e){
					let that=this;
					let tel=e.currentTarget.dataset.tel;
					console.log(tel);
					uni.makePhoneCall({
						phoneNumber:tel
					});
			   },
			   gotokhxq(e){
				  let that=this;
				  let khid=e.currentTarget.dataset.khid;
				  console.log(khid);
				  uni.navigateTo({
				  	url:'/pages/work/khgl/khset?khid='+khid
				  })
			   },
			    addkhfs:function(){
					  let that=this;
					  uni.showActionSheet({
						  itemList: ['二维码邀请','手工添加客户'],
						  success: function (res)
						  {
							var index = res.tapIndex;
							if(index==0){
								uni.navigateTo({
									url:'/pages/work/khgl/yqewm'
								})
							}
							if(index==1){
								uni.navigateTo({
									url:'/pages/work/khgl/khedit'
								})
							}
						 
						  },
					  });
				  },
			
				    // tab选项卡切换
					tabChange(index) {
						let that=this;
						let dengji=that.dengjisort[index].dengji;
						console.log(dengji);
						that.thkw = '';
						that.current = index;
						that.dengji = dengji;
						that.loadData();
					},
					
					selthcity(item) {
						let that=this;
						if(item.fzrlz==1){
							uni.showModal({
								content: that.fzrlztstxt, 
								showCancel: false
							})
							return false;
						}
						
						uni.setStorageSync('thkhdata',item)
						if(this.stype==3 || this.stype==4){ //入库时和出库时
							uni.setStorageSync('thygid',item.ygid)
							uni.setStorageSync('thygname',item.ygname)
							if(item.ygid){
								uni.setStorageSync('thygisbdywy',1)
							}else{
								uni.setStorageSync('thygisbdywy',0)
							}
							
						}
						setTimeout(function(){
							uni.navigateBack();
						},500)
					},
		},
	}
</script>

<style lang="scss" scoped>

	.main-Location {
		height: 100vh;
	}
 
	.Location-Letter {
		position: fixed;
		right: 0rpx;
		top: 370rpx;
		width: 85rpx;
		z-index: 100;
		view {
			display: block;
			width: 85rpx;
			text-align: center;
			height: 35rpx;
			line-height: 35rpx;
			font-size: 28rpx;
			font-weight: 600;
			transition: ease .3s;
			-webkit-transition: ease .3s;
			margin-bottom:30upx;
		}
	}
 
	.ynq-khlist {
		padding: 10upx 85rpx 20upx 32rpx;
	}
	.ynq-ScrollView {
		height: calc(100vh - 265rpx);
	}
 
	.Click-Latter {
		font-size: 30rpx !important;
	}
	
	
	.zmtxt{margin-top:10upx;margin-bottom:20upx;font-weight: 600;font-size:28upx;}
	.ygc{margin-bottom:20upx}
	.ygc .item{background: #FFFFFF;border-radius: 20rpx;margin-bottom:20upx;padding:28upx 28upx;position: relative;}
	.ygc .item .tx{float:left;position: relative;}
	.ygc .item .tx .avatar image{width: 100upx;height:100upx;border-radius:100upx;display: block;}
	.ygc .item .tx .sexicon{position: absolute;bottom:-20upx;left:35upx}
	.ygc .item .tx .sexicon image{width: 32upx;height:32upx;}
	.ygc .item .xx{float:left;margin-left:30upx}
	.ygc .item .xx .tit{height:40upx;line-height:40upx;font-size:28upx;padding-top:10upx;margin-bottom:10upx;}
	.ygc .item .xx .tit .name{font-size:28upx;font-weight:600}
	.ygc .item .xx .tit .uid{font-size: 24rpx;margin-left:15upx;color:#888}
	.ygc .item .xx .ssyg{height:35upx;line-height:35upx;font-size:20upx;color: #7F7F7F;}
	.ygc .djcon{position: absolute;right:0;top:0;font-size:24upx;background:#E7FFE2;border-radius: 0rpx 20rpx 0rpx 20rpx;height:42upx;line-height:42upx;padding:0 15upx;color:#0EC45C}
	.ygc .inf3{text-align: center;margin-top:30upx}
	.ygc .inf3 .ite{float:left;width:33%;}
	.ygc .inf3 .ite .ic{display: inline-block;}
	.ygc .inf3 .ite image{width:36upx;height:36upx;vertical-align: middle;}
	.ygc .inf3 .ite .wb{display: inline-block;font-size: 24rpx;margin-left:10upx}
	.ygc .frzlzsta{position: absolute;right:20upx;top:80upx;font-size:20upx}
	.ygc .frzlzsta .iconfont{font-size:20upx;color:#ff0000;margin-right:5upx}
	
	.khznumcon{font-size: 24rpx; color: #7F7F7F;float:left;padding-bottom:20upx}
	.khznumcon text{font-weight:700;color:#ff0000;margin:0 10upx}
	.khseldp{float:right;}
	.khseldp .icon-jiantou2{font-size:22upx;} 
	
</style>