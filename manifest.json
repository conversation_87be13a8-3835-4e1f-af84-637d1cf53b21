{
    "name" : "漏鱼寄卖管家",
    "appid" : "__UNI__B346A71",
    "description" : "",
    "versionName" : "1.6.0",
    "versionCode" : 160,
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Camera" : {},
            "Barcode" : {},
            "Push" : {},
            "OAuth" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "minSdkVersion" : 21,
                "targetSdkVersion" : 30
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false,
                "privacyDescription" : {
                    "NSLocalNetworkUsageDescription" : "此App将可发现和连接到您所用网络上的设备。",
                    "NSAppleMusicUsageDescription" : "我们需要获取访问您设备媒体的权限，以便您能够选择并上传文件到我们的应用中。",
                    "NSContactsUsageDescription" : "该应用需要读取你的通讯录，以便用户与服务商家联系",
                    "NSCalendarsUsageDescription" : "该应用需要获取你的日历，以便更好的体验",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "如您同意提供位置信息，我们将能够为您提供基于位置的个性化服务，例如显示附近的店铺或路线导航功能。",
                    "NSLocationAlwaysUsageDescription" : "该应用需要持续获取用户地理位置，以便为你进行个性化服务",
                    "NSLocationWhenInUseUsageDescription" : "如您同意提供位置信息，我们将能够为您提供基于位置的个性化服务，例如显示附近的店铺或路线导航功能。",
                    "NSMicrophoneUsageDescription" : "该应用需要使用你的麦克风，以便使用语音播放",
                    "NSCameraUsageDescription" : "需要使用摄像头用于扫描企业二维码码加入企业",
                    "NSPhotoLibraryAddUsageDescription" : "我们需要获取写入权限，以便我们能够将您通过应用程序拍摄的照片或视频保存到您的设备相册中。",
                    "NSPhotoLibraryUsageDescription" : "需要您的同意,访问相册用于上传头像"
                }
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "7e8d000748f7899783e04236fb686bfb",
                        "appkey_android" : "a2072be524bf4d2871115be8502ce617"
                    }
                },
                "push" : {
                    "unipush" : {}
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx259209f2385ec383",
                        "UniversalLinks" : ""
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "7e8d000748f7899783e04236fb686bfb",
                        "appkey_android" : "a2072be524bf4d2871115be8502ce617"
                    }
                },
                "oauth" : {},
                // "weixin" : {
                //     "appid" : "wx259209f2385ec383",
                //     "appsecret" : "a87c41de62254ed99ac2079c7765477c",
                //     "UniversalLinks" : ""
                // }
                "ad" : {},
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx259209f2385ec383",
                        "UniversalLinks" : ""
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "useOriginalMsgbox" : false,
                "iosStyle" : "common"
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "usingComponents" : true,
        "appid" : "wxfde76eb538d091c2",
        "setting" : {
            "urlCheck" : true,
            "postcss" : true,
            "es6" : true,
            "minified" : true
        },
        "permission" : {},
        "libVersion" : "latest"
    },
    "h5" : {
        "router" : {
            "mode" : "hash",
            "base" : "/statics/h5/"
        },
        "devServer" : {
            "https" : false
        },
        "template" : "template.h5.html",
        "sdkConfigs" : {
            "maps" : {}
        }
    },
    "vueVersion" : "2",
    "fallbackLocale" : "en"
}
/* ios打包配置 */

