<template>
	<view class="template-news tn-safe-area-inset-bottom">
	  
	  <view class="tn-navbg" :style="{height: vuex_custom_bar_height + 'px'}">
	    <!-- 顶部自定义导航 -->
	    <tn-nav-bar fixed alpha customBack>
	      <view slot="back" class='tn-custom-nav-bar__back'
	        >
	        <text class='icon tn-icon-left'></text>
	        <text class='icon tn-icon-home-capsule-fill'></text>
	      </view>
	    </tn-nav-bar>
	  </view>
		

		<view class="viewcon">
			  <view class="nrcon">
				   <mp-html :content="viewdata.content" />
				  <!-- <jyf-parser :html="html" ref="article"></jyf-parser> -->
			  </view>
		</view>
		  
	
		
	
	</view>
</template>

<script>
	import template_page_mixin from '@/libs/mixin/template_page_mixin.js'
	export default {
		mixins: [template_page_mixin],
		data() {
			return {
				html:'',
				id:0,
				type:0,
		      	viewdata:[]
			}
		},
		onLoad(options) {
			let id=options.id;
			let type=options.type;
			this.id=id;
			this.type=type;
			
			this.loadData();
		},
		onShow(){
			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				uni.setNavigationBarColor({
				  frontColor: mbjson.tbtxtcolor,
				  backgroundColor: mbjson.tbbgcolor
				});
			}
		},
		methods: {
			 loadData(){
				  let that=this;
				  let id=that.id; 
				  let type=that.type; 
				  let da={  
				 	id:id,
					type:type
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v2/adv/index', da)
				         .then(res => {
				 		    uni.hideLoading();
						
				 			if(res.errcode==0){
				 			   that.viewdata=res.data.viewdata;
							     uni.setNavigationBarTitle({
									 title: res.data.viewdata.title
								 });
				 			   // this.$refs.article.setContent(res.data.viewdata.content);
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false
				 				})
				 			}
				         })
			 }	
		},
	
	
	}
</script>

<style>
	
     .viewcon{margin:30upx 40upx 30upx 40upx;}
     .viewcon .title{font-size:16px;line-height:50upx;}
     .viewcon .rq{font-size:11px;color:#888;margin:30upx 0 }
     .viewcon .nrcon{color:#888;line-height:22px; }

	
</style>

<style lang="scss" scoped>
  /* 胶囊*/
  .tn-custom-nav-bar__back {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 1000rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.5);
    color: #FFFFFF;
    font-size: 18px;
    
    .icon {
      display: block;
      flex: 1;
      margin: auto;
      text-align: center;
    }
    
    &:before {
      content: " ";
      width: 1rpx;
      height: 110%;
      position: absolute;
      top: 22.5%;
      left: 0;
      right: 0;
      margin: auto;
      transform: scale(0.5);
      transform-origin: 0 0;
      pointer-events: none;
      box-sizing: border-box;
      opacity: 0.7;
      background-color: #FFFFFF;
    }
  }
</style>
