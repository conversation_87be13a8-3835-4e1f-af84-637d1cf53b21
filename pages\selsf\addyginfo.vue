<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >员工申请</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
	
	
	<block v-if="isshz==1">
		
		<view class="sqbdcon">
			
					<view class="ytji">
						<view class="icon"><image src="../../static/images/ddtime.png"></image></view>
						<view class="tit">资料已提交,正在审核！</view>
					</view>
					
					<view class="mdinfo">
						<view class="tit1">{{sjdata.title}}</view>
						<view class="tit2">{{mddata.title}}</view>
						<view class="tit2">{{mddata.dizhi}}</view>
						<view class="tit2">{{mddata.dptel}}</view>
					</view>
					
					
					
			        <view style="padding:0 60upx"><view class="gotobtn" @click="gotoback">返回</view></view>
			
				
		</view>	
		
		
		
	</block>
	<block v-else>	
	
		
			<view class="" >
					  <form @submit="formSubmit" >
							<view class="sqbdcon">
										
										<view class="mdinfo">
											<view class="logo"><image :src="staticsfile +  mddata.logo"></image></view>
											<view class="tit1">{{sjdata.title}}</view>
											<view class="tit2">{{mddata.title}}</view>
											<view class="dz">{{mddata.dizhi}}</view>
										</view>
										
										
										<view class="maxcon">
										
												<view class="rowx">
													<view class="tit"><text class="redst"></text>您的姓名</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="realname"   placeholder="请输入您的真实姓名" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx">
													<view class="tit"><text class="redst"></text>您的性别</view>
													<view class="inpcon">
														  <view class="sexcon">
																 <view class="ite" :class="thsex==1 ? 'hoverss' : '' " @click="selsex" :data-sex="1">男</view>
																 <view class="ite" :class="thsex==2 ? 'hoverss' : '' " @click="selsex" :data-sex="2">女</view>
														  </view>
														  <view class="clear"></view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx">
													<view class="tit"><text class="redst"></text>手机号码</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="lxtel"   placeholder="请输入手机号码" placeholder-class="placeholder" maxlength="11" /></view>
													</view>
													<view class="clear"></view>
												</view>
												<view class="rowx">
													<view class="tit"><text class="redst"></text>身份证号</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="text" name="sfzhao"   placeholder="请输入身份证号" placeholder-class="placeholder" maxlength="18" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
										</view>	
										
									
										<view class="maxcon">
											 <view class="vstit">请上传身份证的正反面</view>
											 
											 <view class="uprzimgcon" >
												 <view class="zzx" style="margin-bottom:32upx">
													 <view class="con">
														 <view class="sf1">
														      <view class="vt1">头像面</view>
														      <view class="vt2">上传您的身份证头像面</view>
														 </view>
														 <view class="sf2"  @click="uprzimg1" >	  
																<block v-if="rzimg1" >
																	<image :src='staticsfile + rzimg1'></image>
																</block>
																<block v-else>
																	<image src="/static/images/rzupimg1.png"></image>
																</block>
														 </view>
														 <view class="clear"></view>
													 </view>
												 </view>
												 <view class="zzx"   >
													 <view class="con"  >
														 
														 <view class="sf1">
														      <view class="vt1">国徽面</view>
														      <view class="vt2">上传您的身份证国徽面</view>
														 </view>
														 <view class="sf2" @click="uprzimg2">	 
																<block v-if="rzimg2" >
																	<image :src='staticsfile + rzimg2'></image>
																</block>
																<block v-else>
																	<image src="/static/images/rzupimg2.png"></image>
																</block>	
														  </view>	
														  <view class="clear"></view>	
																
													 </view>
												 </view>
												
											 </view>
											 
										</view>
										
										 
									
									
							</view>	
							
								<view class="tn-flex tn-footerfixed">
								  <view class="tn-flex-1 justify-content-item tn-text-center">
									<button class="bomsubbtn"  form-type="submit">
									  <text>确认提交</text>
									</button>
								  </view>
								</view>
					  </form>
		
		     </view>
		
		
	</block>  
	
	
	<view style="height:120upx"></view>
    
	
	
	
	
  </view>
  
  
</template>

<script>
    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 clicksta:false,
			 html:'',
			 ygdata:'',
			 sjdata:'',
			 mddata:'',
			 rzsta:0,
			 status:0,
			 rzimg1:'',
			 rzimg2:'',
			 ygyqma:'',
			 isshz:0,
			 thsex:1,
		  }
	},

	onLoad(options) {
		let that=this;
		let ygyqma=options.ygyqma ? options.ygyqma : '';
		this.ygyqma=ygyqma;
		this.loadData();
	},

    methods: {
		gotoback(){
			uni.navigateBack();
		},

		selsex(e){
			let thsex=e.currentTarget.dataset.sex;
			console.log(thsex);
			this.thsex=thsex;
		},
		
		loadData(){
				  let that=this;
				  let da={  
					  ygyqma:that.ygyqma ? that.ygyqma : '' 
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/yggl/addyginfo', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(111,res);
				 			if(res.errcode==0){
				 		        that.ygdata=res.data.ygdata;
				 		        that.sjdata=res.data.sjdata;
				 		        that.mddata=res.data.mddata;
				 		        that.isshz=res.data.isshz;
				 			}else{
				 				uni.showToast({
				 					icon: 'none',
				 					title: res.msg,
				 					success() {
				 						setTimeout(function(){
				 							uni.navigateBack();
				 						},1000)
				 					}
				 				});
				 			}
				         })
			
		},
			
				
		uprzimg1(){
				   let that = this;
				   uni.chooseImage({
					   count: 1, //默认9
					   sizeType: ['original', 'compressed'],
					   success: (resx) => {
						  const tempFilePaths = resx.tempFilePaths;
										  let da={
											  filePath:tempFilePaths[0],
											  name: 'file'
										  } 
										  uni.showLoading({title: '上传中...'})
										  this.$req.upload('/v1/upload/upfile', da)
												  .then(res => {
													uni.hideLoading();
													// res = JSON.parse(res);
													console.log(res);
													if(res.errcode==0){
														that.rzimg1=res.data.fname;
														uni.showToast({
															icon: 'none',
															title: res.msg
														});
													}else{
														uni.showModal({
															content: res.msg,
															showCancel: false
														})
													}
										  
										  })
					   }
				   });
		},
			
		uprzimg2(){
					   let that = this;
					   uni.chooseImage({
						   count: 1, //默认9
						   sizeType: ['original', 'compressed'],
						   success: (resx) => {
							  const tempFilePaths = resx.tempFilePaths;
											  let da={
												  filePath:tempFilePaths[0],
												  name: 'file'
											  } 
											  uni.showLoading({title: '上传中...'})
											  this.$req.upload('/v1/upload/upfile', da)
													  .then(res => {
														uni.hideLoading();
														// res = JSON.parse(res);
														console.log(res);
														if(res.errcode==0){
															that.rzimg2=res.data.fname;
															uni.showToast({
																icon: 'none',
																title: res.msg
															});
														}else{
															uni.showModal({
																content: res.msg,
																showCancel: false
															})
														}
											  
											  })
						   }
					   });
		},
		
		
		formSubmit: function(e) {
						let that=this;
									 let pvalue = e.detail.value;
									 
									 
									 if(!pvalue.realname){
									 	uni.showToast({
									 		icon: 'none',
									 		title: '请输入您的姓名',
									 	});
									 	return false;
									 }
									 if(!pvalue.lxtel){
									 	uni.showToast({
									 		icon: 'none',
									 		title: '请输入联系电话',
									 	});
									 	return false;
									 }
									 if(!pvalue.sfzhao){
									 	uni.showToast({
									 		icon: 'none',
									 		title: '请输入身份证号',
									 	});
									 	return false;
									 }
									 
									 if(!that.rzimg1){
									   	uni.showToast({
									   		icon: 'none',
									   		title: '请上传您的身份证头像面',
									   	});
									   	return false;
									 }
									 if(!that.rzimg2){
									 	uni.showToast({
									 		icon: 'none',
									 		title: '请上传您的身份证国徽面',
									 	});
									 	return false;
									 }
									if (this.clicksta) return;this.clicksta = true;
							  uni.showLoading({title: '处理中...'})
							  let da={
								'realname': pvalue.realname ? pvalue.realname : '',
								'sex': that.thsex,
								'sfzhao': pvalue.sfzhao ? pvalue.sfzhao : '',
								'lxtel': pvalue.lxtel ? pvalue.lxtel : '',
								'rzimg1': that.rzimg1 ? that.rzimg1 : '',
								'rzimg2': that.rzimg2 ? that.rzimg2 : '',
								'dpid': that.mddata.id ? that.mddata.id : 0,
							  }
							  this.$req.post('/v1/yggl/addyginfosave', da)
									  .then(res => {
										uni.hideLoading();
										console.log(res);
										if(res.errcode==0){
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													setTimeout(function(){
													   that.loadData();
													},1000)
												}
											});
										}else{
											that.clicksta=false;
											uni.showModal({
												content: res.msg,
												showCancel: false
											})
										}
										
									  })
									  .catch(err => {
									
									  })
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >
page{background: #fff;}
.sqbdcon{margin:20upx;padding-top:32upx}
.sqtip{font-size: 24rpx;color: #7F7F7F;height:40upx;line-height:40upx;margin-bottom:32upx}

.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
	border-bottom:1px solid #efefef;
}
.rowx .icon{position: absolute;right:32upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 105px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{}
.rowx .inp .input{height:90upx;line-height:90upx;}


.maxcon{background:#fff;border-radius:20upx;padding:28upx;position: relative;}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .dptit{text-align: center;}
.maxcon .more{position: absolute;right:28upx;top:28upx;color:#888;font-size: 24rpx;}
.maxcon .more .t1{color: #7F7F7F;}
.maxcon .more .t3{color: #1677FF;margin-left:25upx}
.maxcon .rzicon{position: absolute;right:28upx;top:28upx;}
.maxcon .rzicon .iconfont{font-size:120upx;color:#00aa00}
 
.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.uprzimgcon{margin-top:25upx}
.uprzimgcon .zzx{background: #F7F8FA ;border-radius:10upx;}
.uprzimgcon .zzx .sf1{float:left}
.uprzimgcon .zzx .sf2{float:right}
.uprzimgcon .zzx .vt1{font-size: 36rpx;}
.uprzimgcon .zzx .vt2{font-size: 24rpx;color: #7F7F7F;margin-top:25upx}
.uprzimgcon .con{padding:20upx}
.uprzimgcon image{width: 340upx;height:220upx;display: block;border-radius:10upx;}
 
 
.ddshcon{} 
.ddshcon .icon{} 
.ddshcon .icon image{width: 216upx;display: block;margin:0 auto;} 
.ddshcon .sm{height:80upx;line-height:80upx;font-size:40upx;text-align: center;margin:40upx 0} 
 
 

.mdinfo{text-align: center;}
.mdinfo .logo{}
.mdinfo .logo image{width: 180upx;height:180upx;border-radius:200upx;display: block;margin:0 auto;}
.mdinfo .tit1{font-size: 32rpx;font-weight: 600;margin-top:30upx;} 
.mdinfo .tit2{font-size: 32rpx;margin-top:20upx;} 
.mdinfo .dz{font-size: 28rpx;margin-top:20upx;margin-bottom:30upx;color:#7F7F7F} 

.sexcon{padding-top:12upx} 
.sexcon .ite{float:left;width: 140rpx;height: 64rpx;line-height:64upx;border-radius: 8rpx;border: 1rpx solid #333333;text-align: center;font-size: 28rpx;margin-right:20upx} 
.sexcon .ite.hoverss{border:1px solid #FFD427;background:#FFD427;} 
 
.ytji{text-align: center;margin-bottom:150upx} 
.ytji .icon{} 
.ytji .icon image{width: 225upx;height:225upx;display: block;margin:0 auto;}
.ytji .tit{margin-top:20px;}
 
 
.gotobtn{height: 120rpx;line-height: 120rpx;background: #FFD427;border-radius: 60rpx;text-align: center;font-size: 32rpx;font-weight: 500;margin-top:80upx;} 
 
</style>

