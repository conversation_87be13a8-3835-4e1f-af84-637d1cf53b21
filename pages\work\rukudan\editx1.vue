<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{djdata.ddtypetxt}}信息</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">{{djdata.ddtypetxt}}信息</view>
						<view class="rowx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>{{dtptxt}}时间</view>
							<view class="inpcon">
								<view class="inp">
									<picker mode="date" :value="ywtime" @change="bindDateChange">
										{{ywtime ? ywtime : '点击选择'+dtptxt+'时间'}}
									</picker>
								</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="ddtype==1 || ddtype==3">

							<view class="rowx">
								<view class="icon" @click="selywzq" style="color:#1677FF;z-index:200;">
									{{ywzqdwtxt ? ywzqdwtxt : '选择周期'}}</view>
								<view class="tit"><text class="redst"></text>{{dtptxt}}周期</view>
								<view class="inpcon">
									<view class="inp"><input class="input" type="number" name="ywzqnum"
											v-model="ywzqnum" :placeholder="'请输入'+dtptxt+'周期'"
											placeholder-class="placeholder" @input="jsywdqtime" /></view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">
										{{ywdqtimetxt ? ywdqtimetxt : '--' }}
									</view>
								</view>
								<view class="clear"></view>
							</view>

						</block>

					</view>




					<view class="maxcon">

						<view class="vstit" style="margin-bottom:30upx">物品列表</view>
						<view class="more vls" @click="spedit" :data-spid="0">添加物品 <text
								class="iconfont icon-tianjia2"></text></view>

						<block v-if="djshop">
							<view class="splist">
								<block v-for="(item,index) in djshop" :key="index">
									<view class="spitem">
										<view class="spname">
											<view class="tline"></view>
										{{item.title}}
										<text style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.id}})</text>
										</view>
										<!-- 	<view class="rowa">
												<view class="tip">物品编号</view>
												<view class="nr colorls" >{{item.id}}</view>
												<view class="clear"></view>
											</view> -->
										<view class="rowa">
											<view class="tip">物品价格</view>
											<view class="nr">
												¥{{item.danjia}} x {{item.shuliang}} = ¥<text
													>{{item.zongjia}}</text>
											</view>
											<view class="clear"></view>
										</view>
										
										<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
											<view class="rowa">
												<view class="tip" @click="qtfymx" :data-spid="item.id" :data-spname="item.title">其他收费 <text class="iconfont icon-yewucaozuo"	style="margin-left:10upx;color:#1677FF"></text> </view>
												<view class="nr">
													<text @click="qtfymx" :data-spid="item.id"	:data-spname="item.title">¥{{item.fjfjine}}</text>
													<text class="addotfybtn" @click="qtfyedit" :data-spid="item.id" :data-spname="item.title" data-fyid="0">+新增</text>
												</view>
												<view class="clear"></view>
											</view>
										</block>
								<!-- 		<block v-if="djdata.ddtype==2">
											<view class="rowa">
												<view class="tip" @click="qtcbmx" :data-spid="item.id" :data-spname="item.title">其他成本 <text 	class="iconfont icon-yewucaozuo" style="margin-left:10upx;color:#1677FF"></text> </view>
												<view class="nr">
													<text @click="qtcbmx" :data-spid="item.id" :data-spname="item.title">¥{{item.cbfjine}}</text>
													<text class="addotfybtn" @click="qtcbedit" :data-spid="item.id"	:data-spname="item.title" data-fyid="0">+新增</text>
												</view>
												<view class="clear"></view>
											</view>
										</block> -->
										
										<block v-if="ddtype==1">
											<view class="rowa">
												<view class="tip">服务费比例</view>
												<view class="nr">{{item.fwfbl}} %</view>
												<view class="clear"></view>
											</view>
										</block>
										<block v-if="ddtype==1 || ddtype==3">
											<view class="rowa">
												<view class="tip">服务费金额</view>
												<view class="nr" style="color:#ff0000">¥{{item.fwfjine}}</view>
												<view class="clear"></view>
											</view>
										</block>
										
										<view class="rowa">
											<view class="tip">验货员</view>
											<view class="nr">{{item.yhrystr ? item.yhrystr : '--' }}</view>
											<view class="clear"></view>
										</view>
										
										<block v-if="djdata.ddtype==1 || djdata.ddtype==2">
											<view class="rowa">
												<view class="tip">上架价格</view>
												<view class="nr">¥{{item.sjjiage}}</view>
												<view class="clear"></view>
											</view>
										</block>


										<block v-if="item.vxq==1">
											<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
												<view class="rowa">
													<view class="tip">{{itemx.title}}</view>
													<view class="nr">{{itemx.value}}</view>
													<view class="clear"></view>
												</view>
											</block>
											

											<view class="rowa">
												<view class="tip">仓库</view>
												<view class="nr">{{item.ckname}}</view>
												<view class="clear"></view>
											</view>


											<block v-if="item.picturesarr">
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
														<block v-for="(itemxx,indexx) in item.picturesarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemxx"
																	:data-src='staticsfile + itemxx '
																	@click='previewImagev' :data-picarr="item.picturesarr"></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</view>
											</block>

											<block v-if="item.beizhu">
												<view class="bzcon">
													<view class="bztit">备注：</view>
													<view class="bznr">{{item.beizhu}}</view>
												</view>
											</block>

										</block>


										<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
											<block v-if="item.vxq==0">
												展开详情<text class="iconfont icon-jtdown2"></text>
											</block>
											<block v-else>
												收起详情<text class="iconfont icon-jtup2"></text>
											</block>
										</view>

										<view class="czcon">
											<view class="czbtn btn1" @click="delthwp" :data-spid="item.id">删除物品</view>
											<view class="czbtn btn2" @click="spedit" :data-spid="item.id">编辑物品</view>
											<view class="clear"></view>
										</view>

									</view>
								</block>
							</view>
						</block>

					</view>



					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">费用信息</view>


						<view class="rowx">
							<view class="tit"><text class="redst"></text>物品总价</view>
							<view class="inpcon">
								<view class="inp">{{djdata.spzongjia}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>服务费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fwfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他收费</view>
								<view class="inpcon">
									<view class="inp">{{djdata.fjfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
			<!-- 			<block v-if="djdata.ddtype==2">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他成本</view>
								<view class="inpcon">
									<view class="inp">{{djdata.cbfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block> -->
						<block v-if="djdata.ddtype==2">
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>单据总价</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{djdata.zongjia}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-else>
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>总费用</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">¥{{djdata.zongfy}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

					</view>

				</view>



				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="toback" style="background:#ddd;">
								<text>上一步</text>
							</button>
						</view>
						<view class="bomsfright">
							<button class="bomsubbtn" form-type="submit">
								<text>下一步</text>
							</button>
						</view>
						<view class="clear"></view>
					</view>
				</view>


			</form>








		</view>

		<view style="height:120upx"></view>

		<tn-popup v-model="qtfymxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他收费明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtfymxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtfymxspname}}</view>
				<view class="qtfylist">

					<block v-if="qtfydata.length>0">

						<block v-for="(item,index) in qtfydata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="bjbtn">
											<text @click="qtfydel" :data-fyid="item.id">删除</text>
											<text @click="qtfyedit" :data-fyid="item.id" :data-spid="item.spid"
												style="color:#1677FF">编辑</text>
										</view>


										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
									    <block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他收费</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>

        <tn-popup v-model="qtcbmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
        	<view class="popuptit">其他成本明细</view>
        	<scroll-view scroll-y="true" style="height: 800rpx;">
        		<view class="qtcbmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
        			{{qtcbmxspname}}
        		</view>
        		<view class="qtcblist">
        
        			<block v-if="qtcbdata.length>0">
        
        				<block v-for="(item,index) in qtcbdata" :key="index">
        					<view class="lis-item  ">
        						<view class="nrcon">
        							<view class="info">
        								<view class="bjbtn" >
        									<text @click="qtcbdel" :data-fyid="item.id">删除</text>
        									<text @click="qtcbedit" :data-fyid="item.id" :data-spid="item.spid"
        										style="color:#1677FF">编辑</text>
        								</view>
        
        
        								<block v-if="item.type==1">
        									<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
        								</block>
        								<block v-else>
        									<view><text>金额：</text> <text class="jine"
        											style="color:#4aac09">￥{{item.orjine}}</text></view>
        								</block>
        								<view><text>时间：</text>{{item.addtime}}</view>
        								<view><text>说明：</text>{{item.remark}}</view>
        								<block v-if="item.picturescarr">
        									<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
        										详情 <text class="iconfont icon-jtdown2"></text>
        									</view>
        								</block>
        								<block v-if="item.id==thqtcbvid">
        									<view class="des">
        										<block v-if="item.picturescarr">
        											<view class="pjimgcon">
        												<block v-for="(itemx,indexx) in item.picturescarr"
        													:key="indexx">
        													<view class='item'>
        														<image :src="staticsfile+itemx"
        															:data-src='staticsfile + itemx '
        															:data-picturescarr="item.picturescarr"
        															@tap='previewImagex'></image>
        													</view>
        												</block>
        												<view class="clear"></view>
        											</view>
        										</block>
        									</view>
        								</block>
        
        							</view>
        							<view class="clear"></view>
        						</view>
        					</view>
        
        				</block>
        
        
        			</block>
        			<block v-else>
        				<view class="gwcno">
        					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
        					<view class="tit">暂无其他成本</view>
        				</view>
        			</block>
        
        
        		</view>
        	</scroll-view>
        </tn-popup>

	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				ddtype: 0,
				dtptxt: '',
				ywzqnum: '',
				ywzqdw: '',
				ywzqdwtxt: '',
				ywdqtimetxt: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid:0,
				
				qtcbmxshow: false,
				qtcbmxspname: '',
				qtcbdata: '',
				thqtcbvid: 0,
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			this.clicksta = false;
			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}



			if (uni.getStorageSync('thupspotfyspid') && uni.getStorageSync('thupspotfyspid') > 0) {
				this.getqyfylist(uni.getStorageSync('thupspotfyspid'));
				uni.setStorageSync('thupspotfyspid', 0)
			}
			
			if (uni.getStorageSync('thupspotcbspid') && uni.getStorageSync('thupspotcbspid') > 0) {
				this.getqycblist(uni.getStorageSync('thupspotcbspid'));
				uni.setStorageSync('thupspotcbspid', 0)
			}

		},

		methods: {

			toback() {
				uni.navigateBack();
			},

			jsywdqtime(e) {
				let that = this;

				setTimeout(() => {
					let ywzqnum = that.ywzqnum;
					let ywzqdw = that.ywzqdw;
					let ywtime = that.ywtime;
					// if (!ywzqnum || !ywzqdw) {
					// 	return false;
					// }

					let da = {
						djid: that.djid,
						ywzqnum: ywzqnum,
						ywzqdw: ywzqdw,
						ywtime: ywtime,
					}
					uni.showLoading({
						title: ''
					})
					this.$req.post('/v1/danju/jsywdqrq', da)
						.then(res => {
							uni.hideLoading();
							if (res.errcode == 0) {
								that.ywdqtimetxt = res.data.ywdqtimetxt;
							}
						})
				}, 0)
			},

			selywzq: function() {
				let that = this;

				uni.showActionSheet({
					itemList: ['年', '月', '周', '日'],
					success: function(res) {
						var index = res.tapIndex;
						if (index == 0) {
							that.ywzqdw = 1;
							that.ywzqdwtxt = '年';
						}
						if (index == 1) {
							that.ywzqdw = 2;
							that.ywzqdwtxt = '月';
						}
						if (index == 2) {
							that.ywzqdw = 3;
							that.ywzqdwtxt = '周';
						}
						if (index == 3) {
							that.ywzqdw = 4;
							that.ywzqdwtxt = '日';
						}

						that.jsywdqtime();

					},
				});
			},

			spedit(e) {
				let spid = e.currentTarget.dataset.spid
				let djid = this.djid;
				uni.navigateTo({
					url: './spedit?djid=' + djid + '&spid=' + spid
				})
			},
			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					et: 1,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdeditx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.ddtype = res.data.djdata.ddtype;
							that.ywzqnum = res.data.djdata.ywzqnum != 0 ? res.data.djdata.ywzqnum : '';
							that.ywzqdwtxt = res.data.djdata.ywzqdwtxt;
							that.ywzqdw = res.data.djdata.ywzqdw;
							that.ywdqtimetxt = res.data.djdata.ywdqtime;

							that.ywtime = res.data.ywtime;
							that.djshop = res.data.djshop;
							that.dtptxt = res.data.dtptxt;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			bindDateChange(e) {
				let that = this;
				let ywtime = e.detail.value;
				this.ywtime = ywtime;
				that.jsywdqtime();
			},

			delthwp(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'spid': spid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdspdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			previewImagev: function (e) {
				  let that = this;
				  let src = e.currentTarget.dataset.src;
				  let picarr=e.currentTarget.dataset.picarr;
				  let imgarr=[];
				  picarr.forEach(function(item,index,arr){
					  imgarr[index] = that.staticsfile+item;
				  });
				  wx.previewImage({
					  current: src,
					  urls: imgarr
				  });
			},
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},

			formSubmit: function(e) {
				let that = this;
				let pvalue = e.detail.value;
				let ywtime = that.ywtime;
				let ywzqnum = that.ywzqnum;
				let ywzqdw = that.ywzqdw;
				let ywzqdwtxt = that.ywzqdwtxt;
				let djid = that.djid;
				let ddtype = that.ddtype;
				let ywdqtimetxt = that.ywdqtimetxt;

				if (ddtype == 1) {
					if (!ywzqnum || ywzqnum == 0) {
						uni.showToast({
							icon: 'none',
							title: '请输入寄卖周期',
						});
						return false;
					}
				}

				if (ddtype == 3) {
					if (!ywzqnum || ywzqnum == 0) {
						uni.showToast({
							icon: 'none',
							title: '请输入暂存周期',
						});
						return false;
					}
				}

				if (ddtype == 1 || ddtype == 3) {
					if (!ywzqdwtxt) {
						uni.showToast({
							icon: 'none',
							title: '请选择周期单位',
						});
						return false;
					}
				}

				if (that.djshop == '' || !that.djshop) {
					uni.showToast({
						icon: 'none',
						title: '还未添加物品',
					});
					return false;
				}

				if (this.clicksta) return;
				this.clicksta = true;

				uni.showLoading({
					title: '处理中...'
				})
				let da = {
					'djid': djid,
					'ywtime': that.ywtime,
					'ywzqnum': ywzqnum ? ywzqnum : '',
					'ywzqdw': ywzqdw ? ywzqdw : '',
					'ywzqdwtxt': ywzqdwtxt ? ywzqdwtxt : '',
					'ywdqtimetxt': ywdqtimetxt,
				}
				this.$req.post('/v1/danju/rkdeditxsave', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							uni.setStorageSync('thupdpgllist',1);
							setTimeout(function() {
								uni.navigateTo({
									url: './editx2?djid=' + djid
								})
							}, 500)
						} else {
							that.clicksta = false;
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch(err => {

					})

			},



            
			qtfyedit(e) {
				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if(spname){
					this.qtfymxspname = spname;
				}
				
				let djid = this.djid;
				uni.navigateTo({
					url: './qtfyedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtfydel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtfydel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqyfylist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},
			qtfymx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtfymxspname = spname;
				this.getqyfylist(spid);
			},
			qtfysetvxq(e) {
				let thqtfyvid = e.currentTarget.dataset.id;
				if (thqtfyvid == this.thqtfyvid) {
					this.thqtfyvid = 0;
				} else {
					this.thqtfyvid = thqtfyvid;
				}
			},
			getqyfylist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtfylist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtfydata = res.data.qtfydata;
							that.qtfymxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			
			qtcbedit(e) {
			
				let spid = e.currentTarget.dataset.spid;
				let fyid = e.currentTarget.dataset.fyid;
				let spname = e.currentTarget.dataset.spname;
				if (spname) {
					this.qtcbmxspname = spname;
				}
				let djid = this.djid;
			
				uni.navigateTo({
					url: '/pages/work/rukudan/qtcbedit?spid=' + spid + '&djid=' + djid + '&fyid=' + fyid
				})
			},
			qtcbdel(e) {
				let that = this;
				let fyid = e.currentTarget.dataset.fyid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							let da = {
								'fyid': fyid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdqtcbdel', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										let spid = res.data.spid;
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.getqycblist(spid);
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
			
						}
			
					}
				})
			},
			qtcbmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtcbmxspname = spname;
				this.getqycblist(spid);
			},
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}
			
			},
			getqycblist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtcblist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtcbdata = res.data.qtcbdata;
							that.qtcbmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			
			
			

		
		
		
		}
	}
</script>

<style lang="scss">
	.sqbdcon {
		margin: 32upx;
	}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
	}

	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .viewxqcon {
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		margin-bottom: 10upx;
		margin-top: 15upx;
		text-align: right;
		font-size: 24upx;
	}

	.spitem:last-child {
		padding-botom: 0;
		margin-bottom: 0
	}
 
	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.addotfybtn {
		color: #1677FF;
		border-radius: 0upx;
		font-size: 24upx;
		margin-left: 25upx
	}
</style>