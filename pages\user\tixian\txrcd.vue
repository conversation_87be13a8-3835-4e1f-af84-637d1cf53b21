<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >提现记录</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		<view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :gutter="20" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;" >
			
				<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		</view>
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 7 + 'px'}" ></view>
		
		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>
		
	<view class="tjtiao" >
		
			<view class="fless">
				<view class="ite">
					<view class="sz">{{tjdata.xjine}}</view>
					<view class="tx">累计提现</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum2}}</view>
					<view class="tx">待审核</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum3}}</view>
					<view class="tx">已审核</view>
				</view>
				<view class="ite">
					<view class="sz">{{tjdata.tjnum4}}</view>
					<view class="tx">未通过</view>
				</view>
			</view>
			
	</view>

		
			<block v-if="listdata.length>0">
			
			
			    
						    <view class="cwcon">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="cwli  " >
												<view class="nrcon">
													<view class="info">
														<view >提现单号：{{item.ordernum}}</view>
														<view v-if="item.mdname"><text class="tit">所属门店：</text><text>{{item.mdname}}</text></view>
														<view >提现时间：{{item.addtime}}</view>
														<view >提现金额：<text class="jine">￥{{item.dzjine}}</text></view>
														<view >到帐金额：<text class="jine">￥{{item.dzjine}}</text> <text style="font-size:24upx;color:#888;margin-left:20upx">(手续费￥{{item.sxf}})</text></view>
													    <block v-if="item.bhsm">
															<view >{{item.bhsm}}</view>
														</block>	
													</view>
													<view class="stats " :class="'stacolor'+item.txstatus" >{{item.statusname}}</view>
													<view class="clear"></view>
												</view>
											</view>
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关数据</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">提现单搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入提现单号'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
			
		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427"
			activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20"
			:closeBtn="true"></tn-calendar>
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 2,name: '待审核'},
					{sta: 3,name: '已审核'},
					{sta: 4,name: '未通过'},
				],
				show3:false,
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				tjdata:[],
			}
		},
		onLoad(options) {
		  this.loadData();
		},
		onShow() {
			let that=this;
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			
			loadData(){
					  let that=this;
					  let da={
						  tjtype:3
					  }
					 uni.showLoading({title: ''})
					 this.$req.post('/v1/muser/gettjnum', da)
					         .then(res => {
					 		    uni.hideLoading();
							     console.log(111,res);
					 			if(res.errcode==0){
									that.tjdata= res.data.tjdata;
					 			}else{
					 				uni.showModal({
					 					content: res.msg,
					 					showCancel: false,
										success() {
											uni.navigateBack();
										}
					 				})
					 			}
					         })
				
			},
			
			rqmodal(){
				this.rqshow=true
			},
			rqmodalchange(e){
				let that=this;
				if(e.startDate && e.endDate){
					// this.thkw='';
					this.rqkstime=e.startDate;
					this.rqjstime=e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch(){
				let that=this;
				this.rqkstime='';
				this.rqjstime='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			caozuo(e) {
				let djid=e.currentTarget.dataset.djid;
				console.log(djid);
				uni.navigateTo({
					url: './view?djid='+djid
				})
			},
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;
				
			 //    this.rqkstime='';
				// this.rqjstime='';				
			
				this.show3 = false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch() {
				let that = this;
				this.thkw = '';
				this.thkwtt = '';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			
			// tab选项卡切换
				tabChange(index) {
					let that=this;
					let sta=that.stadata[index].sta;
					console.log(sta);
					that.thkw = '';
					that.current = index;
					that.sta = sta;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				},
				
			
							
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							sta:that.sta,
							kw:that.thkw ? that.thkw : '' ,
							rqkstime:that.rqkstime ? that.rqkstime : 0 ,
							rqjstime:that.rqjstime ? that.rqjstime : 0 ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/muser/ygtxlist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
		
		
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>

.cwcon{padding:10upx 30upx;}
.cwli{position: relative;border-bottom:1px solid #efefef;padding:30upx;background:#fff;margin-bottom:20upx;border-radius:20upx;}

.cwli .info{font-size:28upx;line-height:45upx;}
.cwli .jine{color:#ff0000}
.cwli .time{font-size:12px;color:#888;line-height:15px;margin-top:10upx;}
.cwli .stats{position: absolute;right:30upx;top:30upx;font-size:16px;}
.cwli .stats.stacolor1{color: #0DD400;}
.cwli .stats.stacolor2{color: #FA6400;}
.cwli .stats.stacolor3{color: #3366ff;}

</style>
