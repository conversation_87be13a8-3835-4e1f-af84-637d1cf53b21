<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>入账开单审核</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">

				<block v-if="ygdata">
					<view class="maxcon">
						<view class="ttinfo">
							<view class="inf1">
								<view class="tf1">
									<view class="tx">
										<image :src="staticsfile + ygdata.avatar"></image>
									</view>
									<view class="xx">
										<view class="name">{{ygdata.realname}}</view>
										<view class="tel"><text>员工号:{{ygdata.zzhuid}}</text>
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>
					</view>
				</block>

				<view class="maxcon">

					<view class="vstit">基本信息</view>
					<view class="rowx">
						<view class="tit">入账金额：</view>
						<view class="inpcon">
							<view class="inp" style="color:#ff0000">¥ {{rzdata.jine}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">入账门店：</view>
						<view class="inpcon">
							<view class="inp">{{rzdata.mdname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx" v-if="rzdata.khname">
						<view class="tit">入账客户：</view>
						<view class="inpcon">
							<view class="inp">{{rzdata.khname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">申请时间：</view>
						<view class="inpcon">
							<view class="inp">{{rzdata.addtime}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="bzcon">{{rzdata.remark}}</view>

					<block v-if="rzdata.picturescarr">
						<view class="pjimgcon">
							<block v-for="(itemx,indexx) in rzdata.picturescarr" :key="indexx">
								<view class='item'>
									<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
										:data-picturescarr="rzdata.picturescarr" @tap='previewImagex'>
									</image>
								</view>
							</block>
							<view class="clear"></view>
						</view>
					</block>

				</view>

				<block v-if="rzdata.txstatus==2 || rzdata.txstatus==3"> 
					
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
								<block v-if="rzdata.txstatus==2">
									<view class="more tadd" @click="addczry" :data-spid="0" :data-isofr="1" :data-rytype="9" data-rytypetxt="分润人"
										style="color:#333"><text class="iconfont icon-jia"
											style="margin-right:10upx;color:#1677FF"></text> 新增分润
									</view>
								</block>
								<block v-if="rzdata.czfrry2data">
									<view class="czry4info">
										<block v-for="(itemx,indexx) in rzdata.czfrry2data" :key="indexx">
											<view class="czrli">
												<block v-if="rzdata.txstatus==2 && itemx.rytype==9">
													<view class="del" @click="delczry" :data-czryid="itemx.id"><text
															class="iconfont icon-cuohao02"></text></view>
												</block>
						
												<view class="cname">{{itemx.ygname}}
													<text class="czjs">({{itemx.rytypetxt}})</text>
												</view>
												<view class="xx">
													<view class="czxm">分润金额：<text
															:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
													</view>
													<view class="czxm2" @click="czryedit" :data-spid="0"
														:data-czryid="itemx.id" :data-czygid="itemx.ygid"
														:data-czygname="itemx.ygname" :data-czjine="itemx.czjine"
														:data-rytype="itemx.rytype" :data-rytypetxt="itemx.rytypetxt" :data-frjine="itemx.frjine"
														:data-isdpczzh="itemx.isdpczzh"
														:data-frbl="itemx.frbl" v-if="rzdata.txstatus==2" :data-isofr="1">修改
													</view>
													<view class="clear"></view>
												</view>
											</view>
										</block>
									</view>
								</block>
							</view>
							<view class="maxcon" style="padding-bottom:10upx">
									<view class="czrli">
										<view class="xx">
											<view class="czxm hj" style="width:100%;">已分润金额：<text
													class="ysz">{{rzdata.frjinehj}}</text> <text class="gx">,</text>
												未分润金额：<text class="hs">{{rzdata.frjinehj2}}</text></view>
											<view class="clear"></view>
										</view>
									</view>
							</view>
					 
				</block>
				
				

				<block v-if="rzdata.txstatus!=2">
					<view class="maxcon">
						<view class="vstit">审核信息</view>
						<view class="rowx">
							<view class="tit">审核人员：</view>
							<view class="inpcon">
								<view class="inp">{{rzdata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核时间：</view>
							<view class="inpcon">
								<view class="inp">{{rzdata.shtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核状态：</view>
							<view class="inpcon">
								<view class="inp">
										<view class="stats " :class="'stacolor'+rzdata.txstatus">{{rzdata.statusname}}</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="bzcon">{{rzdata.bhsm}}</view>
					</view>
				</block>


			</view>



			<block v-if="rzdata.txstatus==2">
				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<view class="bomsfleft">
							<button class="bomsubbtn" @click="jjtgsh" style="background:#ddd;">
								<text>拒绝</text>
							</button>
						</view>
						<view class="bomsfright">
							<button class="bomsubbtn" @click="tgsh">
								<text>审核通过</text>
							</button>
						</view>
						<view class="clear"></view>
					</view>
				</view>
			</block>



		</view>

		<view style="height:120upx"></view>


		<tn-modal v-model="jjtgshow" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">拒绝理由</view>
				<view class="bhsmcon">
					<textarea name="bhsm" class="beizhu" v-model="bhsm" placeholder="请输入拒绝的理由"
						style="height:350upx"></textarea>
				</view>

				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" @click="jjsh">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>
		
		<tn-popup v-model="czryeditshow" mode="center" :borderRadius="20" :closeBtn='true'>
			<view class="cznrcon">
				<view class="rowx">
					<view class="tit"><text class="redst"></text>{{addrytypetxt}}</view>
					<view class="inpcon">
						<view class="inp">{{thczygname}} <text v-if="thczygid > 0"
								style="color:#888;margin-left:10upx">({{thczygid}})</text></view>
					</view>
					<view class="clear"></view>
				</view>
		
				<block v-if="isofr==0">
					<view class="rowx">
						<view class="tit"><text class="redst"></text>可用出资</view>
						<view class="inpcon">
							<view class="inp" style="color:#1677FF">{{thygczkyjine}} </view>
						</view>
						<view class="clear"></view>
					</view>
					
					<view class="rowx">
						<view class="tit"><text class="redst"></text>出资金额</view>
						<view class="inpcon">
							<view class="inp"><input class="input" type="number" name="title" v-model="thczjine"
									placeholder="请输入出资金额" placeholder-class="placeholder" /></view>
						</view>
						<view class="clear"></view>
					</view>
				</block>
		
				<block v-if="czfrisfr==1">
					<view class="rowx">
						<view class="tit"><text class="redst"></text>分润金额</view>
						<view class="inpcon">
							<view class="inp">
								<input class="input" type="number" name="frjine" v-model="thfrjine" placeholder=""
									placeholder-class="placeholder" style="width:120upx;" />
							</view>
						</view>
						<view class="clear"></view>
						<view class="rowxff">
							<view class="wz">比例</view>
							<view class="srk"><input class="input2" type="number" name="frbl" v-model="thfrbl"
									placeholder="请输入" placeholder-class="placeholder" @input="frjscl()" />
							</view>
							<view class="tb"  style="color:#1677FF;z-index:200;">%</view>
							<view class="clear"></view>
						</view>
					</view>
					<view class="smtxt">说明：分润比例只用做辅助计算,不保存!</view>
				</block>
		
				<view class="tn-flex" style="z-index:10;margin-top:30px">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" style="width:100%;margin-left:0" @click="czrysettj">
							<text>确认提交</text>
						</button>
					</view>
				</view>
		
			</view>
		
		</tn-popup>
		
		<tn-popup v-model="gzselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">选择分润规则</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="lrgzlist">
					<block v-if="frgzdata">
						<block v-for="(item,index) in frgzdata" :key="index">
							<view class="item" @click="selthfrgz" :data-gzid="item.id" :data-feilv="item.feilv">
								<view class="tit">{{item.title}}</view>
							</view>
		
						</block>
					</block>
				</view>
			</scroll-view>
		</tn-popup>



	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				djid: '',
				rzdata: '',
				ygdata: '',
				bhsm: '',
				jjtgshow: false,
				
				gzselshow: false,
				thfrjine: 0,
				frgzdata: '',
				addrytype: 0,
				addrytypetxt: '人员姓名',
				
				thczjine: 0,
				thfrbl: 0,
				czfrspid: 0,
				czfrisfr: 1,
				isofr: 0,
				thczygid: 0,
				thczygname: '',
				thygczkyjine: '',
				
				czryeditshow: false,
				isdpczzh:0,
				
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			
			uni.setStorageSync('thygid', '');
			uni.setStorageSync('thygname', '');
			uni.setStorageSync('thygczkyjine', '');
			
			if (djid) {
				this.loadData();
			}
		},
		onShow() {
			this.clicksta = false;
			let thczygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			// let thygczkyjine = uni.getStorageSync('thygczkyjine');
			if (thczygid) {
				console.log(thczygid);
				this.thczygid = thczygid;
				this.loadygcz();
				// this.thczygname = thygname;
				// this.thygczkyjine = thygczkyjine;
				uni.setStorageSync('thygid', '');
				uni.setStorageSync('thygname', '');
				uni.setStorageSync('thygczkyjine', '');
				this.czryeditshow = true;
			}
			
			if (uni.getStorageSync('isdpczzh') == 1) {
				this.isdpczzh = 1;
				this.loadygcz();
				uni.setStorageSync('isdpczzh', '');
			}
			
		},

		methods: {
            
            


			jjtgsh() {
				this.jjtgshow = true;
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/yggl/ygrzdpview', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.rzdata = res.data.rzdata;
							that.bhsm = res.data.rzdata.bhsm;
							that.ygdata = res.data.ygdata;
							that.frgzdata = res.data.frgzdata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},


			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},


			tgsh(e) {
				let that = this;
				uni.showModal({
					title: '审核确认',
					content: '通过后不可撤销！确认通过审核吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'djid': that.djid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/yggl/ygrzdptgshsave', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.setStorageSync('thuplist',1);
										uni.setStorageSync('thuprkdshlist', 1)
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},


			jjsh(e) {
				let that = this;
				
				if(!that.bhsm){
				  uni.showToast({
					icon: 'none',
					title: '请输入拒绝理由',
				  });
				  return false;
				}
				
				let da = {
					'djid': that.djid,
					'bhsm': that.bhsm ? that.bhsm: ''
				}
				uni.showLoading({title: ''})
				that.$req.post('/v1/yggl/ygrzdpjjsave', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.setStorageSync('thuplist',1);
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.loadData();
									}, 1000);
								}
							});
							that.jjtgshow = false;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
				
			},
			
			addczry(e) {
				let isfr = this.czfrisfr; 
				let spid = e.currentTarget.dataset.spid;
				let isofr = e.currentTarget.dataset.isofr;
				let addrytype = e.currentTarget.dataset.rytype;
				let addrytypetxt = e.currentTarget.dataset.rytypetxt;
				this.isdpczzh = 0;
				console.log(isofr);
				this.addrytype = addrytype;
				this.addrytypetxt = addrytypetxt;
				this.isofr = isofr;
				this.czfrspid = spid;
				this.thczjine = 0;
				this.thfrbl = 0;
				this.thfrjine = 0;
				let yytitle = "选择出资人";
				if (isfr == 1) {
					yytitle = "选择出资/分润人";
				}
				if (isofr == 1) {
					yytitle = "选择分润人";
				}
				uni.navigateTo({
					url: '/pages/selyg/index?stype=7&xtitle=' + yytitle
				})
			},
			
			czryedit(e) {
			
				let that = this;
				let czfrspid = e.currentTarget.dataset.spid;
				let czryid = e.currentTarget.dataset.czryid;
				let czygid = e.currentTarget.dataset.czygid;
				let czygname = e.currentTarget.dataset.czygname;
				let czjine = e.currentTarget.dataset.czjine;
				let frbl = e.currentTarget.dataset.frbl;
				let frjine = e.currentTarget.dataset.frjine;
				let isofr = e.currentTarget.dataset.isofr;
				let addrytype = e.currentTarget.dataset.rytype;
				let addrytypetxt = e.currentTarget.dataset.rytypetxt;
				let isdpczzh = e.currentTarget.dataset.isdpczzh;
				console.log(addrytype);
				this.czryeditshow = true;
				this.thczjine = czjine;
				this.thfrbl = 0;
				this.addrytype = addrytype;
				this.addrytypetxt = addrytypetxt;
				this.isdpczzh = isdpczzh;
				this.isofr = isofr;
				this.thfrjine = frjine;
				this.czfrspid = czfrspid;
				this.thczygid = czygid;
				this.thczygname = czygname;
				this.loadygcz();
			
			},
			delczry(e) {
				let that = this;
				let czryid = e.currentTarget.dataset.czryid;
				uni.showModal({
					title: '删除确认',
					content: '删除后不可恢复！确认删除吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
			
							let da = {
								'czryid': czryid
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/yggl/shdelczry', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													that.loadData();
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})
			
						}
			
					}
				})
			},
			
			czrysettj() {
				let that = this;
				let djid = that.djid;
				let thczygid = that.thczygid;
				let czfrspid = that.czfrspid;
				let thczjine = that.thczjine;
				let thfrbl = that.thfrbl;
				let thfrjine = that.thfrjine;
				let isofr = that.isofr;
				let addrytype = that.addrytype;
				let isdpczzh = that.isdpczzh;
				let da = {
					shdjtype: 1,//入账店铺
					djid: djid,
					spid: czfrspid,
					czryid: thczygid,
					czjine: thczjine ? thczjine : 0,
					frbl: thfrbl ? thfrbl : 0,
					frjine: thfrjine ? thfrjine : 0,
					isofr: isofr ? isofr : 0,
					addrytype: addrytype ? addrytype : 0,
					isdpczzh: isdpczzh ? isdpczzh : 0,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/yggl/shsetczry', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
			
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.czryeditshow = false;
										that.loadData();
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
							})
						}
					})
			},
			
			sellxgz() {
				this.gzselshow = true;
			},
			selthfrgz(e) {
				let feilv = e.currentTarget.dataset.feilv;
				this.thfrbl = feilv;
				this.gzselshow = false;
				this.frjscl();
			},
			loadygcz() {
				let that = this;
			
				that.thczygname = 0;
				that.thygczkyjine = 0;
				that.thczjine = 0;
				
				let da = {
					shdjtype: 1,//入账店铺
					djid: this.djid,
					spid: 0,
					czryid: this.thczygid,
					isdpczzh: this.isdpczzh,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/yggl/jsygcz', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.thczygid = res.data.thczygid;
							that.thczygname = res.data.thygname;
							that.thygczkyjine = res.data.thygczkyjine;
							that.thczjine = res.data.thczjine;
							that.czryeditshow = true;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},
			
			frjscl() {
				let that = this;
				let da = {
					shdjtype: 1,//入账店铺
					djid: this.djid,
					spid: 0,
					frbl: this.thfrbl ? this.thfrbl : 0,
				}
			
				this.$req.post('/v1/yggl/frjscl', da)
					.then(res => {
					
						console.log(res);
						if (res.errcode == 0) {
							that.thfrbl = res.data.frbl;
							that.thfrjine = res.data.frjine;
						} else {
							if (res.msg) {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						}
					})
			},




		}
	}
</script>

<style lang="scss">
	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}
	.bzcon {
		margin-top:20upx;
	}
	.bhsmcon {
		margin: 10upx 0 30upx 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}
	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}
	
	.maxcon .vstit .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}
	
	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}
	
	.maxcon .more .t1 {
		color: #7F7F7F;
	}
	
	.maxcon .more.vls {
		color: #1677FF;
	}
	
	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.pjimgcon {
		margin-top: 20upx;
	}

	.pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
	
	
	.stats.stacolor1 {
		color: #0DD400;
	}
	
	.stats.stacolor2 {
		color: #FA6400;
	}
	
	.stats.stacolor3 {
		color: #3366ff;
	}
	
</style>