<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">


			<form @submit="formSubmit">
				<view class="sqbdcon">

					<block v-if="isshcz!=1">
						<view class="maxcon">
							<view class="shztip">
								<view class="icon"><text class="iconfont icon-jigou_wushuju"></text></view>
								<view class="tit">{{isshcztip}}</view>
							</view>
						</view>
					</block>

					<view class="maxcon" style="padding-bottom:10upx">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>单据编号</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ordernum}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户姓名</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户电话</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khtel}}</view>
							</view>
							<view class="clear"></view>
						</view>

						<block v-if="djdata.ddtype==4">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>预付金额</view>
								<view class="inpcon">
									<view class="inp">￥{{djdata.yfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>预付时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="icon" style="color:#1677FF;z-index:200;">{{djdata.ywzqdwtxt}}</view>
								<view class="tit"><text class="redst"></text>还款周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>利率</view>
								<view class="inpcon">
									<view class="inp">{{djdata.lilv}}%</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>总利息</view>
								<view class="inpcon">
									<view class="inp colorhs">￥{{djdata.lxzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>总还款金额</view>
								<view class="inpcon">
									<view class="inp colorhs">￥{{djdata.hkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							
							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还款金额</view>
								<view class="inpcon">
									<view class="inp colorls">￥{{djdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp colorhs">￥{{djdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							

						</block>



						<block v-if="djdata.ddtype==1">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>


						<block v-if="djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="isshcz!=1 && sqdata.ywdqtime!=0">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>续期周期</view>
								<view class="inpcon">
									<view class="inp">{{sqdata.ywzqnum}} {{sqdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>续期截止</view>
								<view class="inpcon">
									<view class="inp" style="color:#ff0000">{{sqdata.ywdqtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<block v-if="djdata.ddtype==4">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>续期利息</view>
									<view class="inpcon">
										<view class="inp" style="color:#1677FF">{{sqdata.xqlixi}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
						</block>

					</view>

					<block v-if="isshcz==1">
						<!-- 续期 -->
						<block v-if="type==2">

							<view class="maxcon">

								<block v-if="djdata.ddtype==4">

									<view class="rowx">
										<view class="icon" @click="selywzq" style="color:#1677FF;z-index:200;">
											{{ywzqdwtxt ? ywzqdwtxt : '选择周期'}}
										</view>
										<view class="tit"><text class="redst"></text>续期周期</view>
										<view class="inpcon">
											<view class="inp"><input class="input" type="number" name="ywzqnum"
													v-model="ywzqnum" :placeholder="'请输入续期周期'"
													placeholder-class="placeholder" @input="jsywdqtime" /></view>
										</view>
										<view class="clear"></view>
									</view>

									<view class="rowx">
										<view class="tit"><text class="redst"></text>到期时间</view>
										<view class="inpcon">
											<view class="inp">
												{{ywdqtimetxt ? ywdqtimetxt : '--' }}
											</view>
										</view>
										<view class="clear"></view>
									</view>
									<view class="rowx">
										<view class="tit"><text class="redst"></text>利息</view>
										<view class="inpcon">
											<view class="inp"><input class="input" type="number" name="xqlixi"
													v-model="xqlixi" :placeholder="'请输入利息'"
													placeholder-class="placeholder" /></view>
										</view>
										<view class="clear"></view>
									</view>

								</block>
								<block v-else>

									<view class="rowx">
										<view class="icon" @click="selywzq" style="color:#1677FF;z-index:200;">
											{{ywzqdwtxt ? ywzqdwtxt : '选择周期'}}
										</view>
										<view class="tit"><text class="redst"></text>续期周期</view>
										<view class="inpcon">
											<view class="inp"><input class="input" type="number" name="ywzqnum"
													v-model="ywzqnum" :placeholder="'请输入续期周期'"
													placeholder-class="placeholder" @input="jsywdqtime" /></view>
										</view>
										<view class="clear"></view>
									</view>

									<view class="rowx">
										<view class="tit"><text class="redst"></text>到期时间</view>
										<view class="inpcon">
											<view class="inp">
												{{ywdqtimetxt ? ywdqtimetxt : '--' }}
											</view>
										</view>
										<view class="clear"></view>
									</view>


								</block>



							</view>


						</block>
					</block>

					<block v-if="djdata.ddtype==3">

						<view class="splist">

							<block v-for="(item,index) in splist" :key="index">

								<view class="item ">

									<view class="inf1">
										<view class="spbh">编号：{{item.id}}</view>

										<view class="tf1">
											<view class="tx">
												<image :src="staticsfile + item.smallpic"></image>
											</view>
											<view class="xx">
												<view class="n0"><text class="ddtype"
														:class="'ys'+item.ddtype">{{item.ddtypename}}</text>
													{{item.title}}</view>
												<view class="n2">
													质押金额：¥{{item.danjia}} x {{item.kucun}} = ¥<text
														style="color:#ff0000">{{item.zongjia}}</text>
												</view>
												<view class="n2">
													服务费用：<text style="margin-right:20upx">¥{{item.fwfjinenow}}</text>
													比例：<text>{{item.fwfbl}}%</text>
												</view>

											</view>
											<view class="clear"></view>
										</view>
									</view>

									<view class="inf2">
										<view class="pdczcon">
											<view class="tx1">续期服务费：</view>
											<view class="tx2">
												<input class="input" type="digit" v-model="item.xqfwf" placeholder=" "
													placeholder-class="placeholder" @blur="setff" :data-index='index'
													:data-id='item.id' :data-spid='item.spid' data-upzd="2"
													:disabled="isshcz!=1" />
											</view>
											<view class="tx1">其他费用：</view>
											<view class="tx2">
												<input class="input" type="digit" v-model="item.xqjine" placeholder=" "
													placeholder-class="placeholder" @blur="setff" :data-index='index'
													:data-id='item.id' :data-spid='item.spid' data-upzd="1"
													:disabled="isshcz!=1" />
											</view>
											<view class="clear"></view>
										</view>
									</view>


								</view>

							</block>
						</view>

						<view class="maxcon">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>费用合计</view>
								<view class="inpcon">
									<view class="inp" style="font-size:32upx;color:#ff0000">
										{{xqfyhj}}
									</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>

					</block>

					<block v-if="isshcz==1">
						<view class="maxcon">
							<view class="vstit">备注说明</view>

							<view class="bzcon">
								<textarea name="remark" class="beizhu" v-model="remark"></textarea>
							</view>

							<view class="rowxmttp">
								<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
									<block v-for="(item,index) in img_url_ok" :key="index">
										<view class='item'>
											<view class="del" @tap="deleteImg" :data-index="index"><i
													class="iconfont icon-shanchu2"></i></view>
											<image :src="staticsfile+item" :data-src='staticsfile + item '
												@tap='previewImage'></image>
										</view>
									</block>
									<view class='item' v-if="img_url_ok.length <= 8">
										<image @tap="chooseimage" src='/static/images/uppicaddx.png'></image>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>
					</block>



				</view>

				<block v-if="isshcz==1">
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="bomsubbtn" form-type="submit">
								<text>确认提交</text>
							</button>
						</view>
					</view>
				</block>

			</form>




		</view>

		<view style="height:120upx"></view>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				status: 0,
				jine: 0,
				img_url: [],
				img_url_ok: [],
				spid: 0,
				djid: 0,
				fyid: 0,
				spname: '',
				spdata: [],
				fydata: [],
				jine: '',
				remark: '',
				type: 1,
				xtitle: '',
				djdata: '',
				shfeiyong: 0,

				ywdqtimetxt: '',
				ywzqnum: '',
				ywzqdw: '',
				ywzqdwtxt: '',
				xqlixi: '',


				xqjine: 0,
				xqfwf: 0,
				xqfyhj: 0,

				sqdata: '',
				isshcz: 1,
				isshcztip: '',
				splist: '',
			}
		},

		onLoad(options) {
			let that = this;
			let type = options.type ? options.type : 0;
			let djid = options.djid ? options.djid : 0;
			this.type = type;
			this.djid = djid;
			if (type == 2) {
				this.xtitle = "续期";
			}
			this.loadData();
		},
		onShow() {

		},

		methods: {
			toback: function() {
				uni.navigateBack();
			},


			loadData() {
				let that = this;
				let da = {
					type: that.type,
					djid: that.djid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/shxq', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.shfeiyong = res.data.shfeiyong;
							that.sqdata = res.data.sqdata;
							that.isshcz = res.data.isshcz;
							that.isshcztip = res.data.isshcztip;
							that.splist = res.data.splist;
							that.xqfyhj = res.data.xqfyhj;
							that.ywzqnum = res.data.ywzqnum;
							that.xqlixi = res.data.xqlixi;
						

							that.ywzqdw = res.data.djdata.ywzqdw;
							that.ywzqdwtxt = res.data.djdata.ywzqdwtxt;

							that.jsywdqtime();


						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},
			jsywdqtime() {
				let that = this;

				setTimeout(() => {
					let ywzqnum = that.ywzqnum;
					let ywzqdw = that.ywzqdw;
					let ywtime = that.djdata.ywdqtime;
					if (!ywzqnum || !ywzqdw) {
						return false;
					}

					let da = {
						ywzqnum: ywzqnum,
						ywzqdw: ywzqdw,
						ywtime: ywtime,
					}
					uni.showLoading({
						title: ''
					})
					this.$req.post('/v1/fuwu/jsywdqrq', da)
						.then(res => {
							uni.hideLoading();
							if (res.errcode == 0) {
								that.ywdqtimetxt = res.data.ywdqtimetxt;
							}
						})
				}, 0)
			},
			selywzq: function() {
				let that = this;

				if (this.djdata.ddtype == 4) {

					uni.showActionSheet({
						itemList: ['月', '周', '日'],
						success: function(res) {
							var index = res.tapIndex;
							if (index == 0) {
								that.ywzqdw = 2;
								that.ywzqdwtxt = '月';
							}
							if (index == 1) {
								that.ywzqdw = 3;
								that.ywzqdwtxt = '周';
							}
							if (index == 2) {
								that.ywzqdw = 4;
								that.ywzqdwtxt = '日';
							}
							that.jsywdqtime();
						},
					});

				} else {

					uni.showActionSheet({
						itemList: ['年', '月', '周', '日'],
						success: function(res) {
							var index = res.tapIndex;
							if (index == 0) {
								that.ywzqdw = 1;
								that.ywzqdwtxt = '年';
							}
							if (index == 1) {
								that.ywzqdw = 2;
								that.ywzqdwtxt = '月';
							}
							if (index == 2) {
								that.ywzqdw = 3;
								that.ywzqdwtxt = '周';
							}
							if (index == 3) {
								that.ywzqdw = 4;
								that.ywzqdwtxt = '日';
							}

							that.jsywdqtime();

						},
					});

				}

			},
			bindDateChange(e) {
				let that = this;
				let ywtime = e.detail.value;
				this.ywtime = ywtime;
				that.jsywdqtime();
			},
			formSubmit: function(e) {
				let that = this;
				var formdata = e.detail.value


				let pvalue = e.detail.value;
				let ywtime = that.ywtime;
				let ywzqnum = that.ywzqnum;
				let ywzqdw = that.ywzqdw;
				let ywzqdwtxt = that.ywzqdwtxt;
				let djid = that.djid;
				let ywdqtimetxt = that.ywdqtimetxt;
				let xqjine = that.xqjine ? that.xqjine : 0;
				let xqfwf = that.xqfwf ? that.xqfwf : 0;
				let ddtype = that.djdata.ddtype;
				let splist = that.splist;

				if (that.type == 2) {
					if (!ywzqnum || ywzqnum == 0) {
						uni.showToast({
							icon: 'none',
							title: '请输入续期周期',
						});
						return false;
					}
					if (!ywzqdwtxt) {
						uni.showToast({
							icon: 'none',
							title: '请选择周期单位',
						});
						return false;
					}

					if (ddtype == 4) {
						if (!that.xqlixi) {
							uni.showToast({
								icon: 'none',
								title: '请输入利息金额',
							});
							return false;
						}
					}

					if (ddtype == 3) {

					}


				}

				uni.showModal({
					title: '',
					content: '以上信息已确认无误并提交吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							uni.showLoading({
								title: '处理中...'
							})

							let da = {
								'djid': that.djid,
								'type': that.type,
								'remark': that.remark ? that.remark : '',
								'pictures': that.img_url_ok ? that.img_url_ok : '',
								'shfeiyong': that.shfeiyong,
								'ywzqnum': ywzqnum ? ywzqnum : '',
								'ywzqdw': ywzqdw ? ywzqdw : '',
								'ywzqdwtxt': ywzqdwtxt ? ywzqdwtxt : '',
								'ywdqtimetxt': ywdqtimetxt,
								'xqfwf': xqfwf,
								'xqlixi': that.xqlixi ? that.xqlixi : 0,
								'splist': that.splist ? that.splist : '',
							}


							that.$req.post('/v1/fuwu/shxqsave', da)
								.then(res => {
									uni.hideLoading();
									console.log(res);
									if (res.errcode == 0) {
										let xspid = res.data.spid;
										uni.setStorageSync('thupsplist', 1);
										uni.setStorageSync('thuprkdshlist', 1);
										uni.setStorageSync('thupspotfyspid', xspid);

										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000)
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})


						}
					}
				})

			},


			// lxjscl(upzd) {

			// 	 if(this.djdata.ddtype!=4){
			// 		 return false;
			// 	 }
			// 	let that = this;
			// 	let ywzqdw = this.ywzqdw;
			// 	let ywzqdwtxt = this.ywzqdwtxt;

			// 	let da = {
			// 		djid: this.djid,
			// 		ywzqnum: this.ywzqnum,
			// 	}
			// 	uni.showLoading({
			// 		title: ''
			// 	})
			// 	this.$req.post('/v1/fuwu/lxjscl', da)
			// 		.then(res => {
			// 			uni.hideLoading();
			// 			console.log('2322222', res);
			// 			if (res.errcode == 0) {
			// 				that.xqlixi = res.data.xqlixi;
			// 				that.ywdqtimetxt = res.data.ywdqtimetxt;
			// 			} else {
			// 				console.log(res.msg)
			// 			}
			// 		})
			// },


			setff(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let spid = e.currentTarget.dataset.spid;
				let id = e.currentTarget.dataset.id;
				let upzd = e.currentTarget.dataset.upzd;
				let thvalue = e.target.value;
				let splist = that.splist;
				let thwpdata = splist[index];
				let pdkucun = thwpdata.pdkucun;
				if (upzd == 1) {
					splist[index].xqjine = thvalue;
				}
				if (upzd == 2) {
					splist[index].xqfwf = thvalue;
				}
				that.splist = splist;

				uni.showLoading({
					title: '处理中...'
				})

				let da = {
					'djid': that.djid,
					'splist': splist ? splist : '',
				}

				that.$req.post('/v1/fuwu/jsxqfyhj', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.xqfyhj = res.data.xqfyhj;
						}
					})
			},






		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

	.matxcont {
		margin: 32upx;
		padding: 32upx;
		border-radius: 20upx;
		background: #fff;
		min-height: 500upx;
	}

	.notixiancon {
		text-align: center;
	}

	.tipcon {
		padding: 50px 0
	}

	.tipcon text {
		font-size: 55px;
		color: #FFD427
	}

	.smlink {
		margin-top: 40px
	}

	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
		padding-bottom: 32upx;
		margin-bottom: 32upx
	}

	.ttinfo .inf1 .tx {}

	.ttinfo .inf1 .xx {
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
		margin: 0 auto;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}

	.ttinfo .inf2 {
		text-align: center;
		padding-bottom: 30upx
	}

	.ttinfo .inf2 .kyjine {
		font-size: 48rpx;
		font-weight: 600;
	}

	.ttinfo .inf2 .tmsm {
		color: #7F7F7F;
		margin-top: 20upx;
		font-size: 24rpx;
	}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.jinecon {
		position: relative;
		margin: 40upx 0
	}

	.jinecon .icon {
		position: absolute;
		left: 0;
		top: 10upx;
	}

	.jinecon .icon .iconfont {
		font-size: 40upx
	}

	.jinecon .inputss {
		font-size: 40upx;
		color: #000;
		padding-left: 40upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 110px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}


	.splist {}

	.splist .item {
		margin-bottom: 20upx;
		background: #fff;
		border-radius: 20upx;
		position: relative;
	}

	.splist .item .spbh {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 20upx;
		color: #1677FF
	}

	.splist .item .inf1 {
		padding: 28upx;
		border-radius: 20upx 20upx 0 0;
		background: #fff;
		position: relative;
	}

	.splist .item .inf1 .status {}

	.splist .item .inf1 .tx {
		float: left
	}

	.splist .item .inf1 .tx image {
		width: 130upx;
		height: 130upx;
		display: block;
		border-radius: 8upx;
	}

	.splist .item .inf1 .xx {
		float: left;
		margin-left: 20upx;
		width: calc(100% - 155upx);
		font-size: 24upx
	}

	.splist .item .inf1 .xx .n0 {
		margin-bottom: 15upx;
	}

	.splist .item .inf1 .xx .n1 {
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
		overflow: hidden;
		font-size: 24upx
	}

	.splist .item .inf2 {
		background: #fff;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		font-size: 24upx;
		padding: 15upx 28upx;
		color: #333;
		position: relative;
		line-height: 40upx;
		border-top: 1px solid #efefef;
		padding: 28upx
	}

	.splist .item .inf2 .scpdtime {
		position: absolute;
		right: 28upx;
		top: 28upx;
		font-size: 24upx;
	}

	.splist .item .inf2 .bzbtn {
		position: absolute;
		right: 28upx;
		bottom: 28upx;
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #fafafa;
		border-radius: 10rpx;
		text-align: center;
		color: #333;
		border: 1px solid #eee
	}


	.splist .item .ddtype {
		padding: 5upx 15upx;
		background: #efefef;
		border-radius: 100upx;
		margin-right: 10upx;
		font-size: 20upx
	}

	.splist .item .ddtype.ys1 {
		background: #f86c02;
		color: #fff
	}

	.splist .item .ddtype.ys2 {
		background: #63b8ff;
		color: #fff
	}

	.splist .item .ddtype.ys3 {
		background: #c76096;
		color: #fff
	}

	.splist .item .ddtype.ys4 {
		background: #43b058;
		color: #fff
	}

	.splist .item .status.sta1 {
		color: #43b058;
	}

	.splist .item .status.sta2 {
		color: #FF2828;
	}

	.pdczcon {
		height: 50upx;
		line-height: 50upx;
		position: relative;
	}

	.pdczcon .tx1 {
		float: left
	}

	.pdczcon .tx2 {
		float: left;
		margin-right: 20upx
	}

	.pdczcon .tx3 {
		float: right;
	}

	.pdczcon .tx2 .input {
		border: 1px solid #FFD427;
		text-align: center;
		height: 50upx;
		line-height: 50upx;
		width: 150upx;
		font-size: 24upx;
		border-radius: 10upx;
	}

	.pdczcon .tx3 .setbtn {
		width: 100rpx;
		height: 50upx;
		line-height: 50upx;
		background: #FFD427;
		border-radius: 10rpx;
		text-align: center;
		color: #333
	}
</style>