<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{xtitle}}</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">


			<form @submit="formSubmit">
				<view class="sqbdcon">
					
					<block v-if="isshcz!=1">
						<view class="maxcon">
							<view class="shztip">
								<view class="icon"><text class="iconfont icon-jigou_wushuju"></text></view>
								<view class="tit">{{isshcztip}}</view>
							</view>
						</view>
					</block>	
					
					<view class="maxcon" style="padding-bottom:10upx">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>单据编号</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ordernum}}</view>
							</view>
							<view class="clear"></view>
						</view>
						
						
						<block v-if="spdata">
							
								<view class="rowx">
									<view class="tit"><text class="redst"></text>物品名称</view>
									<view class="inpcon">
										<view class="inp" @click="gotospxq" :data-spid="spdata.id" style="color:#1677FF">({{spdata.id}}){{spdata.title}}</view>
									</view>
									<view class="clear"></view>
								</view> 
								<view class="rowx"  >
									<view class="tit"><text class="redst"></text>质押总价</view>
									<view class="inpcon">
										<view class="inp">
											 {{spdata.danjia}} x {{spdata.kucun}} = <text style="color:#ff0000;margin-left:10upx" > ¥{{spdata.zongjia}}</text> 
										</view>
									</view>
									<view class="clear"></view>
								</view> 
								<view class="rowx"  >
									<view class="tit"><text class="redst"></text>服务费</view>
									<view class="inpcon">
										<view class="inp">
											 <text style="color:#ff0000;margin-left:10upx" > ¥{{spdata.fjfjine}}</text> 
										</view>
									</view>
									<view class="clear"></view>
								</view> 
						</block>
						
					</view>
					
				     	<block v-if="isshcz!=1">
							<view class="maxcon" style="padding:5upx 30upx">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>操作类型</view>
									<view class="inpcon">
									   <view class="inp">
										    {{sqdata.typetxt}}
									   </view> 
									</view>
									<view class="clear"></view>
								</view>
								
								<block v-if="sqdata.type==5">
									<view class="rowx">
										<view class="tit"><text class="redst"></text>增资金额</view>
										<view class="inpcon">
											<view class="inp" >¥{{sqdata.zjzjine}}</view>
										</view>
										<view class="clear"></view>
									</view> 
									<view class="rowx">
										<view class="tit"><text class="redst"></text>增资服务费</view>
										<view class="inpcon">
											<view class="inp" >¥{{sqdata.zzfwf}}</view>
										</view>
										<view class="clear"></view>
									</view> 
								</block>
								<block v-if="sqdata.type==6">
									<view class="rowx">
										<view class="tit"><text class="redst"></text>减资金额</view>
										<view class="inpcon">
											<view class="inp" >¥{{sqdata.zjzjine}}</view>
										</view>
										<view class="clear"></view>
									</view> 
								</block>
								
							</view>
						
						</block>
					 
						<block v-if="isshcz==1">
								<view class="maxcon" style="padding:5upx 30upx">
									<view class="rowx">
										<view class="tit"><text class="redst"></text>操作类型</view>
										<view class="inpcon">
										   <view class="inp">
											   <radio-group  @change="radio_type">
													<label  ><radio :value="5" :checked="type == 5" color="#FFD427" /> 增资</label>
													<label  style="margin-left:10px"><radio :value="6" :checked="type == 6" color="#FFD427" /> 减资</label>
											   </radio-group>
										   </view>
										</view>
										<view class="clear"></view>
									</view>
								</view>
						
								<view class="maxcon">
									<view class="rowx">
										<view class="tit"><text class="redst"></text>{{type==5 ? '增资' : '减资'}}金额</view>
										<view class="inpcon">
											<view class="inp"><input class="input" type="number" name="zjzjine"
													v-model="zjzjine" :placeholder="'请输入金额'"  
													placeholder-class="placeholder" @blur="jsfwjine()" /></view>
										</view>
										<view class="clear"></view>
									</view>
									<block v-if="type==5">
										<view class="rowx">
											<view class="tit"><text class="redst"></text>服务费</view>
											<view class="inpcon">
												<view class="inp"><input class="input" type="number" name="zzfwf"
														v-model="zzfwf" :placeholder="'请输入服务费'"
														placeholder-class="placeholder" /></view>
											</view>
											<view class="clear"></view>
											<view class="rowxff">
												<view class="wz">按比例</view>
												<view class="srk"><input class="input2" type="number" name="fwfbl" v-model="fwfbl"
														placeholder="" placeholder-class="placeholder" @blur="jsfwjine()" />
												</view>
												<view class="tb" style="color:#1677FF;z-index:200;">%</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>
								</view>
					
			
					    
							<view class="maxcon">
								<view class="vstit">备注说明</view>
								<view class="bzcon">
									<textarea name="remark" class="beizhu" v-model="remark"></textarea>
								</view>
								<view class="rowxmttp">
									<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
										<block v-for="(item,index) in img_url_ok" :key="index">
											<view class='item'>
												<view class="del" @tap="deleteImg" :data-index="index"><i
														class="iconfont icon-shanchu2"></i></view>
												<image :src="staticsfile+item" :data-src='staticsfile + item '
													@tap='previewImage'></image>
											</view>
										</block>
										<view class='item' v-if="img_url_ok.length <= 8">
											<image @tap="chooseimage" src='/static/images/uppicaddx.png'></image>
										</view>
										<view class="clear"></view>
									</view>
								</view>
							</view>
					    </block>



				</view>
				
				<block v-if="isshcz==1">
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<button class="bomsubbtn" form-type="submit">
								<text>确认提交</text>
							</button>
						</view>
					</view>
				</block>
				
			</form>




		</view>

		<view style="height:120upx"></view>


	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				status: 0,
				jine: 0,
				img_url: [],
				img_url_ok: [],
				spid: 0,
				djid: 0,
				fyid: 0,
				zzfwf: 0,
				spname: '',
				spdata: [],
				fydata: [],
				jine: '',
				remark: '',
				type: 5,
				xtitle: '',
				djdata: '',
				shfeiyong: 0,
				zjzjine:'',
				sqdata:'',
				isshcz:1,
				isshcztip:'',
				fwfbl:0,
			}
		},

		onLoad(options) {
			let that = this;
	
			let djid = options.djid ? options.djid : 0;
			let spid = options.spid ? options.spid : 0;
		
			this.spid = spid;
			this.djid = djid;
			this.xtitle = "增减资金";
			this.loadData();
		},
		onShow() {

		},

		methods: {
			gotospxq(e){
				let spid=e.currentTarget.dataset.spid;
				uni.navigateTo({
					url:'/pages/work/ckgl/ckshop/spview?spid='+spid
				})
			},
			toback: function() {
				uni.navigateBack();
			},
			radio_type(e) {
				this.type=e.detail.value;
			},	
			loadData() {
				let that = this;
				let da = {
					djid: that.djid,
					spid: that.spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/zijin', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.spdata = res.data.spdata;
							that.sqdata=res.data.sqdata;
							that.isshcz = res.data.isshcz;
							that.isshcztip = res.data.isshcztip;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},
			jsfwjine(){
			    let zongjia=this.zjzjine;
				let fwfbl=this.fwfbl;
				let fwfjine=0;
				
				if(zongjia<=0){
					this.zzfwf=0;
					return false;
				}
				
				if(!fwfbl || fwfbl==0){
					return false;
				}
				
				if(fwfbl > 100){
					uni.showToast({
						icon: 'none',
						title: '请输入正确的比例',
					});
					return false;
				}
				
				fwfjine=zongjia * fwfbl / 100;
				fwfjine=fwfjine.toFixed(2);
			    this.zzfwf=fwfjine;
			},
			formSubmit: function(e) {
				let that = this;
				var formdata = e.detail.value

				let pvalue = e.detail.value;
				let djid = that.djid;
				let zjzjine = that.zjzjine ?  that.zjzjine : 0;
				let zzfwf = that.zzfwf ?  that.zzfwf : 0;
				let ddtype = that.djdata.ddtype;
		
		            let jetip='请输增资金额';
					if(that.type==6){
						jetip='请输减资金额';
					}
					if (!zjzjine || zjzjine == 0) {
						uni.showToast({
							icon: 'none',
							title:jetip ,
						});
						return false;
					}
			

				uni.showModal({
					title: '',
					content: '以上信息已确认无误并提交吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {
							uni.showLoading({
								title: '处理中...'
							})
						
							let da = {
								'djid': that.djid,
								'spid': that.spid,
								'type': that.type,
								'zzfwf': that.zzfwf,
								'remark': that.remark ? that.remark : '',
								'pictures': that.img_url_ok ? that.img_url_ok : '',
								'zjzjine': zjzjine,
							}
						

							that.$req.post('/v1/fuwu/zijinsave', da)
								.then(res => {
									uni.hideLoading();
									console.log(res);
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000)
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})


						}
					}
				})







			},



		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

	.matxcont {
		margin: 32upx;
		padding: 32upx;
		border-radius: 20upx;
		background: #fff;
		min-height: 500upx;
	}

	.notixiancon {
		text-align: center;
	}

	.tipcon {
		padding: 50px 0
	}

	.tipcon text {
		font-size: 55px;
		color: #FFD427
	}

	.smlink {
		margin-top: 40px
	}

	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
		padding-bottom: 32upx;
		margin-bottom: 32upx
	}

	.ttinfo .inf1 .tx {}

	.ttinfo .inf1 .xx {
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
		margin: 0 auto;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}

	.ttinfo .inf2 {
		text-align: center;
		padding-bottom: 30upx
	}

	.ttinfo .inf2 .kyjine {
		font-size: 48rpx;
		font-weight: 600;
	}

	.ttinfo .inf2 .tmsm {
		color: #7F7F7F;
		margin-top: 20upx;
		font-size: 24rpx;
	}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.jinecon {
		position: relative;
		margin: 40upx 0
	}

	.jinecon .icon {
		position: absolute;
		left: 0;
		top: 10upx;
	}

	.jinecon .icon .iconfont {
		font-size: 40upx
	}

	.jinecon .inputss {
		font-size: 40upx;
		color: #000;
		padding-left: 40upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}
	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 100px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
	
	.maxcon .rowxff {margin-top: 10upx;position: absolute;right: 0upx;top: 0	}
	.maxcon .rowxff .tb {height: 70upx;line-height: 70upx;float: left}
	.maxcon .rowxff .wz {float: left;font-size: 28upx;color: #333;height: 70upx;line-height: 70upx;}
	.maxcon .rowxff .srk {float: left;margin: 0 15upx}
	.maxcon .rowxff .srk .input2 {height: 70upx;line-height: 70upx;border: 1px solid #ddd;text-align: center;width: 110upx;border-radius: 15upx;background: #fafafa;	}
	
	
</style>