{"id": "uni-registerRequestPermissionTips", "displayName": "uni-registerRequestPermissionTips", "version": "1.0.3", "description": "支持android平台全局监听权限的申请。当申请权限时，会在页面顶部显示申请权限的目的。主要解决上架华为应用市场审核要求：APP在调用终端权限时，应同步告知用户申请该权限的目的。", "keywords": ["权限", "权限申请", "上架", "华为"], "repository": "", "engines": {"HBuilderX": "^4.0"}, "dcloudext": {"type": "uts", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "uni-ext-api": {"uni": {"registerRequestPermissionTipsListener": {"name": "registerRequestPermissionTipsListener", "app": {"js": false, "kotlin": true, "swift": false}}, "unregisterRequestPermissionTipsListener": {"name": "unregisterRequestPermissionTipsListener", "app": {"js": false, "kotlin": true, "swift": false}}, "setRequestPermissionTips": {"name": "setRequestPermissionTips", "app": {"js": false, "kotlin": true, "swift": false}}}}, "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": "y", "app-ios": "n", "app-harmony": "u"}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}