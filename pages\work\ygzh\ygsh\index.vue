<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >企业员工审核</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 70vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :gutter="20" :badgeOffset="[30, 15]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 30vw;overflow: hidden;" >
			
				<view class="item searchbtn"><text class="iconfont icon-shijian1" @click="rqmodal()"></text></view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		</view>
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}" ></view>
		
		<block v-if="thkw || rqkstime">
			<view class="ssvtiao">
				<block v-if="rqkstime">
					<view class="ssgjc">
						 <text class="gjc">{{rqkstime}}</text> ~ <text class="gjc">{{rqjstime}}</text>
						<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> 
						</view>
					</view>
				</block>
				<view class="clear"></view>
			</view>
		</block>
		

			<block v-if="listdata.length>0">
			
			
			    
						    <view class="yglist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="item " @click="ygset" :data-ygid='item.id' >
											
													<view class="status" :class="'sta'+item.status" >{{item.statusname}}</view>
													<view class="inf1"  >
													
														<view class="tf1">
															<view class="tx"><image :src="staticsfile + item.avatar"></image></view>
															<view class="xx">
																<view class="name">{{item.realname}}</view>
																<view class="tel">所属门店：{{item.mdname}}</view>
																<view class="tel">联系电话：{{item.lxtel}}</view>
																<view class="tel">申请时间：{{item.addtime}}</view>
															</view>
															<view class="clear"></view>
														</view>	
													</view>
									
												
													
											</view>
																					
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else >
				<view class="gwcno">
					<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
					<view class="tit">暂无相关数据</view>
				</view>
			</block>
		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">员工搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入员工名称/电话号'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427" activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20" :closeBtn="true"></tn-calendar>
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:3,
				index: 1,
				current: 1,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 3,name: '待审核'},
					{sta: 1,name: '已通过'},
				],
				show3:false,
				
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				
			}
		},
		onLoad(options) {
			let that=this;
			this.loadData();
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
			
		},
		onShow(){
				let that=this;
				if(uni.getStorageSync('thupygshlist')==1){
					this.loadData();
					that.page=0;
					that.listdata=[];
					that.loaditems(); 
					uni.setStorageSync('thupygshlist',0)
				}
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: {
			
			loadData() {
				let that = this;
				let da = {
					tjtype: 21,
					ygid:0,
				}
				this.$req.post('/v1/ygzh/getygtjnum', da)
					.then(res => {
						console.log(111, res);
						if (res.errcode == 0) {
						    that.stadata=res.data.tjdata;
						}
					})
			},
			rqmodal(){
				this.rqshow=true
			},
			rqmodalchange(e){
				let that=this;
				if(e.startDate && e.endDate){
					// this.thkw='';
					this.rqkstime=e.startDate;
					this.rqjstime=e.endDate;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				}
			},
			rqclssearch(){
				let that=this;
				this.rqkstime='';
				this.rqjstime='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				// this.rqkstime='';
				// this.rqjstime='';	
				
				this.show3=false;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			
		
			ygset(e){
				let that=this;
				let ygid = e.currentTarget.dataset.ygid;
				uni.navigateTo({
					url:'./view?ygid='+ygid
				})
			},
			
			// tab选项卡切换
				tabChange(index) {
					let that=this;
					let sta=that.stadata[index].sta;
					console.log(sta);
					
					if(index==0){
						that.thkw = '';
						that.rqkstime='';
						that.rqjstime='';
					}
					
					that.current = index;
					that.sta = sta;
					that.page = 0;
					that.listdata = [];
					that.isloading = false;
					that.loadingType = -1;
					that.loaditems();
				},
				
			
							
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							sta:that.sta,
							kw:that.thkw ? that.thkw : '' ,
							rqkstime:that.rqkstime ? that.rqkstime : 0 ,
							rqjstime:that.rqjstime ? that.rqjstime : 0 ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/ygzh/ygshlist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },

		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}



.ssnrtip{height:124upx;line-height:124upx;background:#f6f6f6;position: relative;padding:0 32upx}
.ssnrtip .con{}
.ssnrtip .con text{color:#3366ff}
.ssnrtip .cls{position: absolute;right:32upx;}
.ssnrtip .cls text{font-size:45upx}
 

.yglist{padding:20upx 32upx 32upx 32upx}
.yglist .item{margin-bottom:32upx;padding:28upx;border-radius:20upx;background:#fff;position: relative;}

.yglist .item .addtime{position: absolute;right:32upx;top:40upx;color: #7F7F7F;font-size: 24rpx;}
.yglist .item .status{position: absolute;right:32upx;top:52upx;border:1px solid #eee;height:30px;line-height:30px;border-radius:100px;padding:0 10px;font-size:24upx}
.yglist .item .status.sta1{color:#00aa00;border:1px solid #00aa00;}
.yglist .item .status.sta3{color:#ff6600;border:1px solid #ff6600;}
.yglist .item .status .sta4{color:#888;border:1px solid #ddd;}
.yglist .item .inf1{}
.yglist .item .inf1 .tx{float:left;padding-top:10upx}
.yglist .item .inf1 .xx{float:left;margin-left:20upx;padding-top:0upx}
.yglist .item .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;}
.yglist .item .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.yglist .item .inf1 .tel{color: #7F7F7F;font-size: 20rpx;}
.yglist .item .inf1 .tel text{margin-right:30upx}

 

</style>
