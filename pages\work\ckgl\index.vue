<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed alpha="" backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		
		<view class="topbb">
			<view class="icon"><image src="/static/images/cktticon.png"></image></view>
			<view class="txt">
				<view class="t1">店铺仓库</view>
				<view class="t2">用漏鱼仓库管理系统，工作更加高效</view>
			</view>	
		</view>
		
		<view class="topcc">
			<view class="ssvv">
				<view class="ssk" @click="searchmodal()">
				    <text class="iconfont icon-sousuo2" ></text> 搜索仓库物品
				</view>
				<!-- <view class="ckaddbtn" @click="ckedit" :data-ckid='0'><image src="/static/images/addbtn.png"></image></view> -->
				<view class="clear"></view>
			</view>
			<view class="tmdh">
				<view class="item"  @click="gotockqr">
					<view class="icon">
						<view class="num">{{tjdata.tjnum3}}</view>
						<image src="/static/images/ckicon1.png"></image>
					</view>
					<view class="txt">待出库确认</view>
				</view>
				<view class="item" @click="gotorkqr">
					<view class="icon">
						<view class="num">{{tjdata.tjnum2}}</view>
						<image src="/static/images/ckicon1.png"></image>
					</view>
					<view class="txt">待入库确认</view>
				</view>
				<view class="item" @click="gotowpall" > 
					<view class="icon">
						<view class="num">{{tjdata.tjnum1}}</view>
						<image src="/static/images/ckicon1.png"></image>
					</view>
					<view class="txt">仓库物品</view>
				</view>
				<view class="clear"></view>
			</view>
		</view>
	

       
		
			<block v-if="listdata.length>0">
			
			    <view class="cklbtit">仓库列表</view> 
			    
						    <view class="cklist">
			
									<block v-for="(item,index) in listdata" :key="index">
										
											<view class="item "  >
													<view class="kgname"  >
														<view class="aa">库管：{{item.kgname}}</view> 
													</view>
													<view class="inf1"  >
													
														<view class="tf1">
															<view class="tx"><image :src="staticsfile + item.logo"></image></view>
															<view class="xx">
																<view class="name">{{item.title}}</view>
																<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
															</view>
															<view class="clear"></view>
														</view>	
													</view>
													
													<view class="inf2">
													   <view class="vtm">
														   <view class="tt">当前价值</view>
														   <view class="vv">￥{{item.zjiazhi}}</view>
													   </view>
													   <view class="vtm">
														   <view class="tt">物品总量</view>
														   <view class="vv">{{item.wpznum}}</view>
													   </view>
													   <view class="vtm">
														   <view class="tt">库存总量</view>
														   <view class="vv">{{item.kcznum}}</view>
													   </view>
													   <view class="clear"></view>
													</view>
													
												
													<view class="scpdsjcon" v-if="item.lastpdtime">上一次盘点时间：{{item.lastpdtime}}</view>
													
													<view class="inf3">
														<view class="btm"><view class="setbtn pd" @click="gotopd" :data-ckid='item.id'>盘点</view></view>
														<view class="btm"><view class="setbtn" @click="gotowpgl" :data-ckid='item.id'>物品列表</view></view>
														<!-- <view class="btm"><view class="setbtn" @click="ckedit" :data-ckid='item.id'>管理</view></view> -->
														<view class="clear"></view>
													</view>
												
												
												
													
											</view>
																					
										
									</block>
							</view>
			
							<text class="loading-text" v-if="isloading" >
									{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
							</text>
							
			
			</block>
			<block v-else>
				<view class="ckcno">
					<view class="icon"><text class="iconfont icon-cangkupeizhi"></text></view>
					<view class="tit">暂无仓库</view>
					<!-- <view class="gotobtn" @click="ckedit" :data-ckid='0'>立即创建仓库</view> -->
				</view>
			</block>

		    
	
	
	
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">仓库物品搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输物品名称'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
			
				show3:false,
			    tjdata:[],
				
			}
		},
		onLoad(options) {
			let that=this;
			that.page=0;
			that.listdata=[];
			that.loaditems(); 
		},
		onShow() {
		   let that=this;
		   
		   this.loadData();
		   
		   if(uni.getStorageSync('thupckgllist')==1){
		   	that.page = 0;
		   	that.listdata = [];
		   	that.isloading = false;
		   	that.loadingType = -1;
		   	that.loaditems();
		   	uni.setStorageSync('thupckgllist',0)
		   }
		},
		onReady() {
	
		},
		onPullDownRefresh() {
			 
		},
		methods: { 
			
			loadData() {
				let that = this;
				let da = {
					tjtype:1,
					sortid:that.sortid ? that.sortid : 0 ,
					ckid:that.ckid ? that.ckid : 0 ,
					ddtype:that.ddtype ? that.ddtype : 0 ,
					kw:that.thkw ? that.thkw : '' ,
					rqkstime:that.rqkstime ? that.rqkstime : 0 ,
					rqjstime:that.rqjstime ? that.rqjstime : 0 ,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ckgl/getcktj', da)
					.then(res => {
						uni.hideLoading();
						console.log(111222, res);
						if (res.errcode == 0) {
							that.tjdata = res.data.tjdata;
						
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			
			},
			
			
			gotockqr(){
				uni.navigateTo({
					url:'./ckqr/index'
				})
			},
			gotorkqr(){
				uni.navigateTo({
					url:'./rkqr/index'
				})
			},
			gotowpall(){
				uni.navigateTo({
					url:'./ckshop/index'
				})
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				uni.navigateTo({
					url:'./ckshop/index?thkw='+this.thkwtt
				})
				this.show3=false;
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},
			ckedit(e){
				let that=this;
				let ckid = e.currentTarget.dataset.ckid;
				uni.navigateTo({
					url:'./ckedit?ckid='+ckid
				})
			},
			gotopd(e){
				let that=this;
				let ckid = e.currentTarget.dataset.ckid;
				console.log(ckid);
				uni.navigateTo({
					url:'./ckpd/index?ckid='+ckid
				})
			},
			gotowpgl(e){
				let that=this;
				let ckid = e.currentTarget.dataset.ckid;
				uni.navigateTo({
					url:'./ckshop/index?isck=1&ckid='+ckid
				})
			},
			
			
			
							
				 onReachBottom(){
					this.loaditems();
				 },							
				 // 上拉加载
				 loaditems: function() {
					let that=this;
					let page = ++that.page;
					let da={
							page:page,
							sta:that.sta,
							kw:that.thkw ? that.thkw : '' ,
						}
					if(page!=1){
						that.isloading=true;
					}
					
					if(that.loadingType==2){
						return false;
					}
					uni.showNavigationBarLoading();
					this.$req.get('/v1/ckgl/cklist', da)
						   .then(res => {
							   console.log(res);
								if (res.data.listdata && res.data.listdata.length > 0) {
									that.listdata = that.listdata.concat(res.data.listdata); 
									that.loadingType=0
								}else{
									that.loadingType=2
								}
							 uni.hideNavigationBarLoading();
					})
				 },
		
		
		
		
		
				odcz(e){
					let that=this;
					let odid = e.currentTarget.dataset.odid;  
					let czsta = e.currentTarget.dataset.czsta;
					let cztiptxt='';
						if(czsta==8){cztiptxt='确认接单吗？';}
						if(czsta==7){
							cztiptxt='服务确认已完成了吗？';
						}	
							uni.showModal({
							title: '',
							content: cztiptxt,
							success: function(e) {
							
								if (e.confirm) {
									let da = {
										'odid': odid,
										'czsta': czsta
									}
									uni.showLoading({title: ''})
									that.$req.post('/v1/muser/odcz', da)
										.then(res => {
											uni.hideLoading();
											if (res.errcode == 0) {
												uni.showToast({
													icon: 'none',
													title: res.msg,
													success() {
														setTimeout(function() {
																that.page = 0;
																that.listdata = [];
																that.isloading = false;
																that.loadingType = -1;
																that.loaditems();
														}, 1000);
													}
												});
											} else {
												uni.showModal({
													content: res.msg,
													showCancel: false,
												})
											}
										})
								}
							}
						})
					
				},
				
						
				setsta(e){
					let that=this; 
					let ygid = e.currentTarget.dataset.ygid;
					let da = {
						'ygid': ygid
					}
					uni.showLoading({title: ''})
					that.$req.post('/v1/yggl/setsta', da)
						.then(res => {
							uni.hideLoading();
							if (res.errcode == 0) {
								uni.showToast({
									icon: 'none',
									title: res.msg,
									success() {
										setTimeout(function() {
												that.page = 0;
												that.sta = 0;
												that.listdata = [];
												that.isloading = false;
												that.loadingType = -1;
												that.loaditems();
										}, 1000);
									}
								});
							} else {
								uni.showModal({
									content: res.msg,
									showCancel: false,
								})
							}
					})
					
					
				},
		
		
		
		
		   
		}
	}
</script>

<style lang='scss'>
page {
  background:linear-gradient(to bottom, #FFD427,#F6F6F6) fixed;  
}
.toplinex{border-top:0px solid #fff}

.topbb{position: relative;padding-top:150upx;margin:32upx 32upx 0 32upx}
.topbb .txt{padding-bottom:20upx}
.topbb .txt .t1{height: 66rpx;font-size: 48rpx;font-weight: 600;color: #333333;line-height: 66rpx;}
.topbb .txt .t2{height: 34rpx;font-size: 24rpx;font-weight: 600;color: #333333;line-height: 34rpx;margin-top:20upx}
.topbb .icon{position: absolute;right:0;bottom:0}
.topbb .icon image{width: 220upx;height:244upx}

.topcc{background:#fff;border-radius:26upx;padding:26upx;margin:32upx;}
/* .topcc .ssvv .ssk{float:left;width: calc(100% - 45px);height: 80rpx;line-height: 80rpx;background: #F6F6F6;border-radius: 40rpx;color: #7F7F7F;} */
.topcc .ssvv .ssk{float:left;width: 100%;height: 80rpx;line-height: 80rpx;background: #F6F6F6;border-radius: 40rpx;color: #7F7F7F;}
.topcc .ssvv .ssk .iconfont{color:#FFD427;margin-left:40upx;margin-right:20upx;}
.topcc .ssvv .ckaddbtn{float:right;padding-top:15upx;padding-right:10upx}
.topcc .ssvv .ckaddbtn image{width:24px;height:24px;}
.topcc .tmdh{margin-top:34upx}
.topcc .item{float: left;width:33.33%;text-align: center;}
.topcc .item .icon{width: 54upx;position: relative;margin:0 auto;}
.topcc .item .txt{margin-top:20upx}
.topcc .item .icon image{width: 54upx;height:56upx;}
.topcc .item .icon .num{min-width: 28rpx;height: 32rpx;line-height:29upx;background: #FF2828;border: 2rpx solid #FFFFFF;position: absolute;right:-20upx;top:0upx;border-radius:20px;font-size:24upx;color:#fff;padding:0 8upx;z-index:200;}


.cklbtit{font-size: 28rpx;margin:32upx 32upx 20upx 32upx;}


.ssnrtip{height:124upx;line-height:124upx;background:#f6f6f6;position: relative;padding:0 32upx}
.ssnrtip .con{}
.ssnrtip .con text{color:#3366ff}
.ssnrtip .cls{position: absolute;right:32upx;}
.ssnrtip .cls text{font-size:45upx}


.cklist{padding:0 32upx 32upx 32upx}
.cklist .item{margin-bottom:32upx;padding:28upx;border-radius:20upx 20upx 20upx 20upx;background:#fff;position: relative;}
.cklist .item .kgname{position: absolute;right:32upx;top:32upx}
/* .cklist .item .status .aa{float:left;margin-left:10upx;line-height:50upx;} */
.cklist .item .inf1{}
.cklist .item .inf1 .tx{float:left}
.cklist .item .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
.cklist .item .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:10upx;}
.cklist .item .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.cklist .item .inf1 .dizhi{color: #7F7F7F;font-size: 24rpx;margin-top:10upx}
.cklist .item .inf1 .dizhi text{color:#F7B52C;font-size: 24rpx;margin-top:10upx;margin-right:5upx}


.cklist .item .inf2{background: #F6F6F6;margin:20upx 0;padding:20upx 0;}
.cklist .item .inf2 .vtm{float:left;width:33.33%;font-size:24upx;text-align: center;}
.cklist .item .inf2 .vtm .tt{color: #7F7F7F;font-size:24upx}
.cklist .item .inf2 .vtm .vv{color: #333333;height:50upx;line-height:50upx;}
.cklist .scpdsjcon{color: #7F7F7F;margin-top:15upx;font-size: 24rpx;}


.cklist .item .inf3{margin-top:30upx}
.cklist .item .inf3 .btm{float:right;margin-left:15upx}
.cklist .item .inf3 .setbtn{width: 140rpx;height: 60upx;line-height:60upx;border-radius: 8rpx;background: #F6F6F6;font-size: 24rpx;text-align: center;}
.cklist .item .inf3 .setbtn.pd{background: #FFD427;}

.ckcno{text-align: center;padding-top:50px}
.ckcno .icon{padding-top:30px}
.ckcno .icon .iconfont{font-size:55px;}
.ckcno .tit{height:45px;line-height:45px;margin-bottom:12px;}
.ckcno .gotobtn{background:#FFD427;color:#333;font-size:14px;padding:0 10px;border-radius:10px;width:150px;height:45px;line-height:45px;margin:0 auto;}


</style>
