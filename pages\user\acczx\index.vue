<template>
	<view class="tn-safe-area-inset-bottom">
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
						 <view slot="back" class='tn-custom-nav-bar__back'  >
							  <text class='icon tn-icon-left'></text>
						 </view>
						<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
						  <text  >{{viewdata.title}}</text>
						</view>
						<view slot="right" >
						</view>
						
			</tn-nav-bar>
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
			

		<view class="viewcon">
			  <view class="nrcon">
				   <mp-html :content="viewdata.content" />
			  </view>
		</view>
		  
	
		<view class="tn-flex tn-footerfixed" style="z-index:100;">
		  <view class="tn-flex-1 justify-content-item tn-text-center">
			<button class="bomsubbtn"   @click="qrtjzx">
			  <text>提交确认</text>
			</button>
		  </view>
		</view>
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				html:'',
				id:15,
		      	viewdata:[]
			}
		},
		onLoad(options) {
			this.loadData();
		},
		onShow(){
		
		},
		methods: {
			 loadData(){
				  let that=this;
				  let id=that.id; 
				  let da={  
				 	id:id
				  }
				 uni.showNavigationBarLoading();
				 this.$req.post('/v1/danye/index', da)
				         .then(res => {
				 		  uni.hideNavigationBarLoading();
						
				 			if(res.errcode==0){
				 			   that.viewdata=res.data.viewdata;
							     uni.setNavigationBarTitle({
									 title: res.data.viewdata.title
								 });
				 			}else{
				 				uni.showToast({
				 					icon: 'none',
				 					title: res.msg,
									success() {
										setTimeout(function(){
											uni.navigateBack();
										},1000)
									}
				 				});
				 			}
				         })
			 },
			 
			 qrtjzx(){
				   let that=this;
			 	uni.showModal({
			 		title: '',
			 		content: '确认注销该账号吗？',
			 		success: function (resa) {
			 			if (resa.confirm) {
			 				let da={
			 					
			 				}
			 			
			 				uni.showLoading({title: '处理中...'}) 
			 				that.$req.post('/v1/muser/acczxsave', da)
			 						.then(res => {
			 							uni.hideLoading();
										console.log(res);
			 							if(res.errcode==0){
			 								that.$tool.setTokenStorage('');
			 								that.$tool.setMudataStorage('');
			 								uni.showToast({
			 									icon: 'none',
			 									title: res.msg,
			 									success() {
			 							
			 										setTimeout(()=>{
			 											uni.switchTab({
			 											   url: '/pages/index/index',
			 											})
			 										}, 1000)
			 									}
			 								});
			 							}else{
			 								uni.showToast({
			 									icon: 'none',
			 									title: res.msg
			 								});
			 							}
			 						})
			 			}
			 		}			
			 	})	
			 }
			 
		}
		
	
	
	}
</script>

<style>
	    page{background:#fff}
     .viewcon{margin:20upx 40upx 30upx 40upx;}
     .viewcon .nrcon{
		 color: #333333;
		 line-height: 40upx;
		 font-size: 28upx;
	 }

	
</style>
