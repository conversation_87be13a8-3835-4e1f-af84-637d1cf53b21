<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	
   <tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >{{xtitle}}</text>
					</view>
					<view slot="right" >
					</view>
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >
   
				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
										
										
										<view class="maxcon">
												
												
												
												<view class="rowx">
													<view class="icon">%</view>
													<view class="tit"><text class="redst"></text>利率</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="number" name="feilv" v-model="lxdata.feilv"  placeholder="请输入利率" placeholder-class="placeholder" /></view>
													</view>
													<view class="clear"></view>
												</view>
												
										
												
												
										</view>	
											
										
				  					
				  					<view class=" ">
				  					  <view class="tn-flex-1 justify-content-item tn-text-center">
				  						<button class="bomsubbtn"  form-type="submit" style="margin:0 0">
				  						  <text>确认提交</text>
				  						</button>
				  					  </view>
				  					</view>
									
									
				  			</view>	
				  			
				  		
				  </form>
				  
				
				
				
		
			

	
	</view>
  
	<view style="height:120upx"></view>
    
	
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 lxid:0,
			 staticsfile: this.$staticsfile,
			 lxdata:'',
			 xtitle:'',
		  }
	},

	onLoad(options) {
		let that=this;
		let lxid=options.lxid ? options.lxid : 0;
		this.lxid=lxid;
		if(lxid > 0){
			this.loadData();
			this.xtitle='编辑利息规则';
		}else{
			this.xtitle='添加利息规则';
		}
	},
	
	onShow() {

	},

    methods: {
			  
		loadData(){
				  let that=this;
				  let lxid=that.lxid; 
				  let da={  
				 	lxid:lxid,
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/sjgl/lxsetedit', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 		        that.lxdata=res.data.lxdata;
								  if(res.data.lxdata){
								       that.thgztype=res.data.lxdata.type;
								       that.thgztypename=res.data.lxdata.typename;
								  }
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			
			selgztype:function(){
				  let that=this;
				  uni.showActionSheet({
					  itemList: ['提成','合伙','结余'],
					  success: function (res)
					  {
						var index = res.tapIndex;
					    that.thgztype=that.typedata[index].type;
					    that.thgztypename=that.typedata[index].name;
					  },
				  });
			  },
		
		
		
			formSubmit: function(e) {
						let that=this;
							
									 let pvalue = e.detail.value;
								
									 
										// if(!pvalue.title){
										//   uni.showToast({
										// 	icon: 'none',
										// 	title: '请输入规则名称',
										//   });
										//   return false;
										// }
										
										if(!pvalue.feilv){
											uni.showToast({
												icon: 'none',
												title: '请输入利率',
											});
											return false;
										}
								
										
							  uni.showLoading({title: '处理中...'})
							  let da={
								'lxid': that.lxid,
								'title': pvalue.title ? pvalue.title : '' ,
								'feilv': pvalue.feilv ? pvalue.feilv : 0,
							  }
							  this.$req.post('/v1/sjgl/lxseteditsave', da)
									  .then(res => {
										uni.hideLoading();
										console.log(res);
										if(res.errcode==0){
											uni.showToast({
												icon: 'none',
												title: res.msg,
												success() {
													uni.setStorageSync('thuplxsetlist',1);
													setTimeout(function(){
														uni.navigateBack()
													},1000)
												}
											});
										}else{
											uni.showModal({
												content: res.msg,
												showCancel: false
											})
										}
										
									  })
									  .catch(err => {
									
									  })
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.sqbdcon{margin:32upx;}
.tipsm{margin-bottom:20upx}
.rowx{
	position: relative;
}
.rowx .icon{position: absolute;right:2upx;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{}
.rowx .inp .input{height:90upx;line-height:90upx;}


.rowxmttp{margn-top:30upx}
.imgconmt{}
.imgconmt .item{float:left}
.imgconmt image{width: 160upx;height:160upx;border-radius:20upx;}

.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{font-weight:700;margin-bottom:10upx}
.maxcon .dptit{text-align: center;}

 
</style>

