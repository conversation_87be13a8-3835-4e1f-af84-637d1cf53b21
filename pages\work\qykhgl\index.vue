<template>
	<view class="tn-safe-area-inset-bottom main-Location">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >{{xtitle}}</text>
					</view>
					<view slot="right" >
					    <view class="rzkdbb" @click="gotolzygkh">负责人离职<text class="jbxa" v-if="lzygkhnum > 0">{{lzygkhnum}}</text></view>
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 85vw;overflow: hidden;padding-left:10px">
				
				<view class="tbdhdown">
					<view class="xite" :class="fpsta==0 && jysta==0 && dengji==0  ? 'hoverss' : ''" @click="tbdhdownclk"
						:data-dwtype="0">
						<view class="con">全部</view>
					</view>
					<view class="xite" :class="dwtype==1 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="1">
						<view class="con">{{fpsta!=0 ? fpstatxt : '客户状态'}} <text
								class="iconfont icon-jiantou-copy-copy"></text></view>
					</view>
				<!-- 	<view class="xite" :class="dwtype==2 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="2">
						<view class="con">{{jysta!=0 ? jystatxt : '交易状态'}} <text
								class="iconfont icon-jiantou-copy-copy"></text></view>
					</view> -->
					<view class="xite" :class="dwtype==3 ? 'hoverss' : ''" @click="tbdhdownclk" :data-dwtype="3">
						<view class="con">{{dengji!=0 ? dengjitxt : '客户等级'}} <text
								class="iconfont icon-jiantou-copy-copy"></text></view>
					</view>
				</view>
				
			</view>
			<view class="justify-content-item gnssrr" style="width: 15vw;overflow: hidden;" >
				
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
		
			</view> 
		  </view>  
		</view>
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}" ></view>
			
	
		
		<view class="tjtiaox"  >
			<block v-if="thkw">
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> </view>
					</view>
				</block> 
			</block>  
			<block v-else>
				<view class="khznumcon" >客户数量:<text>{{khznum}}</text>人</view>
				<view class="khseldp">
					
						   <text class="iconfont icon-mendian" style="margin-right:10upx;"></text>
							<text @click="seldianpu">{{dpname ? dpname : '按店铺筛选' }}</text>
							<block v-if="dpid > 0">
								<text class="iconfont icon-cuohao02" style="margin-left:10upx" @click="clsdpid"></text>
							</block>
							<block v-else>
								<text class="iconfont icon-jiantou2" style="margin-left:10upx"></text>
							</block>
					
				</view>
			</block>
				<view class="clear"></view>
		</view>
		<block v-if="khlist">
			<!-- 字母区域 -->
			<view class="Location-Letter">
				<view v-for="(l,i) in LatterName" :key="i" hover-class="Click-Latter" @tap="getLetter(l)"  :style="{'color': LetterId === l ? '#FFD427' : '#000' }">{{l}}</view>
			</view>
	 
		
			<scroll-view scroll-y="true" class="ynq-ScrollView" :scroll-into-view="LetterId">
				<view class="ynq-khlist">
					<block v-for="(item,index) in khlist" :key="index">
						<view class="zmtxt" :id="item.initial">{{item.initial}}</view>
						<view class="ygc">
							<block v-for="(item_yg,name_index) in item.list"	:key="name_index">
								<view class="item"  >
									
								
									
									<view class="inf1">
										  <view class="djcon">{{item_yg.dengjitxt}}</view>
										  <view class="frzlzsta" v-if="item_yg.fzrlztxt">
											  <text class="iconfont icon-tishi5"></text>{{item_yg.fzrlztxt}}
										  </view>
										  <view class="tx">
											  <view class="avatar"><image :src="staticsfile + item_yg.avatar"></image></view>
											  <view class="sexicon" v-if="item_yg.sex > 0"><image :src="'/static/images/sexicon'+item_yg.sex+'.png'"></image></view>
										  </view>
										  <view class="xx">
											  <view class="tit">
												  <text class="name">{{item_yg.realname}}</text>
												  <text class="uid" >（编号:{{item_yg.id}})</text>
											  </view> 
											  <view class="ssyg" v-if="item_yg.mdname">所属门店：{{item_yg.mdname}}</view>
											  <view class="ssyg" v-if="item_yg.ygname">所属员工：{{item_yg.ygname}}</view>
											  <view class="ssyg">添加时间：{{item_yg.addtime}}</view>
											
										  </view>
										  <view class="clear"></view>
									</view>
									<view class="inf3"> 
										<view class="ite" @click="gotojytj" :data-khid="item_yg.id" :data-khname="item_yg.realname">
											<view class="ic"><image src="/static/images/khglicon1.png"></view><view class="wb">交易统计</view><view class="clear"></view>
										</view>
										<view class="ite" @click="gotokhxq" :data-khid="item_yg.id">
											<view class="ic" ><image src="/static/images/khglicon2.png"></view><view class="wb">查看详情</view><view class="clear"></view>
										</view>
								<!-- 		<view class="ite" @click="bdtel" :data-tel="item_yg.lxtel">
											<view class="ic"  ><image src="/static/images/khglicon3.png"></view><view class="wb">拨打电话</view><view class="clear"></view>
										</view> -->
										<view class="clear"></view>
									</view>
										  
								</view>  
							</block>
						</view>
					</block>
				</view>
			</scroll-view>
			
		</block>
		<block v-else> 
			<view class="gwcno">
				<view class="icon"><text class="iconfont icon-renyuanguanli"></text></view>
				<view class="tit">暂无客户</view>
			</view>
		</block>
		
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">客户搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入客户名称/电话/客户号'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
		
		
		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">
		
						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
		
					</block>
				</view>
			</scroll-view>
		</tn-popup>
		
		
		<tn-popup v-model="tbdhdownshow" mode="top" :marginTop="vuex_custom_bar_height + 38" :zIndex=100
			:borderRadius="20">
			<block v-if="dwtype==1">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in fpstadata" :key="index">
						<view class="vitem1" :class="item.value==fpsta ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="1" :data-value="item.value" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==fpsta"></text>
						</view>
					</block>
				</view>
			</block>
			<block v-if="dwtype==2">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in jystadata" :key="index">
						<view class="vitem1" :class="item.value==jysta ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="2" :data-value="item.value" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.value==jysta"></text>
						</view>
					</block>
				</view>
			</block>
		
			<block v-if="dwtype==3">
				<view class="tbdhdownxxcon">
					<block v-for="(item,index) in dengjisort" :key="index">
						<view class="vitem1" :class="item.dengji==dengji ? 'hov' : '' " @click="tbdhdownitemsel"
							:data-dwtype="3" :data-value="item.dengji" :data-name="item.name" :data-vname="item.vname">
							{{item.name}}
							<text class="iconfont icon-duigou1 hov" v-if="item.dengji==dengji"></text>
						</view>
					</block>
				</view>
			</block>
		
		</tn-popup>
		
		
	</view>
</template>
 
<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				stype: 0,
				CityName: '',
				LatterName: '',
				khlist:'', 
				LetterId: '',
				show3:false,
				dengjisort:[],
				dengji:0,
				dengjitxt:0,
				current:0,
				khznum:0,
				thkw:'',
				thkwtt:'',
				
				dpselshow:false,
				dpdata:'',
				dpid:0,
				dpname:'',
				
				ismy:0,
				lzygkhnum:0,
				
				xtitle:'企业客户库',
				
				
				tbdhdownshow: false,
				dwtype: 0,
				fpstadata:[],
				fpsta:0,
				fpstatxt: '客户状态',
				jysta:0,
				jystadata:[],
				jystatxt: '交易状态',
			}
		},
		onLoad(options) {
			this.loadData();
		},
		
		onShow(){
				if(uni.getStorageSync('thupkhlist')==1){
					this.loadData();
					uni.setStorageSync('thupkhlist',0)
				}
		},
		
		methods: {
			
			gotolzygkh(){
				uni.navigateTo({
					url: './lzygkh'
				})
			},
			gotodyx() { 
				uni.navigateTo({
					url: '/pages/danye/index?id=12'
				})
			},
			gotorlkhrcd(){
				if(this.ismy==1){
					uni.navigateTo({
						url: './khlrrcd?ismy=1'
					})
				}else{
					uni.navigateTo({
						url: './khlrrcd'
					})
				}
			},
			seldianpu() {
				this.dpselshow = true;
			},
			selthdp(e) {
				let dpid = e.currentTarget.dataset.dpid;
				let dpname = e.currentTarget.dataset.dpname;
		        this.dpid=dpid;
		        this.dpname=dpname;
				this.loadData();
				this.dpselshow = false;
			},
			
			clsdpid(){
				let that=this;
				this.dpid=0;
				this.dpname='';
				that.loadData();
			},
			
			
			loadData() {
				let that=this;
				let da={
					dpid:that.dpid ? that.dpid : 0,
					fpsta:that.fpsta ? that.fpsta : 0,
					jysta:that.jysta ? that.jysta : 0,
					dengji:that.dengji ? that.dengji : 0,
					kw:that.thkw ? that.thkw : '' ,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/qykhgl/khlist', da)
				      .then(res => {
					    uni.hideNavigationBarLoading();
						console.log(res);
						if(res.errcode==0){
						   that.dengjisort = res.data.dengjisort;
						   that.fpstadata = res.data.fpstadata;
						   that.jystadata = res.data.jystadata;
						   that.khlist = res.data.khlist;
						   that.LatterName = res.data.lattername;
						   that.khznum = res.data.khznum;
						   that.dpdata = res.data.dpdata;
						   that.lzygkhnum = res.data.lzygkhnum;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
				      })
			},

			//获取定位点
			getLetter(name) {
				this.LetterId = name
				// uni.pageScrollTo({
				// 	selector:'#'+name,
				// 	duration:300
				// })
			},
		    gotojymx(e){
				let khid=e.currentTarget.dataset.khid;
				let khname=e.currentTarget.dataset.khname;
				uni.navigateTo({
					url:'/pages/work/khgl/jylist?khid='+khid+'&khname='+khname
				})
			},
			gotojytj(e){
				let khid=e.currentTarget.dataset.khid;
				let khname=e.currentTarget.dataset.khname;
				uni.navigateTo({
					url:'/pages/work/khgl/jytj?khid='+khid+'&khname='+khname
				})
			},
			bdlyudi(e){
				let khid=e.currentTarget.dataset.khid;
				uni.navigateTo({
					url:'/pages/work/khgl/bdlyuidewm?khid='+khid
				})
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
				that.loadData();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
			    that.loadData();
			},
			  bdtel(e){
					let that=this;
					let tel=e.currentTarget.dataset.tel;
					console.log(tel);
					uni.makePhoneCall({
						phoneNumber:tel
					});
			   },
			   gotokhxq(e){
				  let that=this;
				  let khid=e.currentTarget.dataset.khid;
				  console.log(khid);
				  uni.navigateTo({
				  	url:'./khset?khid='+khid
				  })
			   },
				
					
					tbdhdownclk(e) {
						let that = this;
						let dwtype = e.currentTarget.dataset.dwtype;
					
						that.dwtype = dwtype;
						if (dwtype == 0) {
							that.tbdhdownshow = false;
							this.thkw = '';
							this.thkwtt = '';
							// that.rqkstime = '';
							// that.rqjstime = '';
							that.fpsta = 0;
							that.jysta = 0;
							that.dengji = 0;
							that.thkw = '';
							// that.sta = 0;
							that.loadData();
						} else {
							that.tbdhdownshow = true;
						}
					
					},
					tbdhdownitemsel(e) {
						let that = this;
						let dwtype = e.currentTarget.dataset.dwtype;
						let value = e.currentTarget.dataset.value;
						let name = e.currentTarget.dataset.name;
						let vname = e.currentTarget.dataset.vname;
					
						if (dwtype == 1) {
							that.fpsta = value;
							that.fpstatxt = vname ? vname : name;
						}
					
						if (dwtype == 2) {
							that.jysta = value;
							that.jystatxt =  vname ? vname : name;
						}
						if (dwtype == 3) {
							that.dengji = value;
							that.dengjitxt =  vname ? vname : name;
						}
						that.dwtype = dwtype;
						that.tbdhdownshow = false;
					
						that.loadData();
					
					},
					
					
		},
	}
</script>

<style lang="scss" scoped>

	.main-Location {
		height: 100vh;
	}
 
	.Location-Letter {
		position: fixed;
		right: 0rpx;
		top: 360rpx;
		width: 85rpx;
		z-index: 99;
		view {
			display: block;
			width: 85rpx;
			text-align: center;
			height: 35rpx;
			line-height: 35rpx;
			font-size: 28rpx;
			font-weight: 600;
			transition: ease .3s;
			-webkit-transition: ease .3s;
			margin-bottom:30upx;
		}
	}
 
	.ynq-khlist {
		padding: 0upx 85rpx 20upx 32rpx;
	}
	.ynq-ScrollView {
		height: calc(100vh - 285rpx);
	}
 
	.Click-Latter {
		font-size: 30rpx !important;
	}
	
	
	.zmtxt{margin-top:0upx;margin-bottom:20upx;font-weight: 600;font-size:28upx;}
	.ygc{margin-bottom:20upx}
	.ygc .item{background: #FFFFFF;border-radius: 20rpx;margin-bottom:20upx;padding:28upx 28upx;position: relative;}
	.ygc .item .wgluid{position: absolute;right:30upx;top:80upx;font-size:20upx;background:#FFD427;border-radius: 100upx;padding:5upx 15upx;}
	.ygc .item .tx{float:left;position: relative;}
	.ygc .item .tx .avatar image{width: 100upx;height:100upx;border-radius:100upx;display: block;}
	.ygc .item .tx .sexicon{position: absolute;bottom:-20upx;left:35upx}
	.ygc .item .tx .sexicon image{width: 32upx;height:32upx;}
	.ygc .item .xx{float:left;margin-left:30upx}
	.ygc .item .xx .tit{height:40upx;line-height:40upx;font-size:28upx;padding-top:10upx;margin-bottom:20upx}
	.ygc .item .xx .tit .name{font-size:28upx;font-weight:600}
	.ygc .item .xx .tit .uid{font-size: 24rpx;margin-left:10upx;color:#888}
	.ygc .item .xx .ssyg{height:30upx;line-height:30upx;font-size:20upx;margin-top:0upx;color: #7F7F7F;overflow: hidden;}
	.ygc .djcon{position: absolute;right:0;top:0;font-size:24upx;background:#E7FFE2;border-radius: 0rpx 20rpx 0rpx 20rpx;height:42upx;line-height:42upx;padding:0 15upx;color:#0EC45C}
	.ygc .inf3{text-align: center;margin-top:10upx;padding-left:120upx}
	.ygc .inf3 .ite{float:left;width:45%;text-align: left;}
	.ygc .inf3 .ite .ic{display: inline-block;}
	.ygc .inf3 .ite image{width:36upx;height:36upx;vertical-align: middle;}
	.ygc .inf3 .ite .wb{display: inline-block;font-size: 24rpx;margin-left:10upx} 
	.ygc .frzlzsta{position: absolute;right:20upx;top:110upx;font-size:20upx}
	.ygc .frzlzsta .iconfont{font-size:20upx;color:#ff0000;margin-right:5upx}
	
	
	.khznumcon{font-size: 24rpx; color: #7F7F7F;float:left;padding-bottom:20upx}
	.khznumcon text{font-weight:700;color:#ff0000;margin:0 10upx}
	.khseldp{float:right;}
	.khseldp .icon-jiantou2{font-size:22upx;} 
	.rullt{}
	.rullt .iconfont{margin-right:5upx}
	
	.rzkdbb {
		margin-right: 30upx;
		color: #333;
		background: #FFD427;
		border-radius: 120upx;
		position: relative;
		font-size:24upx;
		height:45upx;line-height:45upx;padding:0 18upx;
	}
	
	.rzkdbb .jbxa {
		position: absolute;
		top: -5upx;
		right: -15upx;
		color: #fff;
		font-size: 18upx;
		background: #ff0000;
		border-radius: 100upx;
		padding: 0 8upx;
		height: 25upx;
		line-height: 25upx;
	}
	
</style>