<template>
  <view class="page-e tn-safe-area-inset-bottom">


	
	<tn-nav-bar fixed :isBack="false"  customBack  :bottomShadow="false" backgroundColor="#fff" :zIndex="3000" >
		
			<view class="itop">
				<view class="if1" >
					<view class="trrysel"  >
						<block v-if="isdpkb==1">
							<text class="iconfont icon-kehuC" @click="selyg" style="margin-right:10upx;font-size:32upx"></text>
							<text @click="selyg"  >{{ygname ? ygname : '按员工' }}</text>
							<block v-if="ygid > 0">
								<text class="iconfont icon-cuohao02" style="margin-left:10upx;" @click="clsygid"></text> 
							</block>
						</block> 
						<block v-else>
							<text class="iconfont icon-kehuC" style="margin-right:10upx;font-size:32upx"></text>	
							<text   >{{ygname ? ygname : '我的' }}</text>
						</block>
						
					</view>
					
				</view>
				
				<view class="" style="position: absolute;width:100%;text-align: center;left:0;">
				  <text  >数据看板</text> 
				</view>
				
				<view class="if2">
					<view  @click="rqmodal()"  style="float:right;margin-right:30upx">
						<text class="iconfont icon-shijian1" style="font-size:36upx"></text>
					</view>
					<block v-if="isbskb==1">
						<view @click="gotobosskb" style="float:right;margin-right:30upx;font-size:24upx;">
							<text style="color:#1677FF;font-size:28upx;">BOSS看板</text>
						</view>
					</block>
					
				</view>
				<view class="clear"></view>
			</view>
	</tn-nav-bar>
	
	<view :style="{height: vuex_custom_bar_height + 10 + 'px'}"></view>

	
	<block v-if="rqkstime">
		   <view class="tjtiaox" style="z-index: 0;">
				<view class="ssgjc">
					按日期查询 <text class="gjc">{{rqkstime}}</text>  ~  <text class="gjc">{{rqjstime}}</text>
					<view class="clsbb" @click="rqclssearch()"><text class="iconfont icon-cuohao02"></text> </view>
				</view>
			</view>
	</block>
	
	<block v-if="isdpkb==1 && ygid==0">
			<view class="maxcon" style="margin-top:10upx;padding-top:0;" >
						   
						  <view class="qhdps" >
							<view class="tline"></view>    
							<!-- <view class="icon" v-if="dpdata.length > 1"><text class="iconfont icon-qiehuandianpu1"></text></view> -->
							<view class="logo"><image :src="staticsfile + thdpdata.logo"></image></view>
							<view class="tit">{{dpname}}</view>
							<view class="clear"></view>
						  </view>
						   
						  <view class="tjzong">
								<view class="sz" @click="gotodpcwrcd" data-type="0">{{thdpdata.dpyue}}</view>
								<view class="txt">总账金额(店铺收入余额)  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
										<view class="fyzcbtn" @click="gotodpzz">店铺总账</view>
						  </view>
						  <view class="tjitem noline">
							<view class="ite gx"> 
										<view class="cons" @click="gotodpcwrcd" data-type="1">
											<view class="txt">总收入  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
											<view class="sz">{{thdpdata.dpsyzjine}}</view>
										
										</view>
							</view>
							<view class="ite">
										<view class="cons" @click="gotodpcwrcd" data-type="2">
											<view class="txt">总支出  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
											<view class="sz">{{thdpdata.dpzzhichu}}</view>
										</view>
							</view>
							<view class="clear"></view>
						  </view>
						  <view class="tjitem noline">
										<view class="ite gx"> 
													<view class="cons" @click="gotodpczzjrcd0" data-type="0">
														<view class="txt">可出资金额  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
														<view class="sz">{{thdpdata.czkyjine}}</view>
													
													</view> 
										</view>
										<view class="ite">
													<view class="cons"  @click="gotodpczzjrcd0"  data-type="3">
														<view class="txt">当前出资  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
														<view class="sz">{{thdpdata.czzjine}}</view>
													</view>
										</view>
										<view class="clear"></view>
						  </view>
			</view>
	</block>
	
	
	<block v-if="ygid > 0"> 
			<view class="maxcon" style="margin-top:10upx;padding-top:0;" >
						   
						  <view class="qhdps" >
							<view class="tline"></view>    
							<!-- <view class="icon" v-if="dpdata.length > 1"><text class="iconfont icon-qiehuandianpu1"></text></view> -->
							<view class="logo"><image :src="staticsfile + ygdata.avatar"></image></view>
							<view class="tit">{{ygname}}</view>
							<view class="clear"></view>
						  </view>
						   
						  <view class="tjzong">
								<view class="sz" @click="gotoygcwrcd" data-type="0">{{ygdata.yue}}</view>
								<view class="txt">余额(在本店余额)  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
										<!-- <view class="fyzcbtn" @click="gotodpzz">查看详情</view> -->
						  </view>
						  <view class="tjitem noline">
							<view class="ite gx"> 
										<view class="cons" @click="gotoygcwrcd" data-type="6">
											<view class="txt">本店累计收入  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
											<view class="sz">{{ygdata.syzjine}}</view>
										
										</view>
							</view>
							<view class="ite">
										<view class="cons" @click="gotoygcwrcd" data-type="3">
											<view class="txt">已提现  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
											<view class="sz">{{ygdata.ytxjine}}</view>
										</view>
							</view>
							<view class="clear"></view>
						  </view>
						  <view class="tjitem noline">
										<view class="ite gx"> 
													<view class="cons" @click="gotoygzjrcd" data-type="0">
														<view class="txt">可出资金额  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
														<view class="sz">{{ygdata.czkyjine}}</view>
													
													</view> 
										</view>
										<view class="ite">
													<view class="cons"  @click="gotoygzjrcd"  data-type="3">
														<view class="txt">当前出资  <text class="iconfont icon-wodeshufang-yijianzhuanmai mxicon"></text></view>
														<view class="sz">{{ygdata.czzjine}}</view>
													</view>
										</view>
										<view class="clear"></view>
						  </view>
			</view>
	</block>
	
	
	<view class="ddfltj">
				   <!-- 质押 -->
				   <view class="item">
					   <view class="con" @click="gotourl('./ddlist?ddtype=3&ddtypetxt=质押暂存')">
						   <view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
						   <view class="icon"><image src="/static/images/fwicon4.png" mode="widthFix"></image></view>
						   <view class="tjtxt">单量(单)</view> 
						   <view class="tjnum">{{tjdata.numzy1}}</view>
						   <view class="tjtxt">订单金额(元)</view>
						   <view class="tjnum">{{tjdata.numzy2}}</view>
						</view>
				   </view> 
				   <!-- 回收 -->
				   <view class="item">
					   <view class="con" @click="gotourl('./ddlist?ddtype=2&ddtypetxt=实物回收')"> 
					       <view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
						   <view class="icon"><image src="/static/images/fwicon3.png" mode="widthFix"></image></view>
						   <view class="tjtxt">单量(单)</view>
						   <view class="tjnum">{{tjdata.numhs1}}</view>
						   <view class="tjtxt">订单金额(元)</view>
						   <view class="tjnum">{{tjdata.numhs2}}</view>
					   </view>
				   </view>
				   <!-- 寄卖 -->
				   <view class="item">
					   <view class="con" @click="gotourl('./ddlist?ddtype=1&ddtypetxt=寄卖服务')">
						   <view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
						   <view class="icon"><image src="/static/images/fwicon1.png" mode="widthFix"></image></view>
						   <view class="tjtxt">单量(单)</view>
						   <view class="tjnum">{{tjdata.numjm1}}</view>
						   <view class="tjtxt">订单金额(元)</view>
						   <view class="tjnum">{{tjdata.numjm2}}</view>
						</view>   
				   </view>
				   <!-- 信用预付 -->
				   <view class="item" @click="gotourl('./ddlist?ddtype=4&ddtypetxt=信用预付')">
					   <view class="con">
						   <view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
						   <view class="icon"><image src="/static/images/fwicon2.png" mode="widthFix"></image></view>
						   <view class="tjtxt">数量(单)</view>
						   <view class="tjnum">{{tjdata.numyf1}}</view>
						   <view class="tjtxt">金额(元)</view>
						   <view class="tjnum">{{tjdata.numyf2}}</view>
						</view>   
				   </view>
				   
				   <!-- 入账开单 -->
				   <view class="item" @click="gotourl('./rzkdlist?ddtype=0')">
					   <view class="con">
						    <view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
						    <view class="icon"><image src="/static/images/fwicon21.png" mode="widthFix"></image></view>
							<view class="tjtxt">数量(单)</view>
							<view class="tjnum">{{tjdata.numrzkd1}}</view>
							<view class="tjtxt">金额(元)</view>
							<view class="tjnum">{{tjdata.numrzkd2}}</view>
						</view>   
				   </view>
				   
				   
				   <!-- 销售统计 -->
				   <view class="item" @click="gotourl('./ddcklist?ddtype=0')">
					   <view class="con">
						   <view class="more"><text class="iconfont icon-yewucaozuo"></text></view>
						   <view class="icon"><image src="/static/images/fwicon22.png" mode="widthFix"></image></view>
						   <view class="tjtxt">数量(单)</view>
						   <view class="tjnum">{{tjdata.numsm1}}</view>
						   <view class="tjtxt">金额(元)</view>
						   <view class="tjnum">{{tjdata.numsm2}}</view>
						</view>   
				   </view>
			
				   
				   <view class="clear"></view>
	</view>

      <view style="height:60upx"></view>
	  
	  
	  
	<!-- <tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
	   	<view class="popuptit">选择店铺</view>
	   	<scroll-view scroll-y="true" style="height: 800rpx;">
	   		<view class="seldplist">
	   			<block v-for="(item,index) in dpdata" :key="index">
	   
	   				<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
	   					<view class="xzbtn">选择</view>
	   					<view class="tx">
	   						<image :src="staticsfile + item.logo"></image>
	   					</view>
	   					<view class="xx">
	   						<view class="name">{{item.title}}</view>
	   						<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
	   						</view>
	   					</view>
	   					<view class="clear"></view>
	   				</view>
	   
	   			</block>
	   		</view>
	   	</scroll-view>
	</tn-popup> -->
	  
	  
	  <tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" toolTips="请选择日期范围" btnColor="#FFD427" activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20" :closeBtn="true"></tn-calendar>
	  
	  
	  
	  
	  
	  
	  
	  
  </view>
</template>

<script>
	
  export default {
    data() {
      return {
		  staticsfile: this.$staticsfile,
		  isbskb:0,
		  isdpkb:0,
		  ygid:0,
		  ygname:'',
		  ygdata:[],
		  
		  dpselshow:false,
		  dpdata:'',
		  dpid:0,
		  dpname:'',
		  
		  rqshow:false,
		  rqkstime:0,
		  rqjstime:0,
		  tjdata:[],
		  
		    thdpdata:[],
		  
      }
    },
	
	onLoad(){
		let that=this;
		this.loadData();
	},
	onShow(){
		let that=this;
		
		
		let thygid=uni.getStorageSync('thygid');
		let thygname=uni.getStorageSync('thygname');
		
		if(thygid){
			this.ygid=thygid;
			this.ygname=thygname;
			this.loadData();
			uni.setStorageSync('thygid','');
			uni.setStorageSync('thygname','');
		}
		if (uni.getStorageSync('thupdpkb') == 1) {
			this.ygid=0;
			this.ygname='';
			this.loadData();
			uni.setStorageSync('thupdpkb', 0)
		}
		
	},
	
	mounted() {
	    
	 },
	
	computed: { 
	
	},
	
    methods: {
		
		gotobosskb(){
			 uni.navigateTo({
				url:'/pages/work/bosskb/index'
			 })
		},
		gotodpzz(){
			 uni.navigateTo({
				url:'/pages/work/dpzz/index'
			 })
		},
		selyg(){
			 uni.navigateTo({
				url:'/pages/selyg/index?stype=11'
			 }) 
		},
		gotourl(surl){ 
			let dpid=this.dpid;
			let dpname=this.dpname;
			let ygid=this.ygid;
			let ygname=this.ygname;
			uni.navigateTo({
				url:surl+'&dpid='+dpid+'&dpname='+dpname+'&ygid='+ygid+'&ygname='+ygname
			})
		},
		gotolink(surl){
			let dpid=this.dpid;
			let dpname=this.dpname;
			let ygid=this.ygid;
			let ygname=this.ygname;
			uni.navigateTo({
				url:surl+'?dpid='+dpid+'&dpname='+dpname+'&ygid='+ygid+'&ygname='+ygname
			})
		}, 
		
		gotoygcwrcd(e){
			let type=e.currentTarget.dataset.type;
			let ygid=this.ygid;
			uni.setStorageSync('thygid',this.ygid)
			uni.setStorageSync('thygname',this.ygname)
			if(this.isdpkb==1){
				uni.navigateTo({
					url:'/pages/work/yggl/ygrcd/cwrcd?ygid='+this.ygid+'&ygname='+this.ygname+'&type='+type
				})
			}else{
				uni.navigateTo({
					url:'/pages/user/caiwu/index?type='+type
				})
			}
		},
		gotoygzjrcd(e){
			let type=e.currentTarget.dataset.type;
			let ygid=this.ygid;
			uni.setStorageSync('thygid',this.ygid)
			uni.setStorageSync('thygname',this.ygname)
			if(this.isdpkb==1){
				uni.navigateTo({
					url:'/pages/work/yggl/ygrcd/zjrcd?ygid='+this.ygid+'&ygname='+this.ygname+'&type='+type
				})
			}else{
				uni.navigateTo({
					url:'/pages/user/zjrcd/index?type='+type
				})
			}
		},
		gotodpcwrcd(e){
			let type=e.currentTarget.dataset.type;
			uni.navigateTo({
				url:'/pages/work/bosskb/dpcwrcd?dpid='+this.dpid+'&dpname='+this.dpname+'&type='+type
			})
		},
		gotodpczzjrcd0(e){
			let type=e.currentTarget.dataset.type;
			uni.navigateTo({
				url:'/pages/work/bosskb/dpzijinrcd?dpid='+this.dpid+'&dpname='+this.dpname+'&type='+type
			})
		},
		
		seldianpu() {
			this.dpselshow = true;
		},
		selthdp(e) {
			let that=this;
			let dpid = e.currentTarget.dataset.dpid;
			let dpname = e.currentTarget.dataset.dpname;
		    this.dpid=dpid;
		    this.dpname=dpname;
			this.loadData();
			this.dpselshow = false;
		},
		
		clsdpid(){
			let that=this;
			this.dpid=0;
			this.dpname='';
			this.loadData();
			this.dpselshow = false;
		},
		clsygid(){
			let that=this;
			this.ygid=0;
			this.ygname='';
			this.loadData();
			this.dpselshow = false;
		},
		
		loadData() {
			let that=this;
			let da={ 
				ygid:that.ygid ? that.ygid : 0,
				dpid:that.dpid ? that.dpid : 0,
				rqkstime:that.rqkstime ? that.rqkstime : 0 ,
				rqjstime:that.rqjstime ? that.rqjstime : 0 ,
			};
			uni.showNavigationBarLoading();
			this.$req.post('/v1/kanban/index', da)
			      .then(res => {
			         console.log(1222,res);
				    uni.hideNavigationBarLoading(); 
					if(res.errcode==0){
							that.tjdata = res.data.tjdata;
							that.dpdata = res.data.dpdata;
							that.thdpdata = res.data.thdpdata;
							that.dpid = res.data.thdpdata.id;
							that.dpname = res.data.thdpdata.title;
							that.isbskb = res.data.isbskb;
							that.isdpkb = res.data.isdpkb;
							that.ygdata = res.data.ygdata;
							if(res.data.isdpkb==0){
								that.ygid = res.data.ygid;
								that.ygname = res.data.ygname;
							}
					}else{
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
					}
			      })
		},

		rqmodal(){
			this.rqshow=true
		},
		rqmodalchange(e){
			let that=this;
			if(e.startDate && e.endDate){
				this.thkw='';
				this.rqkstime=e.startDate;
				this.rqjstime=e.endDate;
			    this.loadData();
			}
		},
		rqclssearch(){
			let that=this;
			this.rqkstime='';
			this.rqjstime='';
			this.loadData();
		},
		
      // 跳转
      tn(e) {
        uni.navigateTo({
          url: e,
        });
      },
	  
	 
	
	
	
	
    
    }
  }
</script>

<style lang="scss" scoped> 

.itop{padding:0 32upx}
.itop .if1{position: absolute;left:30upx;z-index:200;}
.itop .if1 .sname{font-size:36upx;font-weight: 600;}
.itop .if2{position: absolute;right:0;z-index: 200;}
.itop .if2 .smicon{float:right;margin-right:30upx}
.itop .if2 .smicon text{font-size:44upx;color:#333333}


.kbtops{margin:0 32upx}
.kbtops .sf1{float:left}
.kbtops .sf2{float:right}
.kbtops .wpcksel{font-size:24upx}
.kbtops .wpcksel .iconfont{font-size:24upx;}

.ddfltj{margin:0 20upx}
.ddfltj .item{float:left;width:50%;}
.ddfltj .item .more{position: absolute;right:20upx;top:20upx;color:#888}
.ddfltj .item .con{padding:30upx;text-align: center;background:#fff;margin:10upx;border-radius:16upx;position: relative;}
.ddfltj .item .icon{}
.ddfltj .item .icon image{width:120upx;height:120upx;}
.ddfltj .item .tjtxt{font-size: 24rpx;height:30upx;line-height:30upx;color:#888;margin-top:10upx}
.ddfltj .item .tjnum{font-size: 32rpx;height:50upx;line-height:50upx;}

  .top-backgroup {
	  position: fixed; top:0;left:0;	 width: 100%;    height: 450upx;    z-index: -1;
    .backgroud-image {width: 100%;  height: 450upx;}
  }

.ttrrss{margin-right:30upx;font-size:24upx}
.ttrrss .iconfont{margin-right:10upx;}

.tjtiao{background:#fff;color:#333;font-size:24upx;padding:10upx 30upx;margin-bottom:20upx;border-radius:10upx;}
.tjtiao .ssgjc{font-size:24upx;}


.kbmain{margin:32upx;}
.sxtiao{height: 100rpx;line-height:100upx;background: #FFFFFF;border-radius: 20rpx;margin:0 32upx 0 32upx;display: flex;text-align: center;font-size:28upx}
.sxtiao .ite{flex: 1;}
.sxtiao .ite text{margin-left:10upx;font-size:20upx;}




.maxcon{background:#fff;border-radius: 20rpx;margin:0 32upx 20upx 32upx;padding:20upx}
.maxcon.pdum0{padding:0 20upx 0 20upx}



.tjzong{border-bottom:1px solid #F3F3F3;padding:40upx 0;padding-left:80upx}
.tjzong .sz{font-weight: 600;font-size:56upx}
.tjzong .txt{color: #7F7F7F;margin-top:15upx;font-size:28upx}

.tbtjcon{}
.tbtjcon .tjxm{float:left;padding-left:70upx;}
.tbtjcon .tjxm .xmcon{padding:15upx 0}
.tbtjcon .tjtb{float:right;padding-top:20upx;padding-right:20upx}
.tbtjcon .tjtb image{width:210upx;}
.tbtjcon .sz{font-weight: 600;font-size:36upx}
.tbtjcon .txt{color: #7F7F7F;margin-bottom:15upx;font-size:28upx;position: relative;}
.tbtjcon .txt .iconfont{font-size:45upx;position: absolute;left:-45upx;top:-4upx}
.tbtjcon .txt .ys1{color:#1677ff}
.tbtjcon .txt .ys2{color:#54d216}
.tbtjcon .txt .ys3{color:#ff2828}
.tbtjcon .txt .ys4{color:#ffd427}

.maxcon{background:#fff;border-radius: 20rpx;margin-bottom:20upx;position: relative;}
.maxcon.pdum0{padding:0 20upx 0 20upx}
  
  .qhdps{background:#fff;padding:20upx 40upx;position: relative;font-size:32upx;border-bottom:1px solid #F3F3F3;border-radius:20upx 20upx 0 0;}
  .qhdps .icon{position: absolute;right:40upx;top:40upx;}
  .qhdps .icon .iconfont{font-size:40upx}
  .qhdps .logo{float:left;}   
  .qhdps .tit{float:left;height:80upx;line-height:80upx;margin-left:20upx;} 
  .qhdps .logo image{width:80upx;height:80upx;border-radius:100upx;display: block;}
  
  
  .tjitem{border-bottom:1px solid #F3F3F3;padding:20upx 0;}
  .tjitem .ite{width:50%;float:left;position:relative}
  .tjitem .ite.gx:after {position:absolute;top:35%;right:0;content:'';width:1px;height:45%;-webkit-transform: scaleX(0.5);transform: scaleX(0.5);border-right: 1px solid #D9D9D9;}
  
  .tjitem .txt{color: #7F7F7F;margin-bottom:15upx;font-size:28upx}
  .tjitem .sz{font-weight: 600;font-size:36upx}
  .tjitem.noline{border-bottom:0}
  .tjitem .cons{padding-left:40upx;}
   
  .mxicon{margin-left:10upx;}
  .tjzong{border-bottom:1px solid #F3F3F3;padding:40upx 0;padding-left:40upx;position: relative;}
  .tjzong .sz{font-weight: 600;font-size:56upx}
  .tjzong .txt{color: #7F7F7F;margin-top:15upx;font-size:28upx}
  .fyzcbtn{position: absolute;top:50upx;right:40upx;height:70upx;line-height:70upx;border-radius:100upx;padding:0 20upx;background: #FFD427;font-size:24upx;}  
  
  .tline{position: absolute;left:0;top:43upx;width:10upx;height:30upx;background:#FFD427 ;}
  
</style>
