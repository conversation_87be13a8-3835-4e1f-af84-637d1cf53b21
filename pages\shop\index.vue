<template>
	<view>




		<!--左侧栏-->
		<scroll-view class='scroll_left' scroll-y="true"  >
			
	
			
			<view class="nav_left">
				<view class="nav_left_items" :class="pid == 0 ? 'active' : ''" @tap="swtab" :data-pid="0">
					<view class="con" :style="pid == 0 ? 'color:'+mbjson.ztcolor+';border-left-color: '+mbjson.ztcolor : ''" >全部商品</view>
				</view>
			
				<block v-for="(item,index) in sortdata" :key="index">
					<view class="nav_left_items" :class="pid == item.id ? 'active' : ''" @tap="swtab"
						:data-pid="item.id">
						<view class="con" :style="pid == item.id ? 'color:'+mbjson.ztcolor+';border-left-color: '+mbjson.ztcolor : ''">{{item.sortname}}</view>
					</view>
				</block>
			</view>
		</scroll-view>
		
			<view class="navbar" >
				<view class="barcons">
					<view class="nav-item" :class="issc==1 ? 'current' : ''" @tap="swpx" :data-issc="issc == 1 ? 0 : 1 ">
						常购清单
						<view class="p-box">
							<text :class="{active:issc==1}" class="iconfont icon-shoucang5"></text>
						</view>
					</view>
					<view class="nav-item" :class="pxfs==1 ? 'active' : ''" @tap="swpx" :data-pxfs='1'>
						综合排序
						<view class="p-box">
							<text :class="{active:pxfs==1}" class="iconfont icon-shangxiajiantou"></text>
						</view>
					</view>
					<view class="nav-item" :class="pxfs==3 ? 'active' : ''" @tap="swpx" :data-pxfs='3'>
						<text>价格排序</text>
						<view class="p-box">
							<text :class="{active:pxfs==3}" class="iconfont icon-shangxiajiantou"></text>
						</view>
					</view>
					<text class="cate-item iconfont icon-shaixuan" ></text>
				</view>
				<view class="searchcon" >
					<view class="ssicon"><i class="iconfont icon-sousuo3"></i></view>
				    <view class='search'>
				        <input type='text'  confirm-type="search" @confirm="confirmsearch" placeholder='名称/编号/简称/条形码' v-model="kw" ></input>
				    </view>
				</view>
			</view>
			
		
		<scroll-view scroll-y="true" @scrolltolower="loaditems" class="scroll_right sv"
			:style="{height:SrollHeight+'px'}" :scroll-top="ScrollTop">



			
			

			<block v-if="pid > 0 && zifldata">
				<view class="topfl">
					<view class="con">
						<scroll-view scroll-x="true" style=" width: 100%;height:100%;white-space: nowrap;">
							<view class="item" :class="sortid==0 ? 'active' : ''" @tap="swtab" :data-pid="pid"
								data-sortid="0"
								:style="sortid == 0 ? 'background: '+mbjson.ztcolor : ''"
								><span>全部</span></view>
							<block v-for="(item,index) in zifldata" :key="index">
								<view class="item" :class="sortid==item.id ? 'active' : ''" @tap="swtab" :data-pid="pid"
									:data-sortid="item.id"  :style="sortid == item.id ? 'background: '+mbjson.ztcolor : ''"><span>{{item.sortname}}</span></view>
							</block>
							<view class="clear"></view>
						</scroll-view>
					</view>
				</view>
			</block>

			<view class="fuwuli">

				<block v-if="listdata.length>0">
					<block v-for="(item,index) in listdata" :key="index">
						<view class="item" @tap="viewsp" :data-spid="item.spid">
							<view class="tu">
								<image :src='item.sppic ? staticsfile + item.sppic : defaultpic'></image>
							</view>
							<view class="xx">
								<view class="t1">{{item.spname}}</view>
								<view class="t2">{{item.sortname}}</view>
								<block v-if="isvjg==1">
									<view class="t3">
										￥
										<span v-if="vdhjia==1">{{item.vjiage}}</span>
										<span class="vsj" v-if="vshijia==1" :class="vdhjia==0 ? 'vsj2' : ''">{{item.shijia}}</span>
										{{item.fxdwtxt ? '/'+item.fxdwtxt : ''}}
									</view>
								</block>
								<view class="t4"></view>
							</view>
							<view class="addbtndd"><i class="iconfont " :class="mbjson.jiaicon"></i></view>
							<view class="clear"></view>
						</view>
					</block>
					<text class="loading-text" v-if="isloading">
						{{loadingType === 0 ? contentText.contentdown : (loadingType === 1 ? contentText.contentrefresh : contentText.contentnomore)}}
					</text>
				</block>
				<block v-else>

				</block>
				<view style="height:180upx"></view>
			</view>



		</scroll-view>

		<view class="cont_bot">
			<view class="contcon">
				<view class="ma1">
					<view @tap='gotogwc'>
						<view class="icon"><i class="iconfont icon-gouwucheman1" :style="'color:'+mbjson.ztcolor"></i></view>
						<view class="gwc">
							已选 <span> {{gwcinfo.gwcnum}} </span> 件,
						</view>
						<view class="count_price">￥<span>{{gwcinfo.gwczjine}}</span></view>
						<view class="clear"></view>
					</view>
				</view>
				<view class="ma2">
					<view class='submit' @tap='createOrder'>选好了</view>
				</view>
				<view class="clear"></view>
			</view>
		
		</view>




		<tui-drawer mode="bottom" :visible="visible" @close="closeDrawer" backgroundColor="none">
			<view class="drawer_boxma">
				<view class='closebtn' @click="closeDrawer"><i class='iconfont icon-close'></i></view>

				<view class="gmtop">
					<view class="favicon" :class="isysc==1 ? 'ysc' : ''" @tap="spaddfav" :data-spid="viewdata.id"><i
							class="iconfont icon-shoucang5"></i></view>
					<view class="imgtu">
						<image :src="staticsfile + viewdata.smallpic" 
							:data-smallpic="viewdata.smallpic"></image>
					</view>
					<view class="xx">
						<view class="tit">{{viewdata.title}}</view>
						<view class="ftit">分类：{{viewdata.sortname}}</view>
						<view class="ftit">品牌：{{viewdata.pptxt}}</view>
					</view>
					<view class="clear"></view>
				</view>
				<view class="sctop">
					<view class="xanav">
						<view class="xanav-item" :class="vspms==0 ? 'active' : '' " @tap='swtabvspms' :data-vspms="0">
							<span>商品订货</span>
						</view>
						<view class="xanav-item" :class="vspms==1 ? 'active' : '' " @tap='swtabvspms' :data-vspms="1">
							<span>商品详情</span>
						</view>
					</view>
				</view>
				<view class="drawer_c">



					<view v-show="vspms==0">
						<scroll-view scroll-y="true" :style="{height:SrollHeightaa+'px'}">
							<view class="spggcon">
								<block v-for="(item,index) in gwctemp" :key="index">
									<view class="gglicc" :class="item.shuliang > 0 ? 'yxz'  : '' ">
										<view class="gg">{{item.ggname}}</view>
										<view class="bh">{{item.gghao}}</view>
										<view class="dj">订货价：<span>￥{{item.danjia}}</span></view>
										<view class="zjine">￥{{item.zjine}}</view>
										<view class="dw">
											<block v-for="(itemx,indexx) in item.jldwarr" :key="indexx">
												<view class="ite" :class="itemx.isSel ? 'hoverss' : ''" @tap="seldw"
													:data-sid="item.id" :data-indx="itemx.indx" :data-txt="itemx.txt">
													{{itemx.txt}}
												</view>
											</block>
											<view class="clear"></view>
										</view>
										<view class="slcon">
											<uni-number-box class="step" :min="0" :max="item.kucun"
												:value="item.shuliang > item.kucun ? item.kucun : item.shuliang"
												:isMax="item.shuliang >= item.kucun ? true : false"
												:isMin="item.shuliang===1"
												:index="index" :sid="item.id"  @eventChange="numberChange">
											</uni-number-box>
										</view>

									</view>
								</block>
							</view>
						</scroll-view>
						<!-- #ifdef H5 -->
						<view style="height:90upx"></view>
						<!-- #endif -->
						<view class="addgwcslcon">
							<view class="af1">
								合计数量：<span>{{hjshuliang}}</span> , 合计金额：<span>￥{{hjzjine}}</span>
							</view>
							<view class="af3">
								<view class="tjbtn" @tap="addgwcspv" :data-spid="viewdata.id">
									<view class="con">
										<view class="zcbtn">加入购物车</view>
									</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>


					<view v-show="vspms==1">
						<scroll-view scroll-y="true" :style="{height:SrollHeightaa+'px'}">
							<view class="contt">
								<jyf-parser :html="html" ref="article"></jyf-parser>
							</view>
						</scroll-view>
					</view>




				</view>
			</view>
		</tui-drawer>








	</view>
</template>

<script>
	import uniNumberBox from '@/components/uni-number-box.vue'
import { computed } from "vue";
	var _self;
	export default {
		created() {
			_self = this
		},
		components: {
			uniNumberBox
		},
		data() {
			return {
				mbjson:'',
				gwcinfo: {gwcnum:0,gwczjine:0},
				issc: 0,
				vspms: 0,
				hjzjine: 0,
				hjshuliang: 0,
				visible: false,
				headerPosition: "fixed",
				headerTop: "0px",
				ScrollTop:0,
				isysc: 0,
				pxfs: 1,
				pid: 0,
				pname: '',
				sortid: 0,
				kw: '',
				SrollHeight: 200,
				SrollHeightaa: 200,
				SrollHeightbb: 200,
				staticsfile: this.$staticsfile,
				CdSrollHeight: 200,
				hidsscover: true,
				spid: 0,
				showModalStatus: false,
				num: 1,
				minusStatus: 'disabled',
				kucun: 0,
				gwcnum: 0,
				zongjia: 0,
				sortdata: '',
				zifldata: '',
				page: 0,
				isloading: false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata: [],
				viewdata: '',
				gwctemp: '',
				isdgg: 0,
				shijia: 0,
				vjiage: 0,
				vggstr: '',
				vkucun: 0,
				daggnum: 0,
				ggarr: '',
				yxggid: 0,
				yxggstr: '',
				vggid: 0,
				vggstr: '',
				isvjg:0,
				vdhjia:0,
				vshijia:0,
				html:''
			}
		},
		onShareAppMessage(res) {
					return {
					  title: '',
					  path: ''
					}
		},
		onLoad(options) {
			let that = this;
			let spid = options.spid;
			let issc = options.issc;
			let vsortid = options.vsortid;
			let vpid = options.vpid;
			let kw = options.kw;
			that.spid = spid ? spid : 0
			that.issc = issc ? issc : 0
			that.vpid = vpid ? vpid : 0
			that.vsortid = vsortid ? vsortid : 0
			that.kw = kw ? kw : ''
		},
		onShow() {
			let that = this;
			let spid=that.spid;
			let issc=that.issc;
		
			that.loadData();
		
			if(spid){
				that.viewsp('',spid);
			}
			if(issc==1){
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
			
			}
			if(that.vpid!=0 || that.vsortid!=0){
				that.issc = 0;
				that.kw = '';
				that.sortid = that.vsortid ? that.vsortid : 0;
				that.pid = that.vpid;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.sortad();
			}
			if(this.$tool.getMudataStorage){
			
				
			}
		
			
			
		},
		onReady: function() {
			let that = this;
			let SrollHeight = uni.getSystemInfoSync().windowHeight - 1;
			let CdSrollHeight = uni.getSystemInfoSync().windowHeight - 50;
			let SrollHeightaa = uni.getSystemInfoSync().windowHeight - 250;

			that.SrollHeight = SrollHeight;
			that.CdSrollHeight = CdSrollHeight;
			that.SrollHeightaa = SrollHeightaa;

		},

		methods: {
			seldw(e) {
				let that = this;
				let sid = e.currentTarget.dataset.sid; //购物车TEMPID
				let indx = e.currentTarget.dataset.indx;
				let txt = e.currentTarget.dataset.txt;
				let da = {
					field: 'jldw',
					sid: sid,
					value: indx
				};
				that.upgwtempdata(da);
			},
			//数量
			numberChange(data) {
				let that = this;
				let da = {
					field: 'shuliang',
					sid: data.sid,
					value: data.number
				};
				that.upgwtempdata(da);
			},
			upgwtempdata(da) {
				let that = this;
				// uni.showNavigationBarLoading();
				this.$req.post('/v2/dinghuo/shopview/gwctmpupfieldv1', da)
					.then(res => {
						// uni.hideNavigationBarLoading();
					
						if (res.errcode == 0) {
							that.gwctemp = res.data.gwctemp;
							that.hjzjine = res.data.hjzjine;
							that.hjshuliang = res.data.hjshuliang;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
			},


			addgwcspv(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let da = {
					spid: spid,
				};
				uni.showLoading();
				this.$req.post('/v2/dinghuo/shopview/addgwcspv', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.gwcinfo = res.data.gwcinfo;
										that.visible = false;
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			viewsp(e,vspid=0) {
				let that = this;
				let spid=0;
				if(vspid!=0){
					spid=vspid;
				}else{
					spid = e.currentTarget.dataset.spid;
				}
				that.spid = spid;
				that.gwctemp = '';
				let da = {
					id: spid
				};
				uni.showLoading(); 
				this.$req.post('/v2/dinghuo/shopview/view', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							let isdgg = res.data.viewdata.isdgg;
							that.hjzjine = 0;
							that.hjshuliang = 0;
							that.viewdata = res.data.viewdata;
							that.kucun = res.data.viewdata.kucun;
							that.vkucun = res.data.viewdata.kucun;
							that.vjiage = res.data.viewdata.vjiage;
							that.shijia = res.data.viewdata.shijia;
							that.isdgg = res.data.viewdata.isdgg;
							that.isysc = res.data.isysc;
							that.gwctemp = res.data.gwctemp;
							let SrollHeightaa = that.SrollHeight - 200;
							that.SrollHeightaa = SrollHeightaa;
							this.$refs.article.setContent(res.data.viewdata.content);
							that.visible = true;
							that.vspms = 0;
						} else {
							
							if(res.errcode!=901){
								uni.showModal({
									title: '错误提示',
									content: res.msg,
									showCancel: false,
									success() {
										that.visible = false;
									}
								})
							}
						}
					})
				this.spid = 0;
			},
			closeDrawer() {
				this.visible = false;
			},
			swtabvspms: function(e) {
				let that = this;
				let vspms = e.currentTarget.dataset.vspms;
				that.vspms = vspms;
			},
			swtab(e) {
				let that = this;
				let pid = e.currentTarget.dataset.pid;
				let sortid = e.currentTarget.dataset.sortid;
				that.issc = 0; 
				that.kw = '';
				that.sortid = sortid ? sortid : 0;
				that.pid = pid;
			
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.sortad();
				that.loaditems();
			},
			swpx: function(e) {
				let that = this;
				let pxfs = e.currentTarget.dataset.pxfs
				let issc = e.currentTarget.dataset.issc;
				
				that.issc = issc;
				
				that.kw = '';
				that.sortid = that.sortid ? that.sortid : 0;
				that.pid = that.pid;
				that.pxfs = pxfs;
				that.page = 0;
				that.listdata = [];
				that.isloading = false;
				that.loadingType = -1;
				that.loaditems();
			},

			loadData() {
				let that = this;
				let da = {
					tb: 30,
					parentid: 0,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v2/dinghuo/shop/index', da)
					.then(res => {
						uni.hideNavigationBarLoading();
						
						if (res.errcode == 0) {
							that.sortdata = res.data.sortdata;
							that.gwcinfo = res.data.gwcinfo;
							that.isvjg = res.data.isvjg;
							that.vshijia = res.data.vshijia;
							that.vdhjia = res.data.vdhjia;
							
							let mbjson=res.data.mbjson;
							that.mbjson = mbjson;
							uni.setNavigationBarColor({
							  frontColor: mbjson.tbtxtcolor,
							  backgroundColor: mbjson.tbbgcolor
							});
							uni.setStorageSync('mbjson', mbjson);
							   
							   uni.setTabBarStyle({
							     selectedColor: mbjson.tabbarhovcolor,
							   })
							   let tabbarhovcs=mbjson.tabbarhovcs;
							   uni.setTabBarItem({
							     index: 0,
							     selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-home-current.png',
							   })
							   uni.setTabBarItem({
							     index: 1,
							     selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-cate-current.png',
							   })
							   uni.setTabBarItem({
							     index: 2,
							     selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-cart-current.png',
							   })
							   uni.setTabBarItem({
							     index: 3,
							     selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-tz-current.png',
							   })
							   uni.setTabBarItem({
							     index: 4,
							     selectedIconPath: 'static/images/tabbar'+tabbarhovcs+'/tab-my-current.png',
							   })
							   
							uni.setTabBarBadge({
							   index: 2,
							   text: res.data.gwcinfo.gwcnum
							});
							this.loaditems();
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
			},

			spaddfav(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let da = {
					spid: spid,
				};
				uni.showLoading();
				this.$req.post('/v2/dinghuo/muser/spaddfav', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function() {
										that.isysc = res.data.isysc;
									}, 1000);
								}
							});
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			sortad() {
				let that = this;
				let da = {
					tb: 30,
					pid: that.pid,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v2/dinghuo/com/getsortad', da)
					.then(res => {
						uni.hideNavigationBarLoading();
						if (res.errcode == 0) {
							that.pname = res.data.pname;
							that.zifldata = res.data.zifldata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
					})
			},

			// 上拉加载 
			loaditems: function() {
				let that = this;
				let page = ++that.page;
				let da = {
					issc: that.issc,
					kw: that.kw,
					page: page,
					pxfs: that.pxfs,
					sortid: that.sortid > 0 ? that.sortid : that.pid
				}
				if (page != 1) {
					that.isloading = true;
				}
				if (that.loadingType == 2) {
					return false;
				}
			
				uni.showNavigationBarLoading();
				this.$req.get('/v2/dinghuo/shop/list', da)
					.then(res => {
					
						if (res.data.listdata.length > 0) {
							that.listdata = that.listdata.concat(res.data.listdata);
							that.loadingType = 0
						} else {
							that.loadingType = 2
						}
						uni.hideNavigationBarLoading();
					})
			},
			gotoview(e) {
				let that = this;
				let id = e.currentTarget.dataset.id;
			
				uni.navigateTo({
					url: './view?id=' + id
				})
			},
			gotogwc(){
				uni.switchTab({
					url: '/pages/cart/cart'
				})
			},
			createOrder() {
				uni.navigateTo({
					url: '/pages/cart/ordersqr'
				})
			},
			confirmsearch:function(e){
				let that=this;
				that.kw=e.detail.value;
				that.page=0; 
				that.listdata=[];
				that.isloading=false,
				that.loadingType=-1,
				that.sxstr='', 
				that.loaditems(); 
			},


		}
	}
</script>

<style lang='scss'>
	page {
		background: #fff
	}


	.gglicc {
		border: 1px solid #ddd;
		margin-bottom: 22px;
		position: relative;
		padding: 15px;
		border-radius: 3px;
		line-height: 22px;
	}

	.gglicc.yxz {
		border: 1px solid #f53939;
	}

	.gglicc.yxz .gg {
		color: #f53939
	}

	.gglicc .slcon {
		position: absolute;
		right: 15px;
		top: 15px;
	}

	.gglicc .gg {
		position: absolute;
		left: 10px;
		top: -16px;
		background: #fff;
		font-weight: 700;
		font-size: 15px;
		padding: 5px;
	}

	.gglicc .bh {
		font-size: 15px;
	}

	.gglicc .dj {
		margin-bottom: 8px;
	}

	.gglicc .dj span {
		font-size: 14px;
		color: #ff0000;
	}

	.gglicc .zjine {
		font-size: 14px;
		color: #ff0000;
		position: absolute;
		right: 15px;
		top: 75px
	}

	.gglicc .dw {}

	.gglicc .dw .ite {
		float: left;
		border: 1px solid #eee;
		padding: 5px 10px;
		margin-right: 10px;
		font-size: 12px;
		border-radius: 3px
	}

	.gglicc .dw .ite.hoverss {
		border: 1px solid #f53939;
		background: #fff3f3;
		color: #f53939
	}


	.spggcon {
		padding: 20upx 30upx 100upx 30upx;
		position: relative;
	}

	.addgwcslcon {
		border-top: 1px solid #eee;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 10px 0;
		background: #fff;
		z-index: 100;
		/* #ifdef H5 */
		bottom: 100upx;
		/* #endif */
	}

	.addgwcslcon .af1 {
		float: left;
		line-height: 32px;
		padding-left: 30upx;
		font-size: 13px;
	}

	.addgwcslcon .af1 span {
		font-size: 14px;
		color: #ff0000
	}

	.addgwcslcon .af3 {
		float: right;
		padding-right: 30upx
	}

	.addgwcslcon .txt {
		float: left;
		padding-top: 3px;
	}

	.addgwcslcon .txt2 {
		float: left;
		margin-left: 10px;
	}

	.addgwcslcon .tjbtn {
		height: 32px;
		line-height: 32px;
		background: #f62e30;
		color: #fff;
		font-size: 14px;
		border-radius: 100px;
		padding: 0 15px;
	}


	.navbar {
		position: fixed;
		right: 0;
		top: var(--window-top);
		/* #ifdef H5 */
		 top: 45px;
		/* #endif */
		width: 75%;
		height: 160upx;
		background: #fff;
		box-shadow: 0 2upx 10upx rgba(0, 0, 0, .09);
		z-index: 10;
		
		.searchcon{background:#fff;height:80upx;border-bottom:0px solid #ddd;position: relative;}
		.searchcon .ssicon{position: absolute;right:10px;height:80upx;line-height:80upx;} 
		.searchcon .search{}
		.searchcon .search input{text-align: left;width:calc(100% - 20px);padding:0 10px;background:#fff;height:75upx;line-height:75upx;}
		
		
			
		
        .barcons{display: flex;height:80upx;border-bottom:1px solid #eee}
		.nav-item {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			/* font-size: 30upx; */
			color: $font-color-dark;
			position: relative;

			&.current {
				color: $base-color;

			/* 	&:after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translateX(-50%);
					width: 120upx;
					height: 0;
					border-bottom: 4upx solid $base-color;
				} */
			}
		}
		
		

		.p-box {
			display: flex;
			flex-direction: column;

			.iconfont {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30upx;
				height: 14upx;
				line-height: 1;
				margin-left: 4upx;
				font-size: 26upx;
				color: #888;

				&.active {
					color: $base-color;
				}
			}

			.xia {
				transform: scaleY(-1);
			}
		}

		.cate-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			width: 80upx;
			position: relative;
			font-size: 44upx;

			&:after {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				border-left: 1px solid #ddd;
				width: 0;
				height: 36upx;
			}
		}
	}


	.topfl {
		font-size: 12px;
		padding: 8px;
		border-bottom: 1px solid #efefef;
	}

	.topfl .con {}

	.topfl .item {
		height: 23px;
		line-height: 23px;
		display: inline-block;
		padding: 0 9px;
		background: #f4f5f7;
		margin-right: 10px;
		border-radius: 2px;
	}

	.topfl .item.active {
		background: #fa436a;
		color: #fff;
	}

	.pxaa {
		border-bottom: 1px solid #efefef;
		text-align: center;
		font-size: 13px;
		height: 31px;
		line-height: 31px;
		margin-top: 5px
	}

	.pxaa .item {
		float: left;
		width: 25%;
	}

	.pxaa .item.active {
		color: #fa436a;
	}

	.nav_left {
		background: #f4f4f4;
		display: inline-block;
		width: 100%;
		height: 100%;
		text-align: center;
		left: 0;
		top: 0;
	}

	.nav_left .nav_left_items {
		height: 80upx;
		line-height: 80upx;
		border-bottom: 0px solid #eee;
		font-size: 26upx;
		color: #101010;
	}

	.nav_left .nav_left_items .con {}

	.nav_left .nav_left_items.active {
		background: #fff;
	}

	.nav_left .nav_left_items.active .con {
		background: #fff;
		border-left: 8upx solid #fa436a;
		color: #fa436a;
		font-weight: 700;
	}

	.nav_left .nav_left_items .tb {}

	.nav_left .nav_left_items .tb .txt {
		text-align: center
	}



	.scroll_left {
		width: 25%;
		height: 100%;
		background: #fff;
		text-align: center;
		position: fixed;
		left: 0;
		top: 0px;
		/* #ifdef H5 */
		 top: 45px;
		/* #endif */
	}

	.scroll_right {
		position: fixed;
		top: 73px;
		right: 0;
		overflow: auto;
		flex: 1;
		width: 75%;
		height: 100%;
		padding: 20upx 10upx;
		box-sizing: border-box;
		/* #ifdef H5 */
		  top: 119px;
		/* #endif */
	}

	.fuwuli {
		margin: 20upx;
	}

	.fuwuli .item {
		margin-bottom: 30upx;
		background: #fff;
		border-radius: 5px;
		position: relative;
		font-size: 12px;
		color: #333;
		border-bottom: 1px solid #efefef;
		padding-bottom: 30upx;
	}

	.fuwuli .item .tu {
		float: left;
	}

	.fuwuli .item .tu image {
		width: 80px;
		height: 80px;
		display: block;
		border-radius: 5px;
		border: 1px solid #ddd;
	}

	.fuwuli .item .xx {
		float: right;
		width: calc(100% - 92px)
	}

	.fuwuli .item .xx .t1 {
		height: 27px;
		line-height: 27px;
		overflow: hidden;
		color: #333;
		font-size: 13px;
	}

	.fuwuli .item .xx .t2 {
		height: 20px;
		line-height: 20px;
		overflow: hidden;
		color: #999;
		font-size: 12px;
		margin-bottom: 5px
	}

	.fuwuli .item .xx .t3 {}

	.fuwuli .item .xx .t3 span {
		color: #ff0000;font-size:14px
	}

	.fuwuli .item .xx .t3 .vsj{color:#888;font-size:12px;margin-left:3px;font-weight:400;text-decoration:line-through;}
	.fuwuli .item .xx .t3 .vsj.vsj2{color:#ff0000;font-size:14px;text-decoration:none;}

	.fuwuli .item .xx .t4 {
		margin-top: 1px;
		color: #888
	}

	.fuwuli .item .addbtndd {
		position: absolute;
		right: 0;
		bottom: 30upx;
		color: #b3b3b3;
		font-size: 12px;
	}

	.fuwuli .item .addbtndd i {
		font-size: 28px;
		color: #fa436a
	}


	.sortad {
		margin: 0 20upx 20upx 20upx
	}

	.sortad image {
		width: 100%;
		border-radius: 5px;
		display: block
	}

	.hxstxt {
		text-align: center;
		font-size: 15px;
		margin: 20px 0;
		font-weight: 700
	}

	.hxstxt:before,
	.hxstxt:after {
		content: "";
		width: 20px;
		border-top: 1px solid #ddd;
		display: inline-block;
		vertical-align: middle;
	}

	.hxstxt:before {
		margin-right: 10px;
	}

	.hxstxt:after {
		margin-left: 10px;
	}

	.ziflcon {}

	.ziflcon .item {
		float: left;
		margin-left: 15upx;
		margin-bottom: 15upx;
		padding: 0 10px;
		height: 80upx;
		line-height: 80upx;
		background: #f1f1f6;
		text-align: center;
		font-size: 12px;
		border-radius: 3px
	}

	.ziflcon .item.active {
		color: #fa436a;
		font-weight: 700
	}



	.cont_bot {
		position: fixed;
		left: 30upx;
		bottom: 30upx;
		z-index: 95;
		display: flex;
		align-items: center;
		width: 690upx;
		height: 100upx;
		padding: 0 30upx;
		background: rgba(255, 255, 255, .9);
		box-shadow: 0 0 20upx 0 rgba(0, 0, 0, .5);
		border-radius: 16upx;
		
		/* #ifdef H5 */
		bottom: 130upx;
		/* #endif */
		
	}

	.cont_bot .contcon {
		width: 100%;
	}

	.cont_bot .ma1 {
		float: left;
		font-size: 12px;
		line-height: 90upx;
	}

	.cont_bot .ma1 .icon {
		float: left;
		margin-right: 5px;
		padding-top: 2px
	}

	.cont_bot .ma1 .icon i {
		color: #fa436a;
		font-size: 22px;
	}

	.cont_bot .ma1 .gwc {
		float: left;
	}

	.cont_bot .ma1 .gwc span {
		font-size: 15px;
		color: #ff0000
	}

	.cont_bot .ma1 .count_price {
		float: left;
	}

	.cont_bot .ma1 .count_price span {
		font-size: 15px;
		color: #ff0000
	}

	.cont_bot .ma2 {
		float: right;
		padding-top: 7upx
	}

	.cont_bot .ma2 .submit {
		padding: 0 38upx;
		margin: 0;
		border-radius: 100px;
		height: 76upx;
		line-height: 76upx;
		font-size: 30upx;
		background: #fa436a;
		box-shadow: 1px 2px 5px rgba(217, 60, 93, 0.72);
		color: #fff;
	}

	.contt {
		padding: 20upx 30upx;
		color: #333;
		font-size: 12px;
		line-height: 23px;
	}

	.contt image {
		max-width: 100%;
		display: block
	}
</style>
