<template>
	<view class="login">
		
		
				  <view class="login__bg login__bg--top">
					<image class="bg" src="/static/images/loginbg.jpg" mode="widthFix"></image>
				  </view>
		
		          <view class="topk"></view>
		
		
					<view class="login-type-content">
						
						
						<view class="titwz">欢迎来到漏鱼寄卖管家</view>
						
						
						<view class="main">
						
							
					
								<view class="login-type-form">
									<view class="input-item">
										<input
												class="login-type-input"
												type="text"
												name="mobile"
												v-model="mobile"
												placeholder="手机号码"
												maxlength="11"
										/>
									</view>
									<view class="input-item" v-if="loginByPass">
										<input
												class="login-type-input"
												type="password"
												v-model="password"
												placeholder="登录密码"
												maxlength="20"
										/> 
									</view>
									
									<view class="input-item input-item-sms-code" v-if="!loginByPass">
										<input class="login-type-input"	type="number"  name="mscode"	v-model="mscode"  placeholder="手机验证码"	maxlength="6"	/>
										<button :disabled='disabled'	class="sms-code-btn" @tap.stop="getVerificationCode()">
											<text>{{codename}}</text>
										</button>
									</view>
									
								</view>
						
							<view>
								<button
										class="confirm-btn bg-active tn-main-gradient-indigo" 
										:disabled="btnLoading"
										:loading="btnLoading"
										@tap="toLogin"
								>
									立即登录
								</button>
							</view>
							
							<view class="dlmmcon">
								<view class="df1" @click="gotofindpass">忘记密码</view>
								<view class="df2" @click="gotoreg">注册账号</view>
								<view class="clear"></view>
							</view>
							
							
						     <div class="line-box">
								<span class="line ll"></span>
								<span class="text">其他登录方式</span>
								<span class="line rr"></span>
							 </div>
					
							 <view class="otdlcon">
								<view class="item">
									<view class="con t1"  @tap="showLoginBySmsCode">
										<view class="icon"><image src="../../static/images/otdl_ot.png"></image></view>
										<view class="txt"> {{ loginByPass ? '验证码登录' : '密码登录' }}</view>
									</view>
								</view> 
								
								<view class="item" v-if="apptype==1">
									<view class="con t2"   @click="wxlogin">
										<view class="icon"><image src="../../static/images/otdl_wx.png"></image></view>
										<view class="txt">微信登录</view>
									</view>
								</view>
								
								<view class="clear"></view>
							 </view>
							
						</view>
					</view>
				
				
			
	</view>
</template>
<script>
	
	
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				logininfo:'',
				mobile: '',
				password: '',
				disabled:false,
				btnLoading: false,
				userInfo: null,
				appName: '',
				ydxysta:false,
				clientid:'',
				appver:'',
				apptype:0,
				loginByPass:true,
				codename:'获取验证码',
				mscode:'',
				codeSeconds: 0, // 验证码发送时间间隔
				smsCodeBtnDisabled: true,
				// 是否显示密码
				showPassword: false,
				// 倒计时提示文字
				tips: '获取验证码',
				 clicksta:false,
			};
		},
		
		
		onLoad(options) {
			// this.loadData();
		},
		
		watch: {
		
		},
		onShow() {
			let that=this;
			 this.clicksta = false;
			// #ifdef APP-PLUS
			  let clientid=plus.push.getClientInfo().clientid;
			   that.clientid=clientid ? clientid : ''; 
			  
			   plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
			   that.appver=widgetInfo.version;
			   let platform = uni.getSystemInfoSync().platform
			     if(platform=='android'){
					 that.apptype=1;
				 }else{
					 that.apptype=2;
				 }
			  });
			  
			// #endif
		},
		methods: {
		
		   gotoxy(id){
			   uni.navigateTo({
				   url:'/pages/danye/index?id='+id
			   });
		   },
		   gotofindpass(){
			   uni.navigateTo({
			   		url:'/pages/public/findpass'
			   });
		   },
		   gotoreg(){
			   uni.navigateTo({
					url:'/pages/public/reg'
			   });
		   },
		   selydxy(){
			    if(this.ydxysta){
					this.ydxysta=false;
				}else{
					this.ydxysta=true;
				}
		   },
			// 切换登录方式
			showLoginBySmsCode() {
				this.loginByPass = !this.loginByPass;
			},
		
			// 统一跳转路由
			navTo(url) {
		      uni.navigateTo({ url });
			},
			
			// 提交表单
			async toLogin() {
			      let that=this;
				  let dltype=1;
				  
				  this.ydxysta=true;
				  if(!that.loginByPass){
				  	dltype=2;
				  }
				  
				
				if (this.mobile.length <= 0) {
					uni.showToast({ 
						icon: 'none',
						title: '请输入您的手机号码'
					});
					return;
				}
							
					if(dltype==1){
						  if (this.password.length <= 0) {
							uni.showToast({
								icon: 'none',
								title: '请输入登录密码'
							});
							return;
						  }
					}else{
						  if (this.mscode.length <= 0) {
							uni.showToast({
								icon: 'none',
								title: '请输入验证码'
							});
							return;
						  }
					}
					  
			
				  uni.showLoading({title: '登录中...'})
				  
				  let da={
					username: this.mobile,
					password: this.password,
					mscode: this.mscode,
					dltype: dltype,
					clientid: that.clientid || '' ,
					apptype: that.apptype ||  '' ,
					appver: that.appver ||  '' ,
				  };
				  this.$req.post('/v1/login/loginvalidata', da)
				  		.then(res => {
				  			uni.hideLoading();
							console.log(res);
					
				  			if(res.errcode!=0){
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg
				  				});
				  			}else{
				  				this.$tool.setTokenStorage(res.data.tokendata);
				  				uni.showToast({
				  					icon: 'none',
				  					title: res.msg,
				  					success() {
				  						setTimeout(function(){
				  							uni.redirectTo({
				  							   url: '/pages/selsf/index',
				  							})
				  						},1000)
				  					}
				  				});
				  			}
				  			
				  		})
				  		.catch(err => {
				  		
				  		})
				  
			},
			
			getCode(){
			    let mobile = this.mobile;
			    let that = this;
			    // var myreg = /^(14[0-9]|13[0-9]|15[0-9]|17[0-9]|18[0-9])\d{8}$$/;
			    if (mobile == "") {
			      uni.showToast({
			        title: '手机号不能为空',
			        icon: 'none',
			        duration: 1000
			      });
			      return false;
			    }
			    if (mobile.length!=11) {
			      uni.showToast({
			        title: '请输入正确的手机号',
			        icon: 'none',
			        duration: 1000
			      });
			      return false;
			    }
			
			      // 发起网络请求
			      let da = {
			        'mobile': mobile,
			        'fstype': 2
			      };
				  uni.showLoading({ 'title': '' });
				  this.$req.post('/v1/login/getmscode', da)
				  	.then(res => {
				  
						uni.hideLoading();
				  		if (res.errcode == 0) {
							
				  			 that.disabled= true;
				  			 uni.showToast({
				  			   title: res.msg,
				  			   icon: 'none', 
				  			   duration: 1000,
				  			   success: function () {
				  			     var num = 61;
				  			     var timer = setInterval(function () {
				  			       num--;
				  			       if (num <= 0) {
				  			         clearInterval(timer);
				  			           that.codename='重新发送';
				  			           that.disabled= false;
				  			       } else {
									    that.codename= num + "s"
				  			       }
				  			     }, 1000)
				  			 			
				  			 			
				  			   }
				  			 })
				  				
				  		} else {
				  			uni.showToast({
				  			  title: res.msg,
				  			  icon: 'none'
				  			})
				  		}
				  
				  	})
			 
			  },
			  //获取验证码
			  getVerificationCode() {
			    this.getCode();
			  },
			  
			
			
			wxlogin() {
				
					let that = this;
					 if (this.clicksta) return;this.clicksta = true;
					// #ifdef APP-PLUS
					 console.log(423423);
								uni.login({
										provider: 'weixin',
										success: function (loginRes) {
									        console.log(loginRes);
											uni.getUserInfo({
											  provider: 'weixin',
											  success: function(infoRes) {
										        console.log(infoRes);
												let userinfo=infoRes.userInfo;
											    let openid=userinfo.openId;
											    let unionid=userinfo.unionId;
											    let nickname=userinfo.nickName;
											    let avatar=userinfo.avatarUrl;
												
											     
												 let da={
												 	openid:openid,
												 	unionid:unionid,
												 	nickname:nickname,
												 	avatar:avatar,
													clientid: that.clientid || '' ,
													apptype: that.apptype ||  '' ,
													appver: that.appver ||  '' ,  
												 };
												 uni.showLoading({ 'title': '' });
												 that.$req.post('/v1/login/wxmapp',da)
													.then(res => {
														uni.hideLoading();
														console.log(res);
																
														if(res.errcode!=0){
															uni.showToast({
																icon: 'none',
																title: res.msg
															});
															that.clicksta = false;
														}else{
															//如果已存在,直接登录了
															if(res.data.tokendata){
													
																that.$tool.setTokenStorage(res.data.tokendata);
																uni.showToast({
																	icon: 'none',
																	title: res.msg,
																	success() {
																		setTimeout(function(){
																			uni.redirectTo({
																			   url: '/pages/selsf/index',
																			})
																		},1000)
																	}
																});
															}else{
																
																//保存获取的微信信息，跳转到绑定登录账号页面
																if(res.data.wxlogininfo){
																
																	uni.setStorageSync('wxlogininfo',res.data.wxlogininfo);
																	uni.showToast({
																		icon: 'none',
																		title: '还未绑定账号',
																		success() {
																			setTimeout(function(){
																				uni.navigateTo({
																					url:'/pages/public/bdmobile'
																				})
																			},1000)
																		}
																	});
																	
																}else{
																	uni.showToast({
																		icon: 'none',
																		title: res.msg
																	});
																	that.clicksta = false;
																}
																
															}
															
														}
														
													})
													.catch(err => {
														that.clicksta = false;
													})
												 
												 
												 
												
											},complete(e) {
													console.log('cccc100:',e)
											}
											  
											
											
											
											
											})
				  
										  
										  
										},
										complete(res) {
											console.log(res);
										},fail: function (err) {
										       console.log(err);
										}
								});
					 
					 
					// #endif
					
			
					
			},
			  
			
		
			
			
			
		}
	};
</script>
<style lang="scss">
	page {
		background: #fff;
	}

	.titwz{font-size:44upx;font-weight: 600;margin-bottom:100upx}
	.login {
		
		position: relative;
		height: 100%;
		z-index: 1;
		
		
		/* 背景图片 start */
		&__bg {
		  z-index: -1;
		  position: fixed;
		  
		  &--top {
		    top: 0;
		    left: 0;
		    right: 0;
		    width: 100%;
		    
		    .bg {
		      width: 750upx;
		      will-change: transform;
		    }
		  }
		
		}
		/* 背景图片 end */
		
		.topk{height:280upx}
		
		.dltype{}
		.dltype .item{float:left}
		.dltype .item .text{
			width: 104upx;
			height: 74upx;
			font-size: 52upx;
			font-weight: 600;
			color: #333333;
			line-height: 74upx;
		}
		.dltype .item .line{
			width: 60upx;
			height: 8upx;
			background: #3366FF;
			border-radius: 4upx;
			margin:10upx auto;
		}
		
		.yszc{margin-top:20upx;text-align: center;}
		.yszc .nr{font-size:24upx;color:#B4B3B3}
		.yszc .nr span{color:#3366FF}
		.yszc .nr text{margin-right:10upx;font-size:24upx}
		.yszc .nr .hoverss{color:#3366FF}
		
		.otdlcon{text-align: center;width: 380upx;margin:0 auto;margin-top:20upx;display: flex;}
		.otdlcon .item{text-align: center;flex: 1;}
		.otdlcon image{width: 92upx;height:92upx}
		.otdlcon .txt{height:28upx;line-height:28upx;font-size:24upx;margin-top:10upx;color:#B2B2B2}
		
		.login-type-content {
			margin:0 56upx;
			position: relative;
			.main {
				position: absolute;
				width: 100%;
				top: 160upx;
			    .login-type-form {
							.input-item {
								position: relative;
								margin-bottom: 32upx;
								font-size:32upx;
								color:#7F7F7F;
								.login-type-input {
									height: 112rpx;
									line-height: 112rpx;
									background: #fff;
									border-radius: 24rpx;
									padding:0 30upx;
								}
							}
							.tricon{position: absolute;right:0;top:0;z-index:100;}
							.tricon .iconfont{font-size:44upx}
				}
				.confirm-btn {
				  margin-top: 70upx;
				  height: 112upx;
				  line-height: 112upx;
				  border-radius:24upx;
				  background: #FFD427;
				  color:#333;
				}
			}
			
			.dlmmcon{height: 28upx;font-size: 26upx;line-height:28upx;color: #7F7F7F;margin-top:24upx}
			.dlmmcon .df1{float:left} 
			.dlmmcon .df2{float:right} 
		}
	

		.line-box{
		  display: flex;
		  justify-content: space-between;
		  align-items: center;
		  margin: 70upx auto 50upx auto;
		}
		.line-box .text{
		  min-width: 100px;
		  font-size: 24upx;
		  text-align: center;
		  color:#B2B2B2
		}
		.line-box .line{
		  width: 40%;
		  height: 1px;
		  background: linear-gradient(to right, #C8C8C8,#FFFFFF);
		}
		.line-box .line.ll{
			background: linear-gradient(to left, #C8C8C8,#FFFFFF);
		}
	
    
	}
	
	
	// 发送验证码样式
	.input-item-sms-code {
	
	  .sms-code-btn {
	      position: absolute;right:28upx;top:22upx;z-index: 200;background:#fff;color: #3366FF;font-size:28upx;padding:0
	  }
	
	  .sms-code-resend {
	    color: #666;background:#fff;
	  }
	}
	
	
	
	
	
</style>
