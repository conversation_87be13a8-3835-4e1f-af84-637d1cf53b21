<template>
<view class="mcn">
   
   	  <tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
   			 <view slot="back" class='tn-custom-nav-bar__back'  >
   				  <text class='icon tn-icon-left'></text>
   			 </view>
   	  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
   	  		  <text  >账号安全</text>
   	  		</view>
   	  </tn-nav-bar>
   
 
   
   <view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
	
    <view class="usitem " style="border-top:1upx solid #efefef;">
		<view class="li" @click="tn('/pages/user/yzmobile?st=1')" >
				<view class="tit">登录密码</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
		</view>
		
		<view class="li" @click="tn('/pages/user/yzmobile?st=2')" >
				<view class="tit">手机号码</view>
				<view class="more">
					<block v-if="usetdata.jmmobile">
						<text class="iconfont icon-shouji2" style="color:#666;margin-right:10upx"></text> <text style="color:#666">{{usetdata.jmmobile}}</text>
					</block>	
					<text class="iconfont icon-z043"></text>
					
				</view>
		</view>
		
	    <view class="li" @click="tn('/pages/user/yzmobile?st=3')" >
				<view class="tit">绑定微信</view>
				<view class="more">
					<block v-if="usetdata.bd_wx_xx">
						<text class="iconfont icon-weixin" style="color:#09bb07;margin-right:10upx"></text> <span style="color:#666">{{usetdata.bd_wx_xx}}</span>
					</block>	
					<text class="iconfont icon-z043"></text>
				</view>
		</view>
		
		
		<view class="li" @click="tn('/pages/danye/index?id=1')"  >
				<view class="tit">关于我们</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
		</view>
		<view class="li"  @click="tn('/pages/danye/index?id=6')"  >
				<view class="tit">服务协议</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
		</view>
	
		 <view class="li"   @click="tn('/pages/danye/index?id=7')"   >
				<view class="tit">隐私政策</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
		 </view>
		
			<view class="li"   @click="tn('/pages/user/acczx/index')"   >
				<view class="tit">账号注销</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
			</view>
	
		
    </view>
	
		
	<!-- #ifdef APP-PLUS -->
		<view class="usitem " style="margin-top:20upx;">
			<view class="li" @click="clearhc()">
				<view class="tit">清除缓存</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
			</view>
		
			 <view class="li" @click="checkversion">
				<view class="tit">检查更新</view>
				<view class="more">V{{version}}<text class="iconfont icon-z043"></text></view>
			 </view>
		 </view>
	<!-- #endif -->
		
	<view class="usitem " style="margin-top:20upx;">
		<view class="li"  @click="toLogout()" >
				<view class="tit">退出登录</view>
				<view class="more"><text class="iconfont icon-z043"></text></view>
		</view>
		
	</view>
	
  
	<yomol-upgrade  :type="upgradeType" :url="upgradeUrl" title="发现新版本" :content="upgradeContent" ref="yomolUpgrade"></yomol-upgrade>


</view>


</template>

<script>
	import yomolPrompt from '@/components/yomol-prompt/yomol-prompt.vue'
	import yomolUpgrade from '@/components/yomol-upgrade/yomol-upgrade.vue'
	export default {
		components: {
				yomolPrompt,
				yomolUpgrade,
		},
		data() {
			return {
				    staticsfile: this.$staticsfile,
				    mudata:'',
					fileSizeString: '0B', // 缓存大小
				    version:'',
					upgradeType: 'pkg',  
					upgradeContent: '', 
					upgradeUrl: '', 
					usetdata:'',
			}
		},
		onShow() {
			let that=this;
		    this.loadData();
			// #ifdef APP-PLUS
				plus.runtime.getProperty(plus.runtime.appid,(wgtinfo)=>{
						that.version=wgtinfo.version;
				})
				that.formatSize();
			// #endif
		},
		methods: {
			
			
			loadData(){
					  let that=this;
					  let da={  
					 
					  }
					
					 this.$req.post('/v1/muser/uset', da)
							 .then(res => {
								console.log(res)
								if(res.errcode==0){
								   that.usetdata=res.data.usetdata;
								}
							 })
			},
			
			// 跳转
			tn(e) {
			  uni.navigateTo({
				url: e,
			  });
			},
			checkversion() {
				let that=this;
		
							// 检测升级
							plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
								var platform = uni.getSystemInfoSync().platform
								
								if(platform!="android"){
									uni.showToast({
										icon: 'none',
										title: '当前版本：V'+widgetInfo.version
									});
									
								}else{
								
											let da={
												platform: platform,
												version: widgetInfo.version,
												name: widgetInfo.name
											}
											console.log(da);
											uni.showLoading({title: '检查中...'})
											that.$req.post('/v1/checkver/index', da)
													.then(res => {
														uni.hideLoading();
														console.log(res);
														if(res.errcode==0){
															let isnew=res.data.isnew;
                                                            if(isnew==1){
																if (res.data.appupdata.pkgurl != '' && res.data.appupdata.wgturl == '') {
																	that.upgradeType = 'pkg'
																	that.upgradeContent = res.data.appupdata.content
																	that.upgradeUrl = res.data.appupdata.pkgurl
																	that.$refs.yomolUpgrade.show()
																} else {
																	that.upgradeType = 'wgt'
																	that.upgradeContent = res.data.appupdata.content
																	that.upgradeUrl = res.data.appupdata.wgturl
																	that.$refs.yomolUpgrade.show()
																}
															}else{
																uni.showToast({
																	icon: 'none',
																	title: res.msg
																});
															}												 
														}else{
															uni.showToast({
																icon: 'none',
																title: res.msg
															});
														}
											
											})
								}	
							
							});
						},
						
			
			
			// 清除本地缓存
			clearhc() {
			  uni.showModal({
			    title: '',
			    content: '确认要清除本地缓存吗？',
			    success: (res) => {
			      if (res.confirm) {
			        this.clearCache();
			      }
			    },
			  });
			},
			// 格式化缓存大小
			formatSize() {
			  let that = this;
			  plus.cache.calculate(function (size) {
			    let sizeCache = parseInt(size);
			    console.log('重新计算', sizeCache);
			    if (sizeCache == 0) {
			      that.fileSizeString = '0B';
			    } else if (sizeCache < 1024) {
			      that.fileSizeString = sizeCache + 'B';
			    } else if (sizeCache < 1048576) {
			      that.fileSizeString = (sizeCache / 1024).toFixed(2) + 'KB';
			    } else if (sizeCache < 1073741824) {
			      that.fileSizeString = (sizeCache / 1048576).toFixed(2) + 'MB';
			    } else {
			      that.fileSizeString = (sizeCache / 1073741824).toFixed(2) + 'GB';
			    }
			  });
			},
			// 清除本地缓存
			clearCache() {
			  // uni.clearStorage();
			  let that = this;
			  let os = plus.os.name;
			  
			  uni.setStorage({
			        key: "checkup-time",
			        data: 0,
			  });
			  
			  if (os == 'Android') {
			    let main = plus.android.runtimeMainActivity();
			    let sdRoot = main.getCacheDir();
			    let files = plus.android.invoke(sdRoot, 'listFiles');
			    let len = files.length;
			    for (let i = 0; i < len; i++) {
			      let filePath = '' + files[i]; // 没有找到合适的方法获取路径，这样写可以转成文件路径
			      plus.io.resolveLocalFileSystemURL(
			        filePath,
			        function (entry) {
			          if (entry.isDirectory) {
			            entry.removeRecursively(
			              function (entry) {
			                //递归删除其下的所有文件及子目录
			                that.$message.info('缓存清理完成!');
			            
			              },
			              function (e) {
			                console.log(e.message);
			              }
			            );
			          } else {
			            entry.remove();
			          }
			        },
			        function (e) {
			          console.log('文件路径读取失败');
			        }
			      );
			    }
			  } else {
			    // ios暂时未找到清理缓存的方法，以下是官方提供的方法，但是无效，会报错
			    plus.cache.clear(function () {
			      this.$message.info('缓存清理完成!');
			      that.formatSize();
			    });
			  }
			  
			  that.formatSize(); // 重新计算缓存
			  
			},	
			
			
			//退出登录
			toLogout(){
					uni.showModal({
						content: '确定要退出登录吗？',
						success: (e)=>{
							if(e.confirm){
								// uni.clearStorage();
								this.$tool.setTokenStorage('');
								this.$tool.setMudataStorage('');
								setTimeout(()=>{
									// uni.navigateBack();
									uni.redirectTo({
									   url: '/pages/public/login',
									})
								}, 200)
							}
						}
					});
			},	
						
		}
	}
</script> 

<style>

.usitem{background:#fff;padding:10upx 40upx;}
.usitem .li{height:100upx;line-height:100upx;border-bottom:1upx solid #efefef;overflow: hidden;}
.usitem .li .tit{float:left;font-size:28upx}
.usitem .li .more{float:right;color:#ccc;font-size:14px;}
.usitem .li .more i{display: inline-block;margin-left:5px}
.usitem .li:last-child{border-bottom:0;}

</style>
