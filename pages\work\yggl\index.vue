<template>
	<view class="tn-safe-area-inset-bottom main-Location">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >店铺员工</text>
					</view>
					<view slot="right" >
					</view>
		</tn-nav-bar>
		
		 <view class="tabs-fixed-blank" ></view>
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white " style="z-index: 2000;" :style="{top: vuex_custom_bar_height + 'px'}">
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center ">
			<view class="justify-content-item" style="width: 60vw;overflow: hidden;padding-left:10px">
			  <tn-tabs :list="stadata" :current="current" :isScroll="true" :showBar="true"  activeColor="#000" inactiveColor="#333"  :bold="true"  :fontSize="28" :badgeOffset="[20, 50]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
			<view class="justify-content-item gnssrr" style="width: 40vw;overflow: hidden;" >
				<view class="item btnimg" @click="gotoyqewm" >
					<image src="/static/images/addbtn.png"></image>
				</view>
				<view class="item btnimg" @click="ygmsgtc" >
					<view class="msgbtn"><view class="msgnum"  v-if="msgnumz > 0" style="z-index:200;">{{msgnumz}}</view><image src="../../../static/images/msgbtn.png"></image></view>
				</view>
				<view class="item searchbtn"><text class="iconfont icon-sousuo2" @click="searchmodal()"></text></view>
			</view>
		  </view>  
		</view>
		
		<!-- <view class="tabs-fixed-blank" style="height:140upx" ></view> -->
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 10 + 'px'}" ></view>
		
		
		<view class="tjtiaox" >
			<block v-if="thkw">
				<block v-if="thkw">
					<view class="ssgjc">
						 <text class="gjc">" {{thkw}} "</text>
						<view class="clsbb" @click="clssearch()"><text class="iconfont icon-cuohao02"></text> </view>
					</view>
				</block> 
			</block> 
			<block v-else>
				<view class="khznumcon">员工数量：<text>{{ygznum}}</text>人</view>
				<view class="khseldp">
			
				</view>
			</block>	
		</view>
		
		
		<!-- 字母区域 -->
		<view class="Location-Letter">
			<view v-for="(l,i) in LatterName" :key="i" hover-class="Click-Latter" @tap="getLetter(l)"  :style="{'color': LetterId === l ? '#FFD427' : '#000' }">{{l}}</view>
		</view>
 
	
		<scroll-view scroll-y="true" class="ynq-ScrollView" :scroll-into-view="LetterId">
			<view class="ynq-yglist">
				<block v-for="(item,index) in yglist" :key="index">
					<view class="zmtxt" :id="item.initial">{{item.initial}}</view>
					<view class="yglist">
						<block v-for="(item_yg,name_index) in item.list"	:key="name_index">
							<view class="item"   :class="item_yg.status!=1 ? 'lzstyle' : '' "  >
								
							<!-- 	      <block  v-if="item_yg.isadmin==2 ">
											<view class="status" @click="setsta" :data-ygid='item_yg.id' >
												<view class="aa">{{item_yg.statusname}} </view> 
												<view class="aa"  >
													<tn-switch v-model="item_yg.status==1 ? true : false " activeColor="#FFD427"  inactiveColor="#7F7F7F" leftIcon="success" right-icon="close" change="ss"></tn-switch>
												</view>
											</view>
									  </block>
									  <block  v-else >
											<view class="status"  >
												<view class="aa" :class="'ygsta'+item_yg.status">{{item_yg.statusname}} </view> 
											</view>
									  </block> -->
									  
									  <view class="status"  >
									  	<view class="aa" :class="'ygsta'+item_yg.status">{{item_yg.statusname}} </view> 
									  </view>
									  
										<view class="inf1"  >
										
											<view class="tf1">
												<view class="tx"><image :src="staticsfile + item_yg.avatar"></image></view>
												<view class="xx">
													<view class="name">{{item_yg.realname}} <text class="iconfont icon-mendian cjglicon"  v-if="item_yg.iszzh==1 " ></text></view>
													<view class="tel"><text>员工号:{{item_yg.zzhuid}}</text>电话：{{item_yg.lxtel}}</view>
												</view>
												<view class="clear"></view>
											</view>	
										</view>
										
										<view class="inf2">
										   <view class="vtm">
											   <view class="tt">余额</view>
											   <view class="vv">{{item_yg.yue}}</view>
										   </view>
										   <view class="vtm">
											   <view class="tt">当前出资</view>
											   <view class="vv">{{item_yg.czzjine}}</view>
										   </view>
										   <view class="vtm">
											   <view class="tt">可用资金</view>
											   <view class="vv">{{item_yg.czkyjine}}</view>
										   </view>
										   <view class="clear"></view>
										</view>
										
										<view class="inf3">
											<view class="f1">
												<view class="x1" v-if="item_yg.ormdname">门店：{{item_yg.ormdname}}</view>
												<view class="x2" >角色：<text>{{item_yg.jiaosetxt}}</text></view>
												<view class="clear"></view>
											</view>
											<view class="f2"><view class="setbtn" @click="ygset" :data-ygid='item_yg.id'>员工设置</view></view>
											<view class="clear"></view>
										</view>
																				
									  
							</view>  
						</block>
					</view>
				</block>
			</view>
		</scroll-view>
		
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
		  <view class="custom-modal-content"> 
		    <view class="">
		      <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">员工搜索</view>
		      <view class="tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
		        <input :placeholder="'请输入员工姓名/电话'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA" maxlength="50" style="text-align: center;"></input>
		      </view>
		    </view>
		    <view class="tn-flex-1 justify-content-item  tn-text-center">
		      <tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%"  shape="round" @click="searchsubmit" >
		        <text >确认提交</text>
		      </tn-button>
		    </view>
		  </view>
		</tn-modal>
		
		
		<tn-modal v-model="show1" :custom="true" :showCloseBtn="true" padding="0">
		  <view class="custom-modal-content" > 
			<view class="" >
			  <view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">消息类型</view>
		      <view class="msgtypecon">
				  <view class="item" @click="gotoygsh">
					  <view class="icon"><image src="/static/images/ygmsgicon1.png"></image><view class="msgnum" v-if="msgnum1 > 0">{{msgnum1}}</view></view>
					  <view class="txt">员工审核</view>
				  </view>
				  <view class="item"  @click="gototxsh">
					  <view class="icon"><image src="/static/images/ygmsgicon2.png"></image><view class="msgnum" v-if="msgnum2 > 0">{{msgnum2}}</view></view>
					  <view class="txt">提现审核</view>
				  </view>
				  <view class="item"  @click="gotobxsh">
					  <view class="icon"><image src="/static/images/ygmsgicon3.png"></image><view class="msgnum" v-if="msgnum3 > 0">{{msgnum3}}</view></view>
					  <view class="txt">报销审核</view>
				  </view>
				  <view class="clear"></view>
			  </view>
			</view>
		
		  </view>
		</tn-modal>
		
		
		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">
		
						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
		
					</block>
				</view>
			</scroll-view>
		</tn-popup>
		
	</view>
</template>
 
<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				stype: 0,
				CityName: '',
				LatterName: '',
				yglist:'', 
				LetterId: '',
				show1:false,
				show3:false,
				current:0,
				sta:0,
				thkw:'',
				thkwtt:'',
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 1,name: '在职'},
					{sta: 2,name: '离职'},
				],
				msgnumz:0,
				msgnum1:0,
				msgnum2:0,
				msgnum3:0,
				ygznum:0,
				
				dpselshow:false,
				dpdata:'',
				dpid:0,
				dpname:'',
			
			}
		},
		onLoad(options) {
		
		
			this.loadData();
		},
		
		onShow(){
				if(uni.getStorageSync('thupyglist')==1){
					this.loadData();
					uni.setStorageSync('thupyglist',0)
				}
		},
		
		methods: {
			
			seldianpu() {
				this.dpselshow = true;
			},
			selthdp(e) {
				let dpid = e.currentTarget.dataset.dpid;
				let dpname = e.currentTarget.dataset.dpname;
			    this.dpid=dpid;
			    this.dpname=dpname;
				this.loadData();
				this.dpselshow = false;
			},
			clsdpid(){
				let that=this;
				this.dpid=0;
				this.dpname='';
				that.loadData();
			},
			
			loadData() {
				let that=this;
				let da={
					sta:that.sta,
					dpid:that.dpid ? that.dpid : 0,
					kw:that.thkw ? that.thkw : '' ,
				};
				uni.showNavigationBarLoading();
				this.$req.post('/v1/yggl/yglist', da)
				      .then(res => {
					    uni.hideNavigationBarLoading();
						console.log(res);
						if(res.errcode==0){
						   that.yglist = res.data.yglist;
						   that.LatterName = res.data.lattername;
						   that.ygznum = res.data.ygznum;
						   that.msgnumz = res.data.msgnumz;
						   that.msgnum1 = res.data.msgnum1;
						   that.msgnum2 = res.data.msgnum2;
						   that.msgnum3 = res.data.msgnum3;
						   that.dpdata = res.data.dpdata;
						}else{
							uni.showModal({
								content: res.msg,
								showCancel: false
							})
						}
				      })
			},

			//获取定位点
			getLetter(name) {
				this.LetterId = name
				// uni.pageScrollTo({
				// 	selector:'#'+name,
				// 	duration:300
				// })
			},
		
		
			gotoyqewm(){
				uni.navigateTo({
					url:'./yqewm'
				})
			},
			searchmodal(){
				this.show3=true
			},
			searchsubmit(){
				let that=this;
				this.thkw=this.thkwtt;
				
				this.show3=false;
		    	that.loadData();
			},
			clssearch(){
				let that=this;
				this.thkw='';
				this.thkwtt='';
				that.loadData();
			},
			
			ygmsgtc(){
				this.show1=true
			},
			
			
			ygedit(e){
				let that=this;
				let ygid = e.currentTarget.dataset.ygid;
				uni.navigateTo({
					url:'./edit?ygid='+ygid
				})
			},
			ygset(e){
				let that=this;
				let ygid = e.currentTarget.dataset.ygid;
				uni.navigateTo({
					url:'./ygset?ygid='+ygid
				})
			},
			
			gotoygsh(){
				uni.navigateTo({
					url:'./ygsh/index'
				})
			},
			gototxsh(){
				uni.navigateTo({
					url:'./txsh/index'
				})
			},
			gotobxsh(){
				uni.navigateTo({
					url:'./bxsh/index'
				})
			},
			  bdtel(e){
					let that=this;
					let tel=e.currentTarget.dataset.tel;
					console.log(tel);
					uni.makePhoneCall({
						phoneNumber:tel
					});
			   },
		
				tabChange(index) {
					let that=this;
					let sta=that.stadata[index].sta;
					console.log(sta);
					that.thkw = '';
					that.current = index;
					that.sta = sta;
					that.loadData();
				},
				
				
				
				setsta(e){
					let that=this; 
					let ygid = e.currentTarget.dataset.ygid;
					let da = {
						'ygid': ygid
					}
					uni.showLoading({title: ''})
					that.$req.post('/v1/yggl/setsta', da)
						.then(res => {
							uni.hideLoading();
							if (res.errcode == 0) {
								uni.showToast({
									icon: 'none',
									title: res.msg,
									success() {
										setTimeout(function() {
												that.loadData();
										}, 1000);
									}
								});
							} else {
								uni.showModal({
									content: res.msg,
									showCancel: false,
								})
							}
					})
					
					
				},
				
				
				
				
				
				
				
				
				
				
		},
		
		
		
		
		
		
		
		
	}
</script>

<style lang="scss" scoped>

	.main-Location {
		height: 100vh;
	}
 
	.Location-Letter {
		position: fixed;
		right: 0rpx;
		top: 360rpx;
		width: 85rpx;
		z-index: 100;
		view {
			display: block;
			width: 85rpx;
			text-align: center;
			height: 35rpx;
			line-height: 35rpx;
			font-size: 28rpx;
			font-weight: 600;
			transition: ease .3s;
			-webkit-transition: ease .3s;
			margin-bottom:30upx;
		}
	}
 
	.ynq-yglist {
		padding: 0upx 85rpx 20upx 32rpx;
	}
	.ynq-ScrollView {
		height: calc(100vh - 285rpx);
	}
 
	.Click-Latter {
		font-size: 30rpx !important;
	}
	
	
	.zmtxt{margin-top:0upx;margin-bottom:20upx;font-weight: 600;font-size:28upx;}
	.ygc{margin-bottom:20upx}
	
	
	
	.khznumcon{font-size: 24rpx; color: #7F7F7F;}
	.khznumcon text{font-weight:700;color:#ff0000;margin:0 10upx}
	
	
	
	
	.yglist{}
	.yglist .item{margin-bottom:32upx;padding:28upx;border-radius:20upx 20upx 0 0;background:#fff;position: relative;}
	.yglist .item .status{position: absolute;right:32upx;top:32upx;font-size:24upx;}
	.yglist .item .status .aa{float:left;margin-left:10upx;line-height:50upx;}
	.yglist .item .status .ygsta1{color:#4aac09}
	.yglist .item .status .ygsta2{color:#ff0000}
	.yglist .item .inf1{}
	.yglist .item .inf1 .tx{float:left}
	.yglist .item .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
	.yglist .item .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:100upx;}
	.yglist .item .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
	.yglist .item .inf1 .tel{color: #7F7F7F;font-size: 24rpx;margin-top:10upx}
	.yglist .item .inf1 .tel text{margin-right:30upx}
	
	
	.yglist .item .inf2{border-top:1px solid #F0F0F0;border-bottom:1px solid #F0F0F0;margin:20upx 0 0 0;padding:20upx 0;}
	.yglist .item .inf2 .vtm{float:left;width:33.33%;font-size:28upx}
	.yglist .item .inf2 .vtm .tt{color: #7F7F7F;font-size:24upx}
	.yglist .item .inf2 .vtm .vv{color: #333333;margin-top:15upx}
	
	
	.yglist .item .inf3{padding:20upx 0;}
	.yglist .item .inf3 .f1{float:left;height: 40upx;line-height:40upx;font-size:24upx}
	.yglist .item .inf3 .f1 .x1{}
	.yglist .item .inf3 .f1 .x2{margin-top:7upx}
	.yglist .item .inf3 .f1 .cjgly text{color:#ff6600}
	.yglist .item .inf3 .f2{float:right;padding-top:10upx}
	.yglist .item .inf3 .setbtn{width: 140rpx;height: 60upx;line-height:60upx;border-radius: 8rpx;border: 2rpx solid #B2B2B2;font-size: 24rpx;text-align: center;}
	
	.yglist .item.lzstyle{background: #F0F0F0;}
	.yglist .item.lzstyle .inf3 .f1 .x1{color: #7F7F7F}
	.yglist .item.lzstyle .inf3 .f1 .x2{color: #7F7F7F}
	.yglist .item.lzstyle .inf2{border-top:1px solid #fff;}
	
	.msgtypecon{padding:20upx 0 40upx 0}
	.msgtypecon .item{float:left;width:33.33%;text-align: center;}
	.msgtypecon .item .icon{width:76upx;margin:0 auto;position: relative;}
	.msgtypecon .item .icon image{width:76upx;height:80upx;}
	.msgtypecon .item .txt{font-size:24upx;margin-top:10upx;}
	.msgtypecon .item .icon .msgnum{position:absolute;right:-8upx;top:0;min-width: 22upx;height:28upx;line-height:26upx;font-size:16upx;background: #FF2828;border-radius: 100upx;border:1px solid #fff;color:#fff;padding:0 9upx;}
	
	.khznumcon{font-size: 24rpx; color: #7F7F7F;float:left;padding-bottom:20upx}
	.khznumcon text{font-weight:700;color:#ff0000;margin:0 10upx}
	.khseldp{float:right;}
	.khseldp .icon-jiantou2{font-size:22upx;} 
	
</style>