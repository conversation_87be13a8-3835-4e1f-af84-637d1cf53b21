<template>
	<view class="tn-safe-area-inset-bottom">
		
		<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text>店铺订单管理</text>
					</view>
					<view slot="right" >
						<view class="trrysel" >
							<text class="iconfont icon-renyuan" style="margin-right:10upx"></text>
							<text @click="selkh">{{khname ? khname : '按客户筛选' }}</text>
							<block v-if="khid > 0">
								<text class="iconfont icon-cuohao02" style="margin-left:10upx" @click="clskhid"></text> 
							</block>
						</view> 
					</view>
					
		</tn-nav-bar>
		 
		<!-- 顶部自定义导航 --> 
		
		<view class="tabs-fixed-blank" :style="{height: vuex_custom_bar_height + 5 + 'px'}" ></view>
	
	 <!--    <view class="topss">
			 <view class="ssvv">
				<view class="ssk" @click="searchmodal()">
					<text class="iconfont icon-sousuo2" ></text> 搜索仓库物品
				</view>
				<view class="clear"></view>
			 </view>
	     </view> -->
	
	  
			
		<view class="fwxmcon" @click="gotourl('./ddlist?ddtype=3&ddtypetxt=质押暂存')">
			<view class="icon"><image src="/static/images/fwicon4.png" mode="widthFix"></image></view>
			<view class="tit">质押暂存</view>
			<view class="more"><text class="iconfont icon-jiantou_more1"></text></view>
			<view class="tjcon">
				<view class="xmtj">
					<view class="cscon">
						<view class="ite">
							<view class="sz">{{tjd3.tjnum0}}</view>
							<view class="tx">总单数</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjd3.tjnum1}}</view>
							<view class="tx">今日到期</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjd3.tjnum2}}</view>
							<view class="tx">七日到期</view>
						</view>
						<view class="ite" >
							<view class="sz">{{tjd3.tjnum3}}</view>
							<view class="tx">已逾期</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	
	    <view class="fwxmcon" @click="gotourl('./ddlist?ddtype=2&ddtypetxt=实物回收')">
	    	<view class="icon"><image src="/static/images/fwicon3.png" mode="widthFix"></image></view>
	    	<view class="tit">实物回收</view>
	    	<view class="more"><text class="iconfont icon-jiantou_more1"></text></view>
	    	<view class="tjcon">
	    		<view class="xmtj">
	    			<view class="cscon"> 
						<view class="ite">
							<view class="sz">{{tjd2.tjnum0}}</view>
							<view class="tx">总单数</view>
						</view>
	    				<view class="ite">
	    					<view class="sz">{{tjd2.tjnum1}}</view>
	    					<view class="tx">回收物品</view>
	    				</view>
	    				<view class="ite">
	    					<view class="sz">{{tjd2.tjnum2}}</view>
	    					<view class="tx">物品数量</view>
	    				</view>
	    				<view class="ite">
	    					<view class="sz">{{tjd2.tjnum3}}</view>
	    					<view class="tx">物品库存</view>
	    				</view>
	    			</view>
	    		</view>
	    	</view>
	    </view>
	    
	    <view class="fwxmcon" @click="gotourl('./ddlist?ddtype=1&ddtypetxt=寄卖服务')">
			<view class="icon"><image src="/static/images/fwicon1.png" mode="widthFix"></image></view>
			<view class="tit">寄卖服务</view>
			<view class="more"><text class="iconfont icon-jiantou_more1"></text></view>
			<view class="tjcon">
				<view class="xmtj">
					<view class="cscon">
						<view class="ite">
							<view class="sz">{{tjd1.tjnum0}}</view>
							<view class="tx">总单数</view>
						</view>
						<view class="ite"> 
							<view class="sz">{{tjd1.tjnum1}}</view>
							<view class="tx">今日到期</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjd1.tjnum2}}</view>
							<view class="tx">七日到期</view>
						</view>
						<view class="ite" >
							<view class="sz">{{tjd1.tjnum3}}</view>
							<view class="tx">已逾期</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		
		<view class="fwxmcon" @click="gotourl('./ddlist?ddtype=4&ddtypetxt=信用预付')">
			<view class="icon"><image src="/static/images/fwicon2.png" mode="widthFix"></image></view>
			<view class="tit">信用预付</view>
			<view class="more"><text class="iconfont icon-jiantou_more1"></text></view>
			<view class="tjcon">
				<view class="xmtj">
					<view class="cscon">
						<view class="ite">
							<view class="sz">{{tjd4.tjnum0}}</view>
							<view class="tx">总单数</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjd4.tjnum1}}</view>
							<view class="tx">今日待还</view>
						</view>
						<view class="ite">
							<view class="sz">{{tjd4.tjnum2}}</view>
							<view class="tx">七日待还</view>
						</view>
						<view class="ite" >
							<view class="sz">{{tjd4.tjnum3}}</view>
							<view class="tx">已逾期</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		
		
		
		
	   
	
	
	
		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">
		 
						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
		 
					</block>
				</view>
			</scroll-view>
		</tn-popup>
		
		
		<tn-modal v-model="show3" :custom="true" :showCloseBtn="true">
			<view class="custom-modal-content">
				<view class="">
					<view class="tn-text-lg tn-text-bold  tn-text-center tn-padding">搜索</view>
					<view class="tn-bg-gray--light"
						style="border-radius: 10rpx;padding: 20rpx 30rpx;margin: 50rpx 0 60rpx 0;">
						<input :placeholder="'请输入关键词'" v-model="thkwtt" name="input" placeholder-style="color:#AAAAAA"
							maxlength="50" style="text-align: center;"></input>
					</view>
				</view>
				<view class="tn-flex-1 justify-content-item  tn-text-center">
					<tn-button backgroundColor="#FFD427" padding="40rpx 0" width="100%" shape="round"
						@click="searchsubmit">
						<text>确认提交</text>
					</tn-button>
				</view>
			</view>
		</tn-modal>
		
		
		<tn-calendar v-model="rqshow" mode="range" @change="rqmodalchange" :maxDate="rqmaxdate" toolTips="请选择日期范围" btnColor="#FFD427" activeBgColor="#FFD427" activeColor="#333" rangeColor="#FF6600" :borderRadius="20" :closeBtn="true"></tn-calendar>
	
	    <view style="height:70upx"></view> 
	
		
	</view>
</template>

<script>

	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				sta:0,
				index: 1,
				current: 0,
				thkw:'',
				thkwtt:'',
				ScrollTop:0, 
				SrollHeight:200,
				page:0,
				isloading:false,
				loadingType: -1,
				contentText: {
					contentdown: "上拉显示更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listdata:[],
				stadata:[
					{sta: 0,name: '全部'},
					{sta: 1,name: '已逾期'},
					{sta: 2,name: '今天'},
					{sta: 3,name: '明天'},
					{sta: 4,name: '未来七日'},
				],
				show3:false,
			    checked: true,
				
				
				rqshow:false,
				rqkstime:0,
				rqjstime:0,
				tjdata:[],
				tjd0:[],
				tjd1:[],
				tjd2:[],
				tjd3:[],
				tjd4:[],
				
				khid:0,
				khname:'',
				khdata:[],
				
				dpselshow:false,
				dpdata:'',
				dpid:0,
				dpname:'',
				rqmaxdate:'',
				
				
				
			}
		},
		onLoad(options) {
			let that=this;
			
		},
		onShow() {
			let that=this;
			let thkhdata=uni.getStorageSync('thkhdata');
			console.log(thkhdata)
			if(thkhdata){
				this.khid=thkhdata.id;
				this.khname=thkhdata.realname;
				this.loadData();
				uni.setStorageSync('thkhdata','');
			}else{
				that.loadData(); 
			}
		
		},
		methods: {
		
		
			searchmodal() {
				this.show3 = true
			},
			searchsubmit() {
				let that = this;
				this.thkw = this.thkwtt;
				uni. navigateTo({
					url:'./ddlist?isss=1&thkw='+this.thkwtt 
				})
				this.show3 = false;
			},
		    gotourl(url){
		    	uni.navigateTo({
		    		url:url+'&khid='+this.khid+'&khname='+this.khname+'&dpid='+this.dpid+'&dpname='+this.dpname
		    	})
		    },
		    gotosh(e){
				let type=e.currentTarget.dataset.type;
				uni.navigateTo({
					url:'./ddczsh/index?type='+type+'&khid='+this.khid+'&khname='+this.khname+'&dpid='+this.dpid+'&dpname='+this.dpname
				})
			},
		    selkh(){
		    	 uni.navigateTo({
		    		url:'/pages/selkh/index?stype=1'
		    	 })
		    },
		    
		    seldianpu() {
		    	this.dpselshow = true;
		    },
		    selthdp(e) {
		    	let that=this;
		    	let dpid = e.currentTarget.dataset.dpid;
		    	let dpname = e.currentTarget.dataset.dpname;
		        this.dpid=dpid;
		        this.dpname=dpname;
		    	this.loadData();
		    	this.dpselshow = false;
		    },
		    
		    clsdpid(){
		    	let that=this;
		    	this.dpid=0;
		    	this.dpname='';
		    	this.loadData();
		    	this.dpselshow = false;
		    },
		    clskhid(){
		    	let that=this;
		    	this.khid=0;
		    	this.khname='';
		    	this.loadData();
		    	this.dpselshow = false;
		    },
		
			// tab选项卡切换
			tabChange(index) {
				let that=this;
				let sta=that.stadata[index].sta;
				console.log(sta);
				that.current = index;
				that.sta = sta;
			    this.loadData();
			},
			loadData() {
				let that = this;
				let da = {
				   khid:that.khid ? that.khid : 0,
				   dpid:that.dpid ? that.dpid : 0,
				   rqkstime:that.rqkstime ? that.rqkstime : 0 ,
				   rqjstime:that.rqjstime ? that.rqjstime : 0 ,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/fuwu/index', da)
					.then(res => {
						uni.hideLoading();
						console.log(111222, res);
						if (res.errcode == 0) {
							
							that.dpdata = res.data.dpdata;
							that.tjd0=res.data.tjd0;
							that.tjd1=res.data.tjd1;
							that.tjd2=res.data.tjd2;
							that.tjd3=res.data.tjd3;
							that.tjd4=res.data.tjd4;
							that.rqmaxdate=res.data.rqmaxdate;
							
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() { 
									uni.navigateBack();
								}
							})
						}
					})
			
			},
				rqmodal(){
					this.rqshow=true
				},
				rqmodalchange(e){
					let that=this;
					if(e.startDate && e.endDate){
						this.thkw='';
						this.rqkstime=e.startDate;
						this.rqjstime=e.endDate;
						this.loadData();
					}
				},
				rqclssearch(){
					let that=this;
					this.rqkstime='';
					this.rqjstime='';
					this.loadData();
				},
		
		   
		}
	}
</script>

<style lang='scss'>
page {

}

.searchbtnfa{margin-right:32upx;}
.searchbtnfa .iconfont{font-size:24px;}

.flckselcon{padding:0 30upx;height:70upx;line-height:70upx;width:100%;z-index: 300;overflow:hidden;}
.flckselcon .icon-jiantou2{font-size:20upx;color:#888} 
.flckselcon .sf1{float:left}
.flckselcon .sf2{float:right;}
.flckselcon .wpflsel{}
.flckselcon .wpflsel .da0{float:left;margin-right:10upx}
.flckselcon .wpflsel .da1{float:left}
.flckselcon .wpflsel .da2{float:left}
.flckselcon .wpcksel{}


.fwxmcon{margin:40upx 32upx 46upx 32upx;background:#fff;border-radius:20upx;padding:20upx;position: relative;}
.fwxmcon .icon{position: absolute;left:20upx;top:-30upx}
.fwxmcon .icon image{width:100upx;} 
.fwxmcon .tit{font-size:28upx;padding-left:120upx;font-weight:700}
.fwxmcon .more{position: absolute;right:20upx;top:25upx;color:#ccc}

.xmtj{background:#fafafa;font-size:24upx;padding:20upx 30upx;margin-top:30upx;border-radius:20upx;}
.xmtj .cscon{display:flex;text-align: center;}
.xmtj .ite{flex: 1;position: relative;} 
.xmtj .ite .sz{font-size:28upx;color:#ff0000;}   
.xmtj .ite .tx{font-size:24upx;color:#333;}  
.xmtj .ite:not(:last-child):after {position:absolute;top:35%;right:0;content:'';width:1px;height:40%;-webkit-transform: scaleX(0.5);transform: scaleX(0.5);border-right: 1px solid #ccc;}



</style>
