<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>工资预支</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="sqbdcon">
				
				<block v-if="ygdata">
					<view class="maxcon">
						<view class="ttinfo">
							<view class="inf1">
								<view class="tf1">
									<view class="tx">
										<image :src="staticsfile + ygdata.avatar"></image>
									</view>
									<view class="xx">
										<view class="name">{{ygdata.realname}}</view>
										<view class="tel"><text>员工号:{{ygdata.zzhuid}}</text>
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</view>
						</view>
					</view>
				</block>

				<view class="maxcon">

					<view class="vstit">基本信息</view>
					<view class="rowx">
						<view class="tit">所属门店：</view>
						<view class="inpcon">
							<view class="inp">{{fydata.mdname}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">预支金额：</view>
						<view class="inpcon">
							<view class="inp" style="color:#ff0000">¥ {{fydata.jine}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<view class="rowx">
						<view class="tit">申请时间：</view>
						<view class="inpcon">
							<view class="inp">{{fydata.addtime}}</view>
						</view>
						<view class="clear"></view>
					</view>
					
					
					
					<view class="rowx">
						<view class="tit">收款方式：</view>
						<view class="inpcon">
							<view class="inp">{{fydata.txdtxt}}</view>
						</view>
						<view class="clear"></view>
					</view>
					<block v-if="fydata.txd==4">
					
					
						<view class="rowx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit">所属银行：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.b_name}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit">开户银行：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.b_kaihu}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit">开户姓名：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.b_huming}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
					
							<view class="tit">银行账号：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.b_number}}</view>
							</view>
							<view class="clear"></view>
						</view>
					
					</block>
					
					<block v-if="fydata.txd==3">
					
						<view class="rowx">
							<view class="tit">支付宝名称：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.skzfbname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">支付宝账号：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.skzfbhao}}</view>
							</view>
							<view class="clear"></view>
						</view>
					
						<view class="rowx" v-if="fydata.skzfbewm" style="height:200upx">
							<view class="tit"><text class="redst"></text>支付宝收款码</view>
							<view class="inpcon" style="padding-top:15upx">
								<view class="inp">
									<image :src="staticsfile + fydata.skzfbewm" @click="previewImagex2"
										:data-src="staticsfile + fydata.skzfbewm" style="width:180upx;height:180upx;">
									</image>
								</view>
							</view>
							<view class="clear"></view>
						</view>
					
					</block>
					
					<block v-if="fydata.txd==2">
					
						<view class="rowx">
							<view class="tit">微信名称：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.skwxname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">微信账号：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.skwxhao}}</view>
							</view>
							<view class="clear"></view>
						</view>
					
						<view class="rowx" v-if="fydata.skwxewm" style="height:200upx">
							<view class="tit"><text class="redst"></text>微信收款码</view>
							<view class="inpcon" style="padding-top:15upx">
								<view class="inp">
									<image :src="staticsfile + fydata.skwxewm" @click="previewImagex2"
										:data-src="staticsfile + fydata.skwxewm" style="width:180upx;height:180upx;">
									</image>
								</view>
							</view>
							<view class="clear"></view>
						</view>
					
					</block>
					
					
					
					
					
					
					
					<view class="bzcon">{{fydata.remark}}</view>

					<block v-if="fydata.picturescarr">
						<view class="pjimgcon">
							<block v-for="(itemx,indexx) in fydata.picturescarr" :key="indexx">
								<view class='item'>
									<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
										:data-picturescarr="fydata.picturescarr" @tap='previewImagex'>
									</image>
								</view>
							</block>
							<view class="clear"></view>
						</view>
					</block>

				</view>

				

				<block v-if="fydata.txstatus!=2">
					<view class="maxcon">
						<view class="vstit">审核信息</view>
					<!-- 	<view class="rowx">
							<view class="tit">审核人员：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view> -->
						<view class="rowx">
							<view class="tit">审核时间：</view>
							<view class="inpcon">
								<view class="inp">{{fydata.shtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit">审核状态：</view>
							<view class="inpcon">
								<view class="inp">
									<view class="stats " :class="'stacolor'+fydata.txstatus">{{fydata.statusname}}
									</view>
								</view>
							</view>
							<view class="clear"></view>
						</view>



						<view class="bzcon">{{fydata.bhsm}}</view>

						<view class="bzcon" v-if="fydata.premark">{{fydata.premark}}</view>

						<block v-if="fydata.picturescarr2">
							<view class="pjimgcon">
								<block v-for="(itemx,indexx) in fydata.picturescarr2" :key="indexx">
									<view class='item'>
										<image :src="staticsfile+itemx" :data-src='staticsfile + itemx '
											:data-picturescarr="fydata.picturescarr2" @tap='previewImagex'>
										</image>
									</view>
								</block>
								<view class="clear"></view>
							</view>
						</block>


					</view>
				</block>


			</view>



		


		</view>

		<view style="height:120upx"></view>




	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				djid: '',
				fydata: '',
				ygdata: '',
				bhsm: '',
				jjtgshow: false,

				img_url: [],
				img_url_ok: [],
				premark: '',
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			this.djid = djid;
			if (djid) {
				this.loadData();
			}
		},
		onShow() {

		},

		methods: {

			
			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/muser/gzyzview', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.fydata = res.data.fydata;
							that.bhsm = res.data.fydata.bhsm;
							that.ygdata = res.data.ygdata;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

	previewImagex2: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},
			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picturescarr = e.currentTarget.dataset.picturescarr;
				let imgarr = [];
				picturescarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				uni.previewImage({
					current: src,
					urls: imgarr
				});
			},




		}
	}
</script>

<style lang="scss">
	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {}

	.ttinfo .inf1 .tx {
		float: left
	}

	.ttinfo .inf1 .xx {
		float: left;
		margin-left: 20upx;
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}

	.bzcon {
		margin-top: 20upx;
	}

	.bhsmcon {
		margin: 10upx 0 30upx 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.pjimgcon {
		margin-top: 20upx;
	}

	.pjimgcon .item {
		float: left;
		margin-right: 15upx;
		margin-bottom: 15upx;
		position: relative
	}

	.pjimgcon image {
		width: 120upx;
		height: 120upx;
		display: block;
		border-radius: 8upx
	}


	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 115px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}

	.rowx:last-child {
		border-bottom: 0;
	}


	.stats.stacolor1 {
		color: #0DD400;
	}

	.stats.stacolor2 {
		color: #FA6400;
	}

	.stats.stacolor3 {
		color: #3366ff;
	}
	
	
	
	.rowxmttp {
		margin-top: 30upx
	}
	
	.imgconmt {}
	
	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}
	
	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}
	
	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}
	
	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}
</style>