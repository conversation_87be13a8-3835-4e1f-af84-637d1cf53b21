<template>
	<view class="mcn">
		<tn-nav-bar fixed   :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
			  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
			  		  <text  >登录密码</text>
			  		</view>
			  </tn-nav-bar>
		
		 
		
		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
		
		
		 <form @submit="formSubmit" >
		     <view class='fm-cell '>
		
		<block v-if="isoldpass==1">
		        <view class="fm-item">
		             <input  type="password" name='oldpassword' placeholder='请输入旧密码' maxlength='20' class="ssainput" />
		        </view>
		</block>
		        <view class="fm-item">
		             <input  type="password" name='newpassword' placeholder='请输入新密码' maxlength='20' class="ssainput"  />
		        </view>
		
		        <view class="fm-item">
		             <input  type="password" name='repassword' placeholder='再次输入密码' maxlength='20'  class="ssainput"  />
		        </view>
		
		        <view class="fm-btncon">
		          <button class="bomsubbtn" style="margin:0" form-type="submit">确认提交</button>
		        </view>
		
		      </view>
		    </form>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				isoldpass:0
			}
		},
		
		onShow() {
			this.loadData()
		},
		
		methods: {
			
			loadData() {
				let that=this;
				let da={};
				this.$req.post('/v1/muser/setloginpass', da)
					  .then(res => {
						 console.log(res);
						if(res.errcode==0){
								that.isoldpass = res.data.isoldpass;
						}else{
							uni.showToast({
								icon: 'none',
								title: res.msg,
								success() {
									setTimeout(function(){
										  uni.navigateBack()
									},1000)
								}
							});
						}
					  })
			},
					
			
			 formSubmit: function(e) {
			                var formdata = e.detail.value
							 let pvalue = e.detail.value;
							 	let that=this;
							 
							 if(that.isoldpass==1){
								 if(!pvalue.oldpassword){
								   uni.showToast({
									   icon: 'none',
										title: '请输入旧密码！',
								     	
								   });
								   return false;
								   
								 }
							 }
							      
							
							      if(!pvalue.newpassword){
									  uni.showToast({
										  icon: 'none',
									  		title: '请输入新密码！',
									 
									  });
							          return false;
							      }
							
							      if(!pvalue.repassword){
									  uni.showToast({
										  icon: 'none',
									  		title: '请再次输入新密码！',
									
									  });
							          return false;
							      }
							
							      if (pvalue.newpassword != pvalue.repassword) {
									uni.showToast({
										icon: 'none',
									  	title: '两次输入的密码不匹配！'
									});
							        return false;
							      }
			          
					  uni.showLoading({title: '处理中...'})
					  let da={
						  'oldpassword': pvalue.oldpassword,
						  'newpassword': pvalue.newpassword,
						  'repassword': pvalue.repassword
					  }
					  this.$req.post('/v1/muser/uploginpass', da)
					          .then(res => {
					  		    uni.hideLoading();
					  			if(res.errcode==0){
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function(){
												  uni.navigateBack()
											},1000)
										}
									});
					  			}else{
					  				uni.showToast({
					  					icon: 'none',
					  					title: res.msg
					  				});
					  			}
					  			
					          })
					          .catch(err => {
					  			console.log(err)
					          })
					  
			    },
		}
	}
</script>

<style>
page{background: #fff;}
	.fm-cell {padding: 50upx 70upx 40upx 70upx;background: #fff;border-radius:10upx;}
	.fm-item {border-bottom: 1px solid #efefef;}
	.fm-item input {height: 120upx;line-height: 120upx;font-size:14px;}
	.fm-btncon {margin-top: 40upx;}
	.fm-btncon button{}

</style>
