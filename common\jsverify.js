// 判断输入是否是有效的电子邮件
function isemail(str) {
  var result = str.match(/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/);
  if (result == null) return false;
  return true;
}


// 去除字符串的首尾的空格
function trim(str) {
  return str.replace(/(^\s*)|(\s*$)/g, "");
}


// 返回字符串的实际长度, 一个汉字算2个长度
function strlen(str) {
  return str.replace(/[^\x00-\xff]/g, "**").length;
}

// 判断输入是否是一个由 0-9 / A-Z / a-z 组成的字符串
function isalphanumber(str) {
  var result = str.match(/^[a-zA-Z0-9]+$/);
  if (result == null) return false;
  return true;
}

// 判断是否是数字
function isnumber(str) {
  // var reg = new RegExp("^[0-9]*$");
  // if(!reg.test(obj.value)){
  //     alert("请输入数字!");
  // }
  if (!/^[0-9]*$/.test(str)) {
    return false;
  } else {
    return true;
  }
}

//就否是正确的手机号
function isMobile(str) {
  if (!(/^1[34578]\d{9}$/.test(str))) {
    return false;
  } else {
    return true
  }
}

//是否是数组
function isArray(o) {
  return Object.prototype.toString.call(o) == '[object Array]';
}

module.exports={
  isemail, trim, strlen, isalphanumber, isnumber, isMobile, isArray

}