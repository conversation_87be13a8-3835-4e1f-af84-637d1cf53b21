<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	<tn-nav-bar fixed backTitle=' '  :bottomShadow="false" :zIndex="100">
					 <view slot="back" class='tn-custom-nav-bar__back'  >
						  <text class='icon tn-icon-left'></text>
					 </view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
					  <text  >物品调拨</text>
					</view>
					<view slot="right" >  
					</view> 
	</tn-nav-bar>

	<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>
    
   	<view class="" >

				  <form @submit="formSubmit" >
				  			<view class="sqbdcon">
								
								     	<view class="maxcon" >
								     		<view class="ttinfo">
								     			<view class="inf1"  >
								     				<view class="tf1">
													    <view class="spbh">编号：{{spdata.id}}</view>
								     					<view class="tx"><image :src="staticsfile + spdata.smallpic"></image></view>
								     					<view class="xx">
								     						<view class="name">{{spdata.title}}</view>
								     						<view class="tel">
																<text class="lanse">{{spdata.ckname}}</text>
																库存：<text class="lanse">{{spdata.kucun}}</text>
																调用中：<text class="lanse">{{spdata.dynum}}</text>
															</view>
								     					</view>
								     					<view class="clear"></view>
								     				</view>	
								     			</view>
								     		</view>
								     	</view>
								   
								
								<block v-if="ykrcddata">
								      <view class="maxcon" style="padding:10upx 28upx;">
										  	    <view class="vstit">商品当前移库记录</view>
												<view style="font-size:12px;line-height:23px;position: relative;">
													<view v-if="ykrcddata.ordernum"><text class="tit">相关单号：</text>{{ykrcddata.ordernum}}</view>
													<view><text class="tit">移出仓库：</text>{{ykrcddata.orckname}}</view>
													<view><text class="tit">移入仓库：</text>{{ykrcddata.zrckname}}</view>
													<view>
														<text class="tit">移库状态：</text> 
														<text class="dysta" :class="'ys'+ykrcddata.yksta">{{ykrcddata.ykstatxt}}</text>
													</view>
													<view><text class="tit">物品数量：</text><text class="slnum">{{ykrcddata.czkcnum}}</text>
													</view>
										
										                <block v-if="ykrcddata.yksta==2">
														       <view class="ghbtn" @click="yksh" data-sta=5 :data-id="ykrcddata.id">移库撤回</view>
														</block>
														  <block v-if="ykrcddata.yksta==1">
															   <view class="ghbtn" @click="yksh" data-sta=6 :data-id="ykrcddata.id">收回物品</view>
														</block>
													
												</view>
										</view> 
								</block>
								<block v-else>
										<view class="maxcon" style="padding:10upx 28upx">
											<view class="rowx"  @click="selddtype">
												<view class="icon"><text class="iconfont icon-jiantou" ></text></view>
												<view class="tit"><text class="redst"></text>调拨类型</view>
												<view class="inpcon">
													<view class="inp">
														 {{tbtype ? tbtypetxt : '请选择调拨类型'}}
													</view>
												</view>
												<view class="clear"></view>
											</view>
										</view>
								
								       <block v-if="tbtype==1">
											<view class="maxcon" style="padding:10upx 28upx">
												<view class="rowx" @click="selcangku"> 
													<view class="icon"><text class="iconfont  icon-jiantou"></text></view>
													<view class="tit">移入仓库</view>
													<view class="inpcon"><view class="inp">{{ckname ? ckname : '选择转入仓库'}}</view></view>
													<view class="clear"></view>
												</view>
											</view>
									   </block>
									   <block v-if="tbtype==2">
											<view class="maxcon" style="padding:10upx 28upx">
												<view class="rowx" @click="selyg"> 
													<view class="icon"><text class="iconfont  icon-jiantou"></text></view>
													<view class="tit">调用人员</view>
													<view class="inpcon"><view class="inp">{{thygname ? thygname : '选择调用人员'}}</view></view>
													<view class="clear"></view>
												</view>
											</view>
											<view class="maxcon" style="padding:10upx 28upx">
											    <view class="rowx">
											
													<view class="tit"><text class="redst"></text>物品数量</view>
													<view class="inpcon">
													   <view class="inp"><input class="input" type="number" name="czkcnum"   placeholder="请输入物品数量" placeholder-class="placeholder"   /></view>
													</view>
													<view class="clear"></view>
											    </view>
											</view>
									   </block>
								
									
										<view class="maxcon">
												<view class="vstit">调拨说明</view>
												
												<view class="bzcon">
													  <textarea name="remark" class="beizhu"></textarea>
												</view>
									
												<view class="rowxmttp">
													<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx"> 
																<block v-for="(item,index) in img_url_ok" :key="index">
																   <view class='item'  >
																	   <view class="del" @tap="deleteImg"   :data-index="index" ><i class="iconfont icon-shanchu2"></i></view>
																	   <image  :src="staticsfile+item" :data-src='staticsfile + item ' @tap='previewImage' ></image>
																   </view>
																</block>
															   <view class='item' v-if="img_url_ok.length <= 8" >
																   <image @tap="chooseimage"  src='/static/images/uppicaddx.png'></image>
															   </view>
															   <view class="clear"></view>
													 </view>
												</view>		
												 
												 
										</view>		 
				  				</block>
				  					
									
									
				  			</view>	
				  				<block v-if="!ykrcddata">
									<view class="tn-flex tn-footerfixed" >
									  <view class="tn-flex-1 justify-content-item tn-text-center">
										<button class="bomsubbtn"  form-type="submit">
										  <text>提交</text>
										</button>
									  </view>
									</view>
								</block>	
				  </form>
				  
				
				

			

	
	</view>
  
	<view style="height:120upx"></view>
    
	<tn-popup v-model="ckselshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
		<view class="popuptit">选择仓库</view>
	    <scroll-view scroll-y="true" style="height: 800rpx;"> 
	          <view class="xselcklist">
	              <block v-for="(item,index) in ckdata" :key="index">
					  
					  <view class="item" @click="selthck" :data-ckid="item.id"  :data-ckname="item.title"  >
						    <view class="xzbtn"  >选择</view>
					  		<view class="tx"><image :src="staticsfile + item.logo"></image></view>
					  		<view class="xx">
					  			<view class="name">{{item.title}}</view>
					  			<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}</view>
					  		</view>
					  		<view class="clear"></view>
					  </view>
					  
			      </block>		  
	          </view>
	        </scroll-view>
	</tn-popup>
	
	
	
  </view>
  
  
</template>

<script>



    export default {
		data(){
		  return {
			 staticsfile: this.$staticsfile,
			 html:'',
			 spid:0,
			 spdata:'',
			 status:0,
			 jine:0,
			 img_url: [],
			 img_url_ok:[],
			 systixian:'',
			 tipwztxt:'',
			
			  ckselshow:false,
			  thckdata:'',
			  ckdata:'',
			  ckid:0,
			  ckname:'',
			  
			  
			  tbtype:0,
			  tbtypetxt:'',
			  
			  thygid:0,
			  thygname:'',
			  ykrcddata:'',
			  
		  }
	},

	onLoad(options) {
		let that=this;
		let spid=options.spid ? options.spid : 0;
		this.spid=spid;
		this.loadData();
		
	},
	onShow() {
		let thygid=uni.getStorageSync('thygid');
		let thygname=uni.getStorageSync('thygname');
		console.log(thygid)
		if(thygid){
			this.thygid=thygid;
			this.thygname=thygname;
			this.loadData();
			uni.setStorageSync('thygid','');
			uni.setStorageSync('thygname','');
		}
	},

    methods: {
		yksh(e){
			let that = this;
			let id = e.currentTarget.dataset.id;
			let sta = e.currentTarget.dataset.sta;
		    let tip='确认撤销移库操作吗？';
			if(sta==6){
				tip='确认已将移库商品收回吗？';
			}
			uni.showModal({
				title: '',
				content: tip,
				success: function(e) {
					//点击确定
					if (e.confirm) {
						let da = {
							'rcdid': id,
							'sta': sta,
						}
						uni.showLoading({
							title: ''
						})
						that.$req.post('/v1/ckgl/spyksh', da)
							.then(res => {
								uni.hideLoading();
								if (res.errcode == 0) {
									uni.showToast({
										icon: 'none',
										title: res.msg,
										success() {
											setTimeout(function() {
												that.loadData();
											}, 1000);
										}
									});
								} else {
									uni.showModal({
										content: res.msg,
										showCancel: false,
									})
								}
							})
			
					}
			
				}
			})
			
		},
		selyg(){
			 uni.navigateTo({
				url:'/pages/selyg/index?stype=8'
			 })
		},
		selcangku(){
			this.ckselshow=true;
		},
		selthck(e){
			let ckid=e.currentTarget.dataset.ckid;
			let ckname=e.currentTarget.dataset.ckname;
			this.ckid=ckid;
			this.ckname=ckname;
			this.ckselshow=false;
		},
					
		
		toback:function(){
		   uni.navigateBack();
		},
					   
		loadData(){
				  let that=this;
				  let da={
					  spid:that.spid
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v1/ckgl/tiaobo', da)
				         .then(res => {
				 		    uni.hideLoading();
						    console.log(res);
				 			if(res.errcode==0){
								 that.ckdata=res.data.ckdata;
								 that.spdata=res.data.spdata;
								 that.ykrcddata=res.data.ykrcddata;
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false,
									success() {
										uni.navigateBack();
									}
				 				})
				 			}
				         })
			
		},
			selddtype:function(){
				  let that=this;
				  uni.showActionSheet({
					  itemList: ['移库','调用'],
					  success: function (res)
					  {
						var index = res.tapIndex;
						if(index==0){
							 that.tbtype=1;
							 that.tbtypetxt='移库';
							 that.thygid=0;
							 that.thygname='';
						}
						if(index==1){
							 that.tbtype=2;
							 that.tbtypetxt='调用';
							 that.ckid=0;
							 that.ckname='';
						}
					 
					  },
				  });
			  },
			chooseimage(){
								   let that = this;
								   let img_url_ok=that.img_url_ok;
								   uni.chooseImage({
									   count: 9, //默认9
									   sizeType: ['original', 'compressed'],
									   success: (resx) => {
										  const tempFilePaths = resx.tempFilePaths;
										  
										
												  for(let i = 0;i < tempFilePaths.length; i++) {
														  let da={
															  filePath:tempFilePaths[i],
															  name: 'file'
														  } 
														  uni.showLoading({title: '上传中...'})
														  this.$req.upload('/v1/upload/upfile', da)
																  .then(res => {
																	uni.hideLoading();
																	// res = JSON.parse(res);
																	if(res.errcode==0){
																		img_url_ok.push(res.data.fname);
																		that.img_url_ok=img_url_ok;
																		
																		uni.showToast({
																			icon: 'none',
																			title: res.msg
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
														  
														  })
												}
										   
									   }
								   });
							   
							},
							previewImage: function (e) {
									  let that = this;
									  let src = e.currentTarget.dataset.src;
									  let img_url_ok=that.img_url_ok;
									  let imgarr=[];
									  img_url_ok.forEach(function(item,index,arr){
										  imgarr[index] = that.staticsfile+item;
									  });
									  wx.previewImage({
										  current: src,
										  urls: imgarr
									  });
							},
							deleteImg: function (e) {
								let that = this;
								let index = e.currentTarget.dataset.index;
								let img_url_ok = that.img_url_ok;
								img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
								that.img_url_ok=img_url_ok;
							},
		
		
		
			formSubmit: function(e) {
						let that=this;
					   var formdata = e.detail.value
						
							
									 let pvalue = e.detail.value;
									 let tbtype = that.tbtype;
									 console.log(that.tbtype);
										 
										 if(tbtype==0){
											   uni.showToast({
												icon: 'none',
												title: '请选择调拨类型',
											   });
											   return false;
										 }
										 
										 
										 if(tbtype==1){
											 if(!that.ckid || that.ckid==0){
											   uni.showToast({
												icon: 'none',
												title: '请选择转入仓库',
											   });
											   return false;
											 }
										 }
										if(tbtype==2){
											 if(!that.thygid || that.thygid==0){
											   uni.showToast({
												icon: 'none',
												title: '请选择调用人员',
											   });
											   return false;
											 }
											 if(!pvalue.czkcnum || pvalue.czkcnum <= 0){
											 	   uni.showToast({
											 		icon: 'none',
											 		title: '请输入物品数量',
											 	   });
											 	   return false;
											 }
										}
										
									
										
										
										if(!pvalue.remark){
										  uni.showToast({
											icon: 'none',
											title: '请输入调拨说明',
										  });
										  return false;
										}
									  
								       uni.showModal({
													title: '',
													content: '以上信息已确认无误并提交吗？',
													success: function(e) {
														//点击确定
														if (e.confirm) {
												                   uni.showLoading({title: '处理中...'})
																   let da={
																		'tbtype': that.tbtype,
																		'spid': that.spid,
																		'remark': pvalue.remark ? pvalue.remark : '',
																		'czkcnum': pvalue.czkcnum ? pvalue.czkcnum : 0,
																		'pictures': that.img_url_ok ? that.img_url_ok  : '' ,
																		'zrckid': that.ckid ? that.ckid  : 0 ,
																		'dyygid': that.thygid ? that.thygid  : 0 ,
																   }
															
																   that.$req.post('/v1/ckgl/tiaobosave', da)
																  .then(res => {
																	uni.hideLoading();
																	console.log(res);
																	if(res.errcode==0){
																
																		uni.setStorageSync('thupsplist',1)
																		uni.setStorageSync('thupcksplist', 1)
																		uni.showToast({
																			icon: 'none',
																			title: res.msg,
																			success() {
																				setTimeout(function(){
																					uni.navigateBack({
																						delta: 2
																					})
																				},1000)
																			}
																		});
																	}else{
																		uni.showModal({
																			content: res.msg,
																			showCancel: false
																		})
																	}
																	
																  })
																  
															
														}
													}
								})	  
									  
									  
									  
									  
									  
									  
							  
		   },
			
		
	
    }
  }
</script>

<style lang="scss" >

.toplinex{border-top:1px solid #fff}

.matxcont{margin:32upx;padding:32upx;border-radius:20upx;background: #fff;min-height: 500upx;}
.notixiancon{text-align: center;}
.tipcon{padding:50px 0}
.tipcon text{font-size:55px;color:#FFD427}
.smlink{margin-top:40px}


.ttinfo{position: relative;}
.ttinfo .icon{position: absolute;right:0upx;top:30upx}
.ttinfo .icon .iconfont{color:#ddd}
.ttinfo .inf1{}
.ttinfo .lanse{color:#1677FF;font-size:24upx}
.ttinfo .inf1 .tx{float:left}
.ttinfo .inf1 .xx{float:left;margin-left:20upx;padding-top:10upx}
.ttinfo .inf1 .tx image{width:100upx;height:100upx;display: block;border-radius:10upx;}
.ttinfo .inf1 .name{font-size: 28rpx;font-weight:600;height:40upx;line-height:40upx;}
.ttinfo .inf1 .tel{color: #333;font-size: 24rpx;margin-top:10upx}
.ttinfo .inf1 .tel text{margin-right:30upx}
.ttinfo .spbh{position: absolute;right:0;top:0;font-size:20upx;color:#1677FF}

.maxcon{background:#fff;border-radius:20upx;padding:28upx;margin-bottom:20upx}
.maxcon .vstit{margin-bottom:20upx}


.sqbdcon{margin:32upx;}


.beizhu{border:1px solid #efefef;padding:20upx;height:200upx;border-radius:10upx;width:100%;}

.rowxmttp{margin-top:30upx}
.imgconmt{ }
.imgconmt .item{float:left;margin-right:20upx;margin-bottom:20upx;position: relative}
.imgconmt .item .del{position: absolute;right:-7px;top:-7px;z-index:200}
.imgconmt .item .del i{font-size:18px;color:#333}
.imgconmt image{width:160upx;height: 160upx;display: block;border-radius:3px}

.jinecon{position: relative;margin:40upx 0}
.jinecon .icon{position: absolute;left:0;top:10upx;}
.jinecon .icon .iconfont{font-size:40upx}
.jinecon .inputss{font-size:40upx;color:#000;padding-left:40upx}



.rowx{
	position: relative;
	border-bottom:1px solid #F0F0F0;
}
.rowx .icon{position: absolute;right:0;top:0;height:90upx;line-height:90upx}
.rowx .icon .iconfont{color:#ddd}
.rowx .tit{float:left;font-size:28upx;color:#333;height:90upx;line-height:90upx;}
.rowx .tit .redst{color:#ff0000;margin-right:2px;}
.rowx .tit .redst2{color:#fff;margin-right:2px;}
.rowx .inpcon{padding:0 5px;float:right;width: calc(100% - 75px);font-size:28upx;height:90upx;line-height:90upx;}
.rowx .inpcon.hs{color:#888}
.rowx .inp{color: #7F7F7F;}
.rowx .inp .input{height:90upx;line-height:90upx;}
.rowx:last-child{border-bottom:0;}
.dysta.ys1 {
		color: #4aac09
	}

	.dysta.ys2 {
		color: #ff6600
	}

	.dysta.ys3 {
		color: #ff0000
	}
 .ghbtn {
 	height: 45upx;
 	line-height: 45upx;
 	background: #FFD427;
 	border-radius: 5upx;
 	padding: 0 20upx;
 	position: absolute;
 	right: 30upx;
 	top: 150upx
 }
</style>

