<template>
  <view class="pages-a tn-safe-area-inset-bottom">
	  
	  
	  <tn-nav-bar fixed  customBack  :bottomShadow="false" >
	  			 <view slot="back" class='tn-custom-nav-bar__back'
	  				  >
	  				  <text class='icon tn-icon-left'></text>
	  			 </view>
	  		<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
	  		  <text class="tn-text-bold tn-text-xl "  >
				  
				  <block v-if="sortdata.length > 1">
				  	  {{lmname || ''}}
				  </block>
				  <block v-else>
				  	 {{viewdata.title || ''}}
				  </block>
				  
			  </text>
	  		</view>
	  </tn-nav-bar>
	
	  

	  
	
    <block v-if="sortdata.length > 1">
		<!-- 顶部自定义导航 -->
		<view class="tabs-fixed tn-bg-white tn-shadow"  >
		  <view class="tn-flex tn-flex-col-between tn-flex-col-center " :style="{marginTop: vuex_custom_bar_height + 'px'}">
			<view class="justify-content-item" style="width: 100vw;overflow: hidden;">
			  <tn-tabs :list="sortdata" :current="current" :isScroll="true" :activeColor="mbjson.ztcolor" :bold="true" :fontSize="28" :badgeOffset="[20, 30]" @change="tabChange" backgroundColor="#FFFFFF" :height="70"></tn-tabs>
			</view>
		
		  </view>  
		</view>
	</block>	
    
    <view class="" :style="{paddingTop: vuex_custom_bar_height + 'px'}" >
        <block v-if="sortdata.length > 1">
		   <view style="height:45px"></view>
		</block>
     
      <view class="viewcon">
      	  <view class="nrcon">
			  <mp-html :content="viewdata.content" />
      		  <!-- <jyf-parser :html="html" ref="article"></jyf-parser> -->
      	  </view>
      </view>
        
    
    
    </view>
  
	<view style="height:20px"></view>
    
  </view>
  
  
</template>

<script>

	import template_page_mixin from '@/libs/mixin/template_page_mixin.js'
    export default {
    name: 'about',
	mixins: [template_page_mixin],
    data(){
      return {
		 mbjson:'',
		 staticsfile: this.$staticsfile,
		 sortdata: '',
		 sortid:0, 
		html:'',
		id:0,
		type:0,
		viewdata:[],
		lmname:'',
		index: 1,
		current: 0
        
      }
    },

	onLoad(options) {
		let that=this;
			let id=options.id;
			this.id=id;
			this.loadData();
			
			let mbjson=uni.getStorageSync('mbjson');
			if(mbjson){
				that.mbjson = mbjson;
			}
			
		},


    methods: {
		
		loadData(){
				  let that=this;
				  let id=that.id; 
				  let da={  
				 	id:id,
				  }
				 uni.showLoading({title: ''})
				 this.$req.post('/v2/about/index', da)
				         .then(res => {
				 		    uni.hideLoading();
						     console.log(res);
				 			if(res.errcode==0){
				 			   that.viewdata=res.data.viewdata;
				 			   that.sortdata=res.data.sortdata;
				 			   that.lmname=res.data.lmname;
				 			   // that.html=res.data.viewdata.content;
							  //    uni.setNavigationBarTitle({
									//  title: res.data.viewdata.title
								 // });
				 			   // this.$refs.article.setContent(res.data.viewdata.content);
				 			}else{
				 				uni.showModal({
				 					content: res.msg,
				 					showCancel: false
				 				})
				 			}
				         })
			
		},
			
		

      // tab选项卡切换
      tabChange(index) {
		let that=this;
		let sortid=that.sortdata[index].sortid;
        that.current = index
        that.id = sortid
		this.loadData();
      },
	  swtab(){
		let that=this;
		 that.current = 0;
		that.id = 0;
		this.loadData();
	  },
    
    }
  }
</script>

<style lang="scss" scoped>
  
  .pages-a { 
	  max-height: 100vh;
    // background-image: linear-gradient(to top, #4C3FAE 20%, #6E26BA 80%);
  }
  
	
     .viewcon{margin:10upx 30upx 30upx 30upx;}
     .viewcon .title{font-size:16px;line-height:50upx;}
     .viewcon .rq{font-size:11px;color:#080808;margin:30upx 0 }
     .viewcon .nrcon{color:#080808;line-height:55upx;font-size:14px }
     .viewcon .nrcon image{max-width:100%;}
  
  /* 底部安全边距 start*/
  .tn-tabbar-height {
  	min-height: 250upx;
  	height: calc(140upx + env(safe-area-inset-bottom) / 2);
  }
  
  
  /* 底部tabbar假阴影 start*/
  .bg-tabbar-shadow{
    // background-image: repeating-linear-gradient(to top, rgba(0,0,0,0.1) 10rpx, rgba(255,255,255,0) , rgba(255,255,255,0));
    box-shadow: 0rpx 0rpx 400rpx 0rpx rgba(0, 0, 0, 0.25);
    position: fixed;
    bottom: calc(0rpx + env(safe-area-inset-bottom) / 2);
    bottom: calc(0rpx + constant(safe-area-inset-bottom));
    height: 60rpx;
    width: 100vw;
    z-index: 0;
  }
  
  /* 阴影 start*/
  .home-shadow {
    border-radius: 15rpx;
    box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
  }    
  
  
  
  .tabs-fixed{
    position: fixed;
    top: 0;
    width: 100%;
    transition: all 0.25s ease-out;
    z-index: 1000;
  }
  
  
</style>

