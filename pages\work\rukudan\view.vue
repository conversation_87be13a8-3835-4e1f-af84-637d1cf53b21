<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>{{djdata.ddtypetxt}}详情</text>
			</view>
			<view slot="right">
			</view>
		</tn-nav-bar>

		<view class="toplinex" style="background:#fff;" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<view class="ddtopcon">
				<view class="ddbh">
					{{djdata.ordernum}}
				</view>

				<block v-if="djdata.status==1">
					<view class="ddstacon" :class="'fwyssta'+djdata.ddsta ">{{djdata.ddstatusname}}</view>
				</block>
				<block v-else>
					<view class="ddstacon" :class="'ys'+djdata.status">{{djdata.statusname}}</view>
				</block>

				<view class="ddtticon">
					<image src="/static/images/ddtoicon1.png" style="width:105px;height:90px;"></image>
				</view>
			</view>


			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon" style="padding-bottom:10upx">
						<view class="vstit">{{djdata.ddtypetxt}}信息</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务类型</view>
							<view class="inpcon">
								<view class="inp" style="color:#1677FF">{{djdata.ddtypetxt}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx" style="display: none;">
							<view class="tit"><text class="redst"></text>单据编号</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ordernum}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户姓名</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khname}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户电话</view>
							<view class="inpcon">
								<view class="inp">{{djdata.khtel}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<block v-if="djdata.ddtype==1">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>寄卖时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">

										{{djdata.ywdqtime}}
								
											<text style="color:#1677FF;z-index:200;margin-left:20upx"
												@click="xqrcdv">续期记录</text>
										

									</view>

								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-if="djdata.ddtype==2 && djdata.ywtime>0">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>回收时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="djdata.ddtype==3">
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>暂存时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>到期时间</view>
								<view class="inpcon">
									<view class="inp">
										{{djdata.ywdqtime}}
										<block v-if='xqrcddata.length > 0'>
											<text style="color:#1677FF;z-index:200;margin-left:20upx"
												@click="xqrcdv">续期记录</text>
										</block>

									</view>

								</view>
								<view class="clear"></view>
							</view>
						</block>

						<block v-if="djdata.ddtype==4">

							<view class="rowx" v-if="vsour!=2">
								<view class="tit"><text class="redst"></text>预付金额</view>
								<view class="inpcon">
									<view class="inp">￥{{djdata.yfjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>预付时间</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywtime}}</view>
								</view>
								<view class="clear"></view>
							</view>


							<view class="rowx">
								<view class="tit"><text class="redst"></text>还款方式</view>
								<view class="inpcon">
									<view class="inp" style="color:#1677FF">{{djdata.hkfstxt}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="icon" style="color:#1677FF;z-index:200;">{{djdata.ywzqdwtxt}}</view>
								<view class="tit"><text class="redst"></text>还款周期</view>
								<view class="inpcon">
									<view class="inp">{{djdata.ywzqnum}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>利率</view>
								<view class="inpcon">
									<view class="inp">{{djdata.lilv}}%</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>总利息</view>
								<view class="inpcon">
									<view class="inp">￥{{djdata.lxzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>总还款金额</view>
								<view class="inpcon">
									<view class="inp">￥{{djdata.hkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<!-- 		<block v-if="djdata.hkfs==2">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>到期时间21</view>
									<view class="inpcon">
										<view class="inp">
											{{djdata.dqtimetxt}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</block> -->

							<block v-if="djdata.hkfs==1">
								<view class="rowx" @click="ckhkjh">
									<view class="icon"><text class="iconfont icon-jiantou"></text></view>
									<view class="tit"><text class="redst"></text>还款计划</view>
									<view class="inpcon">
										<view class="inp">
											{{hkjhtip}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>


							<view class="rowx">
								<view class="tit"><text class="redst"></text>已还款金额</view>
								<view class="inpcon">
									<view class="inp colorls">￥{{djdata.sjhkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx">
								<view class="tit"><text class="redst"></text>待还款金额</view>
								<view class="inpcon">
									<view class="inp colorhs">￥{{djdata.whkzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>



							<block v-if="djdata.hkfs==2">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>到期时间</view>
									<view class="inpcon">
										<view class="inp">
											{{djdata.dqtimetxt}}
											<block v-if='xqrcddata.length > 0'>
												<text style="color:#1677FF;z-index:200;margin-left:20upx"
													@click="xqrcdv">续期记录</text>
											</block>
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
							<block v-if="djdata.hkjqtime !=0 ">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>结清时间</view>
									<view class="inpcon">
										<view class="inp">
											{{djdata.hkjqtime}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>

						</block>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>开单时间</view>
							<view class="inpcon">
								<view class="inp">{{djdata.addtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>


					<block v-if="djdata.ddtype!=4">

						<view class="maxcon">

							<view class="vstit" style="margin-bottom:30upx">物品列表</view>

							<block v-if="djshop">
								<view class="splist">
									<block v-for="(item,index) in djshop" :key="index">
										<view class="spitem">
											<view class="spname">
												<view class="tline"></view>
												{{item.title}}
												<text
													style="font-weight:400;font-size:28upx;margin-left:20upx">({{item.id}})</text>
											</view>
											<!-- 	 <view class="rowa">
																 	<view class="tip">物品编号</view>
																 	<view class="nr colorls" >({{item.id}})</view>
																 	<view class="clear"></view>
																 </view> -->
											<view class="rowa">
												<view class="tip">{{dtptxt}}{{dtpjgtxt}}</view>
												<view class="nr">
													¥{{item.danjia}} x {{item.shuliang}} =
													¥<text>{{item.zongjia}}</text>
												</view>
												<view class="clear"></view>
											</view>
											<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
												<view class="rowa">

													<view class="tip" @click="qtfymx" :data-spid="item.id"
														:data-spname="item.title">其他收费 <text
															class="iconfont icon-yewucaozuo"
															style="margin-left:10upx;color:#1677FF"></text> </view>
													<view class="nr"><text @click="qtfymx" :data-spid="item.id"
															:data-spname="item.title">¥{{item.fjfjine}}</text></view>
													<view class="clear"></view>

												</view>
											</block>

											<view class="rowa">
												<view class="tip" @click="qtcbmx" :data-spid="item.id"
													:data-spname="item.title">其他成本 <text
														class="iconfont icon-yewucaozuo"
														style="margin-left:10upx;color:#1677FF"></text> </view>
												<view class="nr">
													<text @click="qtcbmx" :data-spid="item.id"
														:data-spname="item.title">¥{{item.cbfjine}}</text>
												</view>
												<view class="clear"></view>
											</view>


											<block v-if="djdata.ddtype==1">
												<view class="rowa">
													<view class="tip">服务费比例</view>
													<view class="nr">{{item.fwfbl}} %</view>
													<view class="clear"></view>
												</view>
											</block>
									
									<block v-if="djdata.ddtype==1 ">
										<view class="rowa">
											<view class="tip">寄卖服务费</view>
											<view class="nr" style="color:#ff0000">¥{{item.fwfjine}}</view>
											<view class="clear"></view>
										</view>
									</block>
									<block v-if="djdata.ddtype==3">
										<view class="rowa">
											<view class="tip">服务费金额</view>
											<view class="nr" style="color:#ff0000">¥{{item.fwfjinenow}}</view>
											<view class="clear"></view>
										</view>
									</block>
									
											<block v-if="djdata.status==1">
												<view class="rowa">
													<view class="tip">物品状态</view>
													<view class="nr">
														<text class="status"
															:class="'sta'+item.status">{{item.statusname}}</text>
													</view>
													<view class="clear"></view>
												</view>
											</block>
											<view class="rowa">
												<view class="tip">验货员</view>
												<view class="nr">{{item.yhrystr ? item.yhrystr : '--' }}</view>
												<view class="clear"></view>
											</view>
											<block v-if="djdata.ddtype==1 || djdata.ddtype==2">
												<view class="rowa">
													<view class="tip">上架价格</view>
													<view class="nr">¥{{item.sjjiage}}</view>
													<view class="clear"></view>
												</view>
											</block>


											<block v-if="item.vxq==1">
												<block v-for="(itemx,indexx) in item.zdysxarr" :key="indexx">
													<view class="rowa">
														<view class="tip">{{itemx.title}}</view>
														<view class="nr">{{itemx.value}}</view>
														<view class="clear"></view>
													</view>
												</block>

												<view class="rowa">
													<view class="tip">仓库</view>
													<view class="nr">{{item.ckname}}</view>
													<view class="clear"></view>
												</view>


												<block v-if="item.picturesarr">
													<view class="rowxmttp">
														<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
															<block v-for="(itemxx,indexx) in item.picturesarr"
																:key="indexx">
																<view class='item'>
																	<image :src="staticsfile+itemxx"
																		:data-src='staticsfile + itemxx '
																		@click='previewImage'
																		:data-picarr="item.picturesarr"></image>
																</view>
															</block>
															<view class="clear"></view>
														</view>
													</view>
												</block>

												<block v-if="item.beizhu">
													<view class="bzcon">
														<view class="bztit">备注：</view>
														<view class="bznr">{{item.beizhu}}</view>
													</view>
												</block>

											</block>


											<view class="viewxqcon" @click="setvxq" :data-spid="item.id">
												<block v-if="item.vxq==0">
													展开详情<text class="iconfont icon-jtdown2"></text>
												</block>
												<block v-else>
													收起详情<text class="iconfont icon-jtup2"></text>
												</block>
											</view>


											<block v-if="djdata.ddtype==2">


												<block v-if="item.czfrrydata">
													<view class="czfrcon">
														<view class="cstit">
															<view class="tit"><text
																	class="iconfont icon-renminbi"></text>
																出资</view>

															<view class="clear"></view>

														</view>
													</view>
													<block v-for="(itemx,indexxxx) in item.czfrrydata" :key="indexxxx">
														<view class="czrli">

															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm">出资金额：<text
																		:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
																</view>

																<view class="clear"></view>
															</view>
														</view>
													</block>
													<!-- 			<view class="czrli" v-if="isvallfrcz==1">
														<view class="xx">
															<view class="czxm hj">已出资金额：<text
																	class="">{{item.czjinehj}}</text> <text
																	class="gx">,</text> 未出资金额：<text
																	class="hs">{{item.czjinehj2}}</text></view>
															<view class="clear"></view>
														</view>
													</view> -->
												</block>
											</block>




											<block v-if="djdata.ddtype==3">

												<block v-if="item.czfrrydata">
													<view class="czfrcon">
														<view class="cstit">
															<view class="tit"><text
																	class="iconfont icon-renminbi"></text>出资
															</view>

															<view class="clear"></view>

														</view>
													</view>


													<block v-for="(itemx,indexx) in item.czfrrydata" :key="indexx">
														<view class="czrli">

															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm">出资金额：<text
																		:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
																</view>
																<view class="czxm">分润金额：<text
																		:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
																</view>
																<view class="czxm" v-if="itemx.ksjine > 0">
																	亏损金额：<text
																		style="color:#4aac09">{{itemx.ksjine}}</text>
																</view>
																<view class="clear"></view>
															</view>
														</view>
													</block>
													<!-- 		<view class="czrli last" v-if="isvallfrcz==1">
														<view class="xx">
															<view class="czxm hj">已出资金额：<text
																	class="">{{item.czjinehj}}</text> <text
																	class="gx">,</text> 未出资金额：<text
																	class="hs">{{item.czjinehj2}}</text></view>
															<view class="clear"></view>
														</view>
													</view> -->
												</block>






												<block v-if="item.czfrrydata3.length > 0">
													<view class="czfrcon">
														<view class="cstit">
															<view class="tit"><text
																	class="iconfont icon-renminbi"></text>
																分润</view>
															<view class="clear"></view>
														</view>
													</view>
													<block v-for="(itemx,indexx3) in item.czfrrydata3" :key="indexx3">
														<view class="czrli">
															<view class="cname">{{itemx.ygname}}
																<text class="czjs">({{itemx.rytypetxt}})</text>
															</view>
															<view class="xx">
																<view class="czxm">分润金额：<text
																		:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
																</view>
																<view class="czxm" v-if="itemx.ksjine > 0">
																	亏损金额：<text
																		style="color:#4aac09">{{itemx.ksjine}}</text>
																</view>
																<view class="clear"></view>
															</view>
														</view>
													</block>
												</block>
											</block>


										</view>
									</block>
								</view>
							</block>

						</view>

					</block>


					<!-- 信用预付 -->
					<block v-if="djdata.ddtype==4">
						<block v-if="djdata.czfrrydata.length > 0">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 出资</view>


								<view class="czry4info">
									<block v-for="(itemx,indexx) in djdata.czfrrydata" :key="indexx">
										<view class="czrli">


											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">出资金额：<text <text
														:class="itemx.czjine > 0 ? '' : ''">{{itemx.czjine}}</text>
												</view>
												<block v-if="czfrisfr==1">
													<view class="czxm">分润金额：<text
															:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
													</view>

												</block>
												<view class="czxm" v-if="itemx.ksjine > 0">
													亏损金额：<text style="color:#4aac09">{{itemx.ksjine}}</text>
												</view>

												<view class="clear"></view>
											</view>
										</view>
									</block>

								</view>



							</view>
						</block>
					</block>
					<block v-if="djdata.ddtype==4 || djdata.ddtype==3">
						<block v-if="djdata.czfrry2data.length > 0">
							<view class="maxcon" style="padding-bottom:10upx">
								<view class="vstit"><text class="iconfont icon-renminbi"></text> 分润</view>
								<view class="czry4info">
									<block v-for="(itemx,indexx) in djdata.czfrry2data" :key="indexx">
										<view class="czrli">

											<view class="cname">{{itemx.ygname}}
												<text class="czjs">({{itemx.rytypetxt}})</text>
											</view>
											<view class="xx">
												<view class="czxm">分润金额：<text
														:class="itemx.frjine > 0 ? 'ysz' : ''">{{itemx.frjine}}</text>
												</view>
												<view class="czxm" v-if="itemx.ksjine > 0">
													亏损金额：<text style="color:#4aac09">{{itemx.ksjine}}</text>
												</view>
												<view class="clear"></view>
											</view>
										</view>
									</block>

								</view>

							</view>
						</block>

						<view class="maxcon" style="padding-bottom:10upx" v-if="isvallfrcz==1">
							<block v-if="djdata.ddtype==4">
								<!-- 	<view class="czrli">
									<view class="xx">
										<view class="czxm hj">已出资金额：<text class="">{{djdata.czjinehj}}</text> <text
												class="gx">,</text> 未出资金额：<text class="hs">{{djdata.czjinehj2}}</text>
										</view>
										<view class="clear"></view>
									</view>
								</view> -->
							</block>
							<block v-if="czfrisfr==1">
								<view class="czrli">
									<view class="xx">
										<view class="czxm hj" style="width:100%;">已分润金额：<text
												class="">{{djdata.frjinehj}}</text> <text class="gx">,</text>
											未分润金额：<text class="hs">{{djdata.frjinehj2}}</text></view>
										<view class="clear"></view>
									</view>
								</view>
							</block>
						</view>
					</block>

					<block v-if="djdata.ddtype!=4">
						<view class="maxcon" style="padding-bottom:10upx">
							<view class="vstit">费用信息</view>


							<view class="rowx">
								<view class="tit"><text class="redst"></text>物品总价</view>
								<view class="inpcon">
									<view class="inp">{{djdata.spzongjia}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<block v-if="djdata.ddtype==1">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>服务费</view>
									<view class="inpcon">
										<view class="inp">{{djdata.fwfzjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
							<block v-if="djdata.ddtype==3">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>服务费</view>
									<view class="inpcon">
										<view class="inp">{{djdata.fwfzjinenow}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
							<block v-if="djdata.ddtype==1 || djdata.ddtype==3">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>其他收费</view>
									<view class="inpcon">
										<view class="inp">{{djdata.fjfzjine}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>

							<view class="rowx">
								<view class="tit"><text class="redst"></text>其他成本</view>
								<view class="inpcon">
									<view class="inp">{{djdata.cbfzjine}}</view>
								</view>
								<view class="clear"></view>
							</view>

							<block v-if="djdata.ddtype==2">
								<view class="rowx">
									<view class="tit"><text class="redst"></text>单据总价</view>
									<view class="inpcon">
										<view class="inp" style="color:#ff0000">¥{{djdata.zongjia}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
							<block v-else>
								<view class="rowx">
									<view class="tit"><text class="redst"></text>总费用</view>
									<view class="inpcon">
										<view class="inp" style="color:#ff0000">¥{{djdata.zongfy}}</view>
									</view>
									<view class="clear"></view>
								</view>
							</block>
						</view>
					</block>

					<view class="maxcon" style="padding-bottom:10upx">

						<view class="rowx">
							<view class="tit"><text class="redst"></text>业务人员</view>
							<view class="inpcon">
								<view class="inp">{{djdata.ywrytxtstr}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="rowx">
							<view class="tit"><text class="redst"></text>录单人员</view>
							<view class="inpcon">
								<view class="inp">{{djdata.opname}}</view>
							</view>
							<view class="clear"></view>
						</view>

					</view>

					<view class="maxcon">
						<view class="rowxmttp">
							<view class="tit" style="font-weight:600"><text class="redst"></text>身份信息</view>
							<view class="zjzkicon" @click="setvzz">{{vsfnr==0 ? '展开' : '收起'}} <text
									class="iconfont icon-jiantou_liebiaozhankai"></text></view>




							<block v-if="vsfnr==1">
								<view class="uprzimgcon">
									<view class="zzx sf1">
										<view class="con">
											<view class="vt1">
												<block v-if="djdata.rzimg1">
													<image :src='staticsfile + djdata.rzimg1'></image>
												</block>
												<block v-else>
													<image src="/static/images/rzupimg1.png"></image>
												</block>
											</view>
											<view class="vt2">身份证头像面</view>
											<view class="clear"></view>
										</view>
									</view>
									<view class="zzx sf2">
										<view class="con">
											<view class="sf2">
												<block v-if="djdata.rzimg2">
													<image :src='staticsfile + djdata.rzimg2'></image>
												</block>
												<block v-else>
													<image src="/static/images/rzupimg2.png"></image>
												</block>
											</view>
											<view class="vt2">身份证国徽面</view>
										</view>
									</view>
									<view class="clear"></view>
								</view>


								<view class="rowx" style="margin-top:20upx;" v-if="djdata.khsfzhao">
									<view class="tit"><text class="redst"></text>身份证号</view>
									<view class="inpcon">
										<view class="inp">{{djdata.khsfzhao}}</view>
									</view>
									<view class="clear"></view>
								</view>

								<view class="rowx" v-if="djdata.isrlsb==1">
									<view class="tit"><text class="redst"></text>人脸识别</view>
									<view class="inpcon">
										<view class="inp">
											{{djdata.isrlsb==1 ? '需要' : '不需要'}}
										</view>
									</view>
									<view class="clear"></view>
								</view>
								<block v-if="djdata.isrlsb==1">
									<view class="rowx">
										<view class="icon" style="z-index:200;" @click="gotorlsbewm"><text
												class="iconfont icon-qrcode-1-copy"
												style="color:#1677FF;margin-right:10upx"></text> 认证二维码</view>
										<view class="tit"><text class="redst"></text>识别状态</view>
										<view class="inpcon">
											<view class="inp">
												<text :class="'rlsbsta'+ djdata.rlsbsta ">{{djdata.rlsbstatxt}}</text>
											</view>
										</view>
										<view class="clear"></view>
									</view>
								</block>
							</block>
						</view>
					</view>

					<view class="maxcon">
						<view class="vstit" style="font-weight:400;margin-bottom:25upx">单据备注说明 <text
								style="margin-left:10upx;font-size:24upx;color:#ff6600">（用户不可见）</text></view>

						<view class="bzcon">
							{{remark}}
						</view>

					</view>

				</view>

				<block v-if="djdata.ddsta==12">
					<view class="maxcon">
						<view class="vstit" style="margin-bottom:25upx">亏损说明</view>

						<view class="rowx">
							<view class="tit"><text class="redst"></text>亏损金额</view>
							<view class="inpcon">
								<view class="inp" style="color:#4aac09">{{djdata.ksjine}}</view>
							</view>
							<view class="clear"></view>
						</view>
						<view class="bzcon" v-if="djdata.ksremark" style="margin-top:20upx">
							{{djdata.ksremark}}
						</view>

						<block v-if="djdata.kspicturesarr">
							<view class="rowxmttp">
								<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
									<block v-for="(itemxx,indexx) in djdata.kspicturesarr" :key="indexx">
										<view class='item'>
											<image :src="staticsfile+itemxx" :data-src='staticsfile + itemxx '
												@click='previewImagex'></image>
										</view>
									</block>
									<view class="clear"></view>
								</view>
							</view>
						</block>

					</view>
				</block>


				<!-- 	<view class="tn-flex tn-footerfixed">
							  <view class="tn-flex-1 justify-content-item tn-text-center">
								  <view class="bomsfleft">
									  <button class="bomsubbtn"  @click="toback"  style="background:#ddd;" >
										  <text>上一步</text>
									  </button>
								  </view>
								  <view class="bomsfright">
									  <button class="bomsubbtn"  form-type="submit">
									    <text>提交审核</text>
									  </button>
								  </view> 	  
								  <view class="clear"></view>	
							  </view>
							</view> -->


			</form>








		</view>

		<view style="height:20upx"></view>


		<tn-popup v-model="hkjhshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit" style="border-bottom:0">还款计划</view>

			<view class="hkjh-time-smtip">借款{{djdata.ywzqnum}}{{djdata.ywzqdwtxt}}，应还总额</view>
			<view class="hkjh-time-smjine">¥{{djdata.hkzjine}}</view>

			<scroll-view scroll-y="true" style="height: 700rpx;">

				<view class="khjhcon">
					<view class="hkjh-timeline">
						<view class="hkjh-timeline-content">
							<block v-for="(item,index) in hkjhdata" :key="index">
								<view class="item hkjh-timeline-item" :class="'hksta'+item.status">
									<em class="yd-timeline-icon"></em>
									<view class="hkjine">
										¥{{item.yhkzjine}}
										<block v-if="item.bqsyhkjine > 0">
											<text class="yhtip">已还 {{item.bqyhkjine}}</text>
										</block>
										<text class="yhqtxt" v-if="item.status==1">已还清</text>
										<text class="yyqtxt" v-if="item.status==3">已逾期</text>
									</view>
									<!-- <view class="bjlx">本金 {{item.yhkbj}} 元 + 利息 {{item.yhklx}} 元 </view> -->
									<view class="qbcon">
										<view class="t">{{item.xhtit}}</view>
										<view class="r">{{item.rq}}</view>
									</view>
								</view>
							</block>
						</view>
					</view>
				</view>

			</scroll-view>
		</tn-popup>



		<tn-popup v-model="qtfymxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他收费明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtfymxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtfymxspname}}
				</view>
				<view class="qtfylist">

					<block v-if="qtfydata.length>0">

						<block v-for="(item,index) in qtfydata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">

										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他收费</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>



		<tn-popup v-model="qtcbmxshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit">其他成本明细</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="qtcbmxsptit"><text class="iconfont icon-zanwushuju" style="margin-right:10upx"></text>
					{{qtcbmxspname}}
				</view>
				<view class="qtcblist">

					<block v-if="qtcbdata.length>0">
						<block v-for="(item,index) in qtcbdata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<block v-if="item.type==1">
											<view><text>金额：</text> <text class="jine">￥{{item.orjine}}</text></view>
										</block>
										<block v-else>
											<view><text>金额：</text> <text class="jine"
													style="color:#4aac09">￥{{item.orjine}}</text></view>
										</block>
										<view><text>时间：</text>{{item.addtime}}</view>
										<view><text>说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtcbzkbtn" @click="qtcbsetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtcbvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<block v-else>
						<view class="gwcno">
							<view class="icon"><text class="iconfont icon-zanwushuju"></text></view>
							<view class="tit">暂无其他成本</view>
						</view>
					</block>


				</view>
			</scroll-view>
		</tn-popup>

		<tn-popup v-model="xqrcdshow" mode="bottom" :borderRadius="20" :closeBtn='true'>
			<view class="popuptit" style="border-bottom:0">历史记录</view>

			<scroll-view scroll-y="true" style="height: 700rpx;">

             <view v-if="djdata.ddtype==3" style="padding:2upx 32upx 0 32upx;border-bottom:1px solid #eee;padding-bottom:20upx">服务费总额：<text style="color:#ff0000">￥{{djdata.fwfzjine}}</text></view>
<view v-if="djdata.ddtype==4"
					style="padding:2upx 32upx 0 32upx;border-bottom:1px solid #eee;padding-bottom:20upx">利息总金额：<text
						style="color:#ff0000">￥{{djdata.lxzjine}}</text>
						</view>
				<view class="xqrcdlist">

					<block v-if="xqrcddata.length > 0">

						<block v-for="(item,index) in xqrcddata" :key="index">
							<view class="lis-item  ">
								<view class="nrcon">
									<view class="info">
										<view class="rq"><text></text>{{item.addtime}}</view>
										<view><text>续期周期：</text>{{item.ywzqnum}} {{item.ywzqdwtxt}}</view>
										<view><text>到期时间：</text>{{item.ywdqtime}}</view>
										<view v-if="item.xqfwf > 0"><text>续期服务费：</text> <text
												class="jine">￥{{item.xqfwf}}</text></view>
										<view v-if="item.remark"><text>备注说明：</text>{{item.remark}}</view>
										<block v-if="item.picturescarr">
											<view class="qtfyzkbtn" @click="qtfysetvxq" :data-id="item.id">
												详情 <text class="iconfont icon-jtdown2"></text>
											</view>
										</block>
										<block v-if="item.id==thqtfyvid">
											<view class="des">
												<block v-if="item.picturescarr">
													<view class="pjimgcon">
														<block v-for="(itemx,indexx) in item.picturescarr"
															:key="indexx">
															<view class='item'>
																<image :src="staticsfile+itemx"
																	:data-src='staticsfile + itemx '
																	:data-picturescarr="item.picturescarr"
																	@tap='previewImagex'></image>
															</view>
														</block>
														<view class="clear"></view>
													</view>
												</block>
											</view>
										</block>

									</view>
									<view class="clear"></view>
								</view>
							</view>

						</block>


					</block>
					<view class="lis-item  ">
						<view class="nrcon">
							<view class="info">
								<view class="rq"><text></text>{{djdata.addtime}}</view>
								<view><text>周期：</text>{{djdata.ywzqnum}} {{djdata.ywzqdwtxt}}</view>
							
								<view v-if="djdata.orfwfzjine > 0"><text>服务费：</text> <text
										class="jine">￥{{djdata.orfwfzjine}}</text></view>
								<view v-if="djdata.orlxzjine > 0"><text>利息：</text> <text
										class="jine">￥{{djdata.orlxzjine}}</text></view>
											<view><text>到期时间：</text>{{djdata.orywdqtime}}</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>
					
				


				</view>

			</scroll-view>
		</tn-popup>



	</view>


</template>

<script>
	export default {
		data() {
			return {
				djid: 0,
				staticsfile: this.$staticsfile,
				img_url: [],
				img_url_ok: [],
				ywtime: '',
				djdata: '',
				djshop: '',
				vspxq: 0,
				spzongjia: 0,
				clicksta: false,
				remark: '',
				hkjhshow: false,
				hkjhtip: '',
				hkjhdata: '',

				qtfymxshow: false,
				qtfymxspname: '',
				qtfydata: '',
				thqtfyvid: 0,

				qtcbmxshow: false,
				qtcbmxspname: '',
				qtcbdata: '',
				thqtcbvid: 0,

				dtptxt: '',
				dtpjgtxt: '',
				vsfnr: 0,

				xqrcddata: '',
				xqrcdshow: false,

				czfrisfr: 0,

				isvallfrcz: 0,
				vsour: 0,
	
			}
		},

		onLoad(options) {
			let that = this;
			let djid = options.djid ? options.djid : 0;
			let vsour = options.vsour ? options.vsour : 0;
			this.vsour = vsour;
			this.djid = djid;
			this.loadData();
		},

		onShow() {
			if (uni.getStorageSync('thupsplist') == 1) {
				this.loadData();
				uni.setStorageSync('thupsplist', 0)
			}
		},

		methods: {
			xqrcdv() {
				this.xqrcdshow = true;
			},
			ckhkjh() {
				this.hkjhshow = true;
			},
			toback() {
				uni.navigateBack();
			},

			loadData() {
				let that = this;
				let djid = that.djid;
				let da = {
					djid: djid,
					vsour: that.vsour,
					et: 2,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/ydview/rkdeditx', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.djdata = res.data.djdata;
							that.djshop = res.data.djshop;
							that.remark = res.data.djdata.remark;
							that.spzongjia = res.data.spzongjia;
							that.hkjhtip = res.data.hkjhtip;
							that.hkjhdata = res.data.hkjhdata;
							that.dtptxt = res.data.dtptxt;
							that.dtpjgtxt = res.data.dtpjgtxt;
							that.xqrcddata = res.data.xqrcddata;
							that.isvallfrcz = res.data.isvallfrcz;
							if (res.data.djdata.ddtype == 3 || res.data.djdata.ddtype == 4) {
								that.czfrisfr = 1;
							}
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let picarr = e.currentTarget.dataset.picarr;
				let imgarr = [];
				picarr.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},


			previewImagex: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let imgarr = [src];
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			setvxq(e) {
				let that = this;
				let spid = e.currentTarget.dataset.spid;
				let djshop = this.djshop;

				for (var i = djshop.length - 1; i >= 0; i--) {
					if (djshop[i].id == spid) {
						if (djshop[i].vxq == 1) {
							djshop[i].vxq = 0;
						} else {
							djshop[i].vxq = 1;
						}
						break;
					}
				}
				this.djshop = djshop;

			},


			formSubmit(e) {
				let that = this;
				let pvalue = e.detail.value;
				uni.showModal({
					title: '提交确认',
					content: '确认提交审核吗？',
					success: function(e) {
						//点击确定
						if (e.confirm) {

							let da = {
								'djid': that.djid,
								'remark': pvalue.remark,
							}
							uni.showLoading({
								title: ''
							})
							that.$req.post('/v1/danju/rkdtjsh', da)
								.then(res => {
									uni.hideLoading();
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.redirectTo({
														url: '/pages/work/rukudan/index'
													})
												}, 1000);
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false,
										})
									}
								})

						}

					}
				})
			},




			qtfymx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtfymxspname = spname;
				this.getqyfylist(spid);
			},
			qtfysetvxq(e) {
				let thqtfyvid = e.currentTarget.dataset.id;
				if (thqtfyvid == this.thqtfyvid) {
					this.thqtfyvid = 0;
				} else {
					this.thqtfyvid = thqtfyvid;
				}
			},
			getqyfylist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtfylist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtfydata = res.data.qtfydata;
							that.qtfymxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			gotorlsbewm() {
				uni.navigateTo({
					url: './rlsbewm?djid=' + this.djid
				})
			},
			qtcbmx(e) {
				let spid = e.currentTarget.dataset.spid;
				let spname = e.currentTarget.dataset.spname;
				this.qtcbmxspname = spname;
				this.getqycblist(spid);
			},
			qtcbsetvxq(e) {
				let thqtcbvid = e.currentTarget.dataset.id;
				if (thqtcbvid == this.thqtcbvid) {
					this.thqtcbvid = 0;
				} else {
					this.thqtcbvid = thqtcbvid;
				}

			},
			getqycblist(spid) {
				let that = this;
				let da = {
					spid: spid,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/danju/rkdspqtcblist', da)
					.then(res => {
						uni.hideLoading();
						console.log('232', res);
						if (res.errcode == 0) {
							that.qtcbdata = res.data.qtcbdata;
							that.qtcbmxshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},

			setvzz() {
				if (this.vsfnr == 0) {
					this.vsfnr = 1;
				} else {
					this.vsfnr = 0;
				}
			},



		}
	}
</script>

<style lang="scss">
	.sqbdcon {}

	.tipsm {
		margin-bottom: 20upx
	}

	.rowx {
		position: relative;
		border-bottom: 1px solid #efefef;
	}

	.rowx .icon {
		position: absolute;
		right: 2upx;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 95px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
		overflow: hidden;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {}

	.rowx .inp .input {
		height: 90upx;
		line-height: 90upx;
	}

	.rowx:last-child {
		border-bottom: 0;
	}

	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx;
		position: relative;
	}

	.maxcon .vstit {
		font-weight: 700;
		margin-bottom: 10upx
	}

	.maxcon .vstit .iconfont {
		color: #ff0000;
		margin-right: 10upx
	}

	.maxcon .more {
		position: absolute;
		right: 28upx;
		top: 28upx;
		color: #888;
	}

	.maxcon .more .t1 {
		color: #7F7F7F;
	}

	.maxcon .more.vls {
		color: #1677FF;
	}

	.maxcon .more .iconfont {
		margin-left: 10upx
	}


	.spitem {
		margin-bottom: 20upx;
		padding-bottom: 20upx;
		border: 1px solid #FFD427;
		padding: 20upx;
		border-radius: 20upx;
		background: linear-gradient(to bottom, #FFF2C0, #fff, #fff, #fff, #fff);
	}

	.spitem .spname {
		font-weight: 600;
		margin-bottom: 20upx;
		font-size: 32upx;
		position: relative;
		padding-left: 20upx;
		line-height: 40upx;
	}

	.spitem .spname .tline {
		width: 10upx;
		background: #FFD427;
		height: 32upx;
		border-radius: 6upx;
		position: absolute;
		left: 0;
		top: 3upx;
	}

	.spitem .czcon {
		margin-top: 30upx
	}

	.spitem .czcon .czbtn {
		width: 48%;
		height: 80upx;
		line-height: 80upx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
		text-align: center
	}

	.spitem .czcon .btn1 {
		color: #ff0000;
		float: left
	}

	.spitem .czcon .btn2 {
		float: right
	}

	.spitem .viewxqcon {
		color: #ff6600;
		height: 40upx;
		line-height: 40upx;
		margin-bottom: 10upx;
		margin-top: 15upx;
		text-align: right;
		font-size: 24upx;
	}

	.spitem .status {
		border-radius: 30upx;
		padding: 5upx 15upx;
		color: #fff;
		background: #ccc;
		font-size: 24upx
	}

	.spitem .status.sta1 {
		background: #54D216;
	}

	.spitem .status.sta12 {
		background: #54D216;
	}

	.spitem .status.sta2 {
		background: #FF6600;
		color: #fff
	}

	.spitem .status.sta3 {
		background: #FF2828;
		color: #fff
	}

	.spitem .status.sta8 {
		background: #888;
	}

	.spitem .status.sta9 {
		background: #c76096;
		color: #fff
	}

	.spitem .sjstatus.sta1 {
		color: #43b058;
	}

	.spitem .sjstatus.sta2 {
		color: #888;
	}

	.spitem .czfrcon {
		border-top: 1px dashed #FFD427;
		margin-top: 25upx;
		padding: 25upx 0
	}

	.spitem .czfrcon .tit {
		float: left;
		font-size: 28upx;
		font-weight: 600
	}

	.spitem .czfrcon .tit .iconfont {
		margin-right: 10upx;
		color: #ff0000
	}

	.spitem .czfrcon .tadd {
		float: right;
	}

	.spitem .czfrcon .tadd .iconfont {
		margin-right: 10upx;
		color: #1677FF
	}


	.rowa {
		height: 90upx;
		line-height: 90upx;
		border-bottom: 1px dashed #efefef;
		overflow: hidden;
	}

	.rowa .tip {
		float: left;
	}

	.rowa .nr {
		float: right
	}

	.rowa:last-child {
		border-bottom: 0;
	}

	.bzcon {
		color: #7F7F7F;
		margin-bottom: 20upx
	}

	.rowxmttp {
		margin-top: 0upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}


	.uprzimgcon {
		margin-top: 25upx
	}

	.uprzimgcon .zzx {
		background: #EBEBEB;
		border-radius: 10upx;
		width: 48%;
		text-align: center;
	}

	.uprzimgcon .zzx.sf1 {
		float: left;
	}

	.uprzimgcon .zzx.sf2 {
		float: right;
	}

	.uprzimgcon .zzx .vt1 {
		font-size: 36rpx;
	}

	.uprzimgcon .zzx .vt2 {
		font-size: 24rpx;
		color: #7F7F7F;
		margin-top: 25upx
	}

	.uprzimgcon .con {
		padding: 20upx
	}

	.uprzimgcon image {
		width: 100%;
		height: 180upx;
		display: block;
		border-radius: 10upx;
	}

	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.zjzkicon {
		position: absolute;
		right: 25upx;
		top: 25upx;
	}
</style>