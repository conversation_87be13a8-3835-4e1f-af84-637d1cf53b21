<template>
	<view class="pages-a tn-safe-area-inset-bottom">

		<tn-nav-bar fixed backTitle=' ' :bottomShadow="false" :zIndex="100">
			<view slot="back" class='tn-custom-nav-bar__back'>
				<text class='icon tn-icon-left'></text>
			</view>
			<view class="tn-flex tn-flex-col-center tn-flex-row-center ">
				<text>设置客户负责人</text>
			</view>
			<view slot="right">
				<view class="trwhtip" @click="gotodyx">
					<text class="iconfont icon-tishi2" ></text>
				</view>
			</view>
		</tn-nav-bar>

		<view class="toplinex" :style="{marginTop: vuex_custom_bar_height + 'px'}"></view>

		<view class="">

			<form @submit="formSubmit">
				<view class="sqbdcon">


					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx">
							<view class="tit"><text class="redst"></text>客户姓名</view>
							<view class="inpcon">
								<view class="inp">
									{{khdata.realname}}({{khdata.id}})
								</view>
							</view>
							<view class="clear"></view>
						</view>
						<block v-if="khdata.ygname">
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>所属店铺</view>
								<view class="inpcon">
									<view class="inp">
										{{khdata.mdname}}
									</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit"><text class="redst"></text>原所属员工</view>
								<view class="inpcon">
									<view class="inp">
										{{khdata.ygname}}
									</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						<block v-else>
							<view class="rowx" >
								<view class="tit">录入店铺</view>
								<view class="inpcon">
									<view class="inp">{{khdata.lrmdname ? khdata.lrmdname : '--'}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit">录入员工</view>
								<view class="inpcon">
									<view class="inp">{{khdata.lrygname ? khdata.lrygname : '--'}}</view>
								</view>
								<view class="clear"></view>
							</view>
							<view class="rowx" >
								<view class="tit">录入时间</view>
								<view class="inpcon">
									<view class="inp">{{khdata.addtime}}</view>
								</view>
								<view class="clear"></view>
							</view>
						</block>
						
					</view>


                    <block v-if="!khdata.ygname">
						<view class="maxcon" style="padding:10upx 28upx">
							<view class="rowx" @click="seldianpu">
								<view class="icon"><text class="iconfont icon-jiantou"></text></view>
								<view class="tit"><text class="redst"></text>负责店铺</view>
								<view class="inpcon">
									<view class="inp">
										{{thmdname ? thmdname : '请选择负责店铺'}}
									</view>
								</view>
								<view class="clear"></view>
							</view>
						</view>
					</block>	

					<view class="maxcon" style="padding:10upx 28upx">
						<view class="rowx" @click="seljsdx">
							<view class="icon"><text class="iconfont icon-jiantou"></text></view>
							<view class="tit"><text class="redst"></text>负责员工</view>
							<view class="inpcon">
								<view class="inp">
									{{thygname ? thygname : '请选择负责员工'}}
								</view>
							</view>
							<view class="clear"></view>
						</view>
					</view>

					<view class="maxcon">
						<view class="vstit">备注说明</view>

						<view class="bzcon">
							<textarea name="remark" class="beizhu"></textarea>
						</view>

						<view class="rowxmttp">
							<view class="imgconmt" style="margin:20upx 10upx 0upx 0upx">
								<block v-for="(item,index) in img_url_ok" :key="index">
									<view class='item'>
										<view class="del" @tap="deleteImg" :data-index="index"><i
												class="iconfont icon-shanchu2"></i></view>
										<image :src="staticsfile+item" :data-src='staticsfile + item '
											@tap='previewImage'></image>
									</view>
								</block>
								<view class='item' v-if="img_url_ok.length <= 8">
									<image @tap="chooseimage" src='/static/images/uppicaddx.png'></image>
								</view>
								<view class="clear"></view>
							</view>
						</view>
					</view>



				</view>

				<view class="tn-flex tn-footerfixed">
					<view class="tn-flex-1 justify-content-item tn-text-center">
						<button class="bomsubbtn" form-type="submit">
							<text>提交</text>
						</button>
					</view>
				</view>
			</form>







		</view>

		<view style="height:120upx"></view>

		<tn-popup v-model="jsdxselshow" mode="bottom" :borderRadius="20" :closeBtn="true">
			<view class="popuptit">请选择负责员工</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="selqyyglist">
					<radio-group @change="jsrysetChange">
						
						<view class="item">
							<label>
								<view class="icon">
									<radio :value="1" :data-name="thmdname"  color="#FFD427" />
								</view>
								<view class="vstxt">{{thmdname}}</view>
								<view class="clear"></view>
							</label>
						</view>
						
						<block v-for="(item, index) in yglisdata" :key="index">
							<view class="item">
								<label>
									<view class="icon">
										<radio :value="item.id" :data-name="item.realanme" color="#FFD427" />
									</view>
									<view class="vstxt">{{item.realname}}</view>
									<view class="mdtt">({{item.mdname}})</view>
									<view class="clear"></view>
								</label>
							</view>
						</block>
					</radio-group>
					</block>

					<view style="height:160upx"></view>
					<view class="tn-flex tn-footerfixed">
						<view class="tn-flex-1 justify-content-item tn-text-center">
							<view class="bomsfleft">
								<button class="bomsubbtn" @click="closejsdx" style="background:#ddd;">
									<text>关闭</text>
								</button>
							</view>
							<view class="bomsfright">
								<button class="bomsubbtn" @click="qrzdysxm">
									<text>确认选择</text>
								</button>
							</view>
							<view class="clear"></view>
						</view>
					</view>

				</view>
			</scroll-view>
		</tn-popup>

		<tn-popup v-model="dpselshow" mode="bottom" :borderRadius="20" :closeBtn=true>
			<view class="popuptit">选择店铺</view>
			<scroll-view scroll-y="true" style="height: 800rpx;">
				<view class="seldplist">
					<block v-for="(item,index) in dpdata" :key="index">

						<view class="item" @click="selthdp" :data-dpid="item.id" :data-dpname="item.title">
							<view class="xzbtn">选择</view>
							<view class="tx">
								<image :src="staticsfile + item.logo"></image>
							</view>
							<view class="xx">
								<view class="name">{{item.title}}</view>
								<view class="dizhi"><text class="iconfont icon-dingweiweizhi"></text> {{item.dizhi}}
								</view>
							</view>
							<view class="clear"></view>
						</view>

					</block>
				</view>
			</scroll-view>
		</tn-popup>

	</view>


</template>

<script>
	export default {
		data() {
			return {
				staticsfile: this.$staticsfile,
				html: '',
				ygdata: '',
				status: 0,
				jine: 0,
				img_url: [],
				img_url_ok: [],
				systixian: '',
				tipwztxt: '',
				khid: 0,
				khdata: '',
				khidstr: '',
				khnamestr: '',
				thygid: 0,
				thygname: '',
				
				yglisdata: [],
				jsdxselshow: false,
				ygkw: '',
				dpselshow: false,
				dpdata: '',
				thmdid: 0,
				thmdname: '',
			}
		},

		onLoad(options) {
			let that = this;
			let khid = options.khid ? options.khid : 0;
			let khidstr = options.khidstr ? options.khidstr : '';
			this.khid = khid;
			this.loadData();
		},
		onShow() {
			let thygid = uni.getStorageSync('thygid');
			let thygname = uni.getStorageSync('thygname');
			if (thygid) {
				this.thygid = thygid;
				this.thygname = thygname;
				uni.setStorageSync('thygid', ''); 
				uni.setStorageSync('thygname', '');
			}
		},

		methods: {
			gotodyx() {
					uni.navigateTo({
						url: '/pages/danye/index?id=13'
					})
			},
			seljsdx() {
			
				let that = this;
				
				if(!that.khdata.ygname && !that.thmdid){
					uni.showToast({
						icon: 'none',
						title: '请先选择负责店铺'
					});
					return false;
				}
				
				
				let da = {
					ygkw: that.ygkw,
					khid: that.khid,
					mdid: that.thmdid ? that.thmdid : 0,
					gtype: 1,
					bgfzr: 1,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/qykhgl/getqyygdata', da)
					.then(res => {
						uni.hideLoading();
						if (res.errcode == 0) {
							that.yglisdata = res.data.yglisdata;
							that.jsdxselshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									
								}
							})
						}
					})
			},
			closejsdx() {
				this.jsdxselshow = false;
			},
			jsrysetChange(e) {
				console.log(e);
				let value = e.detail.value;
				this.thsjsdxtemp = value;
			},
			
			qrzdysxm() {
				let that = this;
				let jsvalue = this.thsjsdxtemp;
				if (!jsvalue) {
					uni.showToast({
						icon: 'none',
						title: '请选择负责人员',
					});
					return false;
				}
				
				if(jsvalue==1){
					that.jsdxselshow = false;
					that.thygid = 1;
					that.thygname = that.thmdname;
					return false;
				}
				
				let da = {
					ygid: jsvalue,
				
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/qykhgl/getjsygdata', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.jsdxselshow = false;
							if(res.data.thygdata){
								that.thygid = res.data.thygdata.id;
								that.thygname = res.data.thygdata.realname;
							}
						
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {

								}
							})
						}
					})

			},
		

		
			toback: function() {
				uni.navigateBack();
			},

			loadData() {
				let that = this;
				let da = {
					khid: that.khid,
					khidstr: that.khidstr,
				}
				uni.showLoading({
					title: ''
				})
				this.$req.post('/v1/qykhgl/setfzr', da)
					.then(res => {
						uni.hideLoading();
						console.log(res);
						if (res.errcode == 0) {
							that.khdata = res.data.khdata;
							
							if(that.khdata.mdid){
								that.thmdid = that.khdata.mdid;
								that.thmdname = that.khdata.mdname;
							}

						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})

			},

			chooseimage() {
				let that = this;
				let img_url_ok = that.img_url_ok;
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ['original', 'compressed'],
					success: (resx) => {
						const tempFilePaths = resx.tempFilePaths;


						for (let i = 0; i < tempFilePaths.length; i++) {
							let da = {
								filePath: tempFilePaths[i],
								name: 'file'
							}
							uni.showLoading({
								title: '上传中...'
							})
							this.$req.upload('/v1/upload/upfile', da)
								.then(res => {
									uni.hideLoading();
									// res = JSON.parse(res);
									if (res.errcode == 0) {
										img_url_ok.push(res.data.fname);
										that.img_url_ok = img_url_ok;

										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})
						}

					}
				});

			},
			previewImage: function(e) {
				let that = this;
				let src = e.currentTarget.dataset.src;
				let img_url_ok = that.img_url_ok;
				let imgarr = [];
				img_url_ok.forEach(function(item, index, arr) {
					imgarr[index] = that.staticsfile + item;
				});
				wx.previewImage({
					current: src,
					urls: imgarr
				});
			},
			deleteImg: function(e) {
				let that = this;
				let index = e.currentTarget.dataset.index;
				let img_url_ok = that.img_url_ok;
				img_url_ok.splice(index, 1); //从数组中删除index下标位置，指定数量1，返回新的数组
				that.img_url_ok = img_url_ok;
			},



			formSubmit: function(e) {
				let that = this;
				var formdata = e.detail.value


				let pvalue = e.detail.value;


				if (!that.thygid || that.thygid == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择负责员工',
					});
					return false;
				}


				if (!pvalue.remark) {
					uni.showToast({
						icon: 'none',
						title: '请输入备注说明',
					});
					return false;
				}

				let xtip = "以上信息已确认无误并提交吗？";
				uni.showModal({
					title: that.mdname,
					content: xtip,
					success: function(e) {
						//点击确定
						if (e.confirm) {
							uni.showLoading({
								title: '处理中...'
							})
							let da = {
								'khid': that.khid,
								'remark': pvalue.remark ? pvalue.remark : '',
								'pictures': that.img_url_ok ? that.img_url_ok : '',
								'ygid': that.thygid ? that.thygid : 0,
								'thmdid': that.thmdid ? that.thmdid : 0,
							}
							that.$req.post('/v1/qykhgl/setfzrsave', da)
								.then(res => {
									uni.hideLoading();
									console.log(res);
									if (res.errcode == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg,
											success() {
												setTimeout(function() {
													uni.navigateBack()
												}, 1000)
											}
										});
									} else {
										uni.showModal({
											content: res.msg,
											showCancel: false
										})
									}

								})


						}
					}
				})

			},
			
			
			seldianpu() {
				let that = this;
				let da = {}
				this.$req.post('/v1/com/getdpdata', da)
					.then(res => {
						console.log(111, res);
						if (res.errcode == 0) {
							that.dpdata = res.data.dpdata;
							that.dpselshow = true;
						} else {
							uni.showModal({
								content: res.msg,
								showCancel: false,
								success() {
									uni.navigateBack();
								}
							})
						}
					})
			},
			selthdp(e) {
				let that=this;
				let dpid = e.currentTarget.dataset.dpid;
				let dpname = e.currentTarget.dataset.dpname;
				this.thmdid = dpid;
				this.thmdname = dpname;
				that.thygid = 0;
				that.thygname = '';
				this.thsjsdxtemp='';
				this.dpselshow = false;
			},
			
			
			
			
			
			



		}
	}
</script>

<style lang="scss">
	.toplinex {
		border-top: 1px solid #fff
	}

	.matxcont {
		margin: 32upx;
		padding: 32upx;
		border-radius: 20upx;
		background: #fff;
		min-height: 500upx;
	}

	.notixiancon {
		text-align: center;
	}

	.tipcon {
		padding: 50px 0
	}

	.tipcon text {
		font-size: 55px;
		color: #FFD427
	}

	.smlink {
		margin-top: 40px
	}

	.ttinfo {
		position: relative;
	}

	.ttinfo .icon {
		position: absolute;
		right: 0upx;
		top: 30upx
	}

	.ttinfo .icon .iconfont {
		color: #ddd
	}

	.ttinfo .inf1 {
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
		padding-bottom: 32upx;
		margin-bottom: 32upx
	}

	.ttinfo .inf1 .tx {}

	.ttinfo .inf1 .xx {
		padding-top: 10upx
	}

	.ttinfo .inf1 .tx image {
		width: 100upx;
		height: 100upx;
		display: block;
		border-radius: 100upx;
		margin: 0 auto;
	}

	.ttinfo .inf1 .name {
		font-size: 28rpx;
		font-weight: 600;
		height: 40upx;
		line-height: 40upx;
	}

	.ttinfo .inf1 .tel {
		color: #7F7F7F;
		font-size: 24rpx;
		margin-top: 10upx
	}

	.ttinfo .inf1 .tel text {
		margin-right: 30upx
	}

	.ttinfo .inf2 {
		text-align: center;
		padding-bottom: 30upx
	}

	.ttinfo .inf2 .kyjine {
		font-size: 48rpx;
		font-weight: 600;
	}

	.ttinfo .inf2 .tmsm {
		color: #7F7F7F;
		margin-top: 20upx;
		font-size: 24rpx;
	}


	.maxcon {
		background: #fff;
		border-radius: 20upx;
		padding: 28upx;
		margin-bottom: 20upx
	}

	.maxcon .vstit {
		margin-bottom: 20upx
	}


	.sqbdcon {
		margin: 32upx;
	}


	.beizhu {
		border: 1px solid #efefef;
		padding: 20upx;
		height: 200upx;
		border-radius: 10upx;
		width: 100%;
	}

	.rowxmttp {
		margin-top: 30upx
	}

	.imgconmt {}

	.imgconmt .item {
		float: left;
		margin-right: 20upx;
		margin-bottom: 20upx;
		position: relative
	}

	.imgconmt .item .del {
		position: absolute;
		right: -7px;
		top: -7px;
		z-index: 200
	}

	.imgconmt .item .del i {
		font-size: 18px;
		color: #333
	}

	.imgconmt image {
		width: 160upx;
		height: 160upx;
		display: block;
		border-radius: 3px
	}

	.jinecon {
		position: relative;
		margin: 40upx 0
	}

	.jinecon .icon {
		position: absolute;
		left: 0;
		top: 10upx;
	}

	.jinecon .icon .iconfont {
		font-size: 40upx
	}

	.jinecon .inputss {
		font-size: 40upx;
		color: #000;
		padding-left: 40upx
	}



	.rowx {
		position: relative;
		border-bottom: 1px solid #F0F0F0;
	}

	.rowx .icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 90upx;
		line-height: 90upx
	}

	.rowx .icon .iconfont {
		color: #ddd
	}

	.rowx .tit {
		float: left;
		font-size: 28upx;
		color: #333;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .tit .redst {
		color: #ff0000;
		margin-right: 2px;
	}

	.rowx .tit .redst2 {
		color: #fff;
		margin-right: 2px;
	}

	.rowx .inpcon {
		padding: 0 5px;
		float: right;
		width: calc(100% - 105px);
		font-size: 28upx;
		height: 90upx;
		line-height: 90upx;
	}

	.rowx .inpcon.hs {
		color: #888
	}

	.rowx .inp {
		color: #7F7F7F;
	}

	.rowx:last-child {
		border-bottom: 0;
	}
</style>